#!/usr/bin/env python3
"""
Update Admin Password in Existing Databases
This script updates the admin password in all existing database files
"""

import sqlite3
import hashlib
from pathlib import Path

def update_database_password(db_path, new_password):
    """Update admin password in a specific database file"""
    
    if not db_path.exists():
        print(f"⚠️  Database not found: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # Check if admin user exists
        c.execute("SELECT COUNT(*) FROM users WHERE username='admin'")
        admin_count = c.fetchone()[0]
        
        if admin_count == 0:
            print(f"⚠️  No admin user found in: {db_path}")
            conn.close()
            return False
        
        # Hash the new password
        hashed_password = hashlib.sha256(new_password.encode()).hexdigest()
        
        # Update admin password
        c.execute("""
            UPDATE users SET password = ? WHERE username = 'admin'
        """, (hashed_password,))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Updated admin password in: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {db_path}: {e}")
        return False

def main():
    """Update admin password in all database files"""
    
    new_password = "@H@W@LeComptoir@"
    
    print("🔑 UPDATING ADMIN PASSWORD IN ALL DATABASES")
    print("=" * 50)
    print(f"New password: {new_password}")
    print("=" * 50)
    
    # List of database files to update
    database_files = [
        Path("pos_system.db"),                    # Main database
        Path("YES/pos_system.db"),               # Protected version
        Path("YES_OBFUSCATED/pos_system.db"),    # Obfuscated version
        Path("YES_COMPILED/pos_system.db"),      # Compiled version (if exists)
        Path("YES_SECURE/pos_system.db"),        # Secure version (if exists)
    ]
    
    updated_count = 0
    total_count = 0
    
    for db_file in database_files:
        total_count += 1
        if update_database_password(db_file, new_password):
            updated_count += 1
    
    print("\n" + "=" * 50)
    print("📊 UPDATE SUMMARY")
    print("=" * 50)
    print(f"✅ Successfully updated: {updated_count}/{total_count} databases")
    
    if updated_count == total_count:
        print("🎉 All databases updated successfully!")
    else:
        print(f"⚠️  {total_count - updated_count} databases could not be updated")
    
    print(f"\n🔐 Admin login credentials:")
    print(f"   Username: admin")
    print(f"   Password: {new_password}")
    
    # Verify the updates
    print("\n🔍 VERIFICATION")
    print("=" * 30)
    
    for db_file in database_files:
        if db_file.exists():
            try:
                conn = sqlite3.connect(db_file)
                c = conn.cursor()
                
                # Test authentication
                hashed_password = hashlib.sha256(new_password.encode()).hexdigest()
                c.execute("""
                    SELECT username FROM users 
                    WHERE username = 'admin' AND password = ?
                """, (hashed_password,))
                
                result = c.fetchone()
                conn.close()
                
                if result:
                    print(f"✅ {db_file}: Password verified")
                else:
                    print(f"❌ {db_file}: Password verification failed")
                    
            except Exception as e:
                print(f"⚠️  {db_file}: Could not verify - {e}")

if __name__ == "__main__":
    main()
