# POS System Requirements
# Install with: pip install -r requirements.txt
# Or run: python install.py

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (automatically installed on Windows only)
pywin32>=300; sys_platform == "win32"

# Note: The following are included with Python:
# - tkinter (GUI framework)
# - sqlite3 (database support)
# - datetime, os, sys, platform, hashlib, getpass (standard library)
