#!/usr/bin/env python3
"""
Test the 24-hour trial license LC-78C8C6898C3A4
"""

import sys
import os
from pathlib import Path
import json
from datetime import datetime

def test_trial_license():
    """Test the trial license functionality"""
    
    print("Testing 24-Hour Trial License")
    print("=" * 40)
    print("License Code: LC-78C8C6898C3A4")
    print("=" * 40)
    
    # Test in main directory
    print("\n📋 Testing main directory...")
    sys.path.insert(0, str(Path(".").absolute()))
    
    try:
        from license_client import LicenseClient
        
        client = LicenseClient()
        
        # Clean up any existing trial file for fresh test
        trial_file = Path("trial_license.dat")
        if trial_file.exists():
            trial_file.unlink()
            print("🧹 Cleaned up existing trial file")
        
        # Test first activation
        print("\n🔄 Testing first activation...")
        result = client.validate_trial_license("LC-78C8C6898C3A4")
        
        if result["valid"]:
            print(f"✅ First activation successful: {result['message']}")
        else:
            print(f"❌ First activation failed: {result['error']}")
            return False
        
        # Test second activation (should show remaining time)
        print("\n🔄 Testing second activation...")
        result2 = client.validate_trial_license("LC-78C8C6898C3A4")
        
        if result2["valid"]:
            print(f"✅ Second activation successful: {result2['message']}")
        else:
            print(f"❌ Second activation failed: {result2['error']}")
            return False
        
        # Test offline validation
        print("\n🔄 Testing offline validation...")
        result3 = client.validate_license_offline("LC-78C8C6898C3A4")
        
        if result3["valid"]:
            print(f"✅ Offline validation successful: {result3['message']}")
        else:
            print(f"❌ Offline validation failed: {result3['error']}")
            return False
        
        # Test server validation
        print("\n🔄 Testing server validation...")
        result4 = client.validate_license_with_server("LC-78C8C6898C3A4", "TEST123")
        
        if result4["valid"]:
            print(f"✅ Server validation successful: {result4['message']}")
        else:
            print(f"❌ Server validation failed: {result4['error']}")
            return False
        
        # Check trial file contents
        print("\n📄 Checking trial file contents...")
        if trial_file.exists():
            with open(trial_file, 'r') as f:
                trial_data = json.loads(f.read())
            
            print(f"   License Key: {trial_data['license_key']}")
            print(f"   Start Time: {trial_data['start_time']}")
            print(f"   Computer ID: {trial_data['computer_id']}")
            
            # Calculate remaining time
            start_time = datetime.fromisoformat(trial_data['start_time'])
            elapsed_hours = (datetime.now() - start_time).total_seconds() / 3600
            remaining_hours = 24 - elapsed_hours
            
            print(f"   Elapsed: {elapsed_hours:.2f} hours")
            print(f"   Remaining: {remaining_hours:.2f} hours")
        
    except Exception as e:
        print(f"❌ Error testing main directory: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test in YES directory
    print("\n📋 Testing YES directory...")
    sys.path.insert(0, str(Path("YES").absolute()))
    
    try:
        # Clear module cache
        if 'license_client' in sys.modules:
            del sys.modules['license_client']
        
        from license_client import LicenseClient as LicenseClientYES
        
        client_yes = LicenseClientYES()
        
        # Clean up trial file in YES directory
        yes_trial_file = Path("YES/trial_license.dat")
        if yes_trial_file.exists():
            yes_trial_file.unlink()
        
        # Test in YES directory
        os.chdir("YES")
        result_yes = client_yes.validate_trial_license("LC-78C8C6898C3A4")
        os.chdir("..")
        
        if result_yes["valid"]:
            print(f"✅ YES directory test successful: {result_yes['message']}")
        else:
            print(f"❌ YES directory test failed: {result_yes['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing YES directory: {e}")
        return False
    
    # Test in YES_OBFUSCATED directory
    print("\n📋 Testing YES_OBFUSCATED directory...")
    sys.path.insert(0, str(Path("YES_OBFUSCATED").absolute()))
    
    try:
        # Clear module cache
        if 'license_client' in sys.modules:
            del sys.modules['license_client']
        
        # Test obfuscated version
        os.chdir("YES_OBFUSCATED")
        
        # Import the obfuscated license_client
        import license_client as license_client_obf
        
        client_obf = license_client_obf.LicenseClient()
        
        # Clean up trial file
        obf_trial_file = Path("trial_license.dat")
        if obf_trial_file.exists():
            obf_trial_file.unlink()
        
        result_obf = client_obf.validate_trial_license("LC-78C8C6898C3A4")
        os.chdir("..")
        
        if result_obf["valid"]:
            print(f"✅ YES_OBFUSCATED test successful: {result_obf['message']}")
        else:
            print(f"❌ YES_OBFUSCATED test failed: {result_obf['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing YES_OBFUSCATED directory: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 40)
    print("🎉 TRIAL LICENSE TEST PASSED!")
    print("=" * 40)
    print("✅ Trial license LC-78C8C6898C3A4 works in all versions:")
    print("   - Main directory")
    print("   - YES (protected)")
    print("   - YES_OBFUSCATED (secure)")
    print("✅ 24-hour time limit is enforced")
    print("✅ Trial data is properly tracked")
    
    return True

if __name__ == "__main__":
    success = test_trial_license()
    if success:
        print("\n🔑 Trial license is working correctly!")
        print("🕐 Clients can use LC-78C8C6898C3A4 for 24-hour access")
    else:
        print("\n❌ Trial license test failed!")
    sys.exit(0 if success else 1)
