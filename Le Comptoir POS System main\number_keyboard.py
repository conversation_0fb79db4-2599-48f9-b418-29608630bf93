# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['IGgNMCEqLA9gHjwwDRxwOQdlIComfTsbJz96Nno8FXQLeAt2BmMVHWEUNzB9GhYldwcZGG0yGwNoKicqbRE6ZDkyej8zOTwJeTc+', 'Lx0RcjQ9Jwp/dBkJNi4CMyoSPAsABQU/EzcsOTkADXtuPzACWkdfLC0iGT52awILcBMKAx01MxgufysTfi0VOTU8IhkhOx4ZDgo7', 'WV5pJCpheWoLeAknLRMJdBUga2tjDA5yBwY1PWY5IANyLQ8IBGgDIwo6SgMZTC1seiw+GycjM3Z6Ey11Oxw/MQd4NAgnZCgEBRZn', 'UEdUWygBJhAZbjg5AgYNZQo5NS4TPXsHFDsRaxAxOWRtMjQxBQkYE390DS1CRnB9BxgfIjQZbTU3Lh0VcwQEI2ooGAMzJw0HJX8W', 'AyUkBiQMBX0jFSZfB0VwCiMhGgRuaGQNNycjLzgKMgkUfHcUMgE0ZycFFQwyNC54JTcSZAI+OQJDX1c2bBQlJAkLHn02BCAvCHIA', 'Ph4rEjMXJnMCAQAID2MwZhNuFx93FiRyYyYALzgnbgEIFjAZexwGIj16LX0BNG4baj4+HXx8UhRiJTJnc2w9IXZheSl/cRYRFQUj', 'FjY5GBc+PSYNP38IJiAVIytcXgBGKmU6DX0railzEDkxPxgsFwBrEQoTBB00JSYLLmMuGyAsBCMqEgM8Z2pecXk4YiUXNDE9IHUB', 'exwuGSs/C3RkfC8SHQVoDTw8JS0lOQgrBjcmaiglIxcWbnwZbAEOBhV/Ij8aHTU6OQUEaThqPzZUCFZ5HCV7LCUAD2YMDWxhLz44', 'Zgx0Dnk7ZmYWMmJnbiEZLAwuKnF7GyxjLmklAhIDNxt+CCYXDwx2NgBSVHwnDysCfTERfDUPOhofDxs9HGw7BxcSExotYBl7BCER', 'bDspaCF2AnQUKD0jEiV/MTosb3lxDi1KV34EPQ8lLTcPGScXdS0+CCgubCAGBBYxLiFrDhQfPgIDGzkzOD4yGh0xJWJFBUEYMHkm', 'GBwDDWkVdBEpEyh2ZHRmCDsgai0sDSUicDoocjlrZWJELCQbZB0XPGEvKTM1DTo5KCFtOHQzHCsgCDIfGmR2JBknKRwNfQ4fOENi', 'AQwiFxJ6MRwzNzxkBg11LTh9KTUnFCkOdgZ6cW4NJm0/bhJgNgAUYj8tNCMPGT0sDhwmeQwECn43AzU5LToIMyoSNihIA3kMFBo0', 'YGZ/EhQGaggsJgIibRMyHyw/A2Z/NBg+QFReBiUQHmMdOwc7MCY8PAoPIzcgEgIVez4SAmkaZzYkCBkiCxc8b2AHdmZ4V2tRZhx/', 'KRsvFgYZJjsTFDsrPCUaLAZkFyZleB0ndj48Ow0+fBMDFUBkRHVufg4xag8GFxIlDxVqAAcxPCk5AxoTCCg7KQsgLDEwBCxkCiYY', 'fgVycgc9LSJsKCc7GFNmek0PJngOeTATGXcFAxszGnAcNzorFRcqdiE4MmoVfyoaHCYLIRg8HCslQHQBUhkxH2F5JAowJnciKggr', 'dHsqABQbBHAOGwgQGBwGP3smDCIoNxw0fXA3DWsFAB8PEXxteXU8AwFoB2IVGSsgKBN+cgl5EwQ0YhcXMjI2YQcCLCYBK20pAwMB', 'ODYLdWZkAhMlMB19MywVO3oIInh3Yw8AIH5mIwoCLwckaxwnGnAFSVhSDCwfN3k1bnwLFGI+fBY3P20eeCNwHzEDL2FiJ2YJC38/', 'OjRwdhYFDj8ZNB8FcHAxeBE0bgE/Dn8rAhE/K3scPXARbVYJAVIsOhYEMHRmMXJoYCoJfyc8O2wGeBczfAA5YiA3BH0EASA7dygJ', 'EDlqCh55NiN2Ah0lBT0/dXszHRgIZjw/DwUERF9GFhgbJWAOPSUxew80Kx8JPjM1IBcwMhA2MTlqejcLdAAPLH5pH3xyB1cEBVJo', 'DSlqa28mHnosKnUiAH8IN300Gi9EdH5SKWQoZAR5MQEqDC8xEyMgdR0WewoJEgc4KwMnfG1zIQx4CzwpeCAXClhxYkAMGR5hZC8Y', 'DgoKPHsKLwchOiUKHXccKwgne3sUDzItOhN1AH1NEgB7fx4ga3w8DAAEBCYFLi9uLCglHA8lKip9ORMUKTcMLnk9JHwWPEZaHWY5', 'X1U9OjptFTAueC11JxcwZ3A0bm8HBDd+ciU7ZT4+A24zI39+BmYufD5oVwlWeDwaBhhkNg8qfDpnJwB6EA44DwR5DyJ1BhAqAA0M', 'PzcHOyklKRUsJRYNYAVmGW5gOkoDfkw5JR4BEXUmCzAIJSgddSRpAWt9GXQGIWZnYCAibQMwISshZRF7MjoHY1YdADglIC02cA4V', 'Yi10BScQey95KxweOGJwKgETHCsWEXNhCgdkRFIoHnktMAlwJQgBOyA0Gw4idmcGBw9kARoLYzk8MTQTOAgnATsbJzAoY3RffSkT', 'KWUAHXRoED95OiURIDo1RWgZYTxlBjhmAjs4NwEbMSQWGCMxJTIkDiNqPgw3anllImwELXt+JSJ2EHRWAHAFEjwiHGMrFWFwBjw2', 'c2UtNx8+PQQ/K1l2BwMyPQBlOhhmeDZ7YwgcLws9ay4mB2wOJycSKmU+fwQSNRADK2gmKgE1UQFfeGsXHDYLDjgVJy4NIR1+eQ0S', 'DBYwaiMQF3wOBjAECHMOLB4fJxINIxoVJCogZB0FHnFgJypgLXJsAmJ0Xmg5LWw7Cy81JC8NFzJ8cm0/bxEnJA8XIjEFJCMwFSAN', 'ISwjHHd7BC1/LicaPmQBaydIR1sEKx99NQQQaCQ8Nw8cMS0TKSltBgp1LDQ5JmI2OQNwDCUoDTw7Hi0cbRloXHw1OAQ1EycFY31w', 'ZAlQV29mIz95eAwXfToyZnMBBgsYOT8JdgoIBzo1MHkOFTdsZhk8byV1GBkGWkcDDx0VDB00GQMwJHoXEX42PDUIKgsSfw4KaAUq', 'IzU2InY8bW1nDwcbCjYzPWEFe24SIX4hAhUlAyk2SAADcwkGLyAeNTs7C3ozYzQGLgBrCHFudhwNHxEZYR0lMmgkfBwqKH4uOzcA', 'NjsAMBp4ACs7eCcDHgRAXHAeZj4EBA0aKzRsIxQ/fRBsHwYuNxJgHysvBHksIxN3BRgBPWd6FDALR3N/UGoFBhZnc2spdjkWaiR0', 'byo7G2YHNxoIA2EWMzQ0B2NdU0IUOicRZSgNPRMPOjMOJhkiOD4ELwgFDRMLETAVOzIObX8IeCd/FGgcWAUdYQczIiEaFTofHCYM', 'MBcUNSIGYAQTLH9gO2hzBw85RwJ6WmdnKhoYJw8QBwEjJSR/DRkLMH0zNHsUPGY3A3owNA8MCB13OiNwG25dA1MALw0EZxhzJzVx', 'FjcpLRZ2LgJWRFtnPXR7OCVwYBF2bRQLOgwfaDUGeHNkMwMsYmIfEHBwHCxkOmkRcQ4oeRtIZmclOD8nBgVmFyseAgMLdSM3EwB1', 'YHgfMSMcNzpuGh0nGQJlJx0FID4KOHkTL24RDmkYIwQzdxoDfQdwOnQ6G1pXClUXZCYFPStmKgh7PSsdG24DbTkhOxUvIGVvfxYD', 'bggwcQ4FaAQxFmlaBEJwCBN+PB15JWZuGgF9PCQrHjBqDzQrYAlnOiQ9Hi5qASIZZDwbPRw1KUJcQ3YnGTkYKHMVBg4XNzcHBCgL', 'MyYMCydwDjQ0J11IYnw1Gi8CGRFpGiQMGgImHRttDTlwO3R+PRUtIwgXOmoXIXEGBAV+MzgbSkpGAm0lOjh5JDcqCxQwMDcpAmIy', 'Mx10NQUrA3wQAmIaO2cfc20aQ1VAbTp+dBYVAjsECyk3MS07c21hKT0CDiYWFWYmFTskIwFjPxUqOC0rDSoFBwJQOjYcHxMYC3gX', 'awoTIzFyYCd9MiJmJgQzdzsZZDoSLxcvO3NdQXNtDTkHYw4GCi8bZxR2CyUtIGl4JgURd30tJSAhBykCGXAXKW0mBCoIRl0GVW0v', 'OHEKZ0ofRAF0DQsAIAoYIwkJIWApBAcROjorNQ07KhkIOgY5In06YwcOKyYTISEsdQh7UWcmHWd9MR07MiphIQscERIPNnoLJX19', 'cXQacww3OygoGzotEwUPdyoNOygBD1hJU0IqDXsAIAITIgZwNB0cOzQADxccLSUDajMXNhBgJiwFbSYALyUxIz5wZkJ5fBAAf3s6', 'KQsgHwhwLBQ6KT0MfzMMNQwIDnofHwZ6ABsOd10CTA1jGw0RCy4QBHInCgM+d2I9N3gbFjEPJGweBBoQEzk2Ai4rEzh3FRpYZENc', 'fyQ3KBceCCATLTYMJWMZYCIGA24qdF4CV3IwARg1MCAlfAATFjZ2JC8SbXQHJjEpDGQbICUmDgl3Pyo5GGdyNC43antqcSY5ZzER', 'MQRmISIRJggReScVLhQZIjc4IyEyFmAGBD0sOHknBDs+MSwvBAJaWw8UIWJqdjQxAXAXeSk8JDc3NwtqEyF2amtjY3sAPystLzt+', 'GAECeWwnaDYmGWFtej92bVdoC18UEgcGMxY3FxN0Z2MJBBQOahMFF3ABATMPPRo5fw0wDDs5FD0tKDo1BVhfQGYjK2x5MjICfDUG', 'VwYUHjURAm0VLw8SYCB7cw4QNXEuFzIxCjVjawU/BiwhAxl4LwMKAD1jSWJYDzQ1LWcYOQofMQw0cnUDL2gxcW4qLQ8wMWllJiB0', 'O3YpZwcmIhwqByAxEztjGXotDhwINgIcLBYuGiZgNi99fQAnS2NXchQgHCYwIi0hHSc2ZTYjIm8DPj0nDwQOIi1lfXtnCCVgGXsr', 'fXILOShvDAIzKRpqYDw9JDwZIiJ/ewslJz0oNzZBB3Viai0qZTx3NTVzKQNlMh5udWE5Pm43PQsQKxQKLB4BJ3sPLihqBDw4b1RA', 'M2UyBiJsGDAbGA09I2cmMiQuLRBsZRB5PCs7Nj07WkJjAT40PxYALAc1CBRlYHR+Kjg9Zn0iNCAfaw01Ny4nCS1sfix2KDExYCl9', 'HHsWODwcBjQUQHxzdg8EGmIaCxdjFygEZnYjDh9oJz0Ucy8xAiUeKn4SP3ADDyV/NHgNDApXAVFlbDkYYCQgbhIxJDEFKBQ1DDcM', 'P2V5FzkuGxk+EHkbaTIHGiwlM3MDORYTLGZ8JyAFNQEpBjMfbEhnV2xoHAliEDcrYQYWbWYQKDtsFAU9cTsFdwooPDknbDI7HB4C', 'Ki85MDYmPzQ9FTx7FxItLDM5OX0lEhEaP2Z2DG44Jh5ta1EZUW49DyBmKToqE3M5KnMFNHETF3wocA4EJjYYYQAVEzR/YioXGHIX', 'JyYSajQ+JQQDDjo7HmQhAhwGbDN3eTVzaggHaGAHBRojJnsjES9lH3USIB0cLy52FwQEOzl0IxM1ZRYYcC4GcQYoNHILMAVcRnBA', 'eRpjLW18DAJmZB17cG1vbyY0NWA/MCoSNGQdCXoMBgssMB19Dw9CH0tdahY6OgUpKmYHMgQ2LQcJNhE+ByslGyYdLWhiIDYTEy5/', 'MQYNPhdqcC0VPhY0CQYkASwjdjgzHWEyMTcDEx1jXQEKbTkwFiATA3EUBR0vNHQoGgctcQITACAlGGQ9BClwAxgFAGl8Mws+fFFK', 'bmQoNiwZBQR3NDkULAZyLzV8CCYdHRZuaBQsKxQuAzYoNDExGSQvLhgYBxkGcgEpOgtqQEwrDSAlAhEJZDZwMhUkew8DAS44eSJ8', 'EH8ABCkiPiw1NSE9AjFsfX0tMg09DilxWgFBcCU/GD8FORYxGnooPC85Cx06YwAaOQ0XcBwCFh8nLDIOeQYbDyQAElZBXAQoLycZ', 'fAc/NTInLmgeIQQfOSd/DGsVbQ8XdAEWZRclJwACFCk9BCV+ahwvamZbQ0BnbyR1fyYiCTJqDBocCyIVdSMsGzhoIhcCHShhFxsi', 'YDV6ATp9AGwRcSs+XHwLbT0NemdgGxAmPDcNIAc0cSIMPRgPOi8kM3AlZRYfAXNtfisvJzxyDzhbCQBFcDslHjQ3BxwPDQFgAmMV', 'Zzh+PXNwYidkeG8BdSMaaml4TiYSKjwBFmt4NSUgCjwoKxhyJj5xInN0HjgZMRZiBnYnJGQENH8KbztGanRmMRJ9A2AsHiEvcQ8C', 'emMqAhQIdTMtZzdyejQlbCEdOBA8aBo/PBlwchYzCWoEc0xtExVhMyQoIzEaDysJIi0CKC98MHovByAOeygVPTV7NwV9GDx5Dj0F', 'LzNbaEJkCGJnYTAibGF0AA8wKBtwGD4FHDUzOjEfbmQLCyAXJX8iOSolLS4uG0hxSBsPOBQmMXQLZA0aGSI/NHYOaBYFbmwjN2Ev', 'DwArGyg3bWleU2NNKQF7YSQ0EGsXJ2IUKHVuPS1peyImfzdhF3s6GTJuLxgfGWUWc3wSD2EDZlYJBw5jJCcLFihsHR1zKBIQFxMm', 'GXsMCicHBhVsEiFyHCcrPicJGSl5Iz4nZzRkZCd2Bio1BjAsKW8mYghXBC0weQ4LBGhkBAgwYD94GG1ubxEYdh0pfW1lNHoCFWwm', 'MSc6KSMIEzt+I3M7OiwZIDovKRIqZFEFfmowKXs/Di81MDQ6HSwGKRZsLz4kKi1yFWomZRYOEjsEDgwtGwkpDQ5LB3RsGxI4AWET', 'cTINW0d1UTwdKjoVIBwxbjtteXA9Dxh2Ky15ET0LOil/Jn0WPSU1CyZ2Ki50OnBLH2RXZhcmG2QPbh4wchAdH3V5NhIzG3MyHCI8', 'PQQHIiZxLhsMOBouDBMhDB1oMjsHZh0IPixgLT0+P3YXSGkFAjBtPyIYKxFjHXQmEBMEJzAXGhI7Jyk3OxdmFQptBhsfHgk6bzkp', 'e30kNGQiYG0LBVltNGYaLB01MD59NhsQKh9yFBopOBN3Ih0QDgMLKz4jCm15BwZmCSMoKBlIXgM2ZCk3ZjhwAQN2LRwmKy0wGjsH', 'OgMGSFUGBg1nJHltFAgLEgYJNDRxKXRjIhp+LhwHCBkLBnIlBXAgPAkoFC0Te3RoHxMdCA4VNXQ4fCIUCjEPMy8uEhIjDiUoIj5/', 'PQJ/AhN+cRYcWQJBACcaNCBhMh49JnIROBwlGx0pDCY7cwkiMzEUJDYwNzQHC302Pn41CjRRUlR5PBoFJSoZLyIxcx8KAD1zcRMF', 'cC9jLTkIPWAdHCcWcx0hHD4jd2YIDGIQGywlHDAQPmVAGVM3LTogFzsRYy85OgsgKnY9cmgHBg1/NgIoAys1BSAOYAM5BA8lDT81', 'GDQlBiYpEAMMLiQrfDQIMD0MDDt1ESwmJhwWGQ19KDYBGyELGhZvPnBbRV0FHiMnNChvKiYxYyUPeXQwOjwMCRIfK2YMMio3Jg0T', 'MjQOcD12JSIaNB05b24=', 'chl6NWV5LSU5OxQXGydqah8sbHYIenBsfjwwaSkwdiEkaAQnA3c2J31oaEEfHXMZBRptGhIFAwsMYjQjFiAdYBUEOHE9Fh0oNR8Z', 'bwV2Gx5lH3oMKjApADAgaB1wdzA1Li8kNTMTLzYVByNgBjU3Oix3IXsQLDQxHjYqbEV0QB8FGgAcHABnZzN2Ixs/IBE/H2YnAzVg', 'D3InDCAoJDcMYBEKBy0IOQoZcAE5cwNkYxEvIxIzdx07fQEjFBUqIm8DGAQbNhI8EHBiFwcMHQowenc0DDgqYB56VWoBEholNRUR', 'ED42PTMRPRV/cTc5eH0hOy0xAQdbQ0ZbJQw5H2BwChIzDAZqdDsGNBosKy0MLxMnLQADew0AAj0BBxcoETAjEQp+WnoxPzk5EzBr', 'EwtrIHIvZCs5IDs6EDoPOQYIAmcUP3cnKTYyPX8BBj0qPTxXch16NmIuGzAFB2YMFgE2FDwSbw4veHAgEyp5ZmURfiNuITENN35n', 'Hzx8c3Yta3xcBjImJzAiM3ArPxtnIwwvJmMBEB8VdSFqC2oVFwE6AAI9ADctBz0Cdi8LCQFFdA81LB8PZxksCSVgARRqay0JOWoT', 'azU6LnJmOwggGyASIy96RGBmKDMuMyspBQozJzc0LyguGXY5KxIGKhEWEiU/KgILBTAeeyc+G3B2OVFmVm4bLSQFCnI+MHQ6IyZ1', 'Fzd+A3VyEhE6IxByFmUjOSxrcDsADgNmMAQtYBdmbwkaITw1FjYEdwQ7KAo1NR1cQQIsPwgSN3ArYXM6B310KQkQaxQcGwomcQhq', 'Mj0jDH1BRn4vIC0NBSRqG3FzYhosNQ4CNSUCCTo9IBFqARQXPzEieww5CRICMT86BVdGRik7Aiw5Jj0bDTQ3ARx/cXUyPQ8vCCAk', 'MC0zawJHTS1sLwUHFW0HLnstNBYAIj5sB3t3JT8rCzk5YDUuKQciH2AdNGQIcm9IYAVkPXorLD4nbiQqCjM+BBkTKRA2exkgcxQY'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
