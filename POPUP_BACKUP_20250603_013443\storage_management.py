"""
Storage Management Screen
Provides inventory tracking and stock management functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime

from database import get_db_connection

class StorageManagement:
    """Storage and inventory management interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.storage_tree = None

    def show(self):
        """Display the storage management screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create interface
        self.create_interface()

    def hide(self):
        """Hide the storage management screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the storage management interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('storage_management'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button (keep button color)
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Storage list
        list_frame = tk.Frame(content_frame, bg='#2d2d2d')
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Title for storage list
        list_title = tk.Label(list_frame, text=self.app.get_text('items_in_storage'),
                             font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='white')
        list_title.pack(pady=15)

        # Treeview for storage items
        tree_frame = tk.Frame(list_frame, bg='#2d2d2d')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        columns = ('Product', 'Current Stock', 'Min Level', 'Max Level', 'Unit', 'Status')
        self.storage_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)

        # Configure columns
        self.storage_tree.heading('Product', text=self.app.get_text('name'))
        self.storage_tree.heading('Current Stock', text=self.app.get_text('current_stock'))
        self.storage_tree.heading('Min Level', text=self.app.get_text('min_level'))
        self.storage_tree.heading('Max Level', text=self.app.get_text('max_level'))
        self.storage_tree.heading('Unit', text=self.app.get_text('unit'))
        self.storage_tree.heading('Status', text=self.app.get_text('status'))

        self.storage_tree.column('Product', width=200)
        self.storage_tree.column('Current Stock', width=100, anchor='center')
        self.storage_tree.column('Min Level', width=80, anchor='center')
        self.storage_tree.column('Max Level', width=80, anchor='center')
        self.storage_tree.column('Unit', width=80, anchor='center')
        self.storage_tree.column('Status', width=100, anchor='center')

        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.storage_tree.yview)
        self.storage_tree.configure(yscrollcommand=scrollbar.set)

        self.storage_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Action buttons
        buttons_frame = tk.Frame(content_frame, bg='#1a1a1a')
        buttons_frame.pack(fill=tk.X)

        # Update stock button
        update_btn = tk.Button(buttons_frame, text=self.app.get_text('update_stock'),
                              font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                              padx=20, pady=8, command=self.update_stock)
        update_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Set levels button
        levels_btn = tk.Button(buttons_frame, text=self.app.get_text('set_min_max_levels'),
                              font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                              padx=20, pady=8, command=self.set_levels)
        levels_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Refresh button
        refresh_btn = tk.Button(buttons_frame, text=self.app.get_text('refresh'),
                               font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                               padx=20, pady=8, command=self.load_storage_items)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 20))

        # Delete selected button with icon (far right to prevent accidental clicks)
        if self.app.icons.get('remove_selected'):
            delete_btn = tk.Button(buttons_frame, image=self.app.icons['remove_selected'],
                                  text=self.app.get_text('delete_selected'),
                                  compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                  bg='#dc3545', fg='white',
                                  padx=20, pady=8, command=self.delete_selected)
        else:
            delete_btn = tk.Button(buttons_frame, text=self.app.get_text('delete_selected'),
                                  font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                                  padx=20, pady=8, command=self.delete_selected)
        delete_btn.pack(side=tk.LEFT)

        # Load storage items
        self.load_storage_items()

    def load_storage_items(self):
        """Load storage items from database"""
        if not self.storage_tree:
            return

        # Clear existing items
        for item in self.storage_tree.get_children():
            self.storage_tree.delete(item)

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("""
                SELECT p.name, s.current_stock, s.min_stock_level, s.max_stock_level,
                       s.unit, s.product_id
                FROM storage s
                JOIN products p ON s.product_id = p.id
                ORDER BY p.name
            """)
            storage_items = c.fetchall()

            for item in storage_items:
                # Determine status
                current = item['current_stock']
                min_level = item['min_stock_level']
                max_level = item['max_stock_level']

                if current <= min_level:
                    status = self.app.get_text('low_stock')
                    status_color = 'red'
                elif current >= max_level:
                    status = self.app.get_text('overstocked')
                    status_color = 'orange'
                else:
                    status = self.app.get_text('ok_stock')
                    status_color = 'green'

                # Insert item
                item_id = self.storage_tree.insert('', tk.END, values=(
                    item['name'],
                    current,
                    min_level,
                    max_level,
                    item['unit'],
                    status
                ))

                # Color code based on status
                if status == "LOW STOCK":
                    self.storage_tree.set(item_id, 'Status', status)
                    # Note: Tkinter treeview doesn't support easy row coloring
                    # We'll use the status text to indicate the issue

        finally:
            conn.close()

    def delete_selected(self):
        """Delete selected item from storage tracking"""
        selection = self.storage_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'), self.app.get_text('select_item_delete'))
            return

        # Get selected item data
        item = self.storage_tree.item(selection[0])
        product_name = item['values'][0]

        # Confirm deletion
        confirm_msg = f"{self.app.get_text('confirm_delete_storage')} '{product_name}' {self.app.get_text('from_storage_tracking')}\n\n{self.app.get_text('delete_storage_warning')}"
        if not messagebox.askyesno(self.app.get_text('confirm'), confirm_msg):
            return

        # Get product ID
        product_id = self.get_product_id_by_name(product_name)
        if not product_id:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('product_not_found'))
            return

        # Remove from storage tracking
        conn = get_db_connection()
        try:
            c = conn.cursor()

            # Remove from storage table
            c.execute("DELETE FROM storage WHERE product_id = ?", (product_id,))

            # Remove all stock movements for this product
            c.execute("DELETE FROM stock_movements WHERE product_id = ?", (product_id,))

            conn.commit()
            messagebox.showinfo(self.app.get_text('success'), f"'{product_name}' {self.app.get_text('removed_from_storage')}")

            # Refresh the display
            self.load_storage_items()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'), f"{self.app.get_text('failed_delete_storage')} {e}")
        finally:
            conn.close()

    def update_stock(self):
        """Update stock for selected item"""
        selection = self.storage_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'), self.app.get_text('select_item_update_stock'))
            return

        # Get selected item data
        item = self.storage_tree.item(selection[0])
        product_name = item['values'][0]
        current_stock = item['values'][1]

        # Get product ID
        product_id = self.get_product_id_by_name(product_name)
        if not product_id:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('product_not_found'))
            return

        self.show_stock_update_dialog(product_id, product_name, current_stock)

    def set_levels(self):
        """Set min/max levels for selected item"""
        selection = self.storage_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'), self.app.get_text('select_item_set_levels'))
            return

        # Get selected item data
        item = self.storage_tree.item(selection[0])
        product_name = item['values'][0]
        min_level = item['values'][2]
        max_level = item['values'][3]
        unit = item['values'][4]

        # Get product ID
        product_id = self.get_product_id_by_name(product_name)
        if not product_id:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('product_not_found'))
            return

        self.show_levels_dialog(product_id, product_name, min_level, max_level, unit)

    def get_product_id_by_name(self, product_name):
        """Get product ID by name"""
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT id FROM products WHERE name = ?", (product_name,))
            result = c.fetchone()
            return result['id'] if result else None
        finally:
            conn.close()

    def show_stock_update_dialog(self, product_id, product_name, current_stock):
        """Show dialog to update stock"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"{self.app.get_text('update_stock')} - {product_name}")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (200)
        dialog.geometry(f"500x400+{x}+{y}")

        # Main frame
        main_frame = tk.Frame(dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Product info
        tk.Label(main_frame, text=f"{self.app.get_text('name')}: {product_name}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d').pack(pady=(0, 10))
        tk.Label(main_frame, text=f"{self.app.get_text('current_stock')}: {current_stock}",
                font=('Segoe UI', 10), bg='#2d2d2d').pack(pady=(0, 20))

        # Update type
        update_type_var = tk.StringVar(value="add")

        tk.Label(main_frame, text=f"{self.app.get_text('update_type')}:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))

        type_frame = tk.Frame(main_frame, bg='#2d2d2d')
        type_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Radiobutton(type_frame, text=self.app.get_text('add_stock'), variable=update_type_var, value="add",
                      font=('Segoe UI', 10), bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 20))
        tk.Radiobutton(type_frame, text=self.app.get_text('remove_stock'), variable=update_type_var, value="remove",
                      font=('Segoe UI', 10), bg='#2d2d2d').pack(side=tk.LEFT)

        # Quantity
        tk.Label(main_frame, text=f"{self.app.get_text('quantity')}:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))
        quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(main_frame, textvariable=quantity_var,
                                 font=('Segoe UI', 10), width=20)
        quantity_entry.pack(anchor='w', pady=(0, 30))
        quantity_entry.focus()

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=20)

        update_btn = tk.Button(buttons_frame, text=self.app.get_text('update_stock'),
                              font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                              padx=30, pady=10,
                              command=lambda: self.save_stock_update(dialog, product_id,
                                                                   update_type_var, quantity_var))
        update_btn.pack(side=tk.LEFT, padx=(0, 15))

        cancel_btn = tk.Button(buttons_frame, text=self.app.get_text('cancel'),
                              font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                              padx=30, pady=10, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def save_stock_update(self, dialog, product_id, update_type_var, quantity_var):
        """Save stock update"""
        try:
            quantity = int(quantity_var.get().strip())
            if quantity <= 0:
                messagebox.showerror(self.app.get_text('error'), self.app.get_text('quantity_positive'))
                return
        except ValueError:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('enter_valid_quantity'))
            return

        update_type = update_type_var.get()
        reason = f"Manual {update_type}"

        conn = get_db_connection()
        try:
            c = conn.cursor()

            # Get current stock
            c.execute("SELECT current_stock FROM storage WHERE product_id = ?", (product_id,))
            result = c.fetchone()
            if not result:
                messagebox.showerror(self.app.get_text('error'), self.app.get_text('product_not_found'))
                return

            current_stock = result['current_stock']

            # Calculate new stock
            if update_type == "add":
                new_stock = current_stock + quantity
                movement_type = "IN"
            else:  # remove
                new_stock = max(0, current_stock - quantity)  # Don't go below 0
                movement_type = "OUT"
                quantity = current_stock - new_stock  # Actual quantity removed

            # Update stock
            c.execute("UPDATE storage SET current_stock = ? WHERE product_id = ?",
                     (new_stock, product_id))

            # Record movement
            current_user = self.app.current_user['username'] if self.app.current_user else 'Admin'
            c.execute("""
                INSERT INTO stock_movements (product_id, movement_type, quantity, reason, date, user)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (product_id, movement_type, quantity, reason,
                  datetime.now().isoformat(), current_user))

            conn.commit()
            messagebox.showinfo(self.app.get_text('success'), f"{self.app.get_text('stock_updated_success')}\n{self.app.get_text('new_stock')} {new_stock}")
            dialog.destroy()
            self.load_storage_items()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'), f"{self.app.get_text('failed_update_stock')} {e}")
        finally:
            conn.close()

    def show_levels_dialog(self, product_id, product_name, min_level, max_level, unit):
        """Show dialog to set min/max levels"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"{self.app.get_text('set_min_max_levels')} - {product_name}")
        dialog.geometry("500x450")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (225)
        dialog.geometry(f"500x450+{x}+{y}")

        # Main frame
        main_frame = tk.Frame(dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Product info
        tk.Label(main_frame, text=f"{self.app.get_text('name')}: {product_name}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d').pack(pady=(0, 20))

        # Min level
        tk.Label(main_frame, text=f"{self.app.get_text('minimum_stock_level')}:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))
        min_var = tk.StringVar(value=str(min_level))
        min_entry = tk.Entry(main_frame, textvariable=min_var, font=('Segoe UI', 10), width=20)
        min_entry.pack(anchor='w', pady=(0, 15))

        # Max level
        tk.Label(main_frame, text=f"{self.app.get_text('maximum_stock_level')}:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))
        max_var = tk.StringVar(value=str(max_level))
        max_entry = tk.Entry(main_frame, textvariable=max_var, font=('Segoe UI', 10), width=20)
        max_entry.pack(anchor='w', pady=(0, 15))

        # Unit dropdown with comprehensive options
        tk.Label(main_frame, text=f"{self.app.get_text('unit_of_measurement')}:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))

        # Comprehensive list of units
        units = [
            "pieces", "items", "units", "pcs",  # Count-based
            "kg", "g", "lb", "oz", "ton",  # Weight
            "L", "ml", "gal", "fl oz", "cup",  # Volume/Liquid
            "m", "cm", "mm", "ft", "in",  # Length
            "m²", "cm²", "ft²", "sq ft",  # Area
            "box", "pack", "case", "carton",  # Packaging
            "bottle", "can", "jar", "bag",  # Containers
            "dozen", "pair", "set", "roll",  # Groups
            "sheet", "page", "slice", "portion"  # Portions
        ]

        unit_var = tk.StringVar(value=unit)
        unit_combo = ttk.Combobox(main_frame, textvariable=unit_var,
                                 values=units, font=('Segoe UI', 10), width=18, state="readonly")
        unit_combo.pack(anchor='w', pady=(0, 30))

        # Set current value if it exists in the list
        if unit in units:
            unit_combo.set(unit)
        else:
            unit_combo.set("pieces")  # Default

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=20)

        save_btn = tk.Button(buttons_frame, text=self.app.get_text('save_changes'),
                            font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                            padx=30, pady=10,
                            command=lambda: self.save_levels(dialog, product_id, min_var, max_var, unit_var))
        save_btn.pack(side=tk.LEFT, padx=(0, 15))

        cancel_btn = tk.Button(buttons_frame, text=self.app.get_text('cancel'),
                              font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                              padx=30, pady=10, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def save_levels(self, dialog, product_id, min_var, max_var, unit_var):
        """Save min/max levels"""
        try:
            min_level = int(min_var.get().strip())
            max_level = int(max_var.get().strip())
            unit = unit_var.get().strip()

            if min_level < 0 or max_level < 0:
                messagebox.showerror(self.app.get_text('error'), self.app.get_text('levels_positive'))
                return

            if min_level >= max_level:
                messagebox.showerror(self.app.get_text('error'), self.app.get_text('min_less_than_max'))
                return

            if not unit:
                unit = "pieces"

        except ValueError:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('enter_valid_levels'))
            return

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("""
                UPDATE storage
                SET min_stock_level = ?, max_stock_level = ?, unit = ?
                WHERE product_id = ?
            """, (min_level, max_level, unit, product_id))

            conn.commit()
            messagebox.showinfo(self.app.get_text('success'), self.app.get_text('levels_updated_success'))
            dialog.destroy()
            self.load_storage_items()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'), f"{self.app.get_text('failed_update_levels')} {e}")
        finally:
            conn.close()

    def reduce_stock_on_sale(self, product_id, quantity_sold):
        """Reduce stock when item is sold (called from POS)"""
        conn = get_db_connection()
        try:
            c = conn.cursor()

            # Check if product is tracked in storage
            c.execute("SELECT current_stock FROM storage WHERE product_id = ?", (product_id,))
            result = c.fetchone()

            if result:
                current_stock = result['current_stock']
                new_stock = max(0, current_stock - quantity_sold)

                # Update stock
                c.execute("UPDATE storage SET current_stock = ? WHERE product_id = ?",
                         (new_stock, product_id))

                # Record movement
                current_user = self.app.current_user['username'] if self.app.current_user else 'POS'
                c.execute("""
                    INSERT INTO stock_movements (product_id, movement_type, quantity, reason, date, user)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (product_id, "OUT", quantity_sold, "Sale",
                      datetime.now().isoformat(), current_user))

                conn.commit()

        except Exception as e:
            print(f"Error reducing stock: {e}")
        finally:
            conn.close()

    @staticmethod
    def get_low_stock_count():
        """Get count of items with low stock (static method for use anywhere)"""
        try:
            conn = get_db_connection()
            c = conn.cursor()

            # Count items where current stock is at or below minimum level
            c.execute("""
                SELECT COUNT(*) as low_count
                FROM storage s
                JOIN products p ON s.product_id = p.id
                WHERE s.current_stock <= s.min_stock_level
                AND s.min_stock_level > 0
            """)

            result = c.fetchone()
            conn.close()

            return result['low_count'] if result else 0

        except Exception as e:
            print(f"Error checking low stock: {e}")
            return 0
