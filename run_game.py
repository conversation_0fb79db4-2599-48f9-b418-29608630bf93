#!/usr/bin/env python3
"""
Gem Collector Game Launcher
===========================

A simple yet addictive game where you collect gems while avoiding enemies!

FEATURES:
- Smooth player movement with trail effects
- Multiple gem types (Normal, Rare, Epic) with different point values
- Smart enemies that sometimes follow you
- Power-ups: Speed Boost, Shield, Magnet
- Progressive difficulty - more enemies and gems each level
- Satisfying visual effects and feedback

CONTROLS:
- WASD or Arrow Keys: Move player
- SPACE: Pause/Unpause
- R: Restart (when game over)
- ESC: Quit

GAMEPLAY:
- Collect gems to increase your score
- Avoid red enemies (they hurt!)
- Collect power-ups for temporary abilities
- Complete levels by collecting enough gems
- Try to get the highest score possible!

REQUIREMENTS:
- Python 3.6+
- Pygame library

If you don't have Pygame installed, run:
pip install pygame
"""

import sys
import subprocess
import os

def check_pygame():
    """Check if pygame is installed, offer to install if not."""
    try:
        import pygame
        return True
    except ImportError:
        print("Pygame is not installed!")
        print("To install pygame, run: pip install pygame")
        
        response = input("Would you like me to try installing it now? (y/n): ").lower()
        if response == 'y':
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
                print("Pygame installed successfully!")
                return True
            except subprocess.CalledProcessError:
                print("Failed to install pygame. Please install it manually.")
                return False
        return False

def main():
    print("=" * 50)
    print("    GEM COLLECTOR - ADDICTIVE FUN GAME!")
    print("=" * 50)
    print()
    
    if not check_pygame():
        print("Cannot start game without pygame. Exiting...")
        input("Press Enter to exit...")
        return
    
    print("Starting Gem Collector...")
    print("Have fun and try to beat your high score!")
    print()
    
    # Import and run the game
    try:
        from gem_collector import Game
        game = Game()
        game.run()
    except Exception as e:
        print(f"Error starting game: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
