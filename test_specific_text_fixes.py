#!/usr/bin/env python3
"""
Test all specific text color and formatting fixes
"""

import sys
import os
from pathlib import Path

def test_user_management_fixes():
    """Test User Management specific fixes"""
    
    print("Testing User Management Fixes")
    print("=" * 31)
    
    files_to_check = [
        "user_management.py",
        "YES/user_management.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for "Users" text being white
            if "text=self.app.get_text('users')" in content and "fg='white'" in content:
                print(f"   ✅ 'Users' text is white")
            else:
                print(f"   ❌ 'Users' text not white")
                all_fixed = False
            
            # Check for bigger user info text (12pt)
            if "font=('Segoe UI', 12, 'bold')" in content:
                print(f"   ✅ User info text is bigger (12pt)")
            else:
                print(f"   ❌ User info text not bigger")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_product_management_fixes():
    """Test Product Management specific fixes"""
    
    print("\nTesting Product Management Fixes")
    print("=" * 34)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for notebook tab styling (bold and bigger)
            if "font=('Segoe UI', 12, 'bold')" in content and "TNotebook.Tab" in content:
                print(f"   ✅ Categories and Products tabs are bold and bigger")
            else:
                print(f"   ❌ Tab styling not applied")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_sales_history_fixes():
    """Test Sales History specific fixes"""
    
    print("\nTesting Sales History Fixes")
    print("=" * 29)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for white text on dark backgrounds
            white_text_count = content.count("fg='white'")
            
            # Check for problematic black text patterns
            problematic_patterns = [
                "bg='#2d2d2d'.*fg='black'",
                "bg='#404040'.*fg='black'"
            ]
            
            has_problems = False
            for pattern in problematic_patterns:
                import re
                if re.search(pattern, content):
                    has_problems = True
                    break
            
            if white_text_count >= 10 and not has_problems:
                print(f"   ✅ Black text fixed ({white_text_count} white text instances)")
            else:
                print(f"   ❌ Black text issues remain")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_receipt_settings_fixes():
    """Test Receipt Settings specific fixes"""
    
    print("\nTesting Receipt Settings Fixes")
    print("=" * 32)
    
    files_to_check = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for white LabelFrame titles
            if "text=\"Receipt Settings\"" in content and "fg='white'" in content:
                print(f"   ✅ 'Receipt Settings' is white")
            else:
                print(f"   ❌ 'Receipt Settings' not white")
                all_fixed = False
            
            if "text=\"Receipt Preview\"" in content and "fg='white'" in content:
                print(f"   ✅ 'Receipt Preview' is white")
            else:
                print(f"   ❌ 'Receipt Preview' not white")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_popup_windows_fixes():
    """Test popup windows text color fixes"""
    
    print("\nTesting Popup Windows Fixes")
    print("=" * 29)
    
    files_to_check = [
        "user_management.py",
        "product_management.py",
        "storage_management.py",
        "number_keyboard.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for no black text on dark backgrounds
            problematic_patterns = [
                r"bg='#2d2d2d'[^)]*\)(?![^)]*fg='white')",
                r"bg='#1a1a1a'[^)]*\)(?![^)]*fg='white')",
                r"bg='#404040'[^)]*\)(?![^)]*fg='white')"
            ]
            
            has_problems = False
            for pattern in problematic_patterns:
                import re
                matches = re.findall(pattern, content)
                if matches:
                    # Check if these are actually labels/text elements
                    for match in matches:
                        if "Label(" in match or "Checkbutton(" in match:
                            has_problems = True
                            break
            
            if not has_problems:
                print(f"   ✅ No black text on dark backgrounds")
            else:
                print(f"   ❌ Black text on dark backgrounds found")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_all_versions_updated():
    """Test that all versions have the fixes"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main User Management", "user_management.py"),
        ("Protected User Management", "YES/user_management.py"),
        ("Obfuscated User Management", "YES_OBFUSCATED/user_management.py"),
        ("Main Product Management", "product_management.py"),
        ("Protected Product Management", "YES/product_management.py"),
        ("Obfuscated Product Management", "YES_OBFUSCATED/product_management.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name}")
        else:
            print(f"❌ Missing: {version_name}")
            all_updated = False
    
    return all_updated

def main():
    """Run all specific text fix tests"""
    
    print("🔧 SPECIFIC TEXT FIXES TEST SUITE")
    print("=" * 35)
    
    tests = [
        ("User Management Fixes", test_user_management_fixes),
        ("Product Management Fixes", test_product_management_fixes),
        ("Sales History Fixes", test_sales_history_fixes),
        ("Receipt Settings Fixes", test_receipt_settings_fixes),
        ("Popup Windows Fixes", test_popup_windows_fixes),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 35)
    print("📊 RESULTS")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL SPECIFIC FIXES VERIFIED!")
        print("✅ User Management: 'Users' white, bigger text")
        print("✅ Product Management: Bold, bigger tabs")
        print("✅ Sales History: All black text fixed")
        print("✅ Receipt Settings: Titles are white")
        print("✅ Popup Windows: No black text on dark backgrounds")
        print("✅ All versions updated")
    else:
        print("⚠️ Some specific fixes failed")
        print("❌ Text fixes may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All specific text issues completely resolved!")
        print("⚪ Perfect text visibility throughout")
        print("📏 Bigger text where requested")
        print("🔤 Bold formatting applied")
        print("📱 Popup windows properly styled")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Some specific text fixes need attention")
    
    exit(0 if success else 1)
