#!/usr/bin/env python3
"""
Test the touch screen improvements for categories and products buttons
"""

import sys
import os
from pathlib import Path

def test_category_button_improvements():
    """Test that category buttons are wider for touch screen use"""
    
    print("Testing Category Button Improvements")
    print("=" * 36)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_improved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for wider category buttons with images
            if "width=180, height=90" in content and "WIDER FOR TOUCH" in content:
                print(f"   ✅ Category buttons with images made wider (180x90)")
            else:
                print(f"   ❌ Category buttons with images not made wider")
                all_improved = False
            
            # Check for wider text-only category buttons
            if "width=22, height=3" in content and "WIDER FOR TOUCH" in content:
                print(f"   ✅ Text-only category buttons made wider (22x3)")
            else:
                print(f"   ❌ Text-only category buttons not made wider")
                all_improved = False
            
            # Check for larger font in categories
            if "font=('Helvetica', 11)" in content and "cat_btn" in content:
                print(f"   ✅ Category button font increased to 11pt")
            else:
                print(f"   ❌ Category button font not increased")
                all_improved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_improved = False
    
    return all_improved

def test_product_button_improvements():
    """Test that product buttons are wider for touch screen use"""
    
    print("\nTesting Product Button Improvements")
    print("=" * 35)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_improved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for increased minimum width
            if "max(12, min(24, button_size // 7))" in content:
                print(f"   ✅ Product button minimum width increased (12-24 chars)")
            else:
                print(f"   ❌ Product button minimum width not increased")
                all_improved = False
            
            # Check for increased minimum height
            if "max(3, min(7, button_size // 22))" in content:
                print(f"   ✅ Product button minimum height increased (3-7 chars)")
            else:
                print(f"   ❌ Product button minimum height not increased")
                all_improved = False
            
            # Check for touch screen comments
            if "WIDER FOR TOUCH" in content and "prod_btn" in content:
                print(f"   ✅ Touch screen improvements documented")
            else:
                print(f"   ❌ Touch screen improvements not documented")
                all_improved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_improved = False
    
    return all_improved

def test_layout_adjustments():
    """Test that layout was adjusted for wider buttons"""
    
    print("\nTesting Layout Adjustments")
    print("=" * 27)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_adjusted = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for wider categories panel
            if "minsize=240" in content and "Fixed wider categories" in content:
                print(f"   ✅ Categories panel made wider (240px)")
            else:
                print(f"   ❌ Categories panel not made wider")
                all_adjusted = False
            
            # Check for wider categories frame
            if "width=240" in content and "cat_frame" in content:
                print(f"   ✅ Categories frame made wider (240px)")
            else:
                print(f"   ❌ Categories frame not made wider")
                all_adjusted = False
            
            # Check for wider categories canvas
            if "width=220" in content and "cat_canvas" in content:
                print(f"   ✅ Categories canvas made wider (220px)")
            else:
                print(f"   ❌ Categories canvas not made wider")
                all_adjusted = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_adjusted = False
    
    return all_adjusted

def test_touch_screen_usability():
    """Test overall touch screen usability improvements"""
    
    print("\nTesting Touch Screen Usability")
    print("=" * 31)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_usable = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for minimum touch target sizes
            # Categories: 180x90 pixels (good for touch)
            if "width=180, height=90" in content:
                print(f"   ✅ Category buttons meet touch target size (180x90px)")
            else:
                print(f"   ❌ Category buttons too small for touch")
                all_usable = False
            
            # Check for adequate spacing
            if "padx=8, pady=3" in content and "cat_btn.pack" in content:
                print(f"   ✅ Category buttons have adequate spacing")
            else:
                print(f"   ❌ Category buttons spacing inadequate")
                all_usable = False
            
            # Check for product button improvements
            if "button_size // 7" in content and "button_size // 22" in content:
                print(f"   ✅ Product buttons scale better for touch")
            else:
                print(f"   ❌ Product buttons don't scale well for touch")
                all_usable = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_usable = False
    
    return all_usable

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/pos_screen.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all touch screen improvement tests"""
    
    print("📱 TOUCH SCREEN IMPROVEMENTS TEST SUITE")
    print("=" * 40)
    
    tests = [
        ("Category Button Improvements", test_category_button_improvements),
        ("Product Button Improvements", test_product_button_improvements),
        ("Layout Adjustments", test_layout_adjustments),
        ("Touch Screen Usability", test_touch_screen_usability),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Category buttons made wider and taller")
        print("✅ Product buttons minimum size increased")
        print("✅ Layout adjusted for wider buttons")
        print("✅ Touch screen usability improved")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Touch screen improvements may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📱 Touch screen improvements successfully implemented!")
        print("👆 Category buttons: 180x90px (was 160x80px)")
        print("👆 Text categories: 22x3 chars (was 18x2 chars)")
        print("👆 Product buttons: 12-24 chars wide (was 8-20 chars)")
        print("👆 Product buttons: 3-7 chars tall (was 2-6 chars)")
        print("📐 Categories panel: 240px wide (was 200px)")
        print("🎯 Better touch targets for tablet/phone users")
    else:
        print("\n❌ Touch screen improvements need attention")
    
    exit(0 if success else 1)
