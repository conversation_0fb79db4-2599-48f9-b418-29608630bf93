#!/usr/bin/env python3
"""
Final test to confirm the POS system works after popup redesign
"""

import subprocess
import sys
import time
import os

def test_main_system():
    """Test that main system starts without errors"""
    
    print("Testing Main POS System")
    print("=" * 25)
    
    try:
        # Start the system and let it run briefly
        result = subprocess.run([sys.executable, "main.py"], 
                              capture_output=True, text=True, timeout=15)
        
        output = result.stdout + result.stderr
        
        # Check for successful startup indicators
        success_indicators = [
            "POS SYSTEM STARTING",
            "Database initialized successfully",
            "POS Application initialized successfully",
            "Starting POS System"
        ]
        
        error_indicators = [
            "SyntaxError",
            "ImportError", 
            "NameError",
            "AttributeError",
            "keyword argument repeated"
        ]
        
        found_success = 0
        found_errors = 0
        
        for indicator in success_indicators:
            if indicator in output:
                print(f"✅ {indicator}")
                found_success += 1
            else:
                print(f"❌ Missing: {indicator}")
        
        for error in error_indicators:
            if error in output:
                print(f"❌ Error found: {error}")
                found_errors += 1
        
        if found_success >= 3 and found_errors == 0:
            print("✅ Main system starts successfully!")
            return True
        else:
            print("❌ Main system has issues")
            print(f"Debug output:\n{output}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ System started and ran (timeout expected)")
        return True
    except Exception as e:
        print(f"❌ Error testing main system: {e}")
        return False

def test_syntax_check():
    """Test syntax of all Python files"""
    
    print("\nTesting Python Syntax")
    print("=" * 22)
    
    files_to_check = [
        "main.py",
        "pos_app.py", 
        "user_management.py",
        "product_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "number_keyboard.py"
    ]
    
    all_valid = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to compile the code
            compile(content, file_path, 'exec')
            print(f"✅ {file_path} - Valid syntax")
            
        except SyntaxError as e:
            print(f"❌ {file_path} - Syntax error: {e}")
            all_valid = False
        except Exception as e:
            print(f"⚠️ {file_path} - Error: {e}")
    
    return all_valid

def test_popup_features():
    """Test that popup redesign features are present"""
    
    print("\nTesting Popup Redesign Features")
    print("=" * 33)
    
    files_to_check = [
        ("user_management.py", ["bg='#ff8c00'", "geometry(\"500x450\")", "💾"]),
        ("product_management.py", ["bg='#ff8c00'", "geometry(\"550x450\")", "📁"]),
        ("number_keyboard.py", ["bg='#ff8c00'", "relief='flat'", "Segoe UI"])
    ]
    
    all_features_present = True
    
    for file_path, features in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            found_features = 0
            for feature in features:
                if feature in content:
                    print(f"   ✅ {feature}")
                    found_features += 1
                else:
                    print(f"   ❌ Missing: {feature}")
            
            if found_features == len(features):
                print(f"   🎉 All features present ({found_features}/{len(features)})")
            else:
                print(f"   ⚠️ Some features missing ({found_features}/{len(features)})")
                all_features_present = False
                
        except Exception as e:
            print(f"❌ Error checking {file_path}: {e}")
            all_features_present = False
    
    return all_features_present

def test_backup_integrity():
    """Test that backup is intact and can be restored"""
    
    print("\nTesting Backup Integrity")
    print("=" * 26)
    
    # Find backup directory
    backup_dirs = [d for d in os.listdir('.') if d.startswith('POPUP_BACKUP_')]
    
    if not backup_dirs:
        print("❌ No backup directory found")
        return False
    
    latest_backup = sorted(backup_dirs)[-1]
    print(f"✅ Backup found: {latest_backup}")
    
    # Check backup contents
    backup_files = [
        "user_management.py",
        "product_management.py", 
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "number_keyboard.py",
        "pos_screen.py",
        "restore_popups.py",
        "README.md"
    ]
    
    all_present = True
    
    for file_name in backup_files:
        file_path = os.path.join(latest_backup, file_name)
        if os.path.exists(file_path):
            print(f"✅ {file_name}")
        else:
            print(f"❌ Missing: {file_name}")
            all_present = False
    
    return all_present

def test_all_versions():
    """Test that all versions exist and are updated"""
    
    print("\nTesting All Versions")
    print("=" * 21)
    
    versions = [
        ("Main", "user_management.py"),
        ("Protected", "YES/user_management.py"),
        ("Obfuscated", "YES_OBFUSCATED/user_management.py")
    ]
    
    all_exist = True
    
    for version_name, file_path in versions:
        if os.path.exists(file_path):
            print(f"✅ {version_name}: {file_path}")
        else:
            print(f"❌ Missing {version_name}: {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """Run all final tests"""
    
    print("🔍 FINAL SYSTEM TEST SUITE")
    print("=" * 28)
    print("Testing POS system after popup redesign")
    print()
    
    tests = [
        ("Main System Startup", test_main_system),
        ("Python Syntax Check", test_syntax_check),
        ("Popup Redesign Features", test_popup_features),
        ("Backup Integrity", test_backup_integrity),
        ("All Versions Present", test_all_versions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 28)
    print("📊 FINAL RESULTS")
    print("=" * 28)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ POS system working perfectly")
        print("✅ Popup redesign successful")
        print("✅ No syntax errors")
        print("✅ All features present")
        print("✅ Backup intact")
        print("✅ All versions updated")
        print("🎨 Modern orange/black aesthetic applied!")
    else:
        print("⚠️ Some tests failed")
        print("❌ System may need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 POS System is working perfectly!")
        print("🧡 Modern popup design successfully applied")
        print("📱 Professional orange/black aesthetic")
        print("🔧 All functionality preserved")
        print("🛡️ Backup available for reversion")
    else:
        print("\n❌ System needs attention")
    
    exit(0 if success else 1)
