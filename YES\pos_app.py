"""
Main POS Application Class
Coordinates all components of the POS system
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime
from PIL import Image, ImageTk

# Import our modules
from database import init_database, load_language_setting, save_language_setting
from translations import get_text
from login_screen import LoginScreen
from pos_screen import POSScreen
from user_management import UserManagement
from product_management import ProductManagement
from sales_history import SalesHistory
from receipt_settings import ReceiptSettings
from storage_management import StorageManagement
try:
    from license_database import LicenseManager
    LICENSE_MANAGER_AVAILABLE = True
except ImportError:
    from license_client import LicenseClient
    LICENSE_MANAGER_AVAILABLE = False
import platform
import hashlib
import getpass

class POSApplication:
    """Main POS Application class that coordinates all components"""

    def __init__(self):
        print("Initializing POS Application...")

        # Create root window
        self.root = tk.Tk()
        self.root.title("POS System")

        # Set fullscreen by default (user preference)
        self.root.attributes('-fullscreen', True)
        self.root.bind('<Escape>', lambda e: self.root.attributes('-fullscreen', False))
        self.root.bind('<F11>', lambda e: self.root.attributes('-fullscreen', True))

        # Initialize database
        print("Setting up database...")
        init_database()

        # Initialize license system
        print("Checking license...")
        if LICENSE_MANAGER_AVAILABLE:
            self.license_manager = LicenseManager()
        else:
            self.license_client = LicenseClient()

        if not self.check_license():
            return  # Exit if license is invalid

        # Initialize application state
        self.current_user = None
        self.current_screen = None
        self.current_language = load_language_setting()
        self.cart_items = []
        self.icons = {}
        self.logo_image = None

        # Setup styles and load assets
        print("Setting up UI...")
        self.setup_styles()
        self.load_assets()

        # Initialize screen components
        self.login_screen = LoginScreen(self)
        self.pos_screen = None
        self.user_management = None
        self.product_management = None
        self.sales_history = None
        self.receipt_settings = None
        self.storage_management = None

        print("POS Application initialized successfully!")

    def get_computer_id(self):
        """Generate unique computer identifier"""
        computer_info = f"{platform.node()}-{platform.system()}-{getpass.getuser()}"
        computer_id = hashlib.md5(computer_info.encode()).hexdigest()[:16].upper()
        return computer_id

    def get_system_info(self):
        """Get system information for license tracking"""
        return {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "computer_name": platform.node()
        }

    def check_license(self):
        """Check if the application has a valid license"""
        try:
            if LICENSE_MANAGER_AVAILABLE:
                # Full license manager version
                return self.check_license_full()
            else:
                # Client-only version
                return self.check_license_client()

        except Exception as e:
            print(f"License check error: {e}")
            messagebox.showerror("License Error",
                               f"Error checking license: {e}\n\n"
                               f"Please contact your administrator.")
            return False

    def check_license_full(self):
        """Full license check with management capabilities"""
        try:
            # Check if license file exists
            license_file = "license.key"
            if not os.path.exists(license_file):
                return self.request_license()

            # Read license key from file
            with open(license_file, 'r') as f:
                license_key = f.read().strip()

            if not license_key:
                return self.request_license()

            # Validate license
            computer_id = self.get_computer_id()
            computer_name = platform.node()
            system_info = str(self.get_system_info())

            result = self.license_manager.validate_license(
                license_key, computer_id, computer_name, system_info
            )

            if result["valid"]:
                print(f"License validated: {result['message']}")
                return True
            else:
                print(f"License validation failed: {result['error']}")
                messagebox.showerror("License Error",
                                   f"License validation failed:\n{result['error']}\n\n"
                                   f"Please contact your administrator for a valid license.")
                return False

        except Exception as e:
            print(f"License check error: {e}")
            return False

    def check_license_client(self):
        """Client-only license check"""
        try:
            valid, message = self.license_client.check_license()

            if valid:
                print(f"License validated: {message}")
                return True
            else:
                print(f"License validation failed: {message}")
                # Request license from user
                return self.request_license_client()

        except Exception as e:
            print(f"License check error: {e}")
            return False

    def request_license(self):
        """Request license key from user"""
        from tkinter import simpledialog

        # Show computer ID to user
        computer_id = self.get_computer_id()
        computer_name = platform.node()

        info_message = (
            f"This POS system requires a license key to operate.\n\n"
            f"Computer Name: {computer_name}\n"
            f"Computer ID: {computer_id}\n\n"
            f"Please contact your administrator with this information to obtain a license key."
        )

        messagebox.showinfo("License Required", info_message)

        # Request license key
        license_key = simpledialog.askstring(
            "Enter License Key",
            "Please enter your license key:"
            # Removed show='*' to make license key visible
        )

        if not license_key:
            messagebox.showwarning("License Required", "A license key is required to use this application.")
            return False

        # Validate the entered license
        system_info = str(self.get_system_info())
        result = self.license_manager.validate_license(
            license_key, computer_id, computer_name, system_info
        )

        if result["valid"]:
            # Save license key to file
            try:
                with open("license.key", 'w') as f:
                    f.write(license_key)
                messagebox.showinfo("License Activated",
                                  f"License activated successfully!\n{result['message']}")
                return True
            except Exception as e:
                messagebox.showerror("Error", f"Could not save license key: {e}")
                return False
        else:
            messagebox.showerror("Invalid License",
                               f"License validation failed:\n{result['error']}")
            return False

    def request_license_client(self):
        """Request license key from user (client version)"""
        from tkinter import simpledialog

        # Show computer ID to user
        computer_id = self.get_computer_id()
        computer_name = platform.node()

        info_message = (
            f"This POS system requires a license key to operate.\n\n"
            f"Computer Name: {computer_name}\n"
            f"Computer ID: {computer_id}\n\n"
            f"Please contact your administrator with this information to obtain a license key."
        )

        messagebox.showinfo("License Required", info_message)

        # Request license key
        license_key = simpledialog.askstring(
            "Enter License Key",
            "Please enter your license key:"
            # Removed show='*' to make license key visible
        )

        if not license_key:
            messagebox.showwarning("License Required", "A license key is required to use this application.")
            return False

        # Validate the entered license (client version - local database)
        computer_id = self.get_computer_id()
        computer_name = platform.node()
        system_info = str(self.get_system_info())

        result = self.license_client.validate_license_with_server(
            license_key, computer_id, computer_name, system_info
        )

        if result["valid"]:
            # Save license key to file
            if self.license_client.save_license_locally(license_key):
                messagebox.showinfo("License Activated",
                                  f"License activated successfully!\n{result['message']}")
                return True
            else:
                messagebox.showerror("Error", "Could not save license key")
                return False
        else:
            messagebox.showerror("Invalid License",
                               f"License validation failed:\n{result['error']}")
            return False

    def show_license_manager(self):
        """Show license manager (admin only, full version only)"""
        if not LICENSE_MANAGER_AVAILABLE:
            messagebox.showinfo("Not Available",
                              "License management is not available in this version.\n\n"
                              "This is a client version that requires licenses but cannot create them.\n"
                              "Contact your administrator for license management.")
            return

        if not self.current_user or not self.current_user.get('is_admin'):
            messagebox.showerror(self.get_text('error'), self.get_text('admin_access_required'))
            return

        try:
            from license_manager_gui import LicenseManagerGUI
            license_window = tk.Toplevel(self.root)
            LicenseManagerGUI(license_window)
        except Exception as e:
            messagebox.showerror("Error", f"Could not open license manager: {e}")

    def setup_styles(self):
        """Setup modern professional styles for the application"""
        style = ttk.Style()

        # Configure modern theme
        style.theme_use('clam')

        # Header style
        style.configure('Header.TLabel',
                       font=('Helvetica', 16, 'bold'),
                       foreground='#2c3e50',
                       background='#ecf0f1')

        # Large button style
        style.configure('Large.TButton',
                       font=('Helvetica', 12, 'bold'),
                       padding=(20, 10))

        # Medium button style
        style.configure('Medium.TButton',
                       font=('Helvetica', 10, 'bold'),
                       padding=(15, 8))

        # Small button style
        style.configure('Small.TButton',
                       font=('Helvetica', 9),
                       padding=(10, 5))

        # Card style frame
        style.configure('Card.TFrame',
                       relief='solid',
                       borderwidth=1,
                       background='white')

    def load_assets(self):
        """Load icons and logo from assets folder"""
        assets_dir = "assets"
        if not os.path.exists(assets_dir):
            print("Assets folder not found, continuing without icons")
            return

        # Icon mappings (filename -> icon key)
        icon_mappings = {
            'User management.png': 'user_management',
            'product management.png': 'product_management',
            'sales history.png': 'sales_history',
            'receipt settings.png': 'receipt_settings',
            'storage.png': 'storage_management',
            'Display.png': 'display_settings',
            'CHECKOUT.png': 'checkout',
            'add extra.png': 'add_extra',
            'remove selected.png': 'remove_selected',  # Updated to .png
            'clear cart.png': 'clear_cart',
            'exit.png': 'exit',  # Updated to .png
            'enter.png': 'enter',
            'edit.png': 'edit',
            'email.png': 'email'
        }

        # Load icons
        for filename, key in icon_mappings.items():
            try:
                icon_path = os.path.join(assets_dir, filename)
                if os.path.exists(icon_path):
                    image = Image.open(icon_path)
                    # Resize icons to consistent size
                    image = image.resize((32, 32), Image.Resampling.LANCZOS)
                    self.icons[key] = ImageTk.PhotoImage(image)
                    print(f"Loaded icon: {key}")
            except Exception as e:
                print(f"Could not load icon {filename}: {e}")

        # Load logo
        try:
            logo_path = os.path.join(assets_dir, "logo.png")
            if os.path.exists(logo_path):
                logo_image = Image.open(logo_path)
                # Resize logo (make it bigger as per user preference)
                logo_image = logo_image.resize((120, 120), Image.Resampling.LANCZOS)
                self.logo_image = ImageTk.PhotoImage(logo_image)
                print("Logo loaded successfully")
        except Exception as e:
            print(f"Could not load logo: {e}")

    def reload_assets(self):
        """Reload assets from folder to pick up changes"""
        print("Reloading assets from folder...")

        # Clear existing icons
        self.icons.clear()

        # Reload assets
        self.load_assets()

        # Refresh current screen to show new icons
        if self.current_screen:
            if hasattr(self.current_screen, 'refresh'):
                self.current_screen.refresh()
            elif hasattr(self.current_screen, 'show'):
                # Re-show the current screen to refresh icons
                self.current_screen.show()

        print("Assets reloaded successfully!")

    def get_text(self, key):
        """Get translated text for current language"""
        return get_text(self.current_language, key)

    def set_language(self, language):
        """Change application language"""
        self.current_language = language
        save_language_setting(language)
        # Refresh current screen to apply new language
        if self.current_screen:
            self.current_screen.refresh_language()

    def show_login(self):
        """Show login screen"""
        self.clear_current_screen()
        self.current_user = None
        self.cart_items = []
        self.current_screen = self.login_screen
        self.login_screen.show()

    def show_pos_screen(self):
        """Show main POS screen"""
        if not self.current_user:
            self.show_login()
            return

        self.clear_current_screen()
        if not self.pos_screen:
            self.pos_screen = POSScreen(self)
        self.current_screen = self.pos_screen
        self.pos_screen.show()

    def show_user_management(self):
        """Show user management screen (admin only)"""
        if not self.current_user or not self.current_user.get('is_admin'):
            messagebox.showerror(self.get_text('error'), self.get_text('admin_access_required'))
            return

        self.clear_current_screen()
        if not self.user_management:
            self.user_management = UserManagement(self)
        self.current_screen = self.user_management
        self.user_management.show()

    def show_product_management(self):
        """Show product management screen (admin only)"""
        if not self.current_user or not self.current_user.get('is_admin'):
            messagebox.showerror(self.get_text('error'), self.get_text('admin_access_required'))
            return

        self.clear_current_screen()
        if not self.product_management:
            self.product_management = ProductManagement(self)
        self.current_screen = self.product_management
        self.product_management.show()

    def show_sales_history(self):
        """Show sales history screen"""
        self.clear_current_screen()
        if not self.sales_history:
            self.sales_history = SalesHistory(self)
        self.current_screen = self.sales_history
        self.sales_history.show()

    def show_receipt_settings(self):
        """Show receipt settings screen (admin only)"""
        if not self.current_user or not self.current_user.get('is_admin'):
            messagebox.showerror(self.get_text('error'), self.get_text('admin_access_required'))
            return

        self.clear_current_screen()
        if not self.receipt_settings:
            self.receipt_settings = ReceiptSettings(self)
        self.current_screen = self.receipt_settings
        self.receipt_settings.show()

    def show_storage_management(self):
        """Show storage management screen (admin only)"""
        if not self.current_user or not self.current_user.get('is_admin'):
            messagebox.showerror(self.get_text('error'), self.get_text('admin_access_required'))
            return

        self.clear_current_screen()
        if not self.storage_management:
            self.storage_management = StorageManagement(self)
        self.current_screen = self.storage_management
        self.storage_management.show()

    def clear_current_screen(self):
        """Clear the current screen"""
        if self.current_screen and hasattr(self.current_screen, 'hide'):
            self.current_screen.hide()

    def login_user(self, user):
        """Login a user and show POS screen"""
        self.current_user = user
        print(f"User logged in: {user['username']} (Admin: {user.get('is_admin', False)})")
        self.show_pos_screen()

    def logout_user(self):
        """Logout current user"""
        if self.current_user:
            print(f"User logged out: {self.current_user['username']}")

        # Clear current user
        self.current_user = None

        # Show login screen and clear password
        self.show_login()

        # Clear password field after login screen is shown
        if hasattr(self, 'login_screen') and self.login_screen:
            self.login_screen.password_var.set("")

    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno(self.get_text('confirm'), "Are you sure you want to exit?"):
            print("Exiting POS System...")
            self.root.quit()

    def run(self):
        """Start the application"""
        print("Starting POS System...")

        # Show login screen initially
        self.show_login()

        # Start the main loop
        self.root.mainloop()
