"""
Receipt Settings Screen
Provides receipt configuration and printing settings
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk

from database import get_db_connection

class ReceiptSettings:
    """Receipt settings and configuration interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.settings = {}
        self.logo_image = None

    def show(self):
        """Display the receipt settings screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Initialize settings with defaults
        self.settings = {
            'business_name': 'Your Business Name',
            'business_address': 'Your Business Address',
            'business_phone': 'Your Phone Number',
            'header_text': '',
            'footer_text': 'Thank you for your business!',
            'paper_size': '300x95',
            'font_size': 19,
            'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing
            'selected_printer': 'Default Printer',
            'logo_image': None
        }

        # Create interface
        self.create_interface()

    def hide(self):
        """Hide the receipt settings screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the receipt settings interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('receipt_settings'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create settings form
        self.create_settings_form(content_frame)

    def create_settings_form(self, parent):
        """Create the settings form with real-time preview"""
        # Main container with two columns
        main_container = tk.Frame(parent, bg='#1a1a1a')
        main_container.pack(fill=tk.BOTH, expand=True)

        # Left side - Settings form (bigger)
        settings_frame = tk.LabelFrame(main_container, text=self.app.get_text('receipt_settings'),
                                      font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white', width=600)
        settings_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        settings_frame.pack_propagate(False)

        # Right side - Real-time preview (smaller)
        preview_frame = tk.LabelFrame(main_container, text="Receipt Preview",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white', width=400)
        preview_frame.pack(side=tk.RIGHT, fill=tk.Y)
        preview_frame.pack_propagate(False)

        # Scrollable settings form
        canvas = tk.Canvas(settings_frame, bg='#2d2d2d')
        scrollbar = ttk.Scrollbar(settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2d2d2d')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Form fields
        form_frame = tk.Frame(scrollable_frame, bg='#2d2d2d')
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create preview area
        self.create_preview_area(preview_frame)

        # Business name
        self.create_form_field(form_frame, self.app.get_text('business_name'), 'business_name')

        # Business address
        self.create_form_field(form_frame, self.app.get_text('business_address'), 'business_address')

        # Business phone
        self.create_form_field(form_frame, self.app.get_text('business_phone'), 'business_phone')

        # Header text
        self.create_form_field(form_frame, self.app.get_text('header_text'), 'header_text')

        # Footer text
        self.create_form_field(form_frame, self.app.get_text('footer_text'), 'footer_text')

        # Logo selection
        self.create_logo_field(form_frame)

        # Logo size settings
        self.create_logo_size_fields(form_frame)

        # Paper size
        self.create_paper_size_field(form_frame)

        # Font size
        self.create_font_size_field(form_frame)

        # Line spacing
        self.create_line_spacing_field(form_frame)

        # Action buttons
        self.create_action_buttons(form_frame)

        # Load settings after all form fields are created
        self.load_settings()

        # Initial preview update - delay to ensure everything is ready
        self.root.after(100, self.update_preview)

    def create_form_field(self, parent, label_text, field_name):
        """Create a form field with real-time preview update"""
        field_frame = tk.Frame(parent, bg='#2d2d2d')
        field_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(field_frame, text=label_text, font=('Segoe UI', 10, 'bold'),
                        bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Entry with real-time update
        entry_var = tk.StringVar(value=self.settings.get(field_name, ''))
        entry_var.trace('w', lambda *args: self.update_preview())

        entry = tk.Entry(field_frame, textvariable=entry_var, font=('Segoe UI', 10), width=50)
        entry.pack(fill=tk.X)

        # Store reference
        setattr(self, f'{field_name}_var', entry_var)
        print(f"Created form field: {field_name}_var")

    def create_logo_field(self, parent):
        """Create logo selection field"""
        logo_frame = tk.Frame(parent, bg='#2d2d2d')
        logo_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(logo_frame, text=self.app.get_text('logo'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Logo selection frame
        logo_select_frame = tk.Frame(logo_frame, bg='#2d2d2d')
        logo_select_frame.pack(fill=tk.X)

        # Select button
        select_btn = tk.Button(logo_select_frame, text=self.app.get_text('select_logo'),
                              font=('Segoe UI', 9), bg='#007bff', fg='white',
                              padx=15, pady=5, command=self.select_logo)
        select_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Logo preview
        self.logo_preview_label = tk.Label(logo_select_frame, text=self.app.get_text('no_logo_selected'),
                                          font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        self.logo_preview_label.pack(side=tk.LEFT)

        # Load existing logo if any
        if self.settings.get('logo_image'):
            self.logo_preview_label.config(text="Logo loaded")

    def create_paper_size_field(self, parent):
        """Create paper size selection field with printer detection"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(size_frame, text=self.app.get_text('paper_size'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Printer detection button
        detect_frame = tk.Frame(size_frame, bg='#2d2d2d')
        detect_frame.pack(fill=tk.X, pady=(0, 5))

        detect_btn = tk.Button(detect_frame, text="🖨️ Detect Printers",
                              font=('Segoe UI', 9, 'bold'), bg='#17a2b8', fg='white',
                              padx=10, pady=3, command=self.detect_printers)
        detect_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.printer_status_label = tk.Label(detect_frame, text="Click to detect connected printers",
                                           font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        self.printer_status_label.pack(side=tk.LEFT)

        # Printer selection
        printer_select_frame = tk.Frame(size_frame, bg='#2d2d2d')
        printer_select_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Label(printer_select_frame, text="Select Printer:",
                font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 2))

        self.selected_printer_var = tk.StringVar(value=self.settings.get('selected_printer', 'Default Printer'))
        self.selected_printer_var.trace('w', lambda *args: self.update_preview())  # Add real-time update
        self.printer_combo = ttk.Combobox(printer_select_frame, textvariable=self.selected_printer_var,
                                         values=['Default Printer'], state='readonly',
                                         font=('Segoe UI', 9), width=30)
        self.printer_combo.pack(side=tk.LEFT, padx=(0, 10))

        connect_btn = tk.Button(printer_select_frame, text="Connect",
                               font=('Segoe UI', 9, 'bold'), bg='#28a745', fg='white',
                               padx=10, pady=2, command=self.connect_to_printer)
        connect_btn.pack(side=tk.LEFT)

        # Test print button on new line under connect button
        test_frame = tk.Frame(size_frame, bg='#2d2d2d')
        test_frame.pack(fill=tk.X, pady=(5, 0))

        test_print_btn = tk.Button(test_frame, text="Test Print",
                                  font=('Segoe UI', 9, 'bold'), bg='#ffc107', fg='white',
                                  padx=10, pady=2, command=self.test_print)
        test_print_btn.pack(anchor='w')

        # Size selection with real-time update
        self.paper_size_var = tk.StringVar(value=self.settings.get('paper_size', '300x95'))
        self.paper_size_var.trace_add('write', lambda *args: self.update_preview())

        # Get available sizes including detected printer sizes
        available_sizes = self.get_available_paper_sizes()

        size_combo = ttk.Combobox(size_frame, textvariable=self.paper_size_var,
                                 values=available_sizes, state='readonly',
                                 font=('Segoe UI', 10), width=25)
        size_combo.pack(anchor='w', pady=(5, 0))
        size_combo.bind('<<ComboboxSelected>>', lambda e: self.handle_size_selection())

        # Custom size fields (initially hidden)
        self.custom_size_frame = tk.Frame(size_frame, bg='#2d2d2d')

        custom_label = tk.Label(self.custom_size_frame, text="Custom Size:",
                               font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white')
        custom_label.pack(anchor='w', pady=(5, 2))

        custom_input_frame = tk.Frame(self.custom_size_frame, bg='#2d2d2d')
        custom_input_frame.pack(fill=tk.X)

        tk.Label(custom_input_frame, text="Width:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 5))

        self.custom_width_var = tk.StringVar(value="300")
        self.custom_width_var.trace('w', lambda *args: self.update_preview())  # Add real-time update
        width_spinbox = tk.Spinbox(custom_input_frame, from_=100, to=5000, width=8,
                                  textvariable=self.custom_width_var, font=('Segoe UI', 9),
                                  command=self.update_preview)  # Also update on spinbox buttons
        width_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        tk.Label(custom_input_frame, text="Height:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 5))

        self.custom_height_var = tk.StringVar(value="95")
        self.custom_height_var.trace('w', lambda *args: self.update_preview())  # Add real-time update
        height_spinbox = tk.Spinbox(custom_input_frame, from_=50, to=5000, width=8,
                                   textvariable=self.custom_height_var, font=('Segoe UI', 9),
                                   command=self.update_preview)  # Also update on spinbox buttons
        height_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        apply_custom_btn = tk.Button(custom_input_frame, text="Apply",
                                    font=('Segoe UI', 9, 'bold'), bg='#28a745', fg='white',
                                    padx=10, pady=2, command=self.apply_custom_size)
        apply_custom_btn.pack(side=tk.LEFT)

    def create_font_size_field(self, parent):
        """Create font size field"""
        font_frame = tk.Frame(parent, bg='#2d2d2d')
        font_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(font_frame, text=self.app.get_text('font_size'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Font size spinbox with real-time update
        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 19))
        self.font_size_var.trace('w', lambda *args: self.update_preview())

        font_spinbox = tk.Spinbox(font_frame, from_=8, to=72, textvariable=self.font_size_var,
                                 font=('Segoe UI', 10), width=10, command=self.update_preview)
        font_spinbox.pack(anchor='w')

    def create_line_spacing_field(self, parent):
        """Create line spacing field"""
        spacing_frame = tk.Frame(parent, bg='#2d2d2d')
        spacing_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(spacing_frame, text="Line Spacing",
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Line spacing spinbox with real-time update
        self.line_spacing_var = tk.IntVar(value=self.settings.get('line_spacing', 0))  # Changed default from 8 to 0
        self.line_spacing_var.trace('w', lambda *args: self.update_preview())

        spacing_spinbox = tk.Spinbox(spacing_frame, from_=0, to=20, textvariable=self.line_spacing_var,
                                   font=('Segoe UI', 10), width=10, command=self.update_preview)
        spacing_spinbox.pack(anchor='w')

        # Help text
        help_label = tk.Label(spacing_frame, text="Pixels between lines (0-20)",
                            font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        help_label.pack(anchor='w', pady=(2, 0))

    def create_preview_area(self, parent):
        """Create simple, robust receipt preview area"""
        # Preview container
        preview_container = tk.Frame(parent, bg='#2d2d2d')
        preview_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Preview canvas with scrollbar
        preview_canvas = tk.Canvas(preview_container, bg='#404040', relief='solid', bd=1)
        preview_scrollbar = ttk.Scrollbar(preview_container, orient="vertical", command=preview_canvas.yview)
        self.preview_frame = tk.Frame(preview_canvas, bg='#2d2d2d')

        preview_canvas.configure(yscrollcommand=preview_scrollbar.set)
        preview_canvas.pack(side="left", fill="both", expand=True)
        preview_scrollbar.pack(side="right", fill="y")

        preview_canvas.create_window((0, 0), window=self.preview_frame, anchor="nw")
        self.preview_frame.bind("<Configure>",
                               lambda e: preview_canvas.configure(scrollregion=preview_canvas.bbox("all")))

        # Add a simple test label first
        test_label = tk.Label(self.preview_frame, text="Preview Loading...",
                             font=('Segoe UI', 12), bg='#2d2d2d', fg='blue')
        test_label.pack(pady=20)

        # Initialize simple preview system after a delay
        def init_preview():
            try:
                # Clear test label
                test_label.destroy()

                from simple_preview import SimpleReceiptPreview
                self.simple_preview = SimpleReceiptPreview(self.preview_frame, self)
                self.simple_preview.create_preview()
                print("Simple preview initialized successfully")
            except Exception as e:
                # Clear test label and show error
                test_label.destroy()
                error_label = tk.Label(self.preview_frame, text=f"Preview error: {e}",
                                     font=('Segoe UI', 10), bg='#2d2d2d', fg='red')
                error_label.pack(pady=20)
                print(f"Preview initialization error: {e}")
                # Try fallback preview
                self.create_fallback_preview()

        # Delay initialization to ensure everything is ready
        self.root.after(200, init_preview)

    def create_action_buttons(self, parent):
        """Create action buttons"""
        buttons_frame = tk.Frame(parent, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(30, 0))

        # Save button
        save_btn = tk.Button(buttons_frame, text=self.app.get_text('save'),
                            font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                            padx=20, pady=8, command=self.save_settings)
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Reset button
        reset_btn = tk.Button(buttons_frame, text=self.app.get_text('reset_settings'),
                             font=('Segoe UI', 10, 'bold'), bg='#ffc107', fg='white',
                             padx=20, pady=8, command=self.reset_settings)
        reset_btn.pack(side=tk.LEFT)

    def load_settings(self):
        """Load receipt settings from database and populate form fields"""
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM receipt_settings WHERE id = 1")
            result = c.fetchone()

            if result:
                # Convert sqlite3.Row to dictionary properly
                self.settings = {}
                for key in result.keys():
                    self.settings[key] = result[key]

                # Populate form fields with loaded values
                if hasattr(self, 'business_name_var'):
                    self.business_name_var.set(self.settings.get('business_name', ''))
                if hasattr(self, 'business_address_var'):
                    self.business_address_var.set(self.settings.get('business_address', ''))
                if hasattr(self, 'business_phone_var'):
                    self.business_phone_var.set(self.settings.get('business_phone', ''))
                if hasattr(self, 'header_text_var'):
                    self.header_text_var.set(self.settings.get('header_text', ''))
                if hasattr(self, 'footer_text_var'):
                    self.footer_text_var.set(self.settings.get('footer_text', ''))
                if hasattr(self, 'paper_size_var'):
                    self.paper_size_var.set(self.settings.get('paper_size', '300x95'))
                if hasattr(self, 'font_size_var'):
                    self.font_size_var.set(self.settings.get('font_size', 19))
                if hasattr(self, 'line_spacing_var'):
                    self.line_spacing_var.set(self.settings.get('line_spacing', 0))  # Changed default from 8 to 0
                if hasattr(self, 'selected_printer_var'):
                    self.selected_printer_var.set(self.settings.get('selected_printer', 'Default Printer'))

                # Load logo file path first (preferred method)
                logo_file_path = self.settings.get('logo_file_path')
                if logo_file_path and hasattr(self, 'logo_preview_label'):
                    import os
                    if os.path.exists(logo_file_path):
                        # File exists, use it
                        self.logo_file_path = logo_file_path
                        filename = os.path.basename(logo_file_path)
                        self.logo_preview_label.config(text=f"Logo: {filename} (from file)")
                        print(f"Loaded logo file path: {logo_file_path}")
                    else:
                        # File doesn't exist, clear the path
                        print(f"Logo file not found: {logo_file_path}")
                        logo_file_path = None

                # Load logo base64 data (fallback or backup)
                if self.settings.get('logo_image'):
                    logo_data = self.settings['logo_image']

                    # Handle both old raw bytes and new base64 format
                    if isinstance(logo_data, bytes):
                        # Old format: convert raw bytes to base64 and save back to database
                        import base64
                        self.logo_image = base64.b64encode(logo_data).decode('utf-8')
                        print(f"Converted old logo format (bytes length: {len(logo_data)}) to base64 (length: {len(self.logo_image)})")

                        # Save the converted format back to database immediately
                        try:
                            conn = get_db_connection()
                            c = conn.cursor()
                            c.execute("UPDATE receipt_settings SET logo_image = ? WHERE id = 1", (self.logo_image,))
                            conn.commit()
                            conn.close()
                            print("Updated database with base64 logo format")
                        except Exception as e:
                            print(f"Error updating logo format in database: {e}")

                    elif isinstance(logo_data, str):
                        # New format: already base64 encoded
                        self.logo_image = logo_data
                        print(f"Loaded logo in base64 format (length: {len(logo_data)})")
                    else:
                        print(f"Unknown logo format: {type(logo_data)}")
                        self.logo_image = None

                    # Only show "loaded from database" if no file path was found
                    if hasattr(self, 'logo_preview_label') and self.logo_image and not logo_file_path:
                        self.logo_preview_label.config(text=f"Logo loaded from database (base64 length: {len(self.logo_image)})")
                        print(f"Logo loaded successfully, base64 length: {len(self.logo_image)}")

                # Load logo size settings
                if hasattr(self, 'logo_size_var'):
                    self.logo_size_var.set(self.settings.get('logo_size', 100))  # Default 100% size

            else:
                # Default settings
                self.settings = {
                    'business_name': 'Your Business Name',
                    'business_address': 'Your Business Address',
                    'business_phone': 'Your Phone Number',
                    'header_text': '',
                    'footer_text': 'Thank you for your business!',
                    'paper_size': '300x95',
                    'font_size': 19,
                    'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing
                    'selected_printer': 'Default Printer',
                    'logo_image': None
                }
        finally:
            conn.close()

    def select_logo(self):
        """Select logo image"""
        file_path = filedialog.askopenfilename(
            title=self.app.get_text('select_logo'),
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        if file_path:
            try:
                # Load and process image
                image = Image.open(file_path)

                # Convert to grayscale first
                if image.mode != 'L':
                    image = image.convert('L')

                # Convert to black and white for receipt printing
                threshold = 128
                image = image.point(lambda x: 0 if x < threshold else 255, '1')

                # Calculate aspect ratio and resize appropriately
                original_width, original_height = image.size
                aspect_ratio = original_width / original_height

                # Target size for receipt logo (not too big)
                max_width, max_height = 200, 80

                if aspect_ratio > 1:  # Wider than tall
                    new_width = min(max_width, original_width)
                    new_height = int(new_width / aspect_ratio)
                else:  # Taller than wide
                    new_height = min(max_height, original_height)
                    new_width = int(new_height * aspect_ratio)

                # Resize with high quality
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Store the file path for direct loading
                self.logo_file_path = file_path

                # Convert to bytes for storage and encode as base64 (backup compatibility)
                import io
                import base64
                img_bytes = io.BytesIO()
                image.save(img_bytes, format='PNG')
                # Store as base64 encoded string for compatibility with receipt generator
                self.logo_image = base64.b64encode(img_bytes.getvalue()).decode('utf-8')
                print(f"Logo file path: {file_path}")
                print(f"Logo encoded to base64, length: {len(self.logo_image)}")

                # Update preview label
                filename = file_path.split('/')[-1].split('\\')[-1]  # Handle both / and \ separators
                self.logo_preview_label.config(text=f"Logo: {filename} ({new_width}x{new_height})")

                # Update the preview to show the logo
                self.update_preview()

            except Exception as e:
                messagebox.showerror(self.app.get_text('error'), f"Error loading logo: {e}")

    def create_logo_size_fields(self, parent):
        """Create logo size adjustment fields with slider"""
        # Logo size frame
        logo_size_frame = tk.LabelFrame(parent, text="Logo Size Settings",
                                       font=('Segoe UI', 10, 'bold'), bg='#2d2d2d')
        logo_size_frame.pack(fill=tk.X, pady=5)

        # Create inner frame for better layout
        inner_frame = tk.Frame(logo_size_frame, bg='#2d2d2d')
        inner_frame.pack(fill=tk.X, padx=10, pady=10)

        # Logo size label and value display
        size_header_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        size_header_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(size_header_frame, text="Logo Size:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(side=tk.LEFT)

        self.logo_size_var = tk.IntVar(value=100)
        self.logo_size_var.trace('w', lambda *args: self.update_preview())  # Add real-time update
        self.size_value_label = tk.Label(size_header_frame, text="100%",
                                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.size_value_label.pack(side=tk.RIGHT)

        # Logo size slider
        slider_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        slider_frame.pack(fill=tk.X, pady=5)

        # Size labels
        tk.Label(slider_frame, text="Small\n(50%)", font=('Segoe UI', 8),
                bg='#2d2d2d', fg='#6c757d').pack(side=tk.LEFT)

        # Slider - WIDER with real-time preview update
        self.logo_size_slider = tk.Scale(slider_frame, from_=25, to=200,
                                        orient=tk.HORIZONTAL, variable=self.logo_size_var,
                                        font=('Segoe UI', 9), bg='#2d2d2d', fg='#333',
                                        highlightthickness=0, length=400,  # Increased from 300
                                        command=self.update_size_and_preview)
        self.logo_size_slider.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=10)

        tk.Label(slider_frame, text="Large\n(200%)", font=('Segoe UI', 8),
                bg='#2d2d2d', fg='#6c757d').pack(side=tk.RIGHT)

        # Preset buttons
        preset_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        preset_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Label(preset_frame, text="Quick presets:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT)

        presets = [("Small", 75), ("Default", 100), ("Large", 150)]
        for name, value in presets:
            btn = tk.Button(preset_frame, text=name,
                           command=lambda v=value: self.set_logo_size(v),
                           font=('Segoe UI', 8), bg='#404040', relief='ridge',
                           padx=8, pady=2)
            btn.pack(side=tk.LEFT, padx=2)

        # Info label
        info_label = tk.Label(inner_frame,
                             text="💡 Tip: Drag slider or use presets to adjust logo size. 100% = default size",
                             font=('Segoe UI', 9, 'italic'), bg='#2d2d2d', fg='#6c757d')
        info_label.pack(pady=(5, 0))

    def update_size_label(self, value):
        """Update the size percentage label"""
        self.size_value_label.config(text=f"{value}%")

    def update_size_and_preview(self, value):
        """Update both size label and preview in real-time"""
        self.update_size_label(value)
        self.update_preview()

    def set_logo_size(self, value):
        """Set logo size to specific value"""
        self.logo_size_var.set(value)
        self.update_size_label(value)
        self.update_preview()  # Add preview update here too

    def update_preview(self):
        """Update preview using simple preview system"""
        try:
            if hasattr(self, 'simple_preview'):
                self.simple_preview.update_preview()
            else:
                # Fallback: create basic preview if simple_preview doesn't exist
                self.create_fallback_preview()
        except Exception as e:
            print(f"Preview update error: {e}")
            self.create_fallback_preview()

    def create_fallback_preview(self):
        """Create a basic fallback preview"""
        try:
            # Clear existing preview
            for widget in self.preview_frame.winfo_children():
                widget.destroy()

            # Create basic preview
            title_label = tk.Label(self.preview_frame, text="Receipt Preview",
                                 font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='white')
            title_label.pack(pady=20)

            # Get current settings safely
            try:
                business_name = getattr(self, 'business_name_var', tk.StringVar(value='Sample Business')).get()
                font_size = getattr(self, 'font_size_var', tk.IntVar(value=19)).get()
                line_spacing = getattr(self, 'line_spacing_var', tk.IntVar(value=0)).get()  # Changed default from 8 to 0
            except:
                business_name = 'Sample Business'
                font_size = 19
                line_spacing = 0  # Changed default from 8 to 0

            # Create simple receipt preview
            preview_container = tk.Frame(self.preview_frame, bg='#1a1a1a', relief='solid', bd=1)
            preview_container.pack(pady=10, padx=20)

            receipt_area = tk.Frame(preview_container, bg='#2d2d2d', relief='solid', bd=1, width=350, height=500)  # Smaller preview area
            receipt_area.pack(padx=10, pady=10)
            receipt_area.pack_propagate(False)

            # Sample receipt content
            sample_lines = [
                business_name.upper(),
                "123 Business Street",
                "Tel: (*************",
                "",
                "Date: 15/01/2024",
                "Time: 14:30:25",
                "Cashier: Admin",
                "Receipt #: R000123",
                "=" * 30,
                "",
                "TRANSACTION DETAILS",
                "-" * 30,
                "Coffee",
                "  1 x 5.00 MAD = 5.00 MAD",
                "Sandwich",
                "  1 x 12.00 MAD = 12.00 MAD",
                "",
                "-" * 30,
                "TOTAL: 17.00 MAD",
                "=" * 30,
                "",
                "Thank you!"
            ]

            # Display lines
            for line in sample_lines:
                line_label = tk.Label(receipt_area, text=line,
                                    font=('Courier', max(8, font_size//2), 'bold'),
                                    bg='#2d2d2d', fg='white', anchor='w')
                line_label.pack(fill=tk.X, padx=5, pady=(0, max(1, line_spacing//3)))

            # Info
            info_label = tk.Label(self.preview_frame, text=f"Font: {font_size}pt | Spacing: {line_spacing}px",
                                font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
            info_label.pack(pady=5)

        except Exception as e:
            # Last resort: just show an error message
            error_label = tk.Label(self.preview_frame, text="Preview temporarily unavailable",
                                 font=('Segoe UI', 12), bg='#2d2d2d', fg='#666')
            error_label.pack(pady=50)

    def old_update_preview(self):
        """Update the real-time receipt preview showing actual printed format"""
        try:
            # Clear existing preview
            for widget in self.preview_frame.winfo_children():
                widget.destroy()

            # Get current settings
            business_name = getattr(self, 'business_name_var', tk.StringVar()).get()
            business_address = getattr(self, 'business_address_var', tk.StringVar()).get()
            business_phone = getattr(self, 'business_phone_var', tk.StringVar()).get()
            header_text = getattr(self, 'header_text_var', tk.StringVar()).get()
            footer_text = getattr(self, 'footer_text_var', tk.StringVar()).get()
            font_size = getattr(self, 'font_size_var', tk.IntVar(value=19)).get()
            paper_size = getattr(self, 'paper_size_var', tk.StringVar(value='300x95')).get()

            # Generate actual receipt content using the receipt generator
            from receipt_generator import ReceiptGenerator
            receipt_gen = ReceiptGenerator(self.app)

            # Create sample data for preview
            sample_cart = [
                {'name': 'Coffee', 'quantity': 1, 'price': 5.00},
                {'name': 'Sandwich Deluxe', 'quantity': 1, 'price': 12.00},
                {'name': 'Extra Service', 'quantity': 1, 'price': 3.00}
            ]
            sample_total = 20.00
            sample_user = "Admin"
            sample_receipt_number = "R000123"

            # Generate receipt content based on selected type
            preview_type = getattr(self, 'preview_type', tk.StringVar(value="Customer Receipt")).get()

            if preview_type == "Customer Receipt":
                receipt_content = receipt_gen.generate_customer_receipt(
                    sample_cart, [], sample_total, sample_user, sample_receipt_number
                )
            elif preview_type == "Summary Receipt":
                receipt_content = receipt_gen.generate_summary_receipt(
                    sample_user, sample_total, "S000123"
                )
            elif preview_type == "History Report":
                sample_sales_data = [
                    {'id': 1, 'date': '15/01/2024 14:30', 'username': 'Admin', 'total': 24.00, 'items': 'Coffee (1), Sandwich (1)'},
                    {'id': 2, 'date': '15/01/2024 13:15', 'username': 'User1', 'total': 18.50, 'items': 'Tea (2), Cake (1)'}
                ]
                sample_filter_info = {
                    'date_range': '2024-01-01 to 2024-01-15',
                    'user_filter': 'All Users',
                    'time_period': 'Today (06:00 - 06:00 +24h)'
                }
                receipt_content = receipt_gen.generate_history_receipt(
                    sample_sales_data, sample_filter_info, sample_user
                )
            else:
                receipt_content = "Invalid preview type selected"

            # Calculate preview dimensions based on paper size
            if 'x' in paper_size:
                try:
                    width_str, height_str = paper_size.split('x')[0:2]
                    paper_width = int(width_str.split('(')[0].strip())
                    paper_height = int(height_str.split('(')[0].strip())
                except:
                    paper_width, paper_height = 300, 400
            else:
                paper_width, paper_height = 300, 400

            # Get custom line spacing
            custom_line_spacing = getattr(self, 'line_spacing_var', tk.IntVar(value=3)).get()

            # Calculate optimal scale for preview - use full width available
            auto_fit_enabled = getattr(self, 'auto_fit', tk.BooleanVar(value=True)).get()

            # Get available preview area width (use full width of preview area)
            preview_area_width = self.preview_frame.winfo_width() if self.preview_frame.winfo_width() > 1 else 600
            max_preview_width = max(preview_area_width - 100, 400)  # Leave some margin but use most of the space

            if auto_fit_enabled:
                # Auto-fit: scale to show entire receipt optimally
                max_preview_height = 700

                # Calculate required height based on content length
                lines = receipt_content.strip().split('\n')
                content_lines = [line for line in lines if line.strip()]

                # Estimate required dimensions with custom line spacing
                estimated_font_size = max(8, min(font_size, 16))  # Reasonable range
                line_height = estimated_font_size + custom_line_spacing
                estimated_height = len(content_lines) * line_height + 60

                # Calculate scale factors for both dimensions
                width_scale = max_preview_width / paper_width
                height_scale = max_preview_height / estimated_height if estimated_height > 0 else 1.0

                # Use the smaller scale to ensure everything fits
                scale_factor = min(width_scale, height_scale, 1.0)
                preview_width = int(paper_width * scale_factor)
                preview_height = min(int(estimated_height * scale_factor), max_preview_height)

            else:
                # Fixed scale: maintain paper proportions but use available width
                scale_factor = min(max_preview_width / paper_width, 1.0)
                preview_width = int(paper_width * scale_factor)

                lines = receipt_content.strip().split('\n')
                content_lines = [line for line in lines if line.strip()]

                estimated_font_size = max(8, int(font_size * scale_factor * 0.9))
                line_height = estimated_font_size + custom_line_spacing
                required_height = len(content_lines) * line_height + 40
                preview_height = min(required_height, 600)

            # Calculate final font size and line height for preview
            preview_font_size = max(7, int(font_size * scale_factor * 0.9))
            preview_line_spacing = max(1, int(custom_line_spacing * scale_factor))
            line_height = preview_font_size + preview_line_spacing

            # Create preview title and type selector
            title_frame = tk.Frame(self.preview_frame, bg='#2d2d2d')
            title_frame.pack(pady=(10, 10))

            preview_title = tk.Label(title_frame, text=f"Receipt Preview ({paper_size})",
                                   font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white')
            preview_title.pack()

            # Receipt type selector and options
            type_frame = tk.Frame(title_frame, bg='#2d2d2d')
            type_frame.pack(pady=(5, 0))

            tk.Label(type_frame, text="Preview Type:", font=('Segoe UI', 9),
                    bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 5))

            self.preview_type = getattr(self, 'preview_type', tk.StringVar(value="Customer Receipt"))
            type_combo = ttk.Combobox(type_frame, textvariable=self.preview_type,
                                     values=["Customer Receipt", "Summary Receipt", "History Report"],
                                     state='readonly', width=15, font=('Segoe UI', 9))
            type_combo.pack(side=tk.LEFT, padx=(0, 10))
            type_combo.bind('<<ComboboxSelected>>', lambda e: self.update_preview())

            # Auto-fit option
            self.auto_fit = getattr(self, 'auto_fit', tk.BooleanVar(value=True))
            auto_fit_check = tk.Checkbutton(type_frame, text="Auto-fit", variable=self.auto_fit,
                                          font=('Segoe UI', 9), bg='#2d2d2d',
                                          command=self.update_preview)
            auto_fit_check.pack(side=tk.LEFT)

            # Paper representation
            paper_frame = tk.Frame(self.preview_frame, bg='#1a1a1a', relief='solid', bd=1)
            paper_frame.pack(pady=(0, 20))

            # Receipt container - auto-sizing based on content
            receipt_container = tk.Frame(paper_frame, bg='#2d2d2d', relief='solid',
                                       bd=1, width=preview_width)
            receipt_container.pack(padx=10, pady=10)

            # Create scrollable text area for receipt content
            receipt_canvas = tk.Canvas(receipt_container, bg='#2d2d2d', highlightthickness=0,
                                     width=preview_width-20, height=preview_height)
            receipt_scrollbar = ttk.Scrollbar(receipt_container, orient="vertical", command=receipt_canvas.yview)
            receipt_text_frame = tk.Frame(receipt_canvas, bg='#2d2d2d')

            receipt_canvas.configure(yscrollcommand=receipt_scrollbar.set)
            receipt_canvas.pack(side="left", fill="both", expand=True)
            receipt_scrollbar.pack(side="right", fill="y")

            receipt_canvas.create_window((0, 0), window=receipt_text_frame, anchor="nw")

            # Display actual receipt content line by line with custom spacing
            y_position = 0
            for line in content_lines:
                # Create label with custom line spacing
                line_label = tk.Label(receipt_text_frame, text=line,
                                    font=('Courier', preview_font_size, 'bold'),
                                    bg='#2d2d2d', fg='white', anchor='w', justify='left',
                                    wraplength=preview_width-30)  # Wrap long lines
                line_label.pack(fill=tk.X, padx=3, pady=(0, preview_line_spacing))  # Use custom spacing

                # Update position
                y_position += line_height

            # Add empty lines for spacing where needed (preserve original empty lines)
            for line in lines:
                if not line.strip():  # Empty line
                    spacer_label = tk.Label(receipt_text_frame, text=" ",
                                          font=('Courier', max(1, preview_font_size//3), 'bold'),
                                          bg='#2d2d2d', fg='white')
                    spacer_label.pack(fill=tk.X, pady=(0, preview_line_spacing//2))

            # Update scroll region and ensure proper sizing
            receipt_text_frame.update_idletasks()
            receipt_canvas.configure(scrollregion=receipt_canvas.bbox("all"))

            # Auto-adjust canvas height to show more content if possible
            content_height = receipt_text_frame.winfo_reqheight()
            if content_height < preview_height:
                receipt_canvas.configure(height=content_height + 20)

            # Bind mousewheel to canvas for better scrolling
            def on_mousewheel(event):
                receipt_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            receipt_canvas.bind("<MouseWheel>", on_mousewheel)

            # Add paper size info
            size_info = tk.Label(self.preview_frame,
                               text=f"Paper: {paper_width}x{paper_height}px | Font: {font_size}pt | Scale: {scale_factor:.1f}x",
                               font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
            size_info.pack(pady=(0, 10))



        except Exception as e:
            # Handle any errors gracefully
            error_label = tk.Label(self.preview_frame, text=f"Preview Error: {e}",
                                 font=('Segoe UI', 10), bg='#2d2d2d', fg='red')
            error_label.pack(pady=20)

    def save_settings(self):
        """Save receipt settings"""
        try:
            # Collect form data
            settings_data = {
                'business_name': self.business_name_var.get(),
                'business_address': self.business_address_var.get(),
                'business_phone': self.business_phone_var.get(),
                'header_text': self.header_text_var.get(),
                'footer_text': self.footer_text_var.get(),
                'paper_size': self.paper_size_var.get(),
                'font_size': self.font_size_var.get(),
                'line_spacing': self.line_spacing_var.get(),
                'selected_printer': getattr(self, 'selected_printer_var', tk.StringVar()).get(),
                'logo_image': getattr(self, 'logo_image', None),
                'logo_file_path': getattr(self, 'logo_file_path', None),
                'logo_size': getattr(self, 'logo_size_var', tk.IntVar(value=100)).get()
            }

            conn = get_db_connection()
            try:
                c = conn.cursor()

                # Update or insert settings
                c.execute("SELECT id FROM receipt_settings WHERE id = 1")
                if c.fetchone():
                    # Update existing
                    c.execute("""
                        UPDATE receipt_settings SET
                        business_name=?, business_address=?, business_phone=?,
                        header_text=?, footer_text=?, logo_image=?, logo_file_path=?,
                        paper_size=?, font_size=?, line_spacing=?, selected_printer=?,
                        logo_size=?
                        WHERE id=1
                    """, (settings_data['business_name'], settings_data['business_address'],
                         settings_data['business_phone'], settings_data['header_text'],
                         settings_data['footer_text'], settings_data['logo_image'], settings_data['logo_file_path'],
                         settings_data['paper_size'], settings_data['font_size'],
                         settings_data['line_spacing'], settings_data['selected_printer'],
                         settings_data['logo_size']))
                else:
                    # Insert new
                    c.execute("""
                        INSERT INTO receipt_settings (id, business_name, business_address, business_phone,
                        header_text, footer_text, logo_image, logo_file_path, paper_size, font_size, line_spacing, selected_printer,
                        logo_size)
                        VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (settings_data['business_name'], settings_data['business_address'],
                         settings_data['business_phone'], settings_data['header_text'],
                         settings_data['footer_text'], settings_data['logo_image'], settings_data['logo_file_path'],
                         settings_data['paper_size'], settings_data['font_size'],
                         settings_data['line_spacing'], settings_data['selected_printer'],
                         settings_data['logo_size']))

                conn.commit()
                messagebox.showinfo(self.app.get_text('success'),
                                  self.app.get_text('settings_saved'))

            finally:
                conn.close()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_save_settings')}: {e}")

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno(self.app.get_text('confirm'),
                              self.app.get_text('confirm_reset')):
            # Reset form fields
            self.business_name_var.set(self.app.get_text('default_business_name'))
            self.business_address_var.set(self.app.get_text('default_business_address'))
            self.business_phone_var.set(self.app.get_text('default_business_phone'))
            self.header_text_var.set(self.app.get_text('default_header_text'))
            self.footer_text_var.set(self.app.get_text('default_footer_text'))

            self.paper_size_var.set('300x95')
            self.font_size_var.set(19)

            self.logo_image = None
            self.logo_preview_label.config(text=self.app.get_text('no_logo_selected'))

            # Update preview with new values
            self.update_preview()

    def detect_printers(self):
        """Detect connected printers and their capabilities"""
        try:
            import win32print
            self.printer_status_label.config(text="Detecting printers...", fg='#ffc107')
            self.root.update()

            # Get list of printers
            printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)

            detected_sizes = []
            printer_names = ['Default Printer']
            printer_info = []

            for printer in printers:
                printer_name = printer[2]
                printer_names.append(printer_name)

                try:
                    # Get printer capabilities
                    handle = win32print.OpenPrinter(printer_name)

                    # Detect printer type and suggest sizes
                    if 'thermal' in printer_name.lower() or 'receipt' in printer_name.lower():
                        detected_sizes.extend(['80x297 (Thermal)', '58x297 (Thermal)', '80x200 (Thermal)'])
                        printer_type = "Thermal"
                    elif 'label' in printer_name.lower():
                        detected_sizes.extend(['100x150 (Label)', '100x100 (Label)'])
                        printer_type = "Label"
                    else:
                        detected_sizes.extend(['210x297 (A4)', '216x279 (Letter)'])
                        printer_type = "Standard"

                    printer_info.append(f"✓ {printer_name} ({printer_type})")
                    win32print.ClosePrinter(handle)

                except Exception as e:
                    printer_info.append(f"✗ {printer_name} (Error: {str(e)[:20]}...)")

            if len(printer_names) > 1:  # More than just "Default Printer"
                # Update printer combo
                self.printer_combo['values'] = printer_names
                self.detected_printer_sizes = list(set(detected_sizes))

                status_text = f"Found {len(printers)} printer(s)"
                self.printer_status_label.config(text=status_text, fg='#28a745')

                # Show printer info dialog
                info_text = "Detected Printers:\n" + "\n".join(printer_info[:5])
                if len(printer_info) > 5:
                    info_text += f"\n... and {len(printer_info) - 5} more"

                messagebox.showinfo("Printer Detection", info_text)

                # Refresh size combo
                self.refresh_paper_sizes()
            else:
                self.printer_status_label.config(text="No printers detected", fg='#dc3545')

        except ImportError:
            self.printer_status_label.config(text="Printer detection not available (Windows only)", fg='#ffc107')
        except Exception as e:
            self.printer_status_label.config(text=f"Detection failed: {str(e)[:30]}...", fg='#dc3545')

    def connect_to_printer(self):
        """Connect to selected printer"""
        selected_printer = self.selected_printer_var.get()

        if selected_printer == "Default Printer":
            self.printer_status_label.config(text="Using system default printer", fg='#28a745')
            self.connected_printer = None
            return

        try:
            import win32print
            # Test connection to printer
            handle = win32print.OpenPrinter(selected_printer)
            win32print.ClosePrinter(handle)

            self.connected_printer = selected_printer
            self.printer_status_label.config(text=f"Connected to {selected_printer}", fg='#28a745')

            # Auto-detect paper size for this printer
            self.auto_detect_paper_size(selected_printer)

        except Exception as e:
            self.printer_status_label.config(text=f"Connection failed: {str(e)[:30]}...", fg='#dc3545')
            self.connected_printer = None

    def auto_detect_paper_size(self, printer_name):
        """Auto-detect optimal paper size for connected printer"""
        try:
            # Suggest paper size based on printer name/type
            if 'thermal' in printer_name.lower() or 'receipt' in printer_name.lower():
                if '80' in printer_name.lower():
                    self.paper_size_var.set('80x297 (80mm Thermal)')
                elif '58' in printer_name.lower():
                    self.paper_size_var.set('58x297 (58mm Thermal)')
                else:
                    self.paper_size_var.set('300x95 (Default Thermal)')
            elif 'label' in printer_name.lower():
                self.paper_size_var.set('100x150 (Label)')
            else:
                self.paper_size_var.set('2480x3508 (A4)')

            self.update_preview()

        except Exception:
            pass  # Fail silently, keep current size

    def test_print(self):
        """Print a test receipt to verify printer connection"""
        try:
            from datetime import datetime

            # Create test receipt content
            test_content = self.generate_test_receipt()

            # Print using connected printer or default
            printer_name = getattr(self, 'connected_printer', None)

            if self.print_receipt_content(test_content, printer_name):
                messagebox.showinfo("Test Print", "Test receipt sent to printer successfully!")
            else:
                messagebox.showerror("Test Print", "Failed to print test receipt")

        except Exception as e:
            messagebox.showerror("Test Print", f"Print test failed: {e}")

    def generate_test_receipt(self):
        """Generate test receipt content"""
        from datetime import datetime

        business_name = getattr(self, 'business_name_var', tk.StringVar()).get() or "TEST BUSINESS"
        business_address = getattr(self, 'business_address_var', tk.StringVar()).get() or "123 Test Street"
        business_phone = getattr(self, 'business_phone_var', tk.StringVar()).get() or "(*************"

        current_time = datetime.now()

        content = f"""
{business_name.upper()}
{business_address}
Tel: {business_phone}

Date: {current_time.strftime('%d/%m/%Y')}
Time: {current_time.strftime('%H:%M:%S')}
{'='*40}

TEST RECEIPT
This is a test print to verify
printer connection and formatting.

{'='*40}
Receipt #: TEST001
{'='*40}

Thank you for testing!
"""
        return content

    def print_receipt_content(self, content, printer_name=None):
        """Print receipt content to specified printer with proper line spacing"""
        try:
            import win32print
            import win32ui
            import win32con

            # Use specified printer or default
            if printer_name:
                printer = printer_name
            else:
                printer = win32print.GetDefaultPrinter()

            # Create device context
            hdc = win32ui.CreateDC()
            hdc.CreatePrinterDC(printer)

            # Get printer capabilities for better scaling
            try:
                # Get printer resolution
                dpi_y = hdc.GetDeviceCaps(win32con.LOGPIXELSY)
                # Scale font size based on DPI (default 96 DPI)
                font_scale = max(dpi_y / 96.0, 1.0)
            except:
                font_scale = 1.0
                dpi_y = 96

            # Start document
            hdc.StartDoc("POS Test Receipt")
            hdc.StartPage()

            # Get current settings
            font_size = int(getattr(self, 'font_size_var', tk.IntVar(value=19)).get() * font_scale)
            line_spacing = getattr(self, 'line_spacing_var', tk.IntVar(value=3)).get()

            # Calculate proper line height with spacing
            line_spacing_printer = int(line_spacing * font_scale * (dpi_y / 96.0))
            line_height = font_size + line_spacing_printer + 10  # Extra padding for clarity

            # Create font for better text rendering
            try:
                font = win32ui.CreateFont({
                    'name': 'Courier New',
                    'height': font_size,
                    'weight': win32con.FW_BOLD
                })
                hdc.SelectObject(font)
            except:
                pass  # Use default font if creation fails

            # Print content line by line with proper spacing
            y_pos = 100  # Start position
            x_pos = 20   # Reduced left margin for less empty space

            lines = content.strip().split('\n')
            for line in lines:
                if line.strip():  # Only print non-empty lines
                    try:
                        hdc.TextOut(x_pos, y_pos, line)
                    except:
                        # Fallback for encoding issues
                        try:
                            hdc.TextOut(x_pos, y_pos, line.encode('utf-8', 'ignore').decode('utf-8'))
                        except:
                            hdc.TextOut(x_pos, y_pos, str(line))

                    y_pos += line_height
                else:
                    # Empty line - add half spacing
                    y_pos += line_height // 2

            # End document
            hdc.EndPage()
            hdc.EndDoc()
            hdc.DeleteDC()

            return True

        except Exception as e:
            print(f"Print error: {e}")
            return False

    def get_available_paper_sizes(self):
        """Get list of available paper sizes"""
        default_sizes = [
            '300x95 (Default Thermal)',
            '2480x3508 (A4)',
            '80x297 (80mm Thermal)',
            '58x297 (58mm Thermal)',
            '216x279 (Letter)',
            'Custom'
        ]

        # Add detected printer sizes if any
        detected = getattr(self, 'detected_printer_sizes', [])
        all_sizes = default_sizes + [f"{size} (Detected)" for size in detected]

        return all_sizes

    def refresh_paper_sizes(self):
        """Refresh the paper size combobox with detected sizes"""
        try:
            # Find the combobox and update its values
            for widget in self.root.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Combobox):
                            child['values'] = self.get_available_paper_sizes()
                            break
        except:
            pass

    def handle_size_selection(self):
        """Handle paper size selection"""
        selected = self.paper_size_var.get()

        if 'Custom' in selected:
            # Show custom size fields
            self.custom_size_frame.pack(fill=tk.X, pady=(10, 0))
        else:
            # Hide custom size fields
            self.custom_size_frame.pack_forget()

        self.update_preview()

    def apply_custom_size(self):
        """Apply custom paper size"""
        try:
            width = int(self.custom_width_var.get())
            height = int(self.custom_height_var.get())

            if width < 100 or height < 50:
                messagebox.showerror("Error", "Minimum size: 100x50 pixels")
                return

            custom_size = f"{width}x{height}"
            self.paper_size_var.set(custom_size)
            self.custom_size_frame.pack_forget()
            self.update_preview()

            messagebox.showinfo("Success", f"Custom size applied: {custom_size}")

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for width and height")
