#!/usr/bin/env python3
"""
Test all the final language switching fixes
"""

import sys
import os
from pathlib import Path

def test_pdv_translation():
    """Test that POS is translated to PDV in French"""
    
    print("Testing PDV Translation")
    print("=" * 25)
    
    files_to_check = [
        "translations.py",
        "YES/translations.py"
    ]
    
    all_pdv = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for PDV in French translation
            if "'login_title': 'Connexion PDV'," in content:
                print(f"   ✅ French uses PDV (Point De Vente)")
            else:
                print(f"   ❌ French doesn't use PDV")
                all_pdv = False
            
            # Check that English still uses POS
            if "'login_title': 'POS System Login'," in content:
                print(f"   ✅ English still uses POS")
            else:
                print(f"   ❌ English POS missing")
                all_pdv = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_pdv = False
    
    return all_pdv

def test_smaller_french_font():
    """Test that French title uses even smaller font (20px)"""
    
    print("\nTesting Smaller French Font")
    print("=" * 29)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_smaller = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for 20px font for French
            if "font_size = 20 if self.app.current_language == 'french' else 28" in content:
                print(f"   ✅ French uses 20px font (was 24px)")
            else:
                print(f"   ❌ French font size not reduced to 20px")
                all_smaller = False
            
            # Check that font size is used correctly
            if "font=('Segoe UI', font_size, 'bold')" in content:
                print(f"   ✅ Font size variable used correctly")
            else:
                print(f"   ❌ Font size variable not used")
                all_smaller = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_smaller = False
    
    return all_smaller

def test_no_interface_destruction():
    """Test that interface is updated without destruction"""
    
    print("\nTesting No Interface Destruction")
    print("=" * 35)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_no_destruction = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that refresh method doesn't destroy interface
            if "WITHOUT destroying interface" in content:
                print(f"   ✅ Refresh method doesn't destroy interface")
            else:
                print(f"   ❌ Refresh method may still destroy interface")
                all_no_destruction = False
            
            # Check for text updates using config()
            if "self.brand_title.config(text=" in content:
                print(f"   ✅ Brand title updated with config()")
            else:
                print(f"   ❌ Brand title not updated with config()")
                all_no_destruction = False
            
            # Check for multiple element updates
            updates = ["self.tagline.config", "self.welcome_label.config", "self.subtitle_label.config"]
            update_count = sum(1 for update in updates if update in content)
            
            if update_count >= 3:
                print(f"   ✅ Multiple elements updated with config() ({update_count}/3)")
            else:
                print(f"   ❌ Not enough elements updated ({update_count}/3)")
                all_no_destruction = False
            
            # Check that frame.destroy() is NOT called
            if "self.frame.destroy()" not in content or "# Clear and recreate" not in content:
                print(f"   ✅ Interface destruction removed")
            else:
                print(f"   ❌ Interface destruction still present")
                all_no_destruction = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_no_destruction = False
    
    return all_no_destruction

def test_element_references():
    """Test that all UI elements have stored references"""
    
    print("\nTesting Element References")
    print("=" * 28)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_references = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for stored references
            references = [
                "self.brand_title =",
                "self.tagline =", 
                "self.welcome_label =",
                "self.subtitle_label =",
                "self.username_label =",
                "self.password_label =",
                "self.login_btn =",
                "self.feature_labels ="
            ]
            
            found_references = sum(1 for ref in references if ref in content)
            
            if found_references >= 7:
                print(f"   ✅ UI element references stored ({found_references}/8)")
            else:
                print(f"   ❌ Missing UI element references ({found_references}/8)")
                all_references = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_references = False
    
    return all_references

def test_robust_error_handling():
    """Test that error handling is robust and doesn't crash"""
    
    print("\nTesting Robust Error Handling")
    print("=" * 32)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_robust = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for hasattr checks
            hasattr_checks = content.count("if hasattr(self,")
            
            if hasattr_checks >= 8:
                print(f"   ✅ Robust hasattr checks ({hasattr_checks} checks)")
            else:
                print(f"   ❌ Insufficient hasattr checks ({hasattr_checks} checks)")
                all_robust = False
            
            # Check for try-catch in refresh
            if "try:" in content and "except Exception as e:" in content and "refresh_interface" in content:
                print(f"   ✅ Try-catch in refresh method")
            else:
                print(f"   ❌ Try-catch missing in refresh method")
                all_robust = False
            
            # Check that errors don't destroy interface
            if "# Don't destroy interface on error" in content:
                print(f"   ✅ Error handling preserves interface")
            else:
                print(f"   ❌ Error handling may not preserve interface")
                all_robust = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_robust = False
    
    return all_robust

def test_all_versions_updated():
    """Test that all versions have been updated"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main Login", "login_screen.py"),
        ("Main Translations", "translations.py"),
        ("Protected Login", "YES/login_screen.py"),
        ("Protected Translations", "YES/translations.py"),
        ("Obfuscated Login", "YES_OBFUSCATED/login_screen.py"),
        ("Obfuscated Translations", "YES_OBFUSCATED/translations.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name}")
        else:
            print(f"❌ Missing: {version_name}")
            all_updated = False
    
    return all_updated

def main():
    """Run all final language switching fix tests"""
    
    print("🔧 FINAL LANGUAGE SWITCHING FIXES TEST SUITE")
    print("=" * 45)
    
    tests = [
        ("PDV Translation", test_pdv_translation),
        ("Smaller French Font", test_smaller_french_font),
        ("No Interface Destruction", test_no_interface_destruction),
        ("Element References", test_element_references),
        ("Robust Error Handling", test_robust_error_handling),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 45)
    print("📊 RESULTS")
    print("=" * 45)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ POS correctly translated to PDV in French")
        print("✅ French title uses smaller font (20px) to fit")
        print("✅ Interface never gets destroyed during language change")
        print("✅ All UI elements have stored references for updates")
        print("✅ Robust error handling prevents crashes")
        print("✅ All versions (main, protected, obfuscated) updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Final language switching fixes may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All final language switching issues completely resolved!")
        print("🇫🇷 POS → PDV translation for proper French terminology")
        print("📏 French title sized at 20px to fit perfectly")
        print("🔄 Interface updates smoothly without disappearing")
        print("⚡ Lightning-fast language switching with no delays")
        print("🛡️ Rock-solid error handling prevents any crashes")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Some final language switching issues need attention")
    
    exit(0 if success else 1)
