#!/usr/bin/env python3
"""
Test script for email settings functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_manager():
    """Test the EmailManager class directly"""
    print("=== TESTING EMAIL MANAGER ===")
    
    try:
        from email_manager import EmailManager
        
        # Create instance
        em = EmailManager()
        print("✅ EmailManager created successfully")
        
        # Test initial state
        config = em.get_smtp_config()
        print(f"✅ Initial config loaded: email_enabled = {config['email_enabled']}")
        
        # Test saving True
        print("\n--- Testing save True ---")
        result = em.save_smtp_config(True)
        print(f"Save result: {result}")
        
        # Test loading after save
        config = em.get_smtp_config()
        print(f"After save True: email_enabled = {config['email_enabled']}")
        
        # Test saving False
        print("\n--- Testing save False ---")
        result = em.save_smtp_config(False)
        print(f"Save result: {result}")
        
        # Test loading after save
        config = em.get_smtp_config()
        print(f"After save False: email_enabled = {config['email_enabled']}")
        
        # Test saving True again
        print("\n--- Testing save True again ---")
        result = em.save_smtp_config(True)
        print(f"Save result: {result}")
        
        # Test loading after save
        config = em.get_smtp_config()
        print(f"After save True again: email_enabled = {config['email_enabled']}")
        
        print("\n✅ EmailManager tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ EmailManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_direct():
    """Test database operations directly"""
    print("\n=== TESTING DATABASE DIRECTLY ===")
    
    try:
        from database import safe_db_connection
        
        with safe_db_connection() as conn:
            c = conn.cursor()
            
            # Create table
            c.execute("""
                CREATE TABLE IF NOT EXISTS smtp_config (
                    id INTEGER PRIMARY KEY,
                    email_enabled INTEGER DEFAULT 0
                )
            """)
            
            # Insert test value
            c.execute("DELETE FROM smtp_config WHERE id = 1")  # Clean slate
            c.execute("INSERT INTO smtp_config (id, email_enabled) VALUES (1, 1)")
            conn.commit()
            
            # Read back
            c.execute("SELECT email_enabled FROM smtp_config WHERE id = 1")
            result = c.fetchone()
            print(f"✅ Direct DB test: inserted 1, read back {result['email_enabled']}")
            
            # Update test
            c.execute("UPDATE smtp_config SET email_enabled = 0 WHERE id = 1")
            conn.commit()
            
            # Read back
            c.execute("SELECT email_enabled FROM smtp_config WHERE id = 1")
            result = c.fetchone()
            print(f"✅ Direct DB test: updated to 0, read back {result['email_enabled']}")
            
        print("✅ Database direct tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 EMAIL SETTINGS PERSISTENCE TEST")
    print("=" * 50)
    
    # Test database directly
    db_success = test_database_direct()
    
    # Test email manager
    em_success = test_email_manager()
    
    print("\n" + "=" * 50)
    if db_success and em_success:
        print("🎉 ALL TESTS PASSED!")
        print("The email settings persistence should be working.")
        print("If the UI still doesn't save, the issue is in the UI layer.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("There's an issue with the database or email manager.")
