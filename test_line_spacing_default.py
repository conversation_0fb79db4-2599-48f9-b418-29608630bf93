#!/usr/bin/env python3
"""
Test that default line spacing is changed to 0 in receipt settings
"""

import sys
import os
from pathlib import Path

def test_receipt_generator_defaults():
    """Test that receipt generator has default line spacing of 0"""
    
    print("Testing Receipt Generator Defaults")
    print("=" * 35)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_updated = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for default line spacing 0 in get_default_settings
            if "'line_spacing': 0" in content and "get_default_settings" in content:
                print(f"   ✅ Default line spacing set to 0 in get_default_settings")
            else:
                print(f"   ❌ Default line spacing not set to 0 in get_default_settings")
                all_updated = False
            
            # Check for fallback defaults changed to 0
            if "line_spacing = result['line_spacing'] if 'line_spacing' in result.keys() else 0" in content:
                print(f"   ✅ Fallback default changed to 0")
            else:
                print(f"   ❌ Fallback default not changed to 0")
                all_updated = False
            
            # Check for exception handling default changed to 0
            if "line_spacing = 0  # Changed default from 8 to 0" in content:
                print(f"   ✅ Exception handling default changed to 0")
            else:
                print(f"   ❌ Exception handling default not changed to 0")
                all_updated = False
            
            # Check for comments indicating the change
            if "Changed from 8 to 0" in content:
                print(f"   ✅ Change documented with comments")
            else:
                print(f"   ❌ Change not documented")
                all_updated = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_updated = False
    
    return all_updated

def test_receipt_settings_defaults():
    """Test that receipt settings has default line spacing of 0"""
    
    print("\nTesting Receipt Settings Defaults")
    print("=" * 35)
    
    files_to_check = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_updated = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for default line spacing 0 in initial settings
            if "'line_spacing': 0" in content and "Initialize settings with defaults" in content:
                print(f"   ✅ Initial settings default changed to 0")
            else:
                print(f"   ❌ Initial settings default not changed to 0")
                all_updated = False
            
            # Check for line spacing variable default changed to 0
            if "tk.IntVar(value=self.settings.get('line_spacing', 0))" in content:
                print(f"   ✅ Line spacing variable default changed to 0")
            else:
                print(f"   ❌ Line spacing variable default not changed to 0")
                all_updated = False
            
            # Check for load settings default changed to 0
            if "self.line_spacing_var.set(self.settings.get('line_spacing', 0))" in content:
                print(f"   ✅ Load settings default changed to 0")
            else:
                print(f"   ❌ Load settings default not changed to 0")
                all_updated = False
            
            # Check for fallback settings default changed to 0
            if "'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing" in content:
                print(f"   ✅ Fallback settings default changed to 0")
            else:
                print(f"   ❌ Fallback settings default not changed to 0")
                all_updated = False
            
            # Check for preview function defaults changed to 0
            if "tk.IntVar(value=0)" in content and "line_spacing_var" in content:
                print(f"   ✅ Preview function default changed to 0")
            else:
                print(f"   ❌ Preview function default not changed to 0")
                all_updated = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_updated = False
    
    return all_updated

def test_no_old_defaults():
    """Test that no old default values (8) remain"""
    
    print("\nTesting No Old Defaults Remain")
    print("=" * 32)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py",
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_clean = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for any remaining line_spacing: 8 (without comments)
            lines = content.split('\n')
            old_defaults_found = []
            
            for i, line in enumerate(lines, 1):
                if "'line_spacing': 8" in line and "Changed from 8 to 0" not in line:
                    old_defaults_found.append(f"Line {i}: {line.strip()}")
                elif "line_spacing = 8" in line and "Changed from 8 to 0" not in line:
                    old_defaults_found.append(f"Line {i}: {line.strip()}")
                elif "value=8" in line and "line_spacing" in line and "Changed from 8 to 0" not in line:
                    old_defaults_found.append(f"Line {i}: {line.strip()}")
            
            if not old_defaults_found:
                print(f"   ✅ No old default values (8) found")
            else:
                print(f"   ❌ Old default values found:")
                for old_default in old_defaults_found:
                    print(f"      {old_default}")
                all_clean = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_clean = False
    
    return all_clean

def test_consistency_across_versions():
    """Test that all versions have consistent changes"""
    
    print("\nTesting Consistency Across Versions")
    print("=" * 37)
    
    # Compare main vs YES versions
    file_pairs = [
        ("receipt_generator.py", "YES/receipt_generator.py"),
        ("receipt_settings.py", "YES/receipt_settings.py")
    ]
    
    all_consistent = True
    
    for main_file, yes_file in file_pairs:
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                main_content = f.read()
            with open(yes_file, 'r', encoding='utf-8') as f:
                yes_content = f.read()
            
            print(f"\n📋 Comparing: {main_file} vs {yes_file}")
            
            # Check for consistent line spacing defaults
            main_has_zero = "'line_spacing': 0" in main_content
            yes_has_zero = "'line_spacing': 0" in yes_content
            
            if main_has_zero and yes_has_zero:
                print(f"   ✅ Both have line_spacing: 0")
            else:
                print(f"   ❌ Inconsistent line_spacing defaults (main: {main_has_zero}, yes: {yes_has_zero})")
                all_consistent = False
            
            # Check for consistent comments
            main_has_comments = "Changed from 8 to 0" in main_content
            yes_has_comments = "Changed from 8 to 0" in yes_content
            
            if main_has_comments and yes_has_comments:
                print(f"   ✅ Both have change documentation")
            else:
                print(f"   ❌ Inconsistent documentation (main: {main_has_comments}, yes: {yes_has_comments})")
                all_consistent = False
                
        except Exception as e:
            print(f"❌ Error comparing files: {e}")
            all_consistent = False
    
    return all_consistent

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/receipt_generator.py",
        "YES_OBFUSCATED/receipt_settings.py"
    ]
    
    all_updated = True
    
    for obf_file in obf_files:
        if Path(obf_file).exists():
            print(f"✅ Found: {obf_file}")
            
            # Check if it's actually obfuscated
            try:
                with open(obf_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
                    all_updated = False
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {obf_file}")
            all_updated = False
    
    return all_updated

def main():
    """Run all line spacing default tests"""
    
    print("📏 LINE SPACING DEFAULT TEST SUITE")
    print("=" * 36)
    
    tests = [
        ("Receipt Generator Defaults", test_receipt_generator_defaults),
        ("Receipt Settings Defaults", test_receipt_settings_defaults),
        ("No Old Defaults Remain", test_no_old_defaults),
        ("Consistency Across Versions", test_consistency_across_versions),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 36)
    print("📊 RESULTS")
    print("=" * 36)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Default line spacing changed to 0 in receipt generator")
        print("✅ Default line spacing changed to 0 in receipt settings")
        print("✅ No old default values (8) remain")
        print("✅ All versions updated consistently")
        print("✅ Obfuscated version updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Line spacing defaults may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📏 Default line spacing successfully changed to 0!")
        print("📄 Receipts will now have tighter line spacing by default")
        print("🎛️ Users can still adjust line spacing in receipt settings")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Line spacing default changes need attention")
    
    exit(0 if success else 1)
