#!/usr/bin/env python3
"""
Test all final fixes: add products window, period filter, and database aggregation
"""

import sys
import os
from pathlib import Path

def test_add_products_window():
    """Test that add products window is bigger"""
    
    print("Testing Add Products Window")
    print("=" * 28)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_bigger = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bigger window size in add_product function
            if 'choice_dialog.geometry("500x300")' in content and 'def add_product(self):' in content:
                print(f"   ✅ Add products window size increased to 500x300")
            else:
                print(f"   ❌ Add products window size not increased")
                all_bigger = False
            
            # Check for bigger padding in add_product
            if "padx=40, pady=40" in content and "def add_product(self):" in content:
                print(f"   ✅ Add products padding increased to 40px")
            else:
                print(f"   ❌ Add products padding not increased")
                all_bigger = False
            
            # Check for bigger font in add_product
            if "font=('Helvetica', 16, 'bold')" in content and "def add_product(self):" in content:
                print(f"   ✅ Add products title font increased to 16pt")
            else:
                print(f"   ❌ Add products title font not increased")
                all_bigger = False
            
            # Check for bigger buttons in add_product
            if "width=25" in content and "def add_product(self):" in content:
                print(f"   ✅ Add products buttons made bigger (width 25)")
            else:
                print(f"   ❌ Add products buttons not made bigger")
                all_bigger = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_bigger = False
    
    return all_bigger

def test_period_filter_fix():
    """Test that period filter shows 'Today' correctly"""
    
    print("\nTesting Period Filter Fix")
    print("=" * 26)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for filter state tracking initialization
            if "self.today_filter_active = False" in content and "self.date_range_filter_active = False" in content:
                print(f"   ✅ Filter state tracking initialized")
            else:
                print(f"   ❌ Filter state tracking not initialized")
                all_fixed = False
            
            # Check for today filter flag setting
            if "self.today_filter_active = True" in content and "def filter_today(self):" in content:
                print(f"   ✅ Today filter sets active flag")
            else:
                print(f"   ❌ Today filter doesn't set active flag")
                all_fixed = False
            
            # Check for date range filter flag setting
            if "self.date_range_filter_active = True" in content and "def filter_date_range(self):" in content:
                print(f"   ✅ Date range filter sets active flag")
            else:
                print(f"   ❌ Date range filter doesn't set active flag")
                all_fixed = False
            
            # Check for improved get_current_filter_info
            if "if getattr(self, 'today_filter_active', False):" in content:
                print(f"   ✅ Filter info checks today_filter_active flag")
            else:
                print(f"   ❌ Filter info doesn't check today_filter_active flag")
                all_fixed = False
            
            # Check for Today formatting
            if 'f"Today ({current_time.strftime(\'%d/%m/%Y\')})"' in content:
                print(f"   ✅ Today filter formats date correctly")
            else:
                print(f"   ❌ Today filter date formatting incorrect")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_database_aggregation():
    """Test that database aggregation is working"""
    
    print("\nTesting Database Aggregation")
    print("=" * 29)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_working = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for database import
            if "from database import get_db_connection" in content:
                print(f"   ✅ Imports database connection")
            else:
                print(f"   ❌ Missing database import")
                all_working = False
            
            # Check for sale_items table query
            if "FROM sale_items" in content and "WHERE sale_id IN" in content:
                print(f"   ✅ Queries sale_items table with sale IDs")
            else:
                print(f"   ❌ Doesn't query sale_items table properly")
                all_working = False
            
            # Check for product aggregation
            if "product_name = item['product_name']" in content:
                print(f"   ✅ Extracts product names from database")
            else:
                print(f"   ❌ Doesn't extract product names from database")
                all_working = False
            
            # Check for quantity aggregation
            if "total_quantity" in content and "quantity_in_sale" in content:
                print(f"   ✅ Aggregates quantities correctly")
            else:
                print(f"   ❌ Quantity aggregation incorrect")
                all_working = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_working = False
    
    return all_working

def test_ui_improvements():
    """Test UI improvements (sliders, windows)"""
    
    print("\nTesting UI Improvements")
    print("=" * 24)
    
    # Test sliders
    slider_files = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    sliders_ok = True
    
    for file_path in slider_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking sliders in: {file_path}")
            
            if "length=400" in content and "# Slider - WIDER" in content:
                print(f"   ✅ Sliders made wider (400px)")
            else:
                print(f"   ❌ Sliders not made wider")
                sliders_ok = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            sliders_ok = False
    
    # Test add categories window
    category_files = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    categories_ok = True
    
    for file_path in category_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking add categories in: {file_path}")
            
            if 'choice_dialog.geometry("500x300")' in content and 'def add_category(self):' in content:
                print(f"   ✅ Add categories window bigger (500x300)")
            else:
                print(f"   ❌ Add categories window not bigger")
                categories_ok = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            categories_ok = False
    
    return sliders_ok and categories_ok

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/sales_history.py",
        "YES_OBFUSCATED/receipt_generator.py",
        "YES_OBFUSCATED/receipt_settings.py"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all final tests"""
    
    print("🔧 FINAL ALL FIXES TEST SUITE")
    print("=" * 31)
    
    tests = [
        ("Add Products Window", test_add_products_window),
        ("Period Filter Fix", test_period_filter_fix),
        ("Database Aggregation", test_database_aggregation),
        ("UI Improvements", test_ui_improvements),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 31)
    print("📊 RESULTS")
    print("=" * 31)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Add products window is bigger and clearer")
        print("✅ Period filter shows 'Today' correctly")
        print("✅ Database aggregation works properly")
        print("✅ UI improvements applied (sliders, windows)")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Final fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All final fixes successfully implemented!")
        print("🖼️ Add products window is bigger with clearer buttons")
        print("📅 Period filter correctly shows 'Today (15/12/2024)'")
        print("🗃️ Product aggregation uses database directly")
        print("🎚️ Sliders are wider for better control")
        print("🔒 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Final fixes need attention")
    
    exit(0 if success else 1)
