# SECURE POS System Requirements
# This is a protected version with compiled bytecode

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (for printing and shortcuts)
pywin32>=300; sys_platform == "win32"

# IMPORTANT: This secure version requires the SAME Python version
# that was used for compilation. If you get import errors,
# ensure you're using the correct Python version.

# Standard library modules (included with Python):
# - tkinter, sqlite3, datetime, os, sys, platform, etc.
