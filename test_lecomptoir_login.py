#!/usr/bin/env python3
"""
Test LeComptoir Remote Management Login
Quick test to verify the system is working
"""

import requests
import json

def test_login():
    """Test login with one of our generated accounts"""
    
    print("🧪 TESTING LECOMPTOIR REMOTE MANAGEMENT")
    print("=" * 50)
    
    # Test credentials (first account from our 5000)
    test_username = "pos_1000"
    test_password = "V2%7y@Yv"
    
    server_url = "http://localhost:5000"
    
    print(f"🔐 Testing login with account: {test_username}")
    print(f"🌐 Server URL: {server_url}")
    
    try:
        # Test login
        response = requests.post(f"{server_url}/api/v1/auth/login",
                               json={
                                   "username": test_username,
                                   "password": test_password
                               },
                               timeout=10)
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ LOGIN SUCCESSFUL!")
            print(f"   Token received: {data.get('token', 'N/A')[:50]}...")
            print(f"   User info: {data.get('user', {})}")
            print(f"   Expires in: {data.get('expires_in', 'N/A')} seconds")
            
            # Test account info endpoint
            token = data.get('token')
            if token:
                print("\n🔍 Testing account info endpoint...")
                info_response = requests.get(f"{server_url}/api/v1/account/info",
                                           headers={'Authorization': f'Bearer {token}'},
                                           timeout=10)
                
                if info_response.status_code == 200:
                    account_info = info_response.json()
                    print("✅ ACCOUNT INFO RETRIEVED!")
                    print(f"   Username: {account_info.get('username')}")
                    print(f"   Status: {account_info.get('account_status')}")
                    print(f"   Client Name: {account_info.get('client_name', 'Not set')}")
                else:
                    print(f"❌ Account info failed: {info_response.status_code}")
            
            return True
            
        else:
            print("❌ LOGIN FAILED!")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION FAILED!")
        print("   Server is not running or not accessible")
        print("   Make sure to run: python central_remote_server.py")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ CONNECTION TIMEOUT!")
        print("   Server is taking too long to respond")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def test_admin_stats():
    """Test admin statistics endpoint"""
    
    print("\n📊 Testing admin statistics...")
    
    server_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{server_url}/api/v1/admin/stats", timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ ADMIN STATS RETRIEVED!")
            print(f"   Total accounts: {stats.get('total_accounts', 'N/A')}")
            print(f"   Available: {stats.get('available', 'N/A')}")
            print(f"   Active: {stats.get('active', 'N/A')}")
            print(f"   Active connections: {stats.get('active_connections', 'N/A')}")
            return True
        else:
            print(f"❌ Admin stats failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin stats error: {e}")
        return False

def main():
    """Main test function"""
    
    print("🏪 LECOMPTOIR REMOTE MANAGEMENT SYSTEM TEST")
    print("=" * 60)
    
    # Test login
    login_success = test_login()
    
    # Test admin stats
    stats_success = test_admin_stats()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY:")
    print(f"   Login Test: {'✅ PASSED' if login_success else '❌ FAILED'}")
    print(f"   Stats Test: {'✅ PASSED' if stats_success else '❌ FAILED'}")
    
    if login_success and stats_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("   LeComptoir Remote Management is working perfectly!")
        print("   Ready for client deployment!")
    else:
        print("\n⚠️  SOME TESTS FAILED!")
        print("   Check server status and try again")
    
    print("\n🌐 ACCESS THE WEBSITE:")
    print("   URL: http://localhost:5000")
    print("   Test Login: pos_1000 / V2%7y@Yv")
    print("   (Or any account from REMOTE_ACCOUNTS_LIST.csv)")

if __name__ == '__main__':
    main()
