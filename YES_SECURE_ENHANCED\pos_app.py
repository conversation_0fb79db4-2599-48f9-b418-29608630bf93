#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "pos_app.py"
PROTECTION_DATE = "2025-06-03T03:26:12.815591"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
