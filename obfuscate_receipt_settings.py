#!/usr/bin/env python3
"""
Obfuscate receipt_settings.py for YES_OBFUSCATED
"""

import base64
import zlib

def obfuscate_file():
    """Obfuscate the receipt_settings.py file"""
    
    # Read the source file
    with open('receipt_settings.py', 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    print(f"Original file size: {len(source_code)} characters")
    
    # Compress and encode
    compressed = zlib.compress(source_code.encode('utf-8'))
    encoded = base64.b64encode(compressed).decode('ascii')
    
    print(f"Compressed size: {len(compressed)} bytes")
    print(f"Encoded size: {len(encoded)} characters")
    print(f"Compression ratio: {len(encoded)/len(source_code):.2f}")
    
    # Create obfuscated file content
    obfuscated_content = f'''# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """{encoded}"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {{e}}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
'''
    
    # Write obfuscated file (force overwrite)
    import os
    if os.path.exists('YES_OBFUSCATED/receipt_settings.py'):
        os.remove('YES_OBFUSCATED/receipt_settings.py')

    with open('YES_OBFUSCATED/receipt_settings.py', 'w', encoding='utf-8') as f:
        f.write(obfuscated_content)
    
    print("✅ Obfuscated receipt_settings.py created successfully!")
    print(f"📁 Saved to: YES_OBFUSCATED/receipt_settings.py")
    
    # Verify the obfuscation worked
    print("\n🔍 Verification:")
    with open('YES_OBFUSCATED/receipt_settings.py', 'r', encoding='utf-8') as f:
        obfuscated_file = f.read()
    
    print(f"✅ Obfuscated file size: {len(obfuscated_file)} characters")
    
    if "_PROTECTED_CODE" in obfuscated_file and "base64" in obfuscated_file:
        print("✅ Obfuscation pattern verified")
    else:
        print("❌ Obfuscation pattern failed")
    
    return True

if __name__ == "__main__":
    obfuscate_file()
