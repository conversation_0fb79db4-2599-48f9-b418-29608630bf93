"""
Create Desktop Shortcut for POS System
Creates a desktop shortcut with logo.png as icon if none exists
"""

import os
import sys
import shutil
from pathlib import Path

def create_desktop_shortcut():
    """Create desktop shortcut for POS System"""
    try:
        # Get desktop path
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            # Try alternative desktop locations
            desktop = Path.home() / "OneDrive" / "Desktop"
            if not desktop.exists():
                desktop = Path.home() / "Escritorio"  # Spanish
                if not desktop.exists():
                    print("❌ Could not find Desktop folder")
                    return False

        # Shortcut file path
        shortcut_path = desktop / "Le Comptoir.lnk"

        # Check if shortcut already exists
        if shortcut_path.exists():
            print("✓ Desktop shortcut already exists")
            return True

        # Get current directory and main.py path
        current_dir = Path.cwd()
        main_py_path = current_dir / "main.py"

        # Use logo.png for shortcut icon
        logo_path = current_dir / "assets" / "logo.png"

        if not main_py_path.exists():
            print("❌ main.py not found in current directory")
            return False

        if not logo_path.exists():
            print("⚠ logo.png not found, shortcut will use default icon")
            logo_path = None

        # Create shortcut using Windows COM interface
        try:
            import win32com.client

            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(str(shortcut_path))

            # Set shortcut properties
            shortcut.Targetpath = sys.executable  # Python executable
            shortcut.Arguments = f'"{main_py_path}"'  # main.py as argument
            shortcut.WorkingDirectory = str(current_dir)
            shortcut.Description = "Le Comptoir - Point of Sale Application"

            # Set icon if logo.png exists
            if logo_path:
                # Convert PNG to ICO for Windows shortcut
                try:
                    from PIL import Image
                    ico_path = current_dir / "assets" / "logo.ico"

                    # Create ICO file if it doesn't exist
                    if not ico_path.exists():
                        img = Image.open(logo_path)
                        img.save(ico_path, format='ICO', sizes=[(32, 32), (48, 48), (64, 64)])
                        print("✓ Created logo.ico from logo.png")

                    shortcut.IconLocation = str(ico_path)
                    print("✓ Set custom icon for shortcut")

                except ImportError:
                    print("⚠ PIL not available, using default icon")
                except Exception as e:
                    print(f"⚠ Could not set custom icon: {e}")

            # Save the shortcut
            shortcut.save()
            print(f"✅ Desktop shortcut created: {shortcut_path}")
            return True

        except ImportError:
            print("❌ pywin32 not available, cannot create Windows shortcut")
            return create_batch_shortcut(desktop, current_dir, main_py_path)

    except Exception as e:
        print(f"❌ Error creating desktop shortcut: {e}")
        return False

def create_batch_shortcut(desktop, current_dir, main_py_path):
    """Create a batch file shortcut as fallback"""
    try:
        batch_path = desktop / "Le Comptoir.bat"

        if batch_path.exists():
            print("✓ Batch shortcut already exists")
            return True

        # Create batch file content
        batch_content = f'''@echo off
cd /d "{current_dir}"
python "{main_py_path}"
pause
'''

        # Write batch file
        with open(batch_path, 'w') as f:
            f.write(batch_content)

        print(f"✅ Batch shortcut created: {batch_path}")
        return True

    except Exception as e:
        print(f"❌ Error creating batch shortcut: {e}")
        return False

def main():
    """Main function"""
    print("Creating desktop shortcut for Le Comptoir...")

    # Check if we're on Windows
    if os.name != 'nt':
        print("❌ Desktop shortcut creation is only supported on Windows")
        return

    # Create the shortcut
    success = create_desktop_shortcut()

    if success:
        print("\n🎉 Desktop shortcut setup complete!")
        print("\nYou can now:")
        print("• Double-click 'Le Comptoir' shortcut to launch the application")
        print("• The shortcut uses logo.png as its icon")
        print("• The shortcut will open in the correct directory")
    else:
        print("\n❌ Failed to create desktop shortcut")
        print("You can still run Le Comptoir by:")
        print("• Opening command prompt in this folder")
        print("• Running: python main.py")

if __name__ == "__main__":
    main()
