# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['OTl0GxAJDm54', 'NHEKaiE2YQgQdAMOIBcFeRN6c2QzGSQqOG4OGhE8PHAoPyBseFZIAhgCKzAbJQgjDi1mIy0nNgweNyEbNR0MNw0ZNzltMjM1cQs3', 'GWNaUDQiIw4reTYlNwEwHC0gBnUpBiN4AAxqFDpnIRUeDicCBn0ZCXwNHgx9QFUfDwx6BxsvLBAgCGYYARspImp0HjgvJjACFyMU', 'IBoyNmo9ZFQCfWYyBC4rKzEWcBADMy8Ucg4PbXAHJX8IJTA0JjYEAXFgMxweBgksITkKQhl8DBwmYB43Z2YsGRk2PRkwKW0oPw05', 'AG4bBAhwB2Utfwc3LT0BGHYNCh8eJSIDNiwzBDkqAGkqLzw6WBtqYDozOTowCGwwdw1jKnA8NSsUFAB4e3M/HRlpCzkNCScOfD4K', 'Ix8dPiZsGwswJWEwNyFxKQYAe3cmIjAOLQtxUx8FDH86axUOCXUNAH0mCCQ2MWYucCo5JwgMZignMRwHYDAqODICCDs7SENTbh1s', 'Ag4dNiIxPRIEfigENjY/PzsZAnsAfT4bODcVNQ8wPTwHKygxIC46XFUdfwlhBDgnFiYECTI2CjB6cDBhKSB5JTxxZ24RPiIfaiQm', 'BAd7JyQGBCoRKXRoRnYxZhgjHjQ+JjZ3ZGEyIhZxGhMtDncPEzoPaAc1EDMsFgd+KS4pfSANQ0VEWDFhBBM1AD4gFxs8Kgd4cTYU', 'fAY0Zl8Hc2EoETo+PykYPitoAmAvCykjCRQuKwECKjE1Njw7OAYXByN5ZQ8mJgAGS2BdQD4UdBg0FXAhdhk+YRA6Fz1pG3A0EHgq', 'Y3cnNGkWGR8mImQfIhUYJHclKxd7OmA5FSFzGCdiBXEFLBgjIwAmPQZ9CA1haiARImkdDBsJImo9MDYjIGEqKx5iNyklDCwsGEEf', 'bC0bLyl5AHlsKD4hYHMVZyMoCyUvMj0yVFdDTGYiCDMROTgjcHAifRMlOBQXZjhycTsxam5gHCwSMHUXDi1/Mz4BEQ4CG3ZZMiYG', 'FxojezYvPC03HBl4C2YTPQgdHW4JYHUUMwR9Fg0SAGwKEykEPAQ9fz94NywzOQ0fKj0/FGB0aGJ3djUPHiQrcRdnICwAeXMcL3UV', 'FjNxASYzC31kKiUwHQotcAMzOxh9cXVmGiQ9ImczLBtzLDNnNjgDKSkrIAN3PzQaLwg0fwAnNyYHAgAPGw08KF5CfXEbIxh7fTNw', 'ADogCi5iLDtidxE/PQAcJzAzDwAxJQVxNA8pNh87MCdtInYMFCFyLiwZU2sGEQMqPhY0OXg3Lix9DCUMaRo7ZyUCPHMhZwA/Hx0z', 'cHcxMHt+GnMuMylfZwBAbGQpHwEqGmAkDTcTEhwjaBsFeQ0NBiEgNn8GHxYOOTYjCR9vchEvEFNUBENqBCQmCzkdBCQvHSIGHytr', 'D2UgEhcPDD0Mdiw1HXl1Aj8YBnJwOTQZGCQgPmYQKmEefjsrJyA6MVhiZF4pMAsgNyAaJwANZyE8AglsIx0gJy85digxYyN9LiAR', 'GSp1bmlxfGt+JQ97O2E7aT4IEyUzIClwEissfBYMBwgHamUoAT4cMGR/IyQIKTFgbmh+XlY6ImctPyoIKSMkM2svIicQOGt8GHol', 'EiQXMQpyYBV/AxptISQCLWAHaTIWEy9mW1wbOSYqGCQmaWFyB2YkNwsxMRYpBwJ3GgQmMyUxFzggKzt9C3olHSEUZ3ACeQMePX5t', 'dRt2GAJ3L30PHAlnMC4fHTI6MB0/bj4IDmhFV3FVbTB/GjEOGCcvK3o5djUTbwMnMiwUIy4LECF9Bw42KCwedio+HTwPMQJWBFp0', 'Ly49AhxjPQwxMDcdfAQrLCRwPBlcWXcMGhsEEz02JykiJyAbDxwoFyw4BTMtCR9kGzsQfCUNKxkeDGVwOw8NbUJJV1AJNg5kOxU8', 'byoEWlxFNCUoYyYUCDsKJzIaNScgcTxuHwArCg9rGQAwCnsdOSM/dndoBxIYZgNEQV8RBQc+K3lnMg8gITwiNXceLTA8MXYSCzcU', 'NT05bQZ6OBguIW0yDD8ucXxiRWccLiV9MgZlbhE/an0lAgohcC0ULTI2Zzk2NQ06B3BjMHZ7O2QcGGZKeGFWKBQCM2NzBgU0GmEk', 'GhsxNg4dSXZcZj8qBCQOKwoKIRI4cGdwKG5vfSUzD3Y8CRJrfS0mEgxwIComfzItM19qc3spJwUfYDEnMTIKJCgnPyBjCxIydAUM', 'HXklLTQ8BDg7Ly1uCQotIXkFKhEoNXtlfBAoKQYqf3clGW47BnRqQVkSYRYuB3AFEhcbMjknBGoLF2c/FhIhcWtvJiA/OjwUbCQf', 'LxY/anNyPSwFfkRtDSN7GTECZz4DJCwlNykLaQEPIAUxHQ0XFDk+P2QpDiI6ATcQf3YWEAdCSEBnN2cjBBkxIi8TNhxwLwQCFhkm', 'Dx0wHT59cWRgOA88HTcGGRwmFRs2ETsjGR0UO2oBAwAVdGcBLBkxdwwBAD8sfnwDLRlSc2w5MhwxFil0CyMVFgRzOzcqMmseagJk', 'HSI1ED8rejQUCzFoKz16IHsHLGUtJyA9HWotZAgcdjYqH2wPcEVicRUwfhIIdW8JNDYvEw0EFG4KcGNxdCEtFzR7ITkCKzN7Dwk7', 'Q1BmMH8fGQkHJjM7Pjo/fhc4Omh/IDVgDWQRPhE1IQQhYiEqfw4qMBAdfUl3eDk0JABgcWkRB3Z+K3Ekdz1qCi4bEnspNmgYIh8T', 'Zwp3Fi0WEQ8GGzdsfC0hICF8ZygTbBNCCAABaAU8YjQ3bjsRLgczdX4FbAMtOmoAciE+FHscKDtzOyQCIRs2HXQwLWt6eGQ3LDsQ', 'XwYEaR96fwgoEhQgNDd5dDkUFW5wEgktc2ofBjIlOyciEB0/FyYpPQQbaUQFQR8TOAUxawUlJxIhNGAEAi9sMRgtagEMBDALI2sY', 'EW4wHWYGegliFSkiOxUzMGM0fxhkMywHOng4Nz0iCw0He3RcbhEvDjEfJg5kFA07AQ86dTwJNnEjLGAwAi9lCiNsAhVmPiU5KXg0', 'J3NvHhgbMDQgDTURZCJgFR11bS4NCgYcaj5qVEoFBB56YxsLcmk1AxpmZAwjeDRtFBxybDp3AmZoKBUwfWghAXgIBwYkdiVKVh0M', 'JHYRIggqKTg2PHsIKxMdGjV7MSceIhEOfAcKFGQVNgwDQ1dBCTMqLBMRJyV2ARhlKyUbYxExeXIxATxkaAYiGTY1dBYDKndmChUs', 'ZnxxETcjdC8eMxh/CXICLWsbNSJ5IHJ7YjF3eTEecjI8YH8FDS8lNABgKh0qPzUSHWo8FzAvDy4OGyg/GxFoHSsffBB7KGAWFyBu', 'AXwACylwYDABEiUBAwcpaGwkECd8MjgmCREXJTInLhoAGnAJKxsKaF5+fWcbLzUGL2wYIQRtJRB+LHE+bB05cQdqOjI2G3tjNgca', 'IwsXGCYCOQx9dWI5fAM5PBYXDVECcGAIMnsSPzQaMXMqfgd0H243IXQ4JgI8dARqGRN7FW53AjAfNwYGfBAlBHx1fxdiJTI2BTEn', 'fz57Oi0LDQxiRl9RazQWZxM7JTQCGzIodhYsOBsHMiYUMQtrLRgHAmB1ewIkISEOKSEuKUACZVosLxYueXVrYXwbYxs2AhkcCWtw', 'CzEFexQnBiEHFQdhETsDbTcPETQ5HgErG2kaNyEzcS0wHjklMg80DQBZZkN0BR8aIzExGCEOfgU8IzAKKit+KXYyKCRtJBoDHRwb', 'O215dWt5PDY7ez0CdCsjBDg7CX8uIzMQLwolCWoVawkYeGccDGcmez8weXceFV1bAVApLSYWIDk2JjIlPzV3AA42FikgIiZ9Bwoa', 'JRp4ASAXJSZoNkR/RXo+IQsyCDkVZTd6ABkyNngxE2d7JxBkCDE4GCUoLhYNGi83CSsuNTttQgF8dy56GQY1M2sVCncZBAx0Ly5v', 'eT4BOhoDEyMfai0pKjwYKzM+JWAoG312LAtjdHt2OmYGBnl0bxduMQIICjVzCXY7BgRwPAtrHQUdAi4IE396HRcYLyosMUBDWWBw', 'BX0gZDZjJx4adDcVCAgkMAw/Oi8EfwttLG0PNxwxGXgsdAAAIGc7GQMQDiwIKCoWEyUHHzMvdWB9NXlrMQoVbHFecUYlGw4NP3YW', 'GyUHJjd8bCcwJXgsGzYadEBhZUdtB39/BxEVFB0kAmIPHCoZLDU/cHsxPxMeY2NgZQkaZ3kJGTYII205WFpKbnQ+PyMxcG8AITMb', 'QHlUYBY7IQcTFGkfEwEFHSMFGC5uaHl1dHotCmhnFT1lLCQ5ewV2OjpxEzhRCWp/cD17ERwOHh5qEDM1PxsjcQ0xGDIMcjEYBjVl', 'EQwdBCcQIwoCPCd0YwwnFjR7MzAXRmFmYzEtCyQlEygyfSZtNnR9dAIrEydzDB0SAhoKNHxkfDEtIGQEMgkjDxFqA3tANT5jOTYG', 'CgQxdwkyMwl/Pnw2a2wKB3VOHhN9OTozCzEtezAUdQhqdQAnPBU1LhBkCCAmOWIKERkxOh5pOjMgPlFWQAMIAXhhHBIHOAApOBcS', 'OhIocjkqIAwlHAYTFGUGPgsTOSUHB3QNJiwzc1MLRQY3ZwY5FW47CRBmJhYVDQwMdCkSBi4WAmc/Ez8iK3oDeHw4DA0NKglASWVh', 'LzYiICk7ASJzJxsgeyksaCsaNiUJMCcGHCcbPmoUNXsuCggTIS5wfXNBXAUhOTAhBWlqNDUsfTcpOXU9dABxMy0oZwYmfT8SMyZt', 'FjZ8H2ZQBTM8HD50EzR8GxxiESESGDNwGQkSKQMaLQYbJAIiG205PX8ycitoLXpYdgAyfgMkC3hpNjd7YRo3dBMvFDk/LCEbBn0R', 'BHQoFjwdGiYEfD8FagZHcTAxNRxjam0lciUUBwoUMCMTKGMoOygoZTIpBCEgNRVlfCc4ZgQTLAdXeFdBJ2ACPDNzZzgKIi9idDgs', 'IBF+CTJ4LxceUFd5BGYPAAA8Ag8nAQQDeTR6OD5uOnwOOyI8FilkeQBlfDosETg8Zh89KjVAf1BnbQAnZTQFOjARNwdrJj4TKS0t', 'ZHRkFAYrFz4oMQMxdx87PTY3Z0NgSFcpfnpkBHJvKQAUGDcPCgYXLWhxchQ9FBsrP2B5BnwUDQFgAxovdx4Ze0UBRWYeGGYaMjUH', 'HHQeYwAaBHQQfB8dMTEwNm0dfXpCdGItEnkjMhszcWU8ByUgMBsGLAUGAGoLEjJgCRcLDxw+J2EWc3YsD1pJRQEzFyoVHXAFBQA1', 'AjMzA38XeC8+fBJgOSccdCwKcC45IjEjNTQHYAwxLXsWPS1+HnhyCBAAYAABdA0pHCIQLjVyCSY6D3twCgtmKTAwKm5qGyQ/KQAA', 'eBkECzgmKTwXDDczKwZeXFYrZxsHJygleD9xPzQyGScAFDUyAno6fAAQEWtgHAwMIAoVPSovIBtvYVV5fwsXOjsIGHAyfTMnZH0q', 'HB0QaT5ocHAsI2pmE2A6fA0wFAYzNz4sBm4uOEhEZlFrPwEhE2o7NRQObBt9JHZvdhgrNg0vdgB0BzsqAHIpLDF4Fxp6PGk9ZQJ4', 'NylwGxlyMjkFARkraAdqMBp9Jzp3KAkpPzB2EhIBBSUieAMsF2QBYHgscyY2L39IWVA1ej44ERluNiInLGUnHRIALTMHbhssIzcF', 'eRExJX1lLgJrAyJqDT4KOSopGjw4aH5kRGcrZnUifSkYIA9sejQge3JsPB4iDHRyLWFoFxocJ3QgHS94HAYTJCE7R2lnAj0cCmYL', 'KSc6BxolBwIPHD1/ZiYRNS0ZKix+KhErQR9iDBI2OjszMBcVFmwTa30aOzFqZwAvDnsSBxV7ZjcsFnptOiE3MyYGIy5nSlBSZzMu', 'ND5jCQp/OwYfbCkqYG1ARVkMPWEKeyUVOzt2ORBnNiM4YmswYycVEwATCDclNhkmdHsPARsXeQsNCAdzaHcpZzwzaxkeN2obJyIg', 'LCdgLAIDcQQHeT0wfyM2NloId0d0Fz83OzYLOTARNioReTENEhM6BBkPKjcZNAYkDDcOITk3GRkMK24YekVlRRACHzI8AD0yAQUd', 'IzAbEhVqCXtwBRN9G2dnBy0nLHBgDiQPNHITdhlwYmQEHG0OYjARJyYVcgEACC0VYy4tETBsLD9iNyUYKyMzKG0gKRglGz8dG2J9', 'GmYXOWI8FwZrJgsVLDU1JDgXKBQcEyILICwiOz4INXcQB3AAJVxnZVIIAykeGA4vKh8uMCEKfgkVLjF6MTVkJBQsM2QobXwkIxkM', 'HGsCNQwqPHUSFjI1JQ0lAm5kHTgYBzMLNCAECCovJTZqPkdWBmQ1bSsuZTUSHiY6ACR3KRkgb2kDOzQFdjcoZSo1BC8MOwcmNBIf', 'AgI1eGsSfQw2BzYKLi5rNDoLbHIjFxICIiwfMyA4MCV3LC4MFCtCSmoGMQcBGwQWLR0WDyUkLDYgGDM3AXBoficHBXtmGmcUD2Ur', 'JBsPLDQjGGgAAwUwYysOOy0ZAzZoej4WeHUUCCUhMwccCAExFTQGNzYiIgd/LBV/LT5wBXcdVjwUeGwIOxIbbhdsB3MlNiA2JQcD', 'NRduJBoEPAQkKBUOGDE3GQMnB2AoHy41cxcOPXYcLgoqHFNUVUAlPzYcPhcxBWouHmodewATdiY8bgsuKWM+F2Q3Mj0rOyo/DXQd', 'ADYePyYyCXMgOxY8MGRwNwl+HwMCLA97ABgUD2UOEycjcyEFPDALKyUsOHYmJSQwLjkLDDAEfx8RBnAoGwp7VlAPOi1sHiBtEW5z', 'FBY9KHAnLS4QZA4VMwwibnJnEB56OR1yNT1LalR5DBl6YiUNbRl1BSMfBmMjIx1wHCYFfnAkZxNlBTgiLWAMeA9sEgYuaGJWSlks', 'fTsoFD4cbDELGGY/eBcDDzsycFhLeAVgfTYVDAkVEDEUKwgfdRY8DQ54DjMuZTgXECQedzZiMT92ECM0FhRaRHptJ2AnGzxwNDk/', 'JCctLXQzLyYOJwE+KBRwNBYIMzN7GiIoHjkldHIvBAZqQTMzPR82MTh8MjY7BiR5NjIragk7DgEyYxomYX0fCyA+G3kndDwCGy0H', 'BGMjPAEmBhUwMgoxa2UidwIgKAF/FRYpCRVoOVxxUGAcL3U7EDc1Zip2HDcJYw8qagw+ODJ8DAQPIz0cIB80LGZ2fGx5IWsHfXdX', 'HxUtdQVxLB9vYBEsC3Z4e3A6BwtnFRkPKjEMNycsHix1CjYmKXoiCyQTKiY3IQIMAngdd20vNypwXHl+dgwHAm0qBQ1hMABsJScH', 'dj8+fBwgPW5oOxkXPAk6Ezk8BDBuJmEQAzRuIDADE0R8Sgw+EhYuBhcyYWo2DxUzCnIjEGgLMzkbfAg9N2E2M257ey8aJQomFW0c', 'N1BXenwsHykYAgwxJwJyHxcJZzkUbjciLTYpFmUlKTgcJXErFXElCToHfD5wU0FgZj1sGiUIKDImJztkY3Y0CTxtKjE2Lx8DJ28E', 'eBstBGUPIXgpeSoOOnN0UW5vYxYGfTMqIy8bFDQKGSNvahs6GwcaDQISFCcbJhMLZiUbFCktcm8URmJUVxQ7PGUmNzY3fTEQBm57', 'LjN6FylzMi10GQcGQG5meQECJBApITd6FCwfM28uJQAsEmA/GmcIORcDdgo/****************************************', 'BmU2eBkqcgcNGwwkNjo7MwZkGQchYB4lOW44NjNsaFNXdyUyBn8/ai8/HCJsAgl/JhByF30iBx0xASU3GXcnLXd/OX0NOhkoA2pb', 'FDd/Ey0vamMOdBEAKyoBYGQbKCcgLCMaGjkzHSh2RWtNJw88GGUCJQMLBSAwLQoRLjUpMg0ALgQrZxwoHBoIdRtwLBwYJQQ7LmRC', 'Lio/HQ8weS9+MzwSBRE7DBE1agsUY2RgHXsyeAd2MBpzO2lDB2psGjMlEzktaSMgIBsVKHoXMzcGDxYNMi8wEToldzl3bBh+K3YK', 'JjlgNhYQIBszBhYoahgRFD1qWlZybBEDFiYHL3gPCx8DfAoLYxMoOXB0HAwRKyAIGSYVAjEqenlsByEIZ1B/cx8mNxU2JC8Sayc0', 'PBg7fQ0QO1t0HWQXOARmIzkzawMTOnkOO3kUKDw/Jg8DAislJzAuOSExAR43NztkHSk9ZEFaTB0aHQQrOWhmLDkPESg4MTg1BTIj', 'KAQ3OhEBIxE1ICYifR4TVWRqfQUSFBUIFgUiDQ1nF3MjEBwgDg4vcCoHa2seOHcEai8/Ezl3CgJqHWlQcR0NPDgpMCMAPRgOKQEX', 'ChkEeCQ/KBMbaTUHOAwMPD0bHCcOcjc7eFNIQwwNJSIbdhMRcDE9fXE5LR92PnsDc30zIgsFGXsQDWw+On0+bDF1LwhddXBuFSID', 'Kz4QDjY2Ay4sFkRnQAwSMA4OYigOFXdxIScLCwcZGGdjcHAJPBBsIzsfBw43MzF5OR0hfGsvUXl4H2w3NDZhJmpkcwIfPiQhKS8J', 'dBsDNgtmOGUjIDwTIxAVJDoDMwlpHVV8UgcCCGAjFQdjMRMvOCM6eHEwbH5qOxoWIi0nMDg/KnIDOikrHnocIwlAQUdaEzo2YTgx', 'GBBkFStpS2EBXzRkHgwYNRQ6DxYxOg08Nxc4MxAxAQUzazk5PnkhMywwfSodZn8xGmp9V2ptDmY/bWIAKgUnOwEdDgQoayBsfhIP', 'KiwgEX0+KxFhCQRqGzwPHycqEyluOUtnZGwFJDkgYStrYRZ0PTwKJgIzAxh9D2wzMmEZEjkFMTB0FXgHfg4GcR9mAQdnAwV6HTBl', 'KydjJSAVNTJwDxkoEgIZDionPAsxA2onZgUBOyURMR4+LiNsCB8zdGhUC0ElIz9mJCsHNS8iGx93IHhuMjYxdhQCIwt0F2o6JDYa', 'cDQ2ISM9KX8dLBpmCXgEbX4INmM3KjYGcy8VITQ4Aw07DyU7PS4+Hmc6NRZyegFxJ31qJHMgbh1GR0QpGwMsYxIbFSMuehx3NBg/', 'Jykdb31yGG14H0MNHC90H2N2GiYfJhY3dyQ7CHITJWoHOW4FawACYDUjCzAlKQMHPm4yLUcDXXxuACg+Jis2GjMhISAfODsfdmw4', 'Nw10HQRCWBI5OHsrDjA/HSI7YnU4cGlucCIJOx4NMxQbCi4MI3FjCjkkCjogdmpXex0fM3ovPD4wHDwzdgU/DmciGWwcJCpyeTYG', 'Jm4DMj4yKyF9IA1mG3suNAkxJw0sb3IMHw9IRVZWFWQVHSd2LzIxOw84dHQzDT4xPyUoBw8xbhIFfAYdLjI/LAVvOg5oZmMJRAcu', 'EHphGSMNHQUkNzB2YF97ODYpLRx1JQYOBn48Mz0qajA+IxQiOXJkCQY0FWIRNzd5dg8SMnc+NXRee0MmMy8VCHYaHyMTBDhzenI5', 'DiYDAX8LJRsyEgcPDH4HB0ErPxwVGAMIZickbBcMJik2Cmh8GzESC2FrNzwbITcsE3AAJQw9LR4yBlVwXCkEfWEaMQ8YEiQwfQAW', 'YBIObUpyBFM5Egg2Fm4oKRICIzsBNHIeMw84BxMRKSY7aSs6NwclFw17GSUTFS84e31zAzYHehc2GDI7LBtlBSMEIzkdDmMwISYn', 'NgQOKyBvajMYJXcHCTc2ZTsqAmohJzMdfTMxIzZwWgICTCliCCI7ORIADCcCHQYICh9hG2cwDzx1Bi8UCGRmAjJscBssbh0AYAVC', 'EisGY3kTAxE3EQYmFi4dBG0ceHp0LyE0D1peZVgSOX9neTE1PQkvYQcjewARDSYZBA0HdCUzACAnA3Z0EQwpIQd4HR9oaHdEQh5s', 'VwsBLiYkIxg7FGoOKWQ7di95E21uegoHOCsTHDFqPBsuMmF8LQVtDXIXO1gHeVxtYDtjICk3ZCAbYgoVG241PRUqAwsFPGUWAxoo', 'Iw9gc30yBSc1Zw8iBC8LAWoZcTYzAQF3exsFGBUDeBg9PzliNn0DDRYRbwV1FS8IFhgyPR8iCihiEHh6KHkCPTV9XGZ7BzAAIGR4', 'FEtZXgU9AhQbZDEsIDMFATkiBhkDKDkLFiVzLjA5G2F7DTByNRN2NBp7Fh4+VmF2VyVndQUAeS4aAw0yA3wveSkREDF4ewh8NigI', 'H2V2G2cLMRURPh8cFy1rIj9uaRMlcDg0BwJ9KgQgF38DDCMhdycGLW4jPBlXAXNnIGMVCxAQZi10LRoyIDkRP256ADouAQYHPAYa', 'ASg9awVqR3wxMxg4J3U7NSELHyQzKhhqPw5/OxIvbmQUOSU5NiExZiJ8ABwCfSxuAUh+exliKBIxJDYRKwwmJR01ETYdJhkXL30K', 'PwAlY3MMPgweGCgreTIWejgBAxkTGHQ/CENgdAIvNnQ9C24vNXx1Gx4/ORVoG2h6CTEGcSNqfxw/GwsLNjgMBiYKEWw4BUhnRm4x', 'dwUqHTp2Pzd1PhkqJREnMwgpRHULbi8nfXs6CToYIDo/NSc/AApqOgkbFQM1ayxnEHwFcCEmLyJ7JwopbRp/amJ6C2AdIWUNJXwV', 'fwYPLRxuKwt7fAcXfhwZASw3HS9zByUrAXlichY7agYlKyYKNRk4YRYuEi89eWkRcjIdZV55eCs4OBsLNw41JBc0MAMhKCwJGyoK', 'HwUuPnAybGdnAXdwBHUaGixtHnQwESA/CCU9LTUjM3AkIxNoBjB8NQYxPQQiNAcvKz0weht4B2gkeiZqLQgcEwZjKnR9bhs7LjJw', 'YSd6BCNrUFsEWAhjAAMcIh4lbiowayA7E28AHhxyLgUiJBZnJXc3IHU+fwc2DD18bCp/QlxDJ2R7JmcHOQkXOiF5K3sAPGwxJCp0', 'IwFkKAMMCSY/IzVZakhXDiE7ICpqbmUpLR4GFCkVNjQIEBkTOAxnNghrHCx1IWcKISARLAkQO0VqZmdrPwMjYTM0ABUSNGMmABE7', 'SEUZZjkVO3U+IzYLJjZ8PSJxbA0SCDYxFRwsGzt6GwkqGSUHDC4JFC4JBWh/XhcUBD0WGCkYIXZ+NCMbbhEXPhBqNHp3JycgNHgn', 'Mic7eRklJ20QQlRrBWZjNCNgA2tibgliCgEYIyI/dDkOExNxYTgVPCYgfHECJ2AsEBtyaScACB1aPjM8P2ERNmF2LnpicjV1OBMp', 'CwBhWCwjBDgqMz4iDXJlJiMpGDhpN2cVDAwrYTQGImAmBitiPCkYZh8vHTMHBAV/Kw8KImE0bAcKKjglFzgrL2ppEncvPXAKJycE', 'Pn0teiomLHYya3EEeRM+G3s5eTo6NXoxB3E8CCwSBnA4DwQNBQcqYH41FHEZfT86Khx9KgcCVnZ4Cy08GDgZDhA8LjFhDRw4LGwR', 'I2gFa3wtJANgByECD3kiND4zZHhnEAVsPzUPDwwQL2piVlMfESACbWswcAAIOQ8/fAk0YxpqZzsNL2pnBRoGYGMsJjwlego0MiEx', 'PXFmcDUHdx48JjAwPSc0ARQRdHV3exsyGDw6IjFmFiMpEQcqenETbgYwETAkKmI3YQcHOSA2PWIIBjobfW50U3cFRA4FJyI9BToR', 'fz0pKAd1JgwifCkGKy8cIm4LDGoFCn8UNxIjJRotOhwUPH0OFGFzal1tLwBtYDY9Nyx0bB51DRUQOBFnDXUbJAUIOSQHJnBsAh45', 'O2RmdW4xdDU4OC86Jj48Oyd0dBpyGhh/HWAfHSs9G3kYanN1LhwEckZfHDMOMisMbDF8FgQINw4lKisvEQkzAy4QOhsVOhsgLBod', 'JAs1aBc/MXc7EDwWGG46L24GX1ZeBHRsYwQGBTI7NSYcOhcILW8+JQB5DA1zGWwYJy5iFXNtBXgMGBhqGiphdQUHPjZ5DjsYaAt8', 'Bj1iDXoTFjYRDwBzKnUGEBoZfTgSGQQvLnk8fic7K0QfdgIMDRstN3UmAXc1IDZ1DzsLMDoDNzAqfDsZPgALHyEoHnx4GzY9J24y', 'HTEUOSkrHC4xIjURAi0BMSA4FwYiCW9LQ0JaPTEZMGskbRAVBH5nIi40LigcBRN7BQ0XNRF5ABI3F3sqKT8TfSd2DQNhdW0Hfn8k', 'Y38CfSQcJSd2Gz0jCydIBWMHNC0VECUpJzsLNGAqNnsFPzIQLy51HmoDD2EWfj4hMmAGKDcPeHw8JlEDRmJqZj8fJyoYHXASFmss', 'cWFgDSQbcXJnHHQpOStqKBo/HjYWDn8/eTtwcg0BD1B3enk5BHxnHRIQECJ7GWIHOjUCHAs6FRoIPB0SGDMMNz0zPjkjAAk+ahhv', 'DD8PGmYBQAUybDQOfQpwNiQhBWd1FHUUdik4OBUEHDYRJGMHN3U5NiAlJBUEBi8rQHx+Q2thYzI+J3QlIwQCNwd/JWwfCBw0DQc3', 'P3ERbBx8JG4+PxkSdwFkFAB0JTgOf3AJfzs7DxZ7Aw5qVUZdTnAzBxc8CGdmFzM2GncBJTIyM3sFCH0maywyCC1/cQgQLA1hKzkw', 'cQ4jeSwoNXwdETs1MAUFdAc0LxJgVXsABmMDHhwwMAsMJQA6PRR4dSlnJjYnexArMiFnFwUvNTl4NSQ7Eis1OV9TBmN0OBlsOzk9', 'ZwMyY3ksCjx+eDAlNB8KZz0XZQEbNQsfdyVgbjUYBB9FTm8hFWJrFAYDFSAGKHwUJTQjbx0nGj8IZygkPjkyDQgMGys9JQR0Midi', 'fWZ1WxseZxs4cjJqIjEcPD8EDmIBLAJqMxN3EDM0ASkhPQkmBh97CX59NSVjZERdMTQIImA0bj8dOTMxMmc2Pm0aEDt1exQ0Lh4k', 'GWc+B3Y7ZzJ/FSkoPB14aGQ9OG5hGjk4MxseOno+aX8RajJERXAEDCIYOXkmM2QBNgc3I3g5Dmg9IiwMBBU5FjwWIQF3Mzl4dgU+', 'OSEtATZHfgQNcAE5PxotcGM1Kw0ICRsADi50LSpsPH0ga38gBT4KIjEEYAgHEnFpF11RV30VGQ0wHCQdIzQ1YAsLenI/CRgDDA0E', 'Bnpuen4mEyQ1I30xNCMIB3M1IRQyFgsZIWBqPBMrAH1zIys7DTx+M2locHpBeWc+ASUoFxogNzEMY3MPDigOHRAgEnpzMzw6ChsT', 'ESk+ACUMARsncBs+PS4QbhNFWAsfLm07PmolHTd2dRNlcgonAwkpfScLfBN5LGAxAxA2ez0GPCsRLiI2bAt4XUNwPDlsZXk5JQYt', 'UxIPK2Y+L3RgagssAgw1dhtrDhEkLwJ3KxwzNXo5di1hMTphPgd3bhpraEJ3Pjx9HGQFZzR2ATMmCnsRLAAnEHEmMRUkOzQbDm0f', 'PDYfLH0Ea2tYA0txKQYhBAp0HmMDCzAVfBgCbxQOEXcFGzYLFmI8OmVwMTcPOycZZAc0DUUHfmM0Ggg5ZXIxNRwnMyUjeyQPNWoR', 'dhQ0EzN/cRRvBTIbBxkBPhQoGx1kK3I9PSUZKywvCGxXAgFRDj0jGQcjHBwpLSRhBCkpNw86fXcxfGoQPhZlDBoWATILFyImZDE0', 'LyohdCZ5fQgmOD4cJhInYCsJLRA6cxIyXEZ3ex4mIwx9NS8xEDZmHgAEAxFhGnAYDiwQCzooG3lhNXExPXZhOxIINzlkeEZ5dCYY', 'Bg46fTdnBzQKG2UBfgkoai86ODc8ICsOaR8hIHBxITECZQ1/dRs7WEl6TS8tCCUqD2wUB3o8NwoIJ20XbGMDd2QMBGciNAN/Nxc6', 'fxZ5CBQOejQILRZ3aRwpCiIaLT0EJyc9FW0GCSAueDRmZA4RL3NZR1EUL3UDZg1sJxJ3Ih80dSY8HmxwF3QqbjA1PSooNgcvYA8p', 'FzwgEjtsCzsHGi9uCjxiFCNlIic1MA07OH0NFSpKCXofNz97ZD8kOhkpdiUeDSMjNhQKewcMAQQlCDIKeyU9cmQhfHluGC5tCgID', 'BmkncjcPQUcHXBY+YwcxdzUeNzUyYS0VJiIyMQEEdnkzAgoaay4+MRlleGQXPAomDGpBd2FRGTQ6DGEAPR4vOywaEx4OLjAbIDs5', 'eBcNOy4zdAY2ejgrFhUeNhYXAwMJPHQIY0wFYiFlHzc2PXELBDAVFXcfFwsMdCcpIjFoaRg7HjYLDiMnKDAaIxooAEBaUjAzBjgI', 'HScCOCx0NzwDPBspeScTCwVeS3svI34VASk9FwIUDBp2KzMCYHRxKSEDfR4wZQYpfzEkHCQBGhtkAR4rUV8GBxV+JBkDdigLKnIR', 'BzkcLCAnZggfODhuCyQmOgQTCxIiNCMWIwE5ext/BQY2ABJwAl9tOSYtMmJ0LjIACAAdaiAiGyEFKiYnCiNmJWEACxYCCgV6KHwp', 'ZmR/dXceeDYeBygIHjpbQWdhDDg0Oh0AbCB0Fn4rITQZcSE0ehsFfHIBDBp5DW0rET1+PXsoAy5uKVVWRUMLbQQnKHQ2Jj0mLwpy', 'AB8cFjtiInYMKnEUCwJqFBkDehIHZhJnMBkCdwYtOyotJwA/IXAEaB0MdHp9NgoXBmANNDNmNQIPDhRnGioaGx8hOANqBwMCL2UP', 'ZWMbMAweGT4YZhxuOx9iIyFybQE5Jyc5G3EEPWUKfTI2GTFwA3pueTA0N31UQ3wFDCIZYiMYAzUpM2UmKQ5vYGkqKzUYdgsqHx9g', 'azQJInojIT8MOC4aDC0QPXALDQUXewQkey4LIzo0MxJvOlBJeF1qfiIDEG46HScKPHkgDyljPGwcbjAachFqJmofPwx6bGYkZRUJ', 'enEKEiwsEjVkEDM0PiU1IG4NFXwFfxgZAm44ZUVjfDcNOw0idDMmLzJiHnY0I2seMnoNIhMIZxZ7AxgYHWx7Gnh2KT8mPRxCekdb', 'fH8ZcWANeXgqGHETLAIkPRErKzARCDxwfmoRLHlWWAcWNx4NNHFnIQoPZhMoHy8vCxMBJi4RajcPNSUBLioxLCR5eh0zLT8QAXgK', 'IQ4lUFlCAzFiNG0UdTkbKykieRA6cBYqDxt0Knw9ayZnGH97LDkCCAU+EicuICkBBwdaLGE0MyQYcBBwMgwzAGd0GC49OwMTKnMl', 'N2ExekB4RScSNgJ9MR58MgxkY2o7KSBgDBobOT0xYDU7HGAcHwkBEBp6NA0AI21weER+Pjd5AjwDNRsoKCcaai0WAg8POngCYHci', 'ezcZLBwyLAcaBmcHNS4+RAZ+QzQ/CRwVNmwqJi8tODEHL2MuBj4MNjN3GzkoEyY1ETIfESEkKnofKWsFXUIfOWU4EjkgNhUwcxIf', 'KQsjYDkeLCEmLiAKZzl3YiIuZXs5CjgTMgs9Z2J6R2gUFB08cR5hdyIfIXE4J21vLHEgMgAsOzAnJi43ahtkESoZFiEDHQhBVWpe', 'OTsOdngMYzUzCngfBhMZBS57JywBPRdzamJ9cHp7IiszMDUyDzdkaggKM2pnHQstOANrEjsFehMSG3t9JDgMLw09CHoEdAIaYz46', 'ACICBCp1ID8eOGsNMzYGC1RRQg9gKDI8KD06MHMwBB8pGG0uOTkncS0pOGwSFQFgI3smGTsCFH8tbhhiVlYFJQc+M2cJEh0oJRYl', 'InA/bgQeN3wUGSstK3ErCQw0FyVjZXknIy4sBS57LyoyYBx/VBlxLmc6BGFxaGoHc2JiAmM0GW1nATkwKBVjFhI5LSx8dyAwPXsJ', 'ACIbJgwZOWBgNSQ9EX8oIgMTPgM8KF1GWRsxJRZtPy4NIicmZzQ3AjVtbRcAKg4kCStsaRx9ZAspYhgEJBQ+Mxc7dGR3eXBiK2Vl', 'AxgqeAsFcAECZBQgBhQPLAEMKGBzETF7JDk7MHMwfBc+OXl8ES5YZkR1OgEGYWV2EScWcAUoMCgiKmw0f3l3H25jaDJnfjoPFzUl', 'ZjMSDQsGJmoVGBd+DHYtPTw9CQALYhINISAALSwYMyoSRAB1VwVnJBobMiUnfA9mCx96OBJrDyQOMGAjJikdEHZmNgJiGiZ8agEz', 'bkgbBkRpJS04BSo8IAQtMH0uJ3ktKWckKHs5KGArBzNkISt3bSF6KA4Scj4yRAEAd2ocGiwwDjlqHDsnOCIDFTUBPmcmJ303HzoJ', 'CSFqM3kCNDgwOhl0PCBidDY5eHswL2V+DAE7IT87LUdkfnIXMCUXMDVpHQ4tDQZwLg45OBc4dHYSDScrHAIOHBc7Og57CxklKxw2', 'CTgbChw9EiMUbRk5LzR6Kzs1ZUYdehI/G3sQJAYWIC03KwMmJmtrF38kdCwdNh5hZCBlcS8uZjYoOhhyN2ZqBEZ1BgR0ZTVuJj0j', 'LjxucDEoEwxkNQV5K3t9LyUaAiloBSkjNFwbdFEqOBQ8eW4qOxwrYxt3ejFtag8fIAs8J2smGAMXEz1zIngfIXAhcj0mSkpYTCkt', 'MBltJDwdeAtsDD4QBn4yAGk2AmQHPTQFERkIEng3chQZe1hjbT0KGh40MRd1Ag9qCwoXLjcrBSQaCSY3HCkkHmADITt9ZDsQfzEP', 'fBZ2MBEVKzEaewIaDDNgPzEwNDpidiA+CHBsPgsJQW4JM2M5FzcxNSY6fgNxNAAeLSwsACscLwMIYzgkJHIvJycgLDMCNTQeBUdq', 'DTUoCxYpF2oINRdzPScrBGhyHT4dGWFAVioaezg0MzorcA85Jx8AcCsxaBILMB8TFCphNgUjDBUhfAsIBSAQDDIZSEACbg08Fmoz', 'AiwmUFFzQW0jCzExdWZnc3pnfQQGOysKbAAILC91PmtjGis9LCEuHGQXcBIpIWoEUldWDxgKLQArFxoMGxM0IyY4YxopASt2ZC5h', 'LihwZAF9Ny0NIxgmRHFFcmgdBAZiNBg4ASEwEHF7KxwuBgt0FwERahdkYX1/cCYZHHc2DGAAKGhESVwHBjMNe2UFKTsGBWIWExwn', 'WCYEfzpiNGcyMzI3ByEjNXEVZxEFAhsqHzobI3c8FAltLHYPZzo2OjxQG3BzEQFjFgAwbAE1GQM1FTVzLmxmEDElfwIGNAAGIAAD', 'OiQnMHAhJD0qNygqUVsFBhgzL203JGcRKxZiIB0PMSIhEj4JFxk3Bi8GISwSFSkSBncXPR9zOgYLWH8EOCR5Yx42KSkIBSNlIi4U', 'GComJ2JjCT4cOi5iBQEXCHQqDFQGYlc7Zg4YNyMdai8OHyNxNiMsGDAbcxMEdSMHAhspexUXYz0YAB4IfRhwW2lVZT43CWdkGQhg', 'OmV9eGZ3CyZ8BTtufCIvPXpgan4WZiAaEDFrPH0POBgTPhQ+MjEACyY6KCsLaWYFJzMHYxAZLDsgEgAIekEBZj1hJiIxJmglLhIC', 'cB4ZMTp3FxBzGSwgFRobAnYoBQ8tOwthFioeejw/JAA6NiQHfQRqHh1gZHsnbB0uHzJrEHwQOwMiewwDADx4LS4KajM5KCMlGxIn', 'BQU1NjFmKi4NMDAHJBdsKBVkPAchGjUTByAcJCIRHDw/OWYbfWIJEhkwKyQmKTE2bAswAiANYAU6dRYyFR09KnkYLTBwJSQ6FGwq', 'a31KQwYNEwglJhkUazUtJmMpPTIKMGkSORMqASgYJmIZJwoLbC8lKTMlAWo9AHNXZRdnDQAWKQ8cDzUZZgBndCouCzgSdDIrIRgA', 'Ew0hAB12AWt5IBBpAEl8USYaORkZBWgxBDFtO3ccB2wxaicocCQpagsmNSthIi43K3x6cBs2HBIEG0pFNSIUezcPBwtuDX4FKhoZ', 'AGRnEB1tajQ3YW5sZgQDZyc1FS0iNC4lATZwPmY/PAkgNgI8GwdzcD5qBgV6Qjc2ZzICFiolFAEfJnJnERM7a38HKC5xHWcCICIV', 'BXRoPWcndAAnNyYUYzk6B3N6DQt3fW0edGEea0BxVjklKB48MQllKiIHJgwobhg6LjEbLR18AzNpZj9jdBsSMyoYDC83YBwFU14G', 'AAg5NTwwfih2NBE8MxUYbiYPDCgOewQdZABoZws/PggICTc4dmBrY20UKTw4Ejo1FCd6HC4PIxA/cDp2CCl3EQsIPwAjIBQXZgMh'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
