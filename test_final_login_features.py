#!/usr/bin/env python3
"""
Test all the final login screen features including language picker and keyboard styling
"""

import sys
import os
from pathlib import Path

def test_bottom_left_language_picker():
    """Test that language picker is positioned at bottom left"""
    
    print("Testing Bottom Left Language Picker")
    print("=" * 37)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_positioned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bottom left positioning
            if "self.create_bottom_section(self.frame)" in content:
                print(f"   ✅ Language section attached to main frame")
            else:
                print(f"   ❌ Language section not attached to main frame")
                all_positioned = False
            
            # Check for absolute positioning
            if "bottom_frame.place(relx=0.0, rely=1.0, anchor='sw'" in content:
                print(f"   ✅ Bottom left positioning implemented")
            else:
                print(f"   ❌ Bottom left positioning not implemented")
                all_positioned = False
            
            # Check for language container
            if "language_container" in content and "pack(anchor='w')" in content:
                print(f"   ✅ Language container with left alignment")
            else:
                print(f"   ❌ Language container not properly aligned")
                all_positioned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_positioned = False
    
    return all_positioned

def test_translating_property_text():
    """Test that property text translates between languages"""
    
    print("\nTesting Translating Property Text")
    print("=" * 35)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_translating = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for get_property_text method
            if "def get_property_text(self):" in content:
                print(f"   ✅ Property text translation method exists")
            else:
                print(f"   ❌ Property text translation method missing")
                all_translating = False
            
            # Check for English text
            if "Intellectual property of Hossam Lotfi and Walid Abdou" in content:
                print(f"   ✅ English property text correct")
            else:
                print(f"   ❌ English property text incorrect")
                all_translating = False
            
            # Check for French text
            if "Propriété intellectuelle de Hossam Lotfi et Walid Abdou" in content:
                print(f"   ✅ French property text correct")
            else:
                print(f"   ❌ French property text incorrect")
                all_translating = False
            
            # Check for property text update in change_language
            if "self.property_label.config(text=self.get_property_text())" in content:
                print(f"   ✅ Property text updates on language change")
            else:
                print(f"   ❌ Property text doesn't update on language change")
                all_translating = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_translating = False
    
    return all_translating

def test_orange_keyboard_styling():
    """Test that number keyboard uses orange/black styling"""
    
    print("\nTesting Orange Keyboard Styling")
    print("=" * 33)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_orange_keyboard = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark background
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Dark background for keyboard window")
            else:
                print(f"   ❌ Dark background not implemented")
                all_orange_keyboard = False
            
            # Check for orange title
            if "fg='#ff8c00'" in content and "title_label" in content:
                print(f"   ✅ Orange title color")
            else:
                print(f"   ❌ Orange title color missing")
                all_orange_keyboard = False
            
            # Check for orange number buttons
            if "activebackground='#ff8c00'" in content and "Number button" in content:
                print(f"   ✅ Orange active state for number buttons")
            else:
                print(f"   ❌ Orange active state missing")
                all_orange_keyboard = False
            
            # Check for orange enter button
            if "bg='#ff8c00'" in content and "ENTER" in content:
                print(f"   ✅ Orange enter button")
            else:
                print(f"   ❌ Orange enter button missing")
                all_orange_keyboard = False
            
            # Check for orange backspace button
            if "bg='#ff8c00'" in content and "backspace_btn" in content:
                print(f"   ✅ Orange backspace button")
            else:
                print(f"   ❌ Orange backspace button missing")
                all_orange_keyboard = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_orange_keyboard = False
    
    return all_orange_keyboard

def test_keyboard_popup_functionality():
    """Test that keyboard pops up when clicking user names"""
    
    print("\nTesting Keyboard Popup Functionality")
    print("=" * 38)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_popup = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for keyboard trigger method
            if "def select_user_and_show_keyboard" in content:
                print(f"   ✅ Keyboard trigger method exists")
            else:
                print(f"   ❌ Keyboard trigger method missing")
                all_popup = False
            
            # Check for keyboard show call
            if "self.show_password_keyboard()" in content:
                print(f"   ✅ Keyboard show method called")
            else:
                print(f"   ❌ Keyboard show method not called")
                all_popup = False
            
            # Check for user button command
            if "select_user_and_show_keyboard(u, b, c)" in content:
                print(f"   ✅ User buttons trigger keyboard")
            else:
                print(f"   ❌ User buttons don't trigger keyboard")
                all_popup = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_popup = False
    
    return all_popup

def test_modern_keyboard_design():
    """Test that keyboard has modern design elements"""
    
    print("\nTesting Modern Keyboard Design")
    print("=" * 31)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_modern = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern font
            if "Segoe UI" in content:
                print(f"   ✅ Modern Segoe UI font")
            else:
                print(f"   ❌ Modern font not implemented")
                all_modern = False
            
            # Check for flat design
            if "relief='flat'" in content and "bd=0" in content:
                print(f"   ✅ Flat design buttons")
            else:
                print(f"   ❌ Flat design not implemented")
                all_modern = False
            
            # Check for cursor styling
            if "cursor='hand2'" in content:
                print(f"   ✅ Hand cursor on buttons")
            else:
                print(f"   ❌ Hand cursor not implemented")
                all_modern = False
            
            # Check for larger size
            if "420x380" in content:
                print(f"   ✅ Larger keyboard size")
            else:
                print(f"   ❌ Keyboard size not increased")
                all_modern = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern = False
    
    return all_modern

def test_all_versions_updated():
    """Test that all versions have been updated"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main", "login_screen.py"),
        ("Main", "number_keyboard.py"),
        ("Protected", "YES/login_screen.py"),
        ("Protected", "YES/number_keyboard.py"),
        ("Obfuscated", "YES_OBFUSCATED/login_screen.py"),
        ("Obfuscated", "YES_OBFUSCATED/number_keyboard.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name} - {file_path}")
        else:
            print(f"❌ Missing: {version_name} - {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all final login screen feature tests"""
    
    print("🎯 FINAL LOGIN SCREEN FEATURES TEST SUITE")
    print("=" * 43)
    
    tests = [
        ("Bottom Left Language Picker", test_bottom_left_language_picker),
        ("Translating Property Text", test_translating_property_text),
        ("Orange Keyboard Styling", test_orange_keyboard_styling),
        ("Keyboard Popup Functionality", test_keyboard_popup_functionality),
        ("Modern Keyboard Design", test_modern_keyboard_design),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 43)
    print("📊 RESULTS")
    print("=" * 43)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Language picker positioned at bottom left")
        print("✅ Property text translates between languages")
        print("✅ Number keyboard uses orange/black styling")
        print("✅ Keyboard pops up when clicking user names")
        print("✅ Modern keyboard design with flat buttons")
        print("✅ All versions (main, protected, obfuscated) updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Final login features may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 All final login screen features successfully implemented!")
        print("📍 Language picker at bottom left of page")
        print("🌐 Property text translates: English ↔ French")
        print("⌨️ Orange/black themed number keyboard")
        print("🖱️ Keyboard appears when clicking user names")
        print("🎨 Modern flat design with orange accents")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Some final features need attention")
    
    exit(0 if success else 1)
