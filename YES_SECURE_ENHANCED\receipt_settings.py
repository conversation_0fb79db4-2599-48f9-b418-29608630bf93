#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "receipt_settings.py"
PROTECTION_DATE = "2025-06-03T03:26:12.964603"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
