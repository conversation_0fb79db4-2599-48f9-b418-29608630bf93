#!/usr/bin/env python3
"""
SECURE POS SYSTEM LOADER
This file loads and executes the protected POS system
DO NOT MODIFY - PROTECTED SOFTWARE
"""

import sys
import os
import importlib.util
import types
from pathlib import Path

# Security notice
print("🔒 Loading Secure POS System...")
print("⚠️  PROTECTED SOFTWARE - Unauthorized modification prohibited")

def load_secure_module(module_name, pyc_path):
    """Load a compiled module from bytecode"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, pyc_path)
        if spec is None:
            raise ImportError(f"Could not load spec for {module_name}")
        
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        print(f"❌ Failed to load secure module {module_name}: {e}")
        raise

def main():
    """Main entry point for secure POS system"""
    try:
        print("=" * 50)
        print("    SECURE POS SYSTEM STARTING")
        print("=" * 50)
        
        # Get current directory and add to path
        current_dir = Path(__file__).parent
        lib_dir = current_dir / "lib"
        
        # Add directories to Python path
        sys.path.insert(0, str(current_dir))
        sys.path.insert(0, str(lib_dir))
        
        # Load the main module from bytecode
        main_pyc = lib_dir / "main.pyc"
        if not main_pyc.exists():
            raise FileNotFoundError("Secure main module not found")
        
        # Load and execute main module
        main_module = load_secure_module("main", main_pyc)
        
        # Run the application
        main_module.main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Please ensure all required dependencies are installed.")
        print("Run: python install.py")
    except Exception as e:
        print(f"❌ Error starting Secure POS System: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🔒 Secure POS System shutdown complete.")

if __name__ == "__main__":
    main()
