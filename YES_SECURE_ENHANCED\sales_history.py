#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "sales_history.py"
PROTECTION_DATE = "2025-06-03T03:26:12.939296"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
