#!/usr/bin/env python3
"""
Test the UI improvements: menu import moved to product management, 
bigger dialog with scrolling, and exit button removed from POS screen
"""

import sys
import os
from pathlib import Path

def test_pos_screen_changes():
    """Test that POS screen has exit button removed and menu import removed"""
    
    print("Testing POS Screen Changes")
    print("=" * 27)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that exit button is removed
            if "exit_btn" in content or "self.app.exit_application" in content:
                print(f"   ❌ Exit button still present")
                all_correct = False
            else:
                print(f"   ✅ Exit button removed")
            
            # Check that menu import is removed
            if "'menu_import'" in content or "show_menu_import" in content:
                print(f"   ❌ Menu import still present")
                all_correct = False
            else:
                print(f"   ✅ Menu import removed")
            
            # Check that logout button is still there
            if "logout_btn" in content and "self.app.logout_user" in content:
                print(f"   ✅ Logout button preserved")
            else:
                print(f"   ❌ Logout button missing")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_product_management_changes():
    """Test that product management has import menu button and improved dialog"""
    
    print("\nTesting Product Management Changes")
    print("=" * 34)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that import menu button is added
            if "Import Menu" in content and "show_menu_import" in content:
                print(f"   ✅ Import Menu button added")
            else:
                print(f"   ❌ Import Menu button missing")
                all_correct = False
            
            # Check for improved dialog size
            if "700x600" in content:
                print(f"   ✅ Bigger dialog size (700x600)")
            else:
                print(f"   ❌ Dialog size not updated")
                all_correct = False
            
            # Check for proper scrolling
            if "tk.Scrollbar" in content and "yscrollcommand" in content:
                print(f"   ✅ Scrollbar implementation found")
            else:
                print(f"   ❌ Scrollbar implementation missing")
                all_correct = False
            
            # Check for improved text widget
            if "height=12" in content and "width=70" in content:
                print(f"   ✅ Improved text widget sizing")
            else:
                print(f"   ❌ Text widget sizing not improved")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_login_screen_exit_button():
    """Test that login screen still has exit button"""
    
    print("\nTesting Login Screen Exit Button")
    print("=" * 33)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that exit button is still there
            if "exit" in content.lower() and ("self.app.exit_application" in content or "self.root.quit" in content):
                print(f"   ✅ Exit button preserved in login screen")
            else:
                print(f"   ❌ Exit button missing from login screen")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_dialog_improvements():
    """Test specific dialog improvements"""
    
    print("\nTesting Dialog Improvements")
    print("=" * 28)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    improvements_found = 0
    total_improvements = 6
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking improvements in: {file_path}")
            
            # Check for specific improvements
            improvements = [
                ("Bigger window size", "700x600"),
                ("Better padding", "padx=25, pady=25"),
                ("Larger title font", "font=('Helvetica', 16, 'bold')"),
                ("Improved instructions", "Menu.txt Format Instructions:"),
                ("Proper text frame", "text_frame = tk.Frame"),
                ("Enhanced buttons", "📥 Import Menu")
            ]
            
            for improvement_name, search_text in improvements:
                if search_text in content:
                    print(f"   ✅ {improvement_name}")
                    improvements_found += 1
                else:
                    print(f"   ❌ {improvement_name}")
            
            break  # Only check one file for detailed improvements
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return False
    
    return improvements_found >= (total_improvements - 1)  # Allow 1 missing

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/pos_screen.py",
        "YES_OBFUSCATED/Menu.txt"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated (should have scrambled variable names)
            if file_path.endswith('.py'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Obfuscated files should have scrambled names
                    if len(content) > 100 and ('import' in content):
                        print(f"   ✅ File appears to be obfuscated")
                    else:
                        print(f"   ⚠️ File may not be properly obfuscated")
                except:
                    print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all UI improvement tests"""
    
    print("🖥️ UI IMPROVEMENTS TEST SUITE")
    print("=" * 35)
    
    tests = [
        ("POS Screen Changes", test_pos_screen_changes),
        ("Product Management Changes", test_product_management_changes),
        ("Login Screen Exit Button", test_login_screen_exit_button),
        ("Dialog Improvements", test_dialog_improvements),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 35)
    print("📊 RESULTS")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Import Menu moved to Product Management")
        print("✅ Dialog improved with bigger size and scrolling")
        print("✅ Exit button removed from POS screen")
        print("✅ Exit button preserved in login screen")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ UI improvements may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 UI improvements successfully implemented!")
        print("📋 Import Menu is now in Product Management")
        print("🖼️ Dialog is bigger with proper scrolling")
        print("🚪 Exit button removed from POS, kept in login")
    else:
        print("\n❌ UI improvements need attention")
    
    exit(0 if success else 1)
