#!/usr/bin/env python3
"""
Test script for email functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_manager():
    """Test the EmailManager class"""
    print("=== TESTING EMAIL MANAGER ===")
    
    try:
        from email_manager import EmailManager
        
        # Create instance
        em = EmailManager()
        print("✅ EmailManager created successfully")
        
        # Test SMTP connection
        print("\n--- Testing SMTP Connection ---")
        success, message = em.test_smtp_connection()
        print(f"SMTP Test Result: {success}")
        print(f"Message: {message}")
        
        if success:
            print("✅ SMTP connection successful")
        else:
            print("❌ SMTP connection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ EmailManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_email_addresses():
    """Test email address management"""
    print("\n=== TESTING EMAIL ADDRESS MANAGEMENT ===")
    
    try:
        from email_manager import EmailManager
        
        em = EmailManager()
        
        # Test adding email address
        test_email = "<EMAIL>"
        print(f"Adding test email: {test_email}")
        
        success = em.add_email_address(test_email)
        print(f"Add result: {success}")
        
        # Test getting email addresses
        addresses = em.get_email_addresses()
        print(f"Current addresses: {addresses}")
        
        if test_email in addresses:
            print("✅ Email address added successfully")
        else:
            print("❌ Email address not found after adding")
            return False
        
        # Test removing email address
        print(f"Removing test email: {test_email}")
        success = em.remove_email_address(test_email)
        print(f"Remove result: {success}")
        
        # Verify removal
        addresses = em.get_email_addresses()
        print(f"Addresses after removal: {addresses}")
        
        if test_email not in addresses:
            print("✅ Email address removed successfully")
        else:
            print("❌ Email address still present after removal")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Email address test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_generation():
    """Test PDF generation"""
    print("\n=== TESTING PDF GENERATION ===")
    
    try:
        from pdf_generator import PDFGenerator
        
        pdf_gen = PDFGenerator()
        print("✅ PDFGenerator created successfully")
        
        # Test content
        test_content = """SALES HISTORY REPORT
==================================================
Period: Today
Cashier: admin
Generated: 13/06/2025 15:30:25

Date/Time           User        Total
--------------------------------------------------
13/06/2025 15:25:30 admin       25.50 €
13/06/2025 14:15:20 cashier     18.75 €
--------------------------------------------------
TOTAL: 44.25 €"""
        
        business_info = {
            'business_name': 'Test Business',
            'business_address': '123 Test Street',
            'business_phone': '+1234567890'
        }
        
        print("Generating test PDF...")
        pdf_path = pdf_gen.create_history_pdf(test_content, business_info)
        
        if pdf_path and os.path.exists(pdf_path):
            print(f"✅ PDF generated successfully: {pdf_path}")
            
            # Clean up
            pdf_gen.cleanup_temp_file(pdf_path)
            print("✅ PDF cleanup completed")
            return True
        else:
            print("❌ PDF generation failed")
            return False
        
    except Exception as e:
        print(f"❌ PDF generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_email_sending():
    """Test actual email sending"""
    print("\n=== TESTING EMAIL SENDING ===")
    
    try:
        from email_manager import EmailManager
        from pdf_generator import PDFGenerator
        
        em = EmailManager()
        pdf_gen = PDFGenerator()
        
        # Add a test email address
        test_email = "<EMAIL>"
        em.add_email_address(test_email)
        
        # Enable email
        em.save_smtp_config(True)
        
        # Generate test PDF
        test_content = "Test sales history report content"
        business_info = {'business_name': 'Test Business'}
        
        pdf_path = pdf_gen.create_history_pdf(test_content, business_info)
        
        if not pdf_path:
            print("❌ Failed to generate PDF for email test")
            return False
        
        # Test email sending
        subject = "Test Sales History Report"
        body = "This is a test email from the POS system."
        
        print("Attempting to send test email...")
        success, message = em.send_history_email(pdf_path, subject, body)
        
        print(f"Email send result: {success}")
        print(f"Message: {message}")
        
        # Clean up
        pdf_gen.cleanup_temp_file(pdf_path)
        em.remove_email_address(test_email)
        
        if success:
            print("✅ Email sent successfully")
            return True
        else:
            print("❌ Email sending failed")
            return False
        
    except Exception as e:
        print(f"❌ Email sending test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 EMAIL FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Email Manager", test_email_manager),
        ("Email Addresses", test_email_addresses),
        ("PDF Generation", test_pdf_generation),
        ("Email Sending", test_email_sending)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("Email functionality is working correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Check the error messages above for details.")
