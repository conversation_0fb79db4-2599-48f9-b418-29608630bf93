#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "product_management.py"
PROTECTION_DATE = "2025-06-03T03:26:12.916725"
ENCRYPTED_DATA = """eJy0vVlz21rXJkYPRz7WcETRsixTlL0BggCJgcQmCIDERNmW5T7v218dy7JE25pJURRNUZxFShTJ35LhLpXK1XebfJXqpHORpFKVVKqr8t3mN/Rt9gY4WjpHfqs7vrNhgMAe1nrWs561drVY2X6k8pChOU9n65ulqiTk1LqXN7Qwt+UyjZBG0HRVFvY/f0hxZYkQs0A22ZCYMK4gZykmLxKA4nYP9xYTAYMHPK8F/QwX/aNdLX7d1oKSBmBvY3f3GyeDkqxkAPk+KFDBK4rSIANgQw9G9t9uLHJUIlMP0owWDmkhlrhB/zUJGEMKidva7tdIEuh1JRoHOnf+xs8lydq892nGSMri2rqnE4Uy28pOJ4JBbgb8saWYRrRGkITEWG+2PftRYJogS0HTzZqLtGIRisHwvP7lD+Ng882HngsAUQTgcRhYPIjJBJP9ms00qrmO+2htVw9AvU4JYp6PcGFSIZVaNMqRUTWo7a179iGlmlEKwELxfPtN9qpcUjU565vWOWWjVPTxshVqZUkqcVrfLuCrtV5NoROxgvV2zfO0B8nkQTZTz5cuegXFMEIdwWDVRimLrvo2xCT9vt6/GgeyciwL0SCNri7sH6SsXrlawlc1lkxkOYVioWiEM2fVGXFvO77Yv8rw/mBFV5WYJnLAqzIq6M27b3ah/KXUfzIfDiW7fJRTSJ7RAV9cX9vUyqWSlxXgu/forbKmHKrxOhvrEED6/PLX4e+i710hlZARZnuMQDdIVdx5v7nWMyVCJukET2i9OM3JrNmp5H5vVLM9j9uziEfjWuV181gQA35R1QitzVBnjdOLrQdbv1jN9vvhaJxePTMViwGZuMRDz87advSHdw4RlgXQ98KY7xJfvTZgKHCtA4LLZXW1Wr9JU9VYhmqtaJ7+aIgwEDpKJKiYfOy3xHBB0kPS/PpX7+rrqSetLM0nwkC/XOAC7PW2Pfv4nRcWfaQaBiBjMjonCJQmv0czaF/lU3ufvYZ91Te8muvfS2if3d/2deetOjV7JPHso9GYMRTQ2/iSWoyQJkfQap0arroeoKGoch23ZziDZqRkaoy/qZjRqNIbjiS+yoBINBcyLBo0YyzgCFbrtP8YXh3MvqDGTbaWrs3wuvrHNr6qy7ESDQI0b9AuhlJVLRpXiqJo+S9/HY7V8WVhlUskABsvMaqSFRUAtD33+30uXiVgNBMPEiGWJY0qshu1/lh9/ezMrx/KFDta7fiqoh3s+PaUmJrr/NGfX/sqr4QCbUIQ3y+vWNv4dwk6mkc2h4XGPG1yihqIQdB8s33w6fL7cPY7+ZyXZNGKzaqmmo2sHe68MagqKXOkxJMcTGZFS1cEmQJiOdeJD0bjNOPjw6yW+GPwVqZK6am3xU8CTLL1FrJIx78KbNBoGYokFwKMFNReuT9upZuVjthkToWzdM8PRJVh0tnG98PBLMTVsAjgTMSMRMOFEOTjTZmPJEpoTZ6tDHcKwUb0sDplz2BBisGw6+3b/XVLlXRY/EUOGNzlm+FbXVYXlI290e42aU0Ke2UoKFKtlSEShWdbn1JfmuO/m7+iWI67GFgV35ZGzenQVG8ukoYYETXKbVkmRYlJADderCxv9588moXeYAbxFykmAVqFmhfZ9nB29uVZr5eulRoNRjz4/Hy1VhaD+fTRqdBhi5F0oxyKpetRzqDB2C5TTdPPoXWly/5OJEoHOwSvp+Y2Diu+F09d+QwV5JT0VaNRTkZ1tlU9ajtrEt/bzD6QWerCtKCQJGrNOKADVYbdWN//1pNMv65+Pxi8sxA14l6CMLJpD96/AVrXuYJhz683SYYtrZdVRRam8cqpIBt7TdJgSeeYZLEbDYux+nnpcGwf3RqNgGbAtPfF8WY8qZYezJ608t1urZDWk361dZ3PqHKrtTC1+eh7TjlrZQNS/SyB3KXRzHydGMkfnoy+l0Pf281Gtj7//en3myqvB8Myw9Vsaxb2RzoKpwW8kf13xQ/IqrDa+e+De/E7zzKqfiNbm7veD2gfVUG4OOX6dWomlw1BjksfN0oNktL/Pvfo8eW34U65KfwaTxA1w+BU0YC991839nqUJDDDPaglCUYzoU8CVDTlnl/k9JunNBWGxwYnCYX+SBLRUvrm0eaj7QvYOB3aZ+RDdQUyXnun+Na2UrFWjI6HumgfzbpfzzXKLBeJcszZlMqmfJ51K8ZWTxuleiLM0pfN/NX74VgtuCsbGqTZOodgg0CIIJtMmjUuyoiNAM99c2++4SmTrCuqEKBB/M1gjkSCC2meOc8aemcfMCLw+HR9MIMAagm1wH3bPtx0BQj2Jg3iQTJ/OXmvb2v/WyIhazQo1FYfEFyYUZJUvVZjSCVBCvvv9z6ivd+V62Q2iPZ+iOViLBHUCNUsNS6HVgW/FXtkUFwyEaQFpXjywMdyqU/7u7HuVFhJl+pcWIjxqhIg1HT9tAKPiOLOi1eLiSRgJZUn0Oy3DEFE5rL29U9WjhyPcRqMtZZz6Xzvkau0fNcObQ536OW3wb0mFw0ZXTIaaadfrJhQ+3i4f1h5LAm1fEYJ8oF4guUt7uJxO40sPzA2tnee1cqN86i/rV718pdL+a0fLUM5BmhA0bSGvOSrShPZSYVWRSVd87x4v6dQ6lln+fvBu8E+AlrSX9T7v9srVnW0Yr20zpkUG23xBkJ9AjHnF7b2U6tDi2SvOjWpI+zGBtskZXxd/rwvGSU9WXiwsvok3QubIYhG8omqiBN2w+VgUd7wlxgL/jF45w+aETtWVRpdXTCQT1EBoSiKD2HC5oo29LDN/GvHThp41U1iFRrCoM65RggqJhYv47atU9hoFtB8WNMW6TjFxkSCp+k5aH1cfrMpKe3GcKfk2tVZQotCugEpLpXa31BkopSgBDnM+rUAvEL7bRwjGZB2R1UywaPhT4abS9hr2N65sne4sWkmTaqVJZDNKRYjTTPN1S+HKwf/7g2hQ1pB3r3CEmYL4bpZP4wlQc2znvq3XJFg4/6inEgexcNwCnuNdhrE0K7af7u7yTfbpwdDy3Bdnh1iQs/e7gduuK5IJQ4dX5btNuhwpf5266vWgmHgDdBBkqsVGKt4Vi6qTSanpEc7JcQypjuIPWw3iax3MWBYofTGru/S/dibqQOdChJs+rXKy5laJEroZ0eNmmdt6yNdPOp1/hhawlrpiaAA2VCjvc+Ha3isWImFJm8M977IRkCS45Fl8LVeLiH7fOJFb9VKDz0srYYSvq2tNQ05KhOIM6bJaYXWFaXHK09UWqR47f3ywT5PzYWwJcyWBJERY5ZxgdC5EjeUmufdt91gscFAijGZoNnKZaFMx1Rm0pfZ2KwfL9AKPwsRriuoqc2dfahen60+QD4F+6NW9+XMjzPIy5CWaJPR4l6BZJWKDkioc3HvixuEchuZOqR5s2pjQpj0V2JAnaYBs7H8/Jtloj14/vvQS9pIZjS/CFFBUFRpvx4NFPHv1mpRFtJC+olrZTKaGF/PQmrblxINhtSKzkjmsuEEx6gXzVp2Yg+6VfSiUojivg3sBm1tlN6khKTlVyhYr5c0DhSmJdOQkgzJXIeUN+sO+mpleRogAHsJo92XS3NPZl2Lr/K9zcPDr5alVO8cSXr9a+rdmK3DoyGFGS2Gd7ni2nib2qxIJhHlKUJnT2kUiST9ScMIhmMGkd7c3CjwEgDxECHrx6aWPN/6NoFUUxalBnWVhZpbE8KgCZD/bSssVFvc11fdysvH3VKUj8TreqDydHpuOVPTOJnUldbM2vsxa4beeVHRSJCUIAUda/YGRQTYmi2s/vbEWyv4RcN/wcUL3psvQ9x+Xn8M8J9eSBQoYPI1P8/gkdze3hUSqjHyv3HKDVkG8jrhTaxtHm5iG5ss/IL3YDNICfN2fFRqiGT6+TPXx2LsiKuNEAUaKxcaR8Lk+KIdW90kZTp4E6FJCc/Ci17PTzFCqVl65X67rYdKuZN85vTqNLc99KFHjWeE7unvIyFLEKZOwEJUpJiaIrJC4zqspdY8OyxFAy0L9AAwRv6XlZxx3o6AG1plBSYX5LhMGnm6WY2DMNY4z8bH/WCY1Rft7+VMx+aU32+sidY12ik+xdA1MaA4FimfMZJUPDVn76MwJRdNFgDePfbk5Vr8z1DfmKejWR527eiJlUPdoMLNon1CF1tbn3cKfZsjXJymt++694cnI8st8wCtOmTrWt6lqUzdz2tm9bS1DKwkW5v2pkuNDLJ1Lz7uSrHGaeuPP3+r4ZNNvtWtdQrWSeWy2s0mtueev1MTNFv7EV23r778BcodPDklmsmwRvS/lzDn+2wAdz7c3TAkwlEMi++NKYQL7yOTMkr5rJrMzelcklVtj8MoBM3CN6XPG8gi/bm92tZjc31bp7bSd+CN2+/s53W61XyeSW2JctW7ivBVq5funTOKgfxgH6m6ft16VMgiO+mn8hdxWkmC7s37e5+czQQkgpl/+HC5ByROaLVbeZ6I0pQaq9pIpv+9KCIAyiiGxevKiEKfSnOVt5ubh02/zsEWGo1HMy9f569AWGUuirWrar208jI+tLHfSzM6Ca/69oofxDiMrhtZVqWJoS/T/I632ny7XklAoClFiddPW2FCpM6usxOIUeUQjEdGa8FMclfFEhPrPLFnwYt2qCuKvDjaZUs2V9Ao05YVA93JJy/XeA7N+HLj2/2zkM0gFMzNv0t9SXSlJFXArJqmSbDSZFWF8fY9zt+fXd58+ctdNrCTBFqrGIsiSzjvXlyoeXb2v3HF67MngNL50srSef7quNfr1Rk+gRDFK+3+/VstKWS39r3XPQv5k81MLRFrnZAaz6UQ9o5ARc03a618MqJHW8c/tTYakI0ebqKIwK/6owVaBPoAT9LXQ7S5KfnVEerD9yq6hgyUBF0f1/cW02Waun6iABMA2z4LNMewAu1g4OUepaqgZTNUli43VZZxMUAnrtPazteNf9Tm1BIo4ju7Ts8itLnZ+2UFoaDqSbaUN6mQ2Tzt9SDZrVYapV8+ff7UuqmGpNaJn6A17fyn9mBWiilm2uWyR1LJd0p1FvijLGcWbbxBQx3esVOMGM24REqdFiF0okUCDSOOFh965xYaTnRcyHazFw9f9rkg/LvnFyvhkEGxNbTL6Kip0p5PH9a4pFHy86qRpwUxMMR1Rsxh5Cz0TJPmNe04pKF4n2QtMQB8aLVrm+upj62rs/fjX8ShKAbS0wiZFz9tfHN86APbl2VuedhaWSdlks7bGIkKgWMNRdbibw1VOzxc33ShtQFpFQpnz2pvfmKO0NU/4yhibFJjAF452vbh2j+1rrrvf2IfoatBNgYAjXCKat089dZPcKRp6PniyRM+SGgFtxvthYtMPlMVWWnnfi5ocFUIpc9PTenmtHCKvFUMIYoP3/Y5jEVlUoTVm5LNQrDpQv73e/bRIMZJJkmZY5yRJAyDZbRZmiRUUlz7fYRFeb+pVf4Yv3dn+2CTk+dsv28SF+kCwipz9j7q2DNorw1jxmYg070kIKi+9Sblyvb2RoGTWf0mzcAAO4rajJOb7h8/8c7oakjn1Pzx2arGs0bZ6231slW0YlsfDw/Quuo6NocTzruV80mbY91vr/DVRJhlLpv5m5LfUo1usZs19vcPt2GsZDPbAktqSTA18BojppeDIRqBHMFKenWKuM25jUUE57VnBsWMxaGT+YWkGCUGq25f81/340FWE7Kazbh6JcYk2M31NwUOijmMzI8aQ29VyR0vAYCQqmddSCqaCsVxZpuhiGY5N0QUlMqLjj9yvNUjHKXaTG/FMnXI8DqZ6eMcFceS1dH32qPBAE0LsYp+HNXVIn4r12cch95UMfM52qExjfHiXdZ8vubkcZj09867IZK5qT2HmiIaG+88XxT/KNvC8CzlHmRbEOavYySTG71zoyzERnzOaMUGaJNb9HOQVCp+CljXfuVgb34t1nW7XHOlOq9aVoA4eWzSWu8HK9qsTbzVAsLmkpL64PkkktUkzTIEq5tDy0/zCSYI37jnF5vNlRmE+mpPwn5kva+a/Zidw5xMekORUDw4tMCs7JcrfifH96a0saHJ1WodIqyyYmhhSZTjbUlT1OmYah1uv0I71NISnJhnAcnSHHtTAqoR7VY1YO183dvim92SAViJ0GsX3R+jp8Hs25F1VjAg5q+KJ76ldJ+FaEy5098no9TRuiJV9lqgZQFhiEz6BRrJ1p9zuVbY0urnIzbe9s4STXO6kVSmKFPROgIdU3i4vfy86cR0Wi1bwljlxM8mEx1KCtyQ2kZpY5Ohrs9USoVhYNDn5+Wf+qJfAG9gRIGtSl6h45HjSEQvWLI+yymm2mnIwu5W6tnl1ZfJXeYXDEJR6GGU6ieCws3F1PQwsi7Xboa43WZsKATf2CBk53VCUwpAs8To3vLzaxzvt7IxU9Dp9HU5fzWZmepnl5glHc1vAVlB5D+LJ0sDn3JTjZuEatS7N6UfuJHBTvGqNI+5zRBvEu3WI5VVVDSDH3Y/cvFS96QxxvWVey4aalbI3Ho7ZNXwO7ucuNuMqjhPh/bR8YANuBntlFoNIymOw5m4p89dK6tTD5cx60IH4wgT2hmifm6xz2AISZoL3vPOAz5WQ85QPNjb3I2aEiG28NpY4IOqYvihRRREkT7fHnG5eAYtgSJ4SQcuRqXFSb59efbpWMz+wDs1V/Ns/20dcxQuVYFHw/0bIVhmmqCF/b2DHYt0ImuNZjUcWa96l1cWkA/lpQNsn59OZDwXeBzJZFQGcvsHbzBLbNtJg9f4IaKwbd1HO/4lbvOE45ZQQLiu2eZu9CuiO1obOF9WMO2MiXhn3upHhFw5ytSKGbTLhmuyUnpOMPWESegWLZQdHOtw9bsp0RpifkVwbE5Ulto6Le69w5mp6zPkDGZ4VRPYdPn7BPbG8SC2dTOcDnqXWUE/XrDR1zVWHRQdJkHxRXVk63JZ0QzHtFHOa99Afn8+TYqCG1kGNd709LbvR/XVLJf6NxtPbaaXkijMQixCI6i3W1GCme6vup9FX2jFyjbPz4SLL188mnnl/rwLqVKutoQiXPZi+mG6Ue5marU0oVlEa8UX/4kops6gGPb8JsdWi9mzXk9GuD0gKBvbz+0olW9VEKq3rIhxcvbXuachIyce7m990/s8sMAtRBKsGeCZEQOZ+sLLkwg5rOqzMvKSXnPjwPO0RyWOl0QV0MQpvvdnoxhr53Oxb0VPOtn39+FYfDVkQKZVqD5AseS253gJeZxso1S/EAgpki7flGD8In2U+06dTf2CRkORq7UoZ5ps/uew9yS71WxlgU6RERhpOSt2NPsjjjGiR7whEqguCIVJNm9+YX4l3dMpM1htlzAmbI9ZldPMgoD8fT+DUHO4EWRF19SyzuvckJFLhJxsuCRHfF83d6H/JghhwYnKzzMTUXnqo0L9p+4UHMPmH+b++E+KU2RO4+y1gRDUT0cTKFqMUKIaqyLU93Tq2VS+9/lwby3davggycO89wnyg2fI5rQgG4GN85/i6/6zx/uTOwWiGRzkgO6N9wOA1W/FkjKX5GZN5Iw0Q7kPqW7v7HPzJLpNUc2z3E/Gkj/DjD10/bKTUP5xZqxRla2dfzM/VbqpJmVCy1cyFwYVjOFMHAAR6Qbou+uD3T3CdTYX5Hir9T1vBELqRmFFvqrFsk99Syt9hrmI9uCjx+MY6Sy7KvsVTsn2MeGAQ46ZJnOhBFl1uBf4hGfIQWHezKzwQGidIfvKyuIcQm7bm2tarHF6McGp6kqIJlSv4wd/YC+ThKh6CSzC2Xs3yE2wWFGWtpFbNOIvsIrJ8fkXa5vRJMXOIMzA85oWST/+yZHM9ja2/7bm8EgksqUhLekVZJVp1gk6oUKt2EcytfPSwX0e5/9HhtlWHQQvWmUCnOYq35/7PJ/ixUbc4FV4kbmIsARR7CBfJpOQa+V+jpFDV1Wa9OMIyMbek987xhQZhjLJbSpsHKhwCatZcIRbS9dgsO0WuY3DPR9GuTOMzarxAzyJufrWkAuSgDpjsgqXSZv2uhrEsM9Xa99+iv0oXSQoTijm6rMothKr7iU0Gr+gvXCmsBHx4jTdC5EnzXPVOCo3jxrImqXmnwXjug7TPz2DKN43uzi//3Zz55/SZ92SyoYTOB9qrzoVhf2qONfXXl4N8ZVuM2OzNtq8k1UrfxuuyRvPzf62Xu5blVG21N6/IXFr6J3Fm6P39tUYK/eSLAnnRrmnG4yQx3SMuePmK9bacW9uc7IMaJ5m8iFDpLMm1KNmVKHH433Tn9Fo/U614fcJlphkGZbgfh/GdLXqbByQdJ5VlDgUrWBNYUCYHmLRGKCzLODJCyXgoE2RUkWZFXa3+7Ek9lY8rTAEJxPty/wf47NgMwlsWNN4lkuwfcxPILuxsdlM0GxijDnhDYhzTzLgip8/FpAPJSCtmggFjbJaKsP1tic8HfpnZK+sY5ElFJ65Pp+8eswxYZqn2HbH5Zp3Lyz0dj+/WoRYjfZoYeVxSbCSaP+amgays0+WXHOeasG+ejnK4tn57uH3LqbL8DgKVJUIk1JmLCrH3/t5Vyij4BZQuq7gsdKgrsQNlRWJyXceRqmixQBKA7zmcAU3tKbQtJnLI++M8MbSwot8xi+F2NS7ed+AnfacMfr5cqM8yB+9au1tHm625toj1ZBftMJJiZ3UMaqsoOV4UoyDSl8XBFV/7+Bw66MLOLpcLYfzR2PxQsft6e5asSE30hNUxSypFkMRMQL8oJ8EYvIO9mMS5ZoKR6vCeIzzIkTLeoljSEVIELGeBuk4MeSgQrqaQWjT3yO5K9ua9VHQu+Iasua2zWEZRoqohpX7IVNzR0TPiBbgUsu7a5o5Fg9ilTKydSRNzBFmcWv/G5ShbrLCzSjS5AjjakLJRmfyeZ6AgYIWMIFIFL9PXi1CyApsElTGdZuDq0tTy7Oz3p5eEGOZWqTDkXE139t8tbcdlathZqj3nrJsVZiEonJlc/tWFLO06vPUBqPhWb+FCV97ny9kXq/OLnhGGgy0Ux4sFORBBp+f43XPnCdVSSi3Iq/phYUXL3rlvGe9YkkknPBlnUpu8nvReAmawMWzJ2W+k1AgMKWQ5B3LxOF7BZYrY78vAqLKQ8+ASej9/qNfwPrnV283xBjDA8UHdJarkBwrm8h3Iw9LhyvN7uVyj5b8BBDzx3fGOH1OZv6CZltL+StgGjpNq781OOtw+3n+bn+E8eSHrY1SS5J0hJBp0Q0sGnTnSoZQS89p1SnHPocamYO7fFnC/ff+O786oXRa0M6XM92OTNZ+fbhQvsV7X454FUnnrIO9v/82MYN4TXaMJCuK1/0YJ6aG6hQjLg208W0cxdycDZ9cvMw9FIUS0IyBzdEcrmB/nUuYBMIqdF7QJZA1LJ2gK7njcctPAxLwpUgvKCgJXumpPJkwyd5rd7mz/OLFan1p+rfZ4TgbOssqqlff3TzYjZdLJRAuuugkqZlAdjS9UDPhSI9Eo9iJ51wRPm6AWIXh1YRa7NQ+7yz6hpqi85FtByb6e6q0s28ZZhD5FJ1iNSFabGd7GeaoUWu2sY6x/Q2rwRnA1CMmES1U8kfqcSkz9DgpNanz0MuYQT8Rz6KRDGlxWqU7f6LQYAxFBD6oU9J+yrcmQMMwgQAcZSaXBGMx++aemIDsDMHKARCq8Go86gYaU5xgbF6ge3nzgvODqxgdlQxypDeLm7QKfCIIkyJfsGRgaYyqdcoX1zsrLz52ys2S7fePDZGCTYY2E4YXjyTjfel53TtO91qOrl6gWKEb16DM+hiT5f6+vLJYL2d6J+cTPOEritWrNlcw3INUmIEOcutdZFXr5hG+6pt4q7yDRbGirKk/fzm0OYkgDVy2vt3JegxYNc/qii+fQc4SR/RzpKbYSJVtFR7aSlSuejTJXj7Bsz9csboJI3DJHzHDISc38ZRU1VDzcHP3m0sB4QQo1TGevMx548DglSynBbLcvnvroz7wsDTHV3L3ZdLv4Ddqt/gN9L2uzxP8xhjeMOiQVtzb2tEMh2O0tbXF/E3pqHR9Po6gfCZHxfkBomB7jvJnuI9GFSgIXyU1rDSutnpYm2dHXsWwDIgFu24iU8PM9ln5n//lv/hvPQ09NnjyXP/Jnd4ffx0B3aGsvr7s12tkGnU15m+9cn/eSjcDjAnp2n1MkWVFzJNMz44l+VL9+yv3+32d0qqn5fuw9yBn7Y9GIUGWqq+Mb4e7KW1eoeUQL+U4jk+qSih3XXOqdfhvh++HWfgljrEY1mqd/jam3NuHqsXOoJ2TS0YMULTzoe0GirzKlxNa0zls60boK2t+WN/YheVeaWn28bPZ1SEyh2LEmmO4PUeVPUdAVWdyfovkBkonW4Ph+R//m//1/6n+JhKZl4ZfU/SQMabbdP81x3iHyqKNvteuxylOb/7i+o7WZJTs1iyG4cLpv17Ptgr9aKhCf1ArB2gRcvn29V8rFgY8vxQNElDq9fJb33YVSXWLPBmng0d6WBFIwDav8w7Pj7WX86IWD2OtCxenVDDPaSjKpTrt5ZFtZ5Q46ea5gMNs22vD5hkeTPIMXkiIWjLrxPvu14urL5//uvBiTCNnGkoIa3t6825sVWwVzc3IXvUjzU5lcy+lGeW7/e/oe++IypuX/RqTK7V6mcE1JgKKJeM6inFO7tNu2ZzMkwEng9kegzcp5uLi9L4svGIGo1sHO08nLb8pG4xgGCMEJUmECiKYY6ygPaWPVza92ROZOYEIBWf60VPIrSrREB+9xvmylRFmKJ53lgWFKUE2Gc4M9Aw9GcEeI+P8bjac0PWexTIi4Hr7b7e+xeWOEKdAMo/2IEmq40j18+62lJQdxmaFiDAgIvttvNEGrLgx8gv9LxISelJr5x5CJev5sLexHzHU0gsnx6eElfYoJ+Jghq7M6sih6Mppp9PIB68zpcp5plHJevbfprBiAdlYtW7RmkqJAq5uSFJCjObRysHZw1FUTlJyMtgxDC5gyKKQWrbnt19HkMNZnsUwERurMhiOlWNVujphmQorGtDJhpMmACNU4PjuYhRQZvFdaq05uZ5lgtEIuyrK+aIhnmQVNqSox3RERdEiQtcowt3mdWKsGosoV2tNB19V3n/d+yTOEzwNaM2iWc5fLJePbnrH55lWtZh/6PKsoL+PYjoDecm2rlERTS4Edw8+77r80IRAMHg6pCbH7/1+615ChH4j3iFpLQm4td+9nzqn6R5frp2kYwp5ZAGhOFGL5yYlQgIZ4+cquRzLcFVl4vMvFj4lzmDvJF2qEywT98OT0971cF19SW3rMnCyaceWFQxXFDopXcBge3oUsxsArSlzYVQ/iJneyFYKRTGtxXBcoccy+KqCIDHwSrhWy+GB52WOJZt+EI+xhEtW7djKZgKr/+X/8e//Xea5wF14rJCQSCa12pSjhPma+tC5V0EXZQVQz+VnEfaWS1NL9Xzm5CTfe/5p/4vebGec/FEwUaPzRxP5o5FKmeGsCODm/VFTCRUDEh3zTuQ0h/6onP0tpnO9zcO9NbQoSvZYDb83GuUFRzngZIg8+GrOWXWZmhlrnVxmKFzj+WyUTxn5Mrsmrvn568GXNM4uTb/wZVHcrWPt5ROBBrZP+dvcg8e1MhkWje7p1en60KecZh4gP+jZXNu0bC6IIALUhRaAXGi4u3GOr5AgUbSY7dZN6uglnsGcZWmcU/fk8+zEi43zU6IitrlxfCWy0OHqu2RYDReiwE+mk+Gmx/XkRS9LCyQFd+ZON1i5W6IoRqjnurVC/fzq9OtwXa1/9T6DIhsvqhDqkwjKBTe218ZijYsmxRnlpCLUQqyunmfv8N0KMLmL4skUWhspd67Tuql2T/PoeYDUz88nFSmvFhNq6cwvsIA/yznWOwpFgWdwhjfX15wUa638dW6y9lD727rnnRKTWeDw7cNc+RDXrWlB2u3nGYnvfxGWy4SLz/BILmNLOKib4OOX6es0d6wfEycjPQNWVvv8gMO8t11fhkJRJVFM7O4dPMU4Z2VhppVnlQjGZisEq4zhWJxb9MZMIchxuF6D6sxglQXWEpcQ6lNVfaD76tdp4qqoAK9vfJl/ULpnH8WABrs3Fz6EKMDVL1P1Wq1evelmze255y8KR6e9owZCqqFQ+jwKE7rSugflRmGMxJUC2fbcYrdEg4jJh2q49uGXB8ve1XxGVVoX93DmX/c+aCQtIFQv6UBHUUyXYTROSX+/azSyclyi2rxG0Gd0uNJt1I9q5T+txRtjPzTeEJmQDk2HUX8wUCxc48zUDLL8uEqojzYxE7i3jrVMj6enFqZf/PoojdWGZtjULzAK8gZ3P29vcl0U04NYZ1QFZhBsJCFSwiQ77VuanZuzq/tDXSJRmBqrHsU7JUgSlqz3fWiRfrO1tisZECSCpCZUTSPByoYQFToKTUxUoGCewZJY2oiDpI4Mj8hS0bd7KVufM/Q4sgx5RTVC2D5jy7CYLpeLlw+gyVBoulkl9t4FUSxJf3mgvXmX2uD8X57oMLPEJziBJQ1/sdIZ+iN7tcdNgoLmMQPjgKNy9YtO4+SqKktbW6k90VBY2ou/KDe0V+NjBYHmZ6VjvK5Oj8+ahVwh/b1RPe013m79k1PxmonS5kWEoTSZMSQVwPGI3iMKmICGBaiUmShUQ3SDDFmb2992pZhq1hnaCCYYceQlMae6f1D8JEqMMcMLN+iLYPKHzPLup4l1RRrDL0pre59f/2I026V8K0MFwgJ6dRn7MpzfP7vFI3FhU5iPa3oMqG0FSKn1lK91fNPDWjWeYMeqsQb7CM/RTJQVkWOvBFlVoWPNcW81QjKakFUstyyiP9OmyHm31zch1Z7Mw2qJGscTVYisWdGxZlijnstfZBvdm1JdM4mLE5i+9L3b2FDA9Q/5wYG+vfhh92Ncpo9NToS6ftq6LuSbmRpCfUDUWGlne81Wk3b+8l6VFaIgeJrAOkY8v512dWzFAqCDpTij80DoChpUiyf5Rdb48K797RhFXjrni9K8zi/y6tWkLwsmlCCERQUQbPP86nWSp3mfqBYa+dH8sgoYY4mRnZxGlnB7e8ObSEJMDY2zHwtOHZDTY6GargOBMUyuR8KuvxRQAE+McSOCHYcaty3/4bZi/bXlFzUAeU27qwbwvO/Zb9ooimkjX2ZoyMqq1s0T9+lJ52qsolkP+6/84cBQLYwwUvMHRXe/thQzGAhfuRidk4B6jdZVM19vHhV7+J3dvgfD2b9r5dDi3nrqG/f9ltewgEhLpPbx8G9ePm7GoYqWRu7cm1AVEj258jwiSorm2UYYiayGQTYCg4bWw559kBMpdFZuWX5O1mnB/GMiaovzAtTQD7pDosV2eEZlA9fnds2jlDDYOi/UWQC4MCd0Db+kwUL1N661gvevgcuwRKDTVjO9PKHn//p5ESZp58kj1e7g6n7qAw9lBNn0a40hgmGVS5rtzpIKNZCreTzpLxok2LrDIlrJrCyHAVdjBXFQmxa6Kd7eC4wK2bWDSb49SAly0BA0IE7TrGvr8w6ywE89kzoKgWAhjWYwX5y89yz7FJpAqwFS0bLS7lgXlAGTT/d6SR69FQ/RLLyJyiAMQCEOCI7PZc+uGrdUUhpasfnqhURyRhfFvyiEGavFG1OyqUm9BqNBunRnVG6QpiGAAhkgAdxZ/rz0DM+RxYZqpZmoDsFw9rNwaz+1dHknz6+YHLd78GFz+9bVfu2DknpXXBPnJU7EygEW6Bkubo73KsFvpYfqANK0aWZv6UY2BdI0Z4KihL63M4crbmoaofIM0Xu0d7631GtXi79yx9d+wMAM4EhA8Ne3cA7UTUJYRAa61ErXcq/137iVffffd9lyIKyhu+iSIAagaGlKj+JVsypZB193xqp0K51bb7WfKBsAZMNQMyLxMKMEj6N83KBpg226G2sbIkmUPKyiCcV0z16Ts1FAfr97Fu7aR/2883j1t+m1dj7/ekuTTyo8oChRZBmbZZKBqauFy1nTEBO5Rrm4KtYkMqETvX5tCw0V/L0RPVIYYxIGv2tCqNDM+4ldVsm+2vnsGdWWljSxQMTR+rMAuJUPRXOE0VdMmEUoSBDR4/YPvKsTuRgU49xez6oIGYQZ0v1MDa0is1u8vKWhoswYq7pEVtHpbNrjuhVr+CktghbCNGTZvXUU0x19uZWJw3WpOG/l2YdjvPciVObDqp6/EyFzCSPzpyyiwCYUhTxWGIVWCLXYaXePK3OPq1Pehdn6KEffa+O85HnvpNI99j6q5grnx618DmfEGijy+jN+kiVMmUMjKSK/cJH2PLiTF1VMIJKw9+bNwT4nHx+vcioMmwwXdeG9kP2z/gxZVqf9EYXwKaYIN9wvn/aOxzoSGFfQT2hRQmuopvJm/Y11Kx8qigguYEZud927envvC2jJJmhv9E0KzYJSnh3WhuPaQ0WvocsQ8G92PnyRQrVn2kvh8t2P0YSQevfhCzefNKTsWT4fOn8VIYxYo0ZFCUZUvMb++701cmwGX3DtW3OErIoSNBNJTl+klQDB9IrZ2p/tQYOWGG+YlrP1effnpVldR4iJucjHzBgphwazEMk2t/ZQzB7jStBkGfP0fPuWZfiq+YmwBAgdHpsqFRWtkNGsLDnf63m1KMiEFoe+hBbM3WKY+3k6gmZMk6Az57fq2mz9VUQPcmN9IX60/BDyXCSZ9QV5JYJ7hVUp3rP9j9qNUGUlCknVRgXYmp2VIsC6PJrIa9yZaR2tDYvlU8tjNXEclyjQyP862Pv6h1pprMz0hhNcTGMGugL3L5WjcqYOg9ezD6Yn4v0pKbW5tuiowUe8tx+ZjlvddX6os3ayDxWECkDxHkXZXWq0Ae8ditLVk+4/wnvbXJBrwAWdjyoFjCQgmTmM+Uf1OKyfsxI/URU1G5XohmgrykbRcTiuLTKC5aDcF7Ym3/uDthYr99KVf/7X//k/2ra9IRnV0Q69JwOYTBJMup12izKrebLtZ7VyptPKnii0H8VHg4xnO5b9UX13C7mNPzkZ0aVWIWv30NDOvo91UaBkuDTqojDy7B8sWnUrULhOhIVYRhFjLK5uWPA9n5ux1dHBUu44nzk5muDcliTOSO31lZmjbkJ2PuUnV842lFT/Zf6WymLyi6I0oaUz6WmEGLWae7razV48evlq0VRLZ80ayNy02FYum1Sr9aDOq2Ijf0vLNPnkt5s7/zadTtt1i2L1soa8pEmi2Lnl9Bh8tbPJUwAI53fkgCbru1efLD8a6OsYMZadqNK1c/RjkXWu427s7HNY+QMA4LkBGplQOzjVoyigR77b7gQ4jvqwpogBEcjqjBgolscVsAf7a6N7J7uuBbU377a2NPSQa4XTrXhECPCqmrujIjIm6wQD+XT5J1Ssd/QZQ98bpoVh3wAvt1/CGvXG6WREIChQYYxR/GtCOoFwzl2awK6uw2CB4f2ckeQVazHzcLRi+whKmwEcGitekMlfWwEqAlSlVSo8zI98aLE3C4QR9ta31rdGlgEAWGhd4QoFlUG+m4OprY190RqLB2/hK6Nc6mvVdCHOcq1JvGF0VRXSKF7Onc/+9tT3bKXWqD1a8L7OGlvrqS2R1Fkmu+ibXZ3pkcRVsXTe6WULg4yYK6FkOnfq+v7unkdPjrGA5nSGHVMb4t9VPg+0l8bVrXy3Xbn2QU0G3IO6tkUn29ImWIUu9t+KG5ujZIKISskfFWW8xhwLEqUYrT6zrYCE53Pqi0apwydn8Iod07m5lzqLaE1CGveT0YLQiJX6ekJjXJs3nF/087YSdaQ1nYHI/R6bGgM0ijZg+3yIkBWliiDB6N7JOYrKhCEjC0erR7yfDTNhAmocZfeufLW1HunKLLAZudOWQXEWbcbv6wVnP/mO2gc/m9REOKzRdlGmW0Q7FCs0Jtmtte2NN4I1ws8kNMw/+16GCwcrzpNZzXhVKo1ymsN8N632LrIsoQYfP9/Y+sLKxNnTV7WnQ9Q3GGfXntMThgYgPH+RiEgWYJueXy9++2N8JEmoiHKY5UWpEPdTrAY4/q6xshk5Inr/WMVy1B1jJQEzoanhaYcJHOtmIPXOrYEqbNqgoql/ej7Tuc3n3Ke8tZmxtd9vYzO+lq7RvG4UjA9v3+wKrfatKIYuiiwwPqynfJ3FF48WFqbq86sPPIVRB6Sq50Uz3VxUYtVq/RrHGgMVjUsjSjftW1wBWlZqlzeiFq92bl+NhhUWBhXTMIu0xtrZ0uYdtQB9VP9zNTV3VJ4OOJk5pzacW+AtWpMN0Lo+v6UNsCtQQgq0O5n0Pc4PkabN5GOvkTApfaDYZ4rfcX+GubAfYX69N9DWujd2xYkoxrDxc3ZKgCCy8XX/Ww8SJXhbXZlE37u5vWkm2WNG4P08xs/FYf0gnn1cO9xqsLTK0z/ZB0PuBO/wVqEIgpnm/KjzA66Fp5vt6EU2DmPFyWjCr+xtHnyhbzOBIivoN7S0/y7l6/OTIh+6wDZn5AedLHwLK35L9ZFSwmKo7orJaSvTw919WSiuohhn1BOG0ndeYx9a5UwqoodLCSUpXJlA1QguowEKbm+v7kgQUnUU44Ag6ORzwxhH4a2Dw22n38gCTwXYuMY4I0mwPDHWN8B+8rGmJEP9rqdkgCzEU+tFnOPL1QAKntyD1d66xl2bRutKhqTdn8HBKpZfLkZV6MKMa7MpcaJ3oBbujkXlO1+3vojqTRCyYWiaajNfPj9Sc98zOTvrUf66vaWRVcNM0kNf5mTw2wg07f5ta0eSr5/KIRFmS79ajGLc0m/QKMgXTZ7QmraCnRANOMqXxfRwnKWuw2bEXFQMKnf9g6qkz9XrQrg40T3Y7XnRW5TMZJaQWSok4Mq1id6Vm7j/c93RXg5XDsHGLDbue//2I8IqbpNAfiFsxuJjyM3szbu9HxVoAakQM5kQ4XK6JdsdJq/Pe6uvPVOX5V7ppnx5kj867l2NYlgOoevlfoQbZbUIwBXNhkzomkZyxPi9lVv3OmvDG5dNEjkc4Th0/r3bIE6uM/WgaXVDBume6FP0QGA5Sy7pWhQts2FcpiGnQ9O3uzc489vvgxEK5oINnDFRFZ4y6Vb+4ciXjXdqncz/vhihESiH0Wof6y+Ka+JkUkJ+sDlHyf5x5nNjnzNUN86nDPAk7qeKlfM4HvQ+02K6PkOTgA8SQjRdLtPyEF+VdrY5WGXjCl2/oxZvc+eja6C7PhtZBtxVAAiO9hJ3UQhe/xJGOKk4/s6v7PxgKXT5z//67/+XksyRUJbdjMZBq3lvv5E7KiJPRxWRnk/bT1u1WOicrp1cZC5wdunH7HC/P3AAv5WjglNMCfe+O337YXOiQ+yzOImMfhKIw3uxrt4ry7pTV/5IJ1CM9DwGiqGtvQ+b6aEKnbWS1MMmgWK69KuG7h/wosoMQYJgiZPi4Kp5Ntpl7xBilGhW9Nr1dC6dEuJOZD1Fq8pe6mQbe+exWvjVsAax76YB3HDbncSGvpumROt2dw5HMdivxkpbeP82RuqdBAI3zop1uvn52YSFc/Tl9sxytwRNCXfinUmIxo1TjXU6qMZKp+/qiukw+Yu8U5ExtFd4NLyiw9aOK2HGdZt29+CHO1N6GncPhjBAc4nLNJv0a3rd0QaMMkSCTLELPIqtEsCc7Cgy3Y9hT0tobWRPx/fCYcUnCTBU0IIk54o7+LnfQ+Mz1ov+b9WY3VMxbEVwhtdek4XqwA/OG+EEOOnEirc97B317MejevYJFU2t4MddQBNyB2TT6Usn+y8RqsDT9bNEq9+1OF3u60W9uJ7ueiwCwjUmKHgjgOFoL59jxeAdXxTaeru7bRpqFedxlhIR4Z6KZr9i6RfN+hJa8qCXu3q+3Ds+Ou33Uy20K/mTXr1W81Nnjai6u556dk+FYMIwoKqOddNtUQJC5uma523qiyLHqjV/RDZx92DiJDvygxiZu2wNZJFC9zYFXjZwNfRqX+mE1bNOPvQqc3HRHutGgvn2IvI4QLnGDbCM0wk2bza6lcJr0mJVzBSUTBR5iUli+Lu7+1tb2jzRvzrWNSIc07m7ukbclAgtydNSKc3v7+1+bKGrYqs6Undo0ShJT6NJwriuAiQ+WQyYAT/WujzodzHq4K7Fv6yMMdu4/jceZZNNNJtR1cs7cXfXzobXQ+f/3f/1f/6//8N//NfeXciN9ls86F5Fa02frBomZx28dN9GyLJowm6nsYTistTi9MtGpl6vtPJnvMgQ3au+Gg2rpB7ijscx3M88GmkKpXa67GSlSR0K4trc83ctW1ur51vXdnV/qNrGvbOOxqv7+TCKnm5nw3e/WqrqZuxGnuDPOuKO3Tt5HsGTW797q6uAc++Pvf6+Hm7H5UEW3u5yoxT/q3/5l3+XCd8R0yGMJPOMy56F9OUfnnu6VH0SLbp6k3ajeJ/rvZrFbWPQW52oZkh/PmDkGrUMFwr+I4yckNrb3UwUGyEU2tayJV6/bDR+6JSeu0e7JfEAmeGJPtu1bK5mPnjxatGSaOT1A7rOOsjtJ99q0HfLLwcJMdhwEIXT73oYL8zbveBuBvyG29kLJMV983n28VkkMyFS4f25sa6ncYnUcGcAZ6eIpsDinfL8+dMn/fqU5kNcWXw15gf5GIoWXUmKEC2HU3X0hI4PtStfej2cBSgN+6kGIfQBXtWa55wyb3d6HOt9d18nkzvUpOkB3jjisG3/E7yRSIYl4IsHBU66rUabwBtLMBmvGbQKcUd62yLZOWsUa4x3d78VawQJdqAcYL1MjKYwe+lr2b970pnsTWr3gC0dbJuyNVDgYD1SsehGxpnW6axyiCLNbHlhUA+rsspVgozGlZzT3e6Za9bKnV7XJnIEq073huHKUewuKKZrcL4GDLYXsQLW+9VKGI6W+M86mE2oaO7a+1r+uNOtFRrlsT75bzbXN11RmnAUzrNsEipW7of+hH/Wtxbfe9/s36GBrF1V0+b+3s6zy4FfaEbOemW09+X4RTpm6lB7/tc1JmtbH5niUa+VlWRBqeX+2jsP9IQhMi4S1q9ON2xDluhhlUHOqTLoDjhVzo1gvJwt4fzvFB0m7GodleZSn4vOHpRJQJyOcLtC+lXcx8a3bkmJHMMzEPdwfuJ6/bjfdfy6UfM8eLg6tmINltNk/6zJcM5ZM1W7x2BuYvb/fA/ScYtXpJ/gzVi4Med9NivLAGaXVi+w389fNQVDUtPf+wr2h0Yu1xLTpXokeO+pAf0n21alOKh6O88POzD06wigqnBrh38f71kNg0CeFxjoxGX87ucUjsseLTx60Rk7D0gJ0QrtHc6CGSQl3q7R7neX9QUkqBY3tjb6PTOdSGQyJ5KxI4LSgEN2bN14J/wEaZIotrLP1kFXASC4GsLtKqFs7e1vjRBFUGHAFAgzaq2VQftI6/MMYSbMUzyBr6qQVJoTY0XTIETnCz47czFC9UvYMvygulfopMYYkki0Ht/SURTbL7HdyAYMC9dbeZYaXZKQsvlrfzLUOy5IgI31rgqUEcs2fJ8O1zt37MHi8S++h73PXw82+Wq3dFkqUkzw/PL2TmlWl18vPMxEgZ/j8/mrarYj+8lM7yYSSaXcd/SyaFYeRxJVFC9sv1mqlcvZ69JxZ0Ib8Gq4JseqSEi//b2zMklok50BABmJh70hqOuuAAXbBOC8O7tftHkyDITvE9kHPEecUwug6H7olWSTOeL1kEjl6uX6+XIrs3zHk+0zbjoo4ovxca/dJzBWfGLnvDi9kiXxW5lUWIzkap4d2x815Wrz+KKfl+SUhLO7B2vS7k6ZLQ1qEAb9Y4uft9asmyohB+mZZNio9T2sn1LNOclamzzfapaVjMn80VKnOlwbkA0a5aqtCpumKMGXwvU4NwZQWWrsLBIH5Ua21r8tpvt7IRHW1EUpTmKeYbxKF/lBjgd5IhIN9yYUC3dq84bdotDvurBqt3hiq7POz+/t4XxHBnCiun9WpSmGF1IT54ncpbC6KbtYmcigeQlpuDMeW1Pk7OA8EWtyfnFXkN5Fdk4zwHhXEPveRTlOMfmJjgQoxrmty0XQXPUcvt2ErdFI5rJdUqhf1iixEbvdywLf61gkKEHLM+f5VCHVsFicXLFAV1SZ8zT+ltIATetcRtZK3G0F3Q+KFFv1174jTmENgVJRrPEzPdliOe/a2/2NHn4rXpDydoxj78F8No5W+23tlmpQGgG/jvKhooW7GN06226s3whQtYuJvQ8aJYRyD/a8RgAYuloYq12yEVRs0sPWMHJzmQm9pnG8SOhcfNj5/9YcoXttfOWKSbp8m+fH77zl22RkqAFQuPS9nn712nU1Oruhd9w4tfuc5K9LzsppnZaXV7wPXBlDaR3dU9Pq9n6ze8478ZHOwvjNKCO2s7mx/bipc7Xl751bKint4F3qtgLHHqu2amqb7zc2m9Unr92PXyzkRzqK2tLUAzsqn5/pPfFOT7kyplCtdO5hIYqqXT066Hhc/GhR0mKNZ2qLp7e1TG/eIBBgMF7kztA/D/tQUSJt1y/4Rn0h9EgEIWRulkfOTSSU1qTSGDNyGaz39hBMwwDBP59B5QYwqa0Pa1qrPOVdmppaLT1ZeD09PkfeA5sroBMUBY9Ky6+euH2PSu2jXvbmpvwXsVVBt3lRJzrG6+ro9Jaa5d2HlCETRl+negeXO6zCDrN6mJjya6KhKOK4rn6Q389idYfGtSdzXgkgMih6Mimz6WeMoMYFtOs7kJvCS7zCA4y+FHZzC1lgrKCbzNH36yV3tt+sCUnurrPtLNbaKY36Ix2PqWh+wuZEU+/eoIheBHxBNOg4JyRY7s5MDVb85u/ADD9WkBWDd3gN5L9ILeLtn2Vge8nnL//4rcep0bFeQwptWsmQNqn6AzpDHBkMy06ce7j1UR/0r7ujb0/to142WH+236fXqf81bD7H1rebAYvCOnNdOe/kx7VqQmpzY7MHq7zBsirf9+ylbm9h9rHnxYve8dVZZYRjaZGkjX5tuMMSn+5GDDUBQwU7PkqwCjVi5Da/7O8b8txERqzo0dXss35f4jHNtqBSvCb3651vSG1/P7UmhK6Lx40af93rdtL4lCIvFFU6RgDds7O2Gy2f9k7MysnV3b1K+nVA6PubNneN6yYqcIy7VgXeZJE1cyqLOYISaB3ADfucqRtTZ9WRNRNVgwfinIOQKyoIK14RmrmJc1sWgF17yMXVksOZj85OEngDAtetPN3FTezkEoGw04azU/DJpMd+XojSbtwVhGidolixXst2nc7DQtLU+5kp3BUklLtGAWA1mz7vn5yFR/Ko/jjMwIJdRz/0+/tb+3A+ZiJshk8LimUf4avNibfqZ5fw+YNNszmawVF3jklO9cnsA2++l/o3bzGfs0KLdMvRyBWP+hq508bEW63i8xaHqw55eqnPJDhV549wV5CKGdbgxLla5VmD5Rm+RItGVkiWomjlDDpa/3ZvDe8dXeUzP3aV73/veGfLlbvOiHSiNps3ax51S7nGZLfzJVaXQ9qgx1Hd6ZvXO82uvPx10ftgpAlULBnhdgL0WWKPxnAGV8AnsTqnBfXZ2uJ//y//4T9cLXBqz4UWgwV66bf7H1mJvk8Rmg3ojF5rni8ZKlB6U7MnV9V6YZBBOGnUlUion0GgVA3S1fvyZeh7//b0/Khfh9tt1wpRMabr6ePyfXH34FwPQyIVgrzIL07ubkJ7s762S7faGUdlgVHfVL9r07STS/Vdzo1UJXFeVf3KqEPs8Oyk7M3k2UluUfrqHvSSyjgKyWy9POddXnQtDN85yviTPzcL/7s9C6/ECKHLUsqd7j/5uHUL59yjrpwacLnNttxtt/8BLveHfhTXg34UxXv6UdB8PIkrqQ+2o1ElfJFdVtEXGyheSISTYqzAiAQU9n/3rtaczmkMARKukCjr7n4f9YHKou0Z+V894I//xCwsUHBz+6Cvf85c5eeeLM+6ZsbOL9vbSn25xbmNV45j/1u9nCI1liXvQ313Kn47+YwSruZvOvRFtqWivR+IltLBb183Nlv32Q07R3Dd6+cIMBsfMxSozj94cp9qSDLJBCWI9Qkv2VYAv/F2/5/GPJ0VoA0Dn7rlVOj3+9hw0J/501nAc/R9FGtUOpUl3B/J/LD3dXjeMU9oQjTrzC96Z1roHR7ubApWFdCUIPFHvMTSURTR8xopjPeQdLyVc+rHihFJAlm0ikOPM5r9/sqx+yN5bbzxWotDlgqwF/NOv2u+MsKEekLy62RR4oFfS6qgVTz5njEb2XTuEnec8CQBW6f5RCIscPbpZgbL4Nr/kLW3ifNWdhffH/UqkIWfv26/EeRrmzVthXvWNdW9whb4AckksubW1tctIckQ4q13ZiSO393eW3NUYVCnx2qH+6qS1Nq2OU+wYLKK0w0EQlXtc6WdL/qxZwggCWtju7ivG6ouK0FmjKPgst3GdR8zMDoRZX0Ma6Jps61o8zp7fl1tXnpWPb6zbLrU/p45HdUv0LVWptNXlQQTRkj3iqrOoiVi1+CP35s/+vHefs+QeOrwcJMjuVw0Vz++Ulul9gUZjLYoNn48cS7eNJSZINdzemjc2btjPKfpKLr7PqV/qmmtIBiJoM5d1MZOWBhXs0xqA77f6pJhn6lnd8nAKrgIlHF1oSfoN7X0H0PGBp+Rsb1WkHCmxuk48YxlDCPnqGgmOqUf/9f/97/+T71pwiw/pCWGlxJqdt45z5q414feZb0f+zzPzKTF1c5LdS5Ya535k2d8plTPZyS51Xr/ZXdNa468Vb8biZ1dKkTQmvRpQRKfBOfStckzqX+1dg8wz0CzGszYtQ9DKzrKHTs99zz4asZRGv/Z2cHBeIKYTRBUuH/CwifFsPAJC0tTK1O1AiAAzgIsQKDmHcV+ZqDYx2dVDMe5kp1L7e+/1WJVWlNU2uA1heLYcrrGAoRGxmvw7a4CF0pAEeSwOt5PZkWOhYmQOtTmYWYMAALzBkP9c1815KiVnDO4X+JsS9OUaO2vzzydvPcHpVM/J9Js56+Or68vR3zOWqz7KqyAyM1QMWhrXa7sevb5pcE7Tw/euZ0w1EGXyA/2KXKsAgYchVLj/QAYMV7befnr0m2W+I6Twk5/PCkM96BzfZw4Kezc2SmUoGisgPNWvw7yVsgfdc8nOg83U2u9PkcBWNOu0uUI0U/wpDHc3aMzT7MnzokwmN8YKee9EVPTxxS/gzPmCpgpYtAu86Dvdacxr9L6LRvQAOavJisTl0YrRwGJrfcHa0NllztuJdDv6vEKAg2GQT7deuudOMfTHslZwwgSGq4rR9/rSyYFneH54+FVez137Qr9txaph5UQnRfYoGWFxiojQltvf/I0KDLo17V4a0Id/e7VIk+hd26d0HyYFQMhcM+Z48MnJ/YPNp42mw9Y5FrrvilkCev5q0zNOc/6sjRiioY8sAQs03D8/tbB5p5I4pMOFiejCZqo+pW1dxuDCl8hHzZ7EZCEWpKr8YGGJFm7e1i3OX4myOQXTWq2+/cOzwIropG8MSAsDNXvWGGlLGfnJ05YcPGq1mNVA5ZkkcgSwAr3UgcbG/RIc6JJd5xZnAyb3KCSy9Z84rj7iGj90I3kjizt6uupJ60sggVhoF8ucAG234Hwdpc55I98YfRNxVE3oX7n/8I04MLqlcuJJuhqUdzb9qxrZNVvcnSn9p9Df+VET23HtlsWZHQVd195WcOniisXzR/VLP2RRLhdZRmnglsx1g4PvpgQjL4IEJSbUlTMyNm+zFEaP8K9d+w8+1+fPItPnZ6NshAruu84eXbON7OSz4isbO19nj8SAuqY+g5r410Uz0RV6KjCHmP+aqxjzGAPqqmv23tcEdfTOacjdcr35ETu6K2UHfhQrArLTfrQe3QUcZqSkAk9+vXxi9WhhgrGfJnc9eJzX6bOMPnWX9fxlZx+MkLUiJMQxX36/OCM9btG466zRwcdzNzTM9c+zyYD8PlHL2iDHYuARAWw0sGnrQ2nR9nwBOeWrjCMF0UT+Gys/imuuH4f4Q20nqemH8x4px6+HJ1PN9YvqBlKaBo1Qtejc5btvmp+WtXwW7lml6/x6YQJy5+HwWv3CIviTPpcVNlJpdb6JzjfiIYgUCEZWaQo7mI0OlkJWYYJDIyZ7fEeOECXqSSpFfCZPkM7+RYFdVrc9GN9LO7jWmhdVYsVnxEir6rlyhPXq8UVUieSyu8vgT9QK1w2j3pfVwlAwm626PvloeVLxkIQlOqV4+ZpLruQ1GurikQzEqm90Y7RaARoVSjVe8dX3/O5USX1eC8aVtNYg8ukz7pYbShHYgjp7O+hCGge6zdGmamxWXi1uQsVPXiDT+44v8zVOsdnZ9e4EtMDKNqztf9FJO2e5GOdecZxzsuAwQOe1/I6gQyVGvMrPX+IVFRBPPic2uhFVUaWWF0fIXPFn9EMb2J/68MHV8JwD06hyoCITDA/dNGnoxNdm+IKLFQzhdJzhEU16/9r78uW28bW9di226Nk0bJkybSsvQCCAImBBEgCIDFRUrvl03tn17ZaltS2BkukKIqmKFAkJVIDhapcJJXXSB4gV3mAVKpSuclwmTqPkKvkKrdZCyBIgISH3vukUqdyboUCBSys9Y/f/31SsUwe9qrNlqUmcvB/8AUUA6PVGJ/W8b7v5lpGJOmbYvPq8rhonQA568ESuycFKQuH81IK14gDntejdPoqmhuelGEGRIuWBnN2naSXzd3lgbqKdqToQCaO/J3HfNZUaNnm+J2BGVBcYlD34ap4uhCanbc6MsYfN+XTo322IPNglN19lMNK0iWSIuvdu4t3YL6PR0WS1MgoQr+j83vx1Xv7/FdI33mucvy5c9I79PhfL6uPDsO4m6I1jfiC7jCVTqnF65GYIvBs/cNOzVWCC2YE8t57j+dOj63gtQrmsMoCmR9oRqCrjfIPjn7KtMPHeFwSKE3lkmWeOAZWDO4jPqF4mFo9yh1e/ue0oGIun1twVdyHwPHzPz90GfmQMimKc1w0WsM62g+/9uDrME0ygREDC278/I+DTajPV3/zHd1hdBVFFFS1eXqPYmXxADAYm+ldw5hwZfo3yZBNnSnJMJdUYkInWihIVP3wkVa8QHxBiGvoCuZWoHB08cJX3drenP7IE84vt8ZYmj9tLL81ZJlTdbtep6A3qpxFENtMo2qF9/fWjKhsCgx8YTPF1BKaoFFw15HnfdWts+b4pB58REoWcr6eJq1wBKupg75Vfc/JQ3kQE7WoEhogBsUsQP2ykbk2ttDANKaFkZkMn1l1+3QaSoO/t/tfuU8BmkPInykNcb/351LLOYRISfMp7HfzIx2XF0UVWWAcGMJgqqI98cCHWBh7Zp2Df/6RlkXfBDfSD4W5VUmnPHoi1lg9FiTQTPpgmsPsWpsb7zdojRzODiOuoWqgph7D5GleJMKDPvv2++lHKOObKtL86XRgnJMGtJj8xfdGQwUNm+MI5XT+e+F+2l1ZdtiifCt505rF4TazttbWdmz+q7/4M1y5pN3abFEzktGkFT4pqDB3LiFmLdBqtQPYhAwjL9CNZutelBCwG4lZ+rCycGHzi2ZEFVUSymIek86AmNKgbV+ce1UdZ26RxHKK1+qp9ZXVtxcNs9luj89oO945bBhSm0H1K4MjMnyWlgPXGeGCYJZaSHMUyoACeyJAVzSU4xRrZqBumv8bZeWEyKfAQLthQpLHGZ9iZBwGZXLX/r+f0d4IPQ7EYDTKk9kCsNb3NmdSuMoKqMObp9QkFqgqzjj1OpVgCO2h+fT38wUZHE4i/FW+QOPYlWgsvVk2TyevEaaIiOsKG27fa9AFViq3rouBayXKvBIVSneRio3NNHX8xah+a5UrAA1pRLIY3eEqs/DrW3HWMJsTwpu1vV2krKSTZPs0a++cJ7dP8faIFskIrwsawQCB/ZRBrUDaDE/NqE61Z8izrahjMQOZg+c3k2TOQBJjwEnp2CyfPAzkF71pPbGZh1dWtrdgHppUsMFcOfIpMZbipcC1MpzaVywjM8uIYyGozjCmf+Q/3ZoAYNb2/jdeICsEIQBlxrpThCmIGTUwgX9amre2dpbXrP0bK5BHXfnj+tYbpnEvmePJZwjndvbFbos9Oyx1hEJU0bFapvFIgpn1XyTu2HwNjWWMpC1V5jN0qxsXcYqU7t2t/+V57/KL3Zb13Y9wIyrCXwKxTM+nm5IhDVGsE68Dd6wPXzdYyVDwSg6qW/bU+RMYeAfqICB9WPh/2wDz8JtdfnHXwWiExLFHRTS59vzOrQGiQLjZN1tFd9pdW0D4nIWd3Z8UPIgV0/tGA7/A6vAMQv8731SxrvlVVtt3K1sb2Y+B66xGrZ1PwzrSC5Ie978Di4S+fq6WAwVJkOuxziIrbIV/2cU/u9w78jirgM18mLZ55MYRVh40i0oJBhizV6KhBUyso6s2pxN2IIoZgWKO8tVH0Ke0CCV3eX6f5qd31z+SDTwJw+H2AZ6iahcle0KwWooh5eiFAZNJrzaOg3L6dBhgOYz9eexqn8sRTb2NR1/oXgUHgJTqYr7ACKl6oXlXEaRyTktZVmd3ZYl0leBs5HwAf53NOAFjQinGax/HrrqqW6ECfR8GcWhCn9ewTh8VViBZIJRND0/C5xFmLT1l2lim6Dh/nR0Dh6MsCz1dfZYnGarv95/eNZ5iF+Xjdu056JVTUh67dtgLF8t3Q+ZrxIj7gGOj1QTF5YTLA6vPbRjE5SiK7r0n1kT47r1y4JQfDzObJfPT21zjstmOHjykDvbPajB3NuMZianZV396NdNslKxEOamq9Kkd5Wrjulo2OutZOodfXjdbvSnmD+oL2vrTp7czcUFT2wJrM3niDCuahERxSFUNWn4XG3AzzucmvQ/vbDONAk1JeAZuD6pM0KoksTpd68w5MdJL1FusmI4ljGSj8YAsFb2vphR4xvjZ9xX6/CpDhTItlMao8RgpquSknEwLPFwMXU8rCMXaac7BvFG4KJsnzwiYr0QVdagFtsQ3EHflwTj2ktt683ZV8FuGljntQdHMMIUmmgOS4hrjO78wt0KTtoj9G8OeINSfMy3b8XNXqkEMOSS99NMuqn2xKsNd0YrGEvLZ+FlI8hjJv4COPyZc1s3S2Pf1M+OdllufxtYqzufLDubT9oOtAfYyQvNVErjqwCAiK5OEuhkcX1GyEsy5ZyPKbKbHmvLHjbVlRryqX1S+yqw1Mflg4nXEuvNk8g+mq0fvojr7vRg5hGPnMCqQePDlSAb98uzMYjhyEI48aTXqpfbV5y8q7zTqF9XSDWXX2wf2WcVa00tLu5p4ffRF/ZQNNSszT0gWo9TTNrQBgR6WZGnaYGpwh5CUsl97rEIfuvRh9c8RZAmDrTe6N5onCXHwfeEJPUXIkDtOHmoJhi4QFOeoT9LjltDZOSCaxoWzy5/HrBnC4cZU++zXTbF31+VIWX+nipJJQSPCanreSIiJ0ylhijkYW2edYVH/CAe51untebayuPl+6125kfDsScKtBmAvhfCY/4XxFcgq0D73mR6t06YV7NmXNpaXM8QkTuElq23Ge/NJHnpYtVrOkXk2tbKxWdOgVQG50k08qik0Bho0IwHhc2ecLzeI5ab5gyKT8MvUi1Ye0LEoFTIk4mZsP/eVSmQFCILCBDCX2nwjGY0YKHSzXt4A9EYSXNcM1gxRaxsrS1JBM0WpFOAlv16jeD99scEIPMro7XjjrIyL6TN/vS5LKkQqx49zKSOtxlshqkqktzImg9i5kFDWVxzUbs+/J71176zNnXVTu903q1WkdBB5/cPncW1K1sYjDfaktrexvvpdE4IXjxEv4rcUygJ6IodDXEHmtnT8e3oiNlrp2kUr1VoD3Nca4pC0uTuG9UmHy5FidhZXNjT1StMxEvVTnj+9O3npZV852EiI/WnKwb1sOkMiPdzpSdRJD9us4wecre8c9XfS/+1//ff/o32P4qtP8UxtlikwpEZJB1/nn8zoDDM1P1uB1ptuhx+3yqZVr5Rv06Cgtk/cnpczPVotpZPNajzKcuKxFZjRu7/s6P9WAvR/iZToIqyk4VmQcJwd6lsBmLWh7lLxujV3z658su3K9cj06LOww7jYr7gOfCiJqwLIjOmVUyyhQkflYzKpzxGczEZPvqVIHsCxUHI5Fqrx5kmF+T0cC4gJQb89urRVp0G9Y1k8ncxlGHWqz9vDAx7uHdmLDdBIZc98tyQ4ui0UYBjUt7pzf2buQV9P5P3Wr5YokSDSR6Hb/EievsaLXnOO0V2WOaC7NrZR6Q2/gq39LRFoBt+eqHJryCEC0EKNF2I0x+C07OEJ7FdNNSFhU5gJ/hx2xCLFaVnOY9mOzRRHpFYWN9YYu0NUaxlJTcAZo3P09X1l/7Ktm7a28uYMxUioY1KpssmEllFt5WhoZkOrn+prap48HmEgxIAMA1MP54AsEO4EirsnQ6srW2tnSCu5PnjfJ5ZHF68fx6I4p3kqFUD2yXHMKPAkeT5/707dyzX0SCmUEziQaV4ooVmtIYqGVRAClgXcFCOEEO+WTDLbn3YWhkhFhUv7J+bWUQVSx5QawFQN40YYgD3zR5HHdyIPn11embOTCw+HKBp3lgfe1K6cNyouylHaXHjlryGLjt0o354LtfzWm7Ud3pBHJkEk0Y1jx3teNtYFxc+hFQc73Ve+O1Jxkuv2d931sOuRhtYkFpX92bEzyaXpmuBiAlFfw68bHoDM/MFWYB8w87gTGZXr5sCnsFs7W1u2Tmu3ZM9NDCNVSgZnyxt7axrMFBTPPB0pWjotI7Vch9nDX1UbYUumMrrCchlH1RRm9Kh7uIAYYiNzP29whoplip+/jmF2cNfjU1EOJ+oT+OiqQXIoi5FBiidB99VLX3TNJQUWx4aaTR9W19IN07QZcpDiPK8Q2gSDAfJaFZRPaObC4fyZupweVhL6OY65/W6ccdFT2+xrrNv9BeuizBO0ca+dwfMUYGfr1R/vfPTiZLKiBowswBS6Zixv/2mNNfRKEG8PwpouffgObodKLoAZYO3Tzq8SXuhzVz5LOfjY7lWu1Ez0d47NCGS0LqCfG9t1M8UGYhShpBdIdXqMqzNw9tC+F+k784Z24PDVj3A52lONZyJNy7wsHNQfRx6EJmf/6hPar7h6WNZHrxoKkPC0TPF/XETKhggBWwtiUfh5bUuJYse9b7EWo6sBk2v9PuwTjsbh0dSnNFKDzos8P/nOKNf7zDSjSaJ8PqdrW0s2DjlJlcnxdf6HniBDV8VKLsCn5GlWFJkJVUqQmBxJOSjW86OEVUqx6RP/981v763/lkmM19sdrs69T8trrrIhR8etHCdRQThzs+2ebibNpLtosvhiWPc+uKgtoHkc16dkJayx9X57qc8EaCs51uJSlsaEFi1kNVx5/esejDabjCDRGap+MtSqcLDEjhLrK1VMCQUa/wrHoD2PE8hdybMDi9Q58vgFewLUFBK8UA2whJ55umMPn0yfs3pjjddlxI51YVm3VK9RbR7Zan1A4Kx3/8SpGMSp2Gr99ZyKre/gVKxen2NH/19wKkJfyCkcc3JymU+03HgSzVnbNXNHGdxQxQ7i2bbVCorG5vt63w8eDiuBfWx8wni3sefgCR8h9qQjOUnoou7F1j7KI8XM/15KOVNvCEWDn31L8TaIX6U3yq8yUAgd8qsM49iPa0ZW1h1Op/0UTaa7wZp6n815I8/m0lGJHdy7s5EWJ/OiXKpW1djZvMDAqP5RWq5FUedx0o2QKVFPznZjRFY8fmopUdfT1VSBTLZBimLNqkc3bcA7TWZ0J99/zeRZWX6Z5jz+1+7DfpnLMUrqN/0vOIOUaB7CqImW5OIdW0kKAMDzALxgUgxd1jRNstrndcTmN5IBKakIryezDmeIw1I1QQBSwWdFteJBDQlZIRlafrO7iqa/cUJwZjwfSAWYMOrPcerKh0R9GSeBBVMggtaZlj1rGcDl+MWTcps6pdu5v2E+9LM7H7rvnw996Vhvu49T7jM9Ds6gl5/weyYFhjyBEpsDCLVL6fXboRVF9ViXr29El7ZfK+gWS+32Rc9TZaKS9ZcZStmbt3ed5nBlF23UblPs/Zu//2//qWnYM1MG3DMZINsYyFKx7yXtDuDsy7/8Q0wKdFKJC+36/PbSwU7DrFzeXZl6Hu/0axRoTtPBIRfPzfOKJwpC2IAwIxRk0Gi2mFztvkrKZMS7GiVbYZBMYlKaSvGmzYu4YuDfiIEDJm0r11Xr3dbU85vD4vXhWatVNuO5i2IyR1JK+xtWhVYxwK3+Cq2ojKiJqOppjoQZroc1Mb+3tvq81eCZauOznyOURG/ksGK6Sp0v7NWQkX53KZbByKn5O/PWQdHDz486CCGJkSmmZiMzR9Sf+xX1YebV7H/94Xy3y3m77GRt36OH26rJmkZK+dMirxisOguvqvXSsFY/6B2TTlYeSQkCcROlc7EmPN2vbTY/ulsr35ZPH7z0qNgg3i1CFaPXA+zWT06m2bUxkK189d/9x//wX1pEQPXDxUIcXwVZ/hQAzEX99i58Kqn8YuG8at29szjTTetRHuaSVQ6pbdayiJOt2Gd7e/9rJVO0WieOZaAZJQewYjuNdDzRTOuPn93ZltvbVq3hr6qdrzGeGZNaVMJNmdNESokxw/n9gea4WylCXNlDHbGFHNCUToBtR0y8EvQaM5xRWLxFyIH2fCmjywgVhrBMw6mZ+XXP1GqfN49V45gKfTcDLf9zNM1BsdzV4KrPMuTTnEJgebk7YH2B9vk78m50NZ/OkIA/9vHH8t2qBsNMcNjRJQMGgXmm2ArEI41nqeMMhKWTgIkqpYALfNBEldw6va1egqRMntZbl822+crnnV8rhbIS0zFFF4Z69PmsKWA0PMTDaJONuVhEm8emjCscQmdhbEadUNMCjqL6yJwHfXc8kh0jdOWWQTRJxcV+sFpAv1uPIgTObBfHbifN0+KgkoBYqR8de1AWnelg9N0Au5VIy3VSFjpwb5hdtHOKi+OdVnTvJidmCC+n8R9Qj6/UP7+DtcrraAqbFlmYFvS57y7QvMYVLrLck5SeSiddlSLoyzrIMqwqmkqnu4cUGaXUE3iVOT298lQ/EDRKciqfDktkXkmkEC73c5+h7ubcbHd9mtTQhz5HKuodAEgZLFBOxfXcxop3Y51//ff/+Z+Hf3mraFmEwUC43BDaz5V2CmMBf36CT89b568uG6Wx3mJM0YRiZO4AnkHZ/GHisFu9vWnVimohKnevRlmaY6h2ned6yr4Fva2964yd9Y/bXP0816qWiDgjFa8arq07LFYtZOv8K6kZKkY6UUFk583Sn4PeKINTKqHvbUw5a5XByXIgusPbWY4Jh93jkJKlyfMHE93F8N37Lltjz7oyrfPfwdYYnt4VorIOKFzG4xpvMPqAgSGAqbUxzLxEbnt9q6YYpM7VMjCnO8rjCbGu5s6e2L6MQjPpFsgwHFycsmmZxZOhvTqyDpyY0AQy/L8kyNVIQWRzuqYPp0gIXUd8X/aucyb1ZmU8J9fT6x92HG33/hz9JYZse9ejb8UUAOImtee8vnfXkSTNrW9t1bSczBbgF0JWFNQiOZrPCrZabseNYx2ez6/P8ngxcoiFO0AhhdDTWi5Cq5MSs7uyvq50rsd37Jd+eRAFCdz6+oeau1aI3e4bnZqv5kdX3+aRGyK7UARVlHp8h2p9a8cOr35/D8hX6YUWSdMSLGUWb+6H7t+r7Z9//1oJ0JrFAAYzkdzSp7cfVU+c891rlYF2cnXrVx6X4yRClLk9Pg/X/eo2h9hHSUagCaStY/fp0gzQdbk4+9arZ123FhmeGM4dT8/vfswkmppGZ5VqjlRiZZoAmJEFJQwQ2ocPO+tWHjMFBpdiND2sBUmqKoxPYQ/nrPta9mgW7wjz5N2koAhO1Od0WhN6IoU6rQ9shna6eA0j81Ljos/8gJ4ZRqp3jAQQu4ouUb7+oCYkYlfxgnGGOjXImtUPgcYy9c3d1VCUBCRBCEG6LQFTjaUvTDVeOOfX0ElGAd3uz3OLMylBkqr1VrfqO78Xzfv8p6WlNVnUNJh8tWOkztToQgM9szSwKrShqvbssG/HdnBWWXM4M1Eln6qVTnsG/EYazA4qGQmrtBBmewFhIY4xUaFp3xtVshvTizM9uVFqV1ISupcoXN+YZ4zGHFyiaejK1m4uR/rylPofelb5IGcgneWNjQxiAY2m2qR40qpVlFgBZSX75/fozNvdrVfP8wjU6qs/n8XhL1e0T+j/6oV6t3yQ1utFq8rKzRKqBE7xGs589CFDTtL/6n+jp9KMf/m/Orfl3vs3//Nf9G6adSpbsu+tHV0+g5GbD+3Q4FT9oFutozzUvjctkejetbWlJd6A74uy4ySXpXHeiPvinF5aV0GCRKgwjWbQvWJaRs+sr2+F5208A4z6YL6v5CVfn256czsmauiNiDip9Srl6yhNobWi9EQmvID4J2d1lc9QvjjngFSAsopWA2TiQsVs1zRcigP0f1OajHjGeugLThUA6uMM7306rH29/SgRX67YhN9IokBTlWFOZ58FeIYpHqEOYCw6gWJvBkgdtHO8qIM3G1w+w+XALUaRSkgnmO9Q3HM6RFM/7+wy0dsvcXS/3X7H5MzeYbVE6TA/uqgwYlLm9QR0JQN8Dkyb5Cc0iOrEuEVy/68CpEI/upZI7KqS2t5YW2Ob16gmE2YTKZoSvL67/fOO0x8U2DbiJj3k+CzdonXW0wFscqJE8lUArJwg4cms0iIZXkjqu383sCpIO4khcEOjJ9C9jwmW3f51+zcYGI3iZJSoYwnN5SUeh98XVbeQclbdwzLne187ZnDeV2RTeYSUOPbYjdvrEb02hEIfR4TaT5VF/N42d3Q4TQsZVh3RXxC232xvZDTG1FhKsFeyTItRh43T1WokYGoCw9SkqTOEnJPpUWyAShKg8EJT+DxhiOGYgaGeZv3Cj+dXBbw/z25P6Ifef1paV4grn1LJpCLIZQAT6+y7T39E+EnkU64xklZqvJxX+xzOLL8z+GUekDpialXsOIdWPTw2MJdMIoWU4G/kV0hRFT6JWED7PaAmSZXxnEqn1HSZw5N8VsJKmlZmVz8tbeTEZiXKkUqVpqUCK8s+pITbLeXjaur/UefxdeRBKHLP9pKl56HH065nb08/efbq8Wm/WxpSMJgfXfUWwxHvM987QVmbdTGuok6ympopwDiW7eUlAZ5BRom8HIvM84m++nPwVIWKGXxywY1UEwI4oIB881pXlWJgFw9jaIAUUsqoU4MpgE9hj4r0zofV53eCZx9IOrexs1VDXvJJhqYAB9MTAXTt7lJY19PW271tj/aoLwbOssL6zt6fx88RUjO/FhL88kbEFwXdicOzMNyTcCUzgkBrdIpn9dM8IV8agLgY17jJQ6/xLQ52m+G5e1U+Pj8vd3p2tzQT7RXNMZxqlN95M7XQvQsTclD6IXRctK7OzxvHSEPl8GYkgqoUUhqo5wxW5bHuIX7uqcc2vJx7kkEQ/DhPQppm5a/zJNh6BJ5OzUid0GFvqPg4FR1ViIQgO1VTZw63r0XS1yOwdRAC+r9OJT+Eo6/QvrACp7GGcTurkt3ObGlrnRebkddFs9046k/plsxDKpaBq/EeZgTxHIxKA9bZ98v+3PniG+rPo3x9drfUsbERt5dqM//fBPQmqn0FHAOx6/T2R75RINqwrwZlq9iEvCo2VJrGtaloWpcSTm3kBmmBXdmMuE5vol+j4Hkjera+tBLJiaTdTTs4BvyMwzoOQIoXBGXz2zqPko6Bbq0VoRN8sjzx8tiyPp/4d46DNnR55IY2x6NF4vStNEWVurJx8yjSPvTUY09OJ6J0ssGSHIbmnVlbS0iSmqhnrVNDjtDRzhTB8dU+TsZWNkS1zc6srURzPjJD5Kyz3eOLaAUtoeM1dEKL1fssrksC5SikPHTZC8uMqDjr3HR4Ly/2e8aP9x489+mJPHCQioP39Sl017oIPfsSZUBXcWN7r27PpPexiIhRs8h4enzvlhlCxyinU3OmEopaEaVC4hbL1x7YWtgJs3J0brYvvSyCfJYAoXdvdpzT/cBGV3YSPEcAnfVxhNrshWK+sJ9LCvcRC8p1Ed0Lc5yv1mQ2dz8y9avjB4BQWfPVi5Pq5YFlWW2KzUtFV0W9d0K0929+h4q6XZO5c6dfk7n+eqXX1YzQkoYuE5fntpbQ8kZCnOzPAR05zLSWqwE6yrH/3Zl14NXBpABBcPArrEoZu3vY9bBwf8NefQdC0tbHCTkqNkRcJVs1gYERYwgILGL2cJQsKEwXFvC47sFguF/f6XpI8LCgrsejhRevbEYgzmEEuudlBIoTcoYuIHZKh5e4Ye+6Gx8TQsvmWOgG9OkcjfXi52/Yye/yZdVM+aqEHXariUTxJC3koV341kpmspJeDIX68XPPbKPZB5rR69/LIbn8YXOtSOQP5kM/hvpdS8TOkQUDZSVJxMI5lgcBKuqISUxTNVBm014O9ihLNTFud2lzO2406hdDPfoCplunZYQJVN+Za9uCIdssKB7mw7GZGtBumxwfYzoZBWM1ueafUABXrMxohqB166HIH0KhH5qsumj395uP7j9+6vLI5WlgLcw9CIfKr2v21YZ/tgVTKF5Qm4vq9vrqAleIHbWjLHQ5WLzGym5MmElkIi/NQQ05RQKDSgB/juPEsQmWYl2GZ1lx9RfMAfIWRQU2eqfUt8Cisfqb3S01bd2WMI7qOZfD3kR4OlxRNLhWA45u24d61eolAPJluQAwRcnDqwWNo2wNFAc1NLTPeTyhTRXSjFq+hTFhZquPCxKluAoXCV1FXa1r/6yWLqSEdv21rf1t3nVwX/+kzPKPXpmlnw9eoTpht8pQBkUbN+YA17f+Zn1+oD1acieayYkMTt3CXPLD2hrCunQ93bTx/Wx3EML9zBp1xMxzipFWzTcbnIfXpVq5vpuMKW6vLYx6bV67ITtcNBz03aE3a0socqv0ajxQSeNLXdry7VDfeZCzD/akKCD+drYgX9bh+yrk36YX4+4Nu5KwQOlAQ4ohdZPi0mT8MSbwS0h9A64V6i94fplkaMRjg7K24VWENLaz8glJVgCR2wpv/caKzTrBkjKrnXjZku8RmW3o2aWC5t2TBqAs9PWPfVNg8PPo8KnUgtxjVTprT8s6fPVJQqxnWPUZykS6xxKH0ULz+ht6fLZ6bP42UyBqSOUEsfmd2VwlkcGkvIN0EoScQnFJzKs6jX6ZE0mFZH4Zxaj7etb++dA4SWJwNX7d/WdnUQVNypOkfhSEgkPThd+BUUc6CIiR4Ax5OhJQq/Ozb20bG7EZCQZMNcXPfo8jCLqiCZ5+KNDKPltX0KQ4+vpiRiPkHi5Laxv1twzuRwvD97WRXeXSdGh8BmEEky8CXZWD8M9bK0vb3Hd2pgLe1+lpkjiy66EYMARVSUij7+uZSnZ/+RvMD4FzfAEKRySpAq1brAaykYxOZPizY5bD41FKnRGZPCt1r/xVJhLImIoNdT2OStNyMG+Pn9njWUGjByuJcCO8ATMg1cVv5GXtdHSKBO72xS5Sj+1ONptVP6dEdSxyG3BmkjK0Zva92+9nxxknoHUn4mMqVB5OGOtr+imSLCigHhCp2vxI31a+awZpr2gazTPQ8udVzMXJSK0Tvy8b5YPab7+iU6qj52WJbIpHFVd3tmWwkk41AEOhQHH6dvejMszo8x4tML81A6rUHutb/bzL5JqAANzN0P+2WkVBPj+x1n+a/jWYVcDRprQ1x5vtaB3xuHZG6s9/t7aWIpqPWZIkEFKiRtCNAOalOEkpyYSSoLFbTeUJIFC63AnoLIs6xmPfzhfsq7/2ZtHZL6GnkmK4U4OyI6jy9J+2fgngSBmdEfuHmdXy9Z1HkKgW0uKMyKx8LuvS8pvtjzmXy2Ks77y92a9+IEXy8Sldm2HjhmYBwHT6qFetfNEyRBZfPX62MJizLk90DqeRzlSzVvWesuJJn9ljcnLeUUS9rX3RL8wvhJ49q5J0sR1Yrb2oHHSrFWDXkTx2Y8FB0XT7tb4UwQklH+76b9O3atS/aNsPpmaev3rmZgTS5d3HD2y/b1752AzajaLDrnO6/0Vtyqfzj3+ItFjquB7INdSsn7Vadc6unHh+uewwPvkjRrObl44fnXhYQN1n7kebkyRiEvPOiSwealLxzvjEjaP3FEmSIrQMAfFG/+tP8im4zmvqFObMsx/7+WRAZnnj4YzVaj7lsZLC5twewdj5JeuCQHN0AZzB9w1Edt0PTTydm2zcf/1kYciD0TFsrItn1w04Jeya2zhvHvrl54u35U7F6iJ+BrN9UTrdH+8R9JqNUrvO2YyLA948WujH3q6OJ5dUjOLICbWndJ0zGKbUWvcyc1Aw0Px+jFX/+nUm1M2NzRobE1hB4lv7fg6cs/Lc+t7mb0oDU1UZ5rDQqtBkOo9lfStpaygwIsXgyozEFXRUUZepDJnd3ttY0G5cHptnUqp66SBweEyXjY2NRRsb0PMzPiFrdkPyn3a3Ydzus4Q6Ddg0TsKNerb82+5PkpEFN7ZeTAo6JMRLjPg2e+d9HbEOUpORZJrW2Zk4Lfsj1b4aBc9qCkdoI2wkNs4tR+P8C3cy8drDG79GZWBGXr5v82DgtNxIAkv6v+u7o71fAr5gIi0VAPU4g6n1vtJQ68Q4rWYZvDqcmgnsEKGKzRmfgLlHvd/XkOh0+UtfkBWNBMwHba9Rcee8FjSpaq2uoNmW/t4ondIxlqvXo9BokGTjZBg/Vw4ufkgClQKXAiNLOn5CyXH+DcLY9D1ONaHx5BgCB54UL+4adUs9eFGEz5F5gWQxJTPoeA6+gqO3ODz7Cy+ev7g/d65iVrvYu7T6OJnq/P3Iy9ClqzQEr06FJ1+0GleeHdu49ETIH5aXJEMMUwJLwu9rXSCMK+CxWoyH9+/ubS1c+GZL/Rnf+ke1oQFQJlmgKdDv34+qGgf3s78ytt9+CL1vgb7sXz0nZWNtG/nfGAm4EpWnOYljtazdLfXtOqfTuq9kgBJTpY63jhRk6/pncHrBnZcEDLRIPNzSOoOeWUi2pne3PyJMEcx/c2yeyVTKWUGeYCkaGJMJ5c3G3rwV00hwMjanee/e88r5wUzHvDUPq6UDhEW8rl6a/jPoWMK+p4vw2ZLd/b8Z4ijoHBtr63pKAxF3b2Q1DEZu22+ZvBZ/IsmqCmMzf57yLFx5owok3QaqrqjwSw32la1qurf1k/3Mvph/lNXnh9kHs68fn7IFmkFRbig0Z9c3YDax3/jxwbO5+wNPZ8f8Nx1o9lG+3/LY5+LwDMqJPpcUqYtk/ImiYhyNa42krrYAPFSCtLNV7+Mn/dnxUekBOikb079yhYLp7rqCnMsQipD1ZSIsJvOqesBQSZIl6OveXGgqfD/0t+x29Mtz1jNDU2F2TICcUoOWgc1ZBi3yBSCs/uTrp2iGTBf0nsLAI02CTrjd6UbuzZnTcxMvq5rCx5P3dRBTDMDBuO7h1fn+2SDaJGl4jo6apVbH7Wt82JxHlfz/A9UHdpQ="""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
