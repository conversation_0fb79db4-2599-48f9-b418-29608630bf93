#!/usr/bin/env python3
"""
Test all popup window redesigns
"""

import sys
import os
from pathlib import Path

def test_user_management_popup():
    """Test user management popup redesign"""
    
    print("Testing User Management Popup")
    print("=" * 32)
    
    files_to_check = [
        "user_management.py",
        "YES/user_management.py"
    ]
    
    all_redesigned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern design elements
            modern_features = [
                ("Orange header", "bg='#ff8c00'"),
                ("Dark background", "configure(bg='#1a1a1a')"),
                ("Larger dialog", "geometry(\"500x450\")"),
                ("Header with icon", "👤"),
                ("Section styling", "relief='solid', bd=1"),
                ("Modern buttons", "relief='flat', bd=0"),
                ("Button icons", "💾"),
            ]
            
            found_features = 0
            for feature_name, pattern in modern_features:
                if pattern in content:
                    print(f"   ✅ {feature_name}")
                    found_features += 1
                else:
                    print(f"   ❌ {feature_name} missing")
            
            if found_features >= 6:
                print(f"   🎉 Modern design applied ({found_features}/7 features)")
            else:
                print(f"   ⚠️ Incomplete redesign ({found_features}/7 features)")
                all_redesigned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_redesigned = False
    
    return all_redesigned

def test_product_management_popup():
    """Test product management popup redesign"""
    
    print("\nTesting Product Management Popup")
    print("=" * 35)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_redesigned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern design elements
            modern_features = [
                ("Orange header", "bg='#ff8c00'"),
                ("Dark background", "configure(bg='#1a1a1a')"),
                ("Larger dialog", "geometry(\"550x450\")"),
                ("Header with icon", "📁"),
                ("Section styling", "relief='solid', bd=1"),
                ("Modern buttons", "relief='flat', bd=0"),
                ("Button icons", "💾"),
            ]
            
            found_features = 0
            for feature_name, pattern in modern_features:
                if pattern in content:
                    print(f"   ✅ {feature_name}")
                    found_features += 1
                else:
                    print(f"   ❌ {feature_name} missing")
            
            if found_features >= 6:
                print(f"   🎉 Modern design applied ({found_features}/7 features)")
            else:
                print(f"   ⚠️ Incomplete redesign ({found_features}/7 features)")
                all_redesigned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_redesigned = False
    
    return all_redesigned

def test_number_keyboard_design():
    """Test number keyboard design"""
    
    print("\nTesting Number Keyboard Design")
    print("=" * 32)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_modern = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern design elements
            modern_features = [
                ("Orange theme", "bg='#ff8c00'"),
                ("Dark background", "configure(bg='#1a1a1a')"),
                ("Modern buttons", "relief='flat'"),
                ("Orange accents", "#ff8c00"),
                ("Segoe UI font", "font=('Segoe UI'"),
            ]
            
            found_features = 0
            for feature_name, pattern in modern_features:
                if pattern in content:
                    print(f"   ✅ {feature_name}")
                    found_features += 1
                else:
                    print(f"   ❌ {feature_name} missing")
            
            if found_features >= 4:
                print(f"   🎉 Modern design confirmed ({found_features}/5 features)")
            else:
                print(f"   ⚠️ Design needs work ({found_features}/5 features)")
                all_modern = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern = False
    
    return all_modern

def test_general_popup_improvements():
    """Test general popup improvements across files"""
    
    print("\nTesting General Popup Improvements")
    print("=" * 37)
    
    files_to_check = [
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "pos_screen.py"
    ]
    
    all_improved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for general improvements
            improvements = [
                ("Button icons", "💾"),
                ("Flat buttons", "relief='flat'"),
                ("Segoe UI font", "font=('Segoe UI', 12"),
                ("Modern styling", "bd=0"),
            ]
            
            found_improvements = 0
            for improvement_name, pattern in improvements:
                if pattern in content:
                    print(f"   ✅ {improvement_name}")
                    found_improvements += 1
                else:
                    print(f"   ❌ {improvement_name} missing")
            
            if found_improvements >= 2:
                print(f"   ✅ Improvements applied ({found_improvements}/4)")
            else:
                print(f"   ⚠️ Limited improvements ({found_improvements}/4)")
                all_improved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_improved = False
    
    return all_improved

def test_backup_exists():
    """Test that backup was created"""
    
    print("\nTesting Backup Exists")
    print("=" * 23)
    
    # Look for backup directories
    backup_dirs = [d for d in os.listdir('.') if d.startswith('POPUP_BACKUP_')]
    
    if backup_dirs:
        latest_backup = sorted(backup_dirs)[-1]
        print(f"✅ Backup found: {latest_backup}")
        
        # Check if restore script exists
        restore_script = os.path.join(latest_backup, "restore_popups.py")
        if os.path.exists(restore_script):
            print(f"✅ Restore script: {restore_script}")
        else:
            print(f"❌ Restore script missing")
            return False
        
        return True
    else:
        print(f"❌ No backup directories found")
        return False

def test_all_versions_updated():
    """Test that all versions have popup redesigns"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main User Management", "user_management.py"),
        ("Protected User Management", "YES/user_management.py"),
        ("Obfuscated User Management", "YES_OBFUSCATED/user_management.py"),
        ("Main Product Management", "product_management.py"),
        ("Protected Product Management", "YES/product_management.py"),
        ("Obfuscated Product Management", "YES_OBFUSCATED/product_management.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name}")
        else:
            print(f"❌ Missing: {version_name}")
            all_updated = False
    
    return all_updated

def main():
    """Run all popup redesign tests"""
    
    print("🎨 POPUP REDESIGN TEST SUITE")
    print("=" * 30)
    
    tests = [
        ("User Management Popup", test_user_management_popup),
        ("Product Management Popup", test_product_management_popup),
        ("Number Keyboard Design", test_number_keyboard_design),
        ("General Popup Improvements", test_general_popup_improvements),
        ("Backup Exists", test_backup_exists),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL POPUP REDESIGNS SUCCESSFUL!")
        print("✅ Modern orange/black aesthetic applied")
        print("✅ Flat design with icons throughout")
        print("✅ Larger, more spacious dialogs")
        print("✅ Orange header sections")
        print("✅ Improved button styling")
        print("✅ Backup created for reversion")
        print("✅ All versions updated")
    else:
        print("⚠️ Some popup redesigns failed")
        print("❌ Redesign may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 Popup window redesign completely successful!")
        print("🧡 Modern orange/black aesthetic throughout")
        print("📱 Professional flat design with icons")
        print("📏 Larger, more spacious layouts")
        print("🎯 Enhanced user experience")
        print("🔄 Fully revertable with backup")
    else:
        print("\n❌ Popup redesign needs attention")
    
    exit(0 if success else 1)
