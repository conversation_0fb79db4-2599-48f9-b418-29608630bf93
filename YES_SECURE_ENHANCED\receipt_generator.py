"""
Professional Receipt Generator
Generates three types of receipts:
1. Customer Receipt - Full transaction details for customer
2. Summary Receipt - Brief receipt with user, date, time, total
3. History Receipt - Sales history report with filters applied
"""

import tkinter as tk
from datetime import datetime
from database import get_db_connection
from PIL import Image, ImageDraw, ImageFont
import io
import base64


class ReceiptGenerator:
    def __init__(self, app):
        self.app = app
        self.settings = self.load_receipt_settings()

    def load_receipt_settings(self):
        """Load receipt settings from database"""
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM receipt_settings WHERE id = 1")
            result = c.fetchone()

            if result:
                # Handle sqlite3.Row object properly
                try:
                    line_spacing = result['line_spacing'] if 'line_spacing' in result.keys() else 0  # Changed default from 8 to 0
                except (KeyError, TypeError):
                    line_spacing = 0  # Changed default from 8 to 0

                # Convert Row to dict for easier access
                result_dict = dict(result)

                # Handle logo loading - prefer file path over base64
                logo_file_path = result_dict.get('logo_file_path')
                logo_data = result_dict.get('logo_image')
                processed_logo = None

                # Try to load from file path first (better quality, direct loading)
                if logo_file_path:
                    try:
                        import os
                        if os.path.exists(logo_file_path):
                            print(f"Receipt generator: Loading logo directly from file: {logo_file_path}")
                            processed_logo = logo_file_path  # Store file path for direct loading
                        else:
                            print(f"Receipt generator: Logo file not found: {logo_file_path}, falling back to base64")
                            logo_file_path = None
                    except Exception as e:
                        print(f"Receipt generator: Error checking logo file: {e}, falling back to base64")
                        logo_file_path = None

                # Fallback to base64 if file path not available or file doesn't exist
                if not logo_file_path and logo_data:
                    if isinstance(logo_data, bytes):
                        # Old format: convert raw bytes to base64
                        import base64
                        processed_logo = base64.b64encode(logo_data).decode('utf-8')
                        print("Receipt generator: Converted old logo format (bytes) to base64")
                    elif isinstance(logo_data, str):
                        # New format: already base64 encoded
                        processed_logo = logo_data
                        print("Receipt generator: Loaded logo in base64 format")
                    else:
                        print(f"Receipt generator: Unknown logo format: {type(logo_data)}")
                        processed_logo = None

                return {
                    'business_name': result_dict.get('business_name') or 'POS BUSINESS',
                    'business_address': result_dict.get('business_address') or '123 Business Street',
                    'business_phone': result_dict.get('business_phone') or '(*************',
                    'header_text': result_dict.get('header_text') or '',
                    'footer_text': result_dict.get('footer_text') or 'Merci pour votre visite!',  # French default
                    'font_size': result_dict.get('font_size') or 19,
                    'paper_size': result_dict.get('paper_size') or '300x95',
                    'line_spacing': line_spacing,
                    'logo_image': processed_logo,
                    'logo_size': result_dict.get('logo_size', 100)
                }
            else:
                return self.get_default_settings()
        finally:
            conn.close()

    def get_default_settings(self):
        """Get default receipt settings"""
        return {
            'business_name': 'POS BUSINESS',
            'business_address': '123 Business Street',
            'business_phone': '(*************',
            'header_text': '',
            'footer_text': 'Merci pour votre visite!',  # French: Thank you for your visit!
            'font_size': 19,
            'paper_size': '300x95',
            'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing
            'logo_image': None
        }

    def process_logo_for_receipt(self, max_width=200, max_height=80):
        """Process logo image for receipt printing - convert to B&W and resize"""
        logo_data = self.settings.get('logo_image')
        if not logo_data:
            return None

        try:
            from PIL import Image

            # Load image from binary data
            if isinstance(logo_data, bytes):
                image = Image.open(io.BytesIO(logo_data))
            else:
                return None

            # Convert to grayscale first
            if image.mode != 'L':
                image = image.convert('L')

            # Convert to black and white (1-bit)
            # Use threshold to create clean B&W image
            threshold = 128
            image = image.point(lambda x: 0 if x < threshold else 255, '1')

            # Calculate aspect ratio
            original_width, original_height = image.size
            aspect_ratio = original_width / original_height

            # Calculate new dimensions maintaining aspect ratio
            if original_width > max_width or original_height > max_height:
                if aspect_ratio > 1:  # Wider than tall
                    new_width = max_width
                    new_height = int(max_width / aspect_ratio)
                else:  # Taller than wide
                    new_height = max_height
                    new_width = int(max_height * aspect_ratio)

                # Ensure minimum size
                new_width = max(new_width, 50)
                new_height = max(new_height, 20)
            else:
                new_width, new_height = original_width, original_height

            # Resize image with high quality
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            return image, new_width, new_height

        except Exception as e:
            print(f"Logo processing error: {e}")
            return None

    def create_logo_ascii_art(self, image, width, height):
        """Convert logo image to ASCII art for text-based printing"""
        try:
            if not image:
                return []

            # Convert image to ASCII characters
            ascii_chars = ['█', '▓', '▒', '░', ' ']

            # Scale down for ASCII representation
            ascii_width = min(width // 4, 40)  # Max 40 chars wide
            ascii_height = min(height // 8, 10)  # Max 10 lines tall

            if ascii_width < 10:  # Too small for ASCII
                return []

            # Resize for ASCII conversion
            ascii_image = image.resize((ascii_width, ascii_height), Image.Resampling.LANCZOS)

            # Convert to grayscale values
            ascii_image = ascii_image.convert('L')

            ascii_lines = []
            for y in range(ascii_height):
                line = ""
                for x in range(ascii_width):
                    pixel = ascii_image.getpixel((x, y))
                    # Map pixel value to ASCII character
                    char_index = min(int(pixel / 51), 4)  # 0-255 -> 0-4
                    line += ascii_chars[char_index]

                # Center the line
                centered_line = line.center(48)  # Center in 48-char width
                ascii_lines.append(centered_line)

            return ascii_lines

        except Exception as e:
            print(f"ASCII conversion error: {e}")
            return []

    def generate_customer_receipt(self, cart_items, extra_charges, total, user_name, receipt_number=None):
        """Generate professional customer receipt with full transaction details"""
        current_time = datetime.now()

        if not receipt_number:
            receipt_number = f"R{current_time.strftime('%Y%m%d%H%M%S')}"

        # No tax calculation needed - prices already include tax

        receipt_lines = []

        # Logo at the very top - print as actual image
        if self.settings.get('logo_image'):
            receipt_lines.append("__PRINT_LOGO_IMAGE__")
            receipt_lines.append("")  # Space after logo

        # Business information (after logo)
        receipt_lines.extend([
            self.settings['business_name'].upper(),
            self.settings['business_address'],
            f"Tel: {self.settings['business_phone']}",
            ""
        ])

        # Header section (if exists)
        if self.settings['header_text']:
            receipt_lines.append(self.settings['header_text'].upper())
            receipt_lines.append("")

        # Transaction details - FRENCH
        receipt_lines.extend([
            f"Date: {current_time.strftime('%d/%m/%Y')}",
            f"Heure: {current_time.strftime('%H:%M:%S')}",  # French: Time
            f"Caissier: {user_name}",  # French: Cashier
            f"Reçu #: {receipt_number}",  # French: Receipt #
            "=" * 40,
            ""
        ])

        # Items section - FRENCH
        receipt_lines.append("DÉTAILS DE LA TRANSACTION")  # French: Transaction Details
        receipt_lines.append("-" * 40)

        # Cart items
        for item in cart_items:
            product_name = item['name'][:22]  # Shorter to fit better
            quantity = item['quantity']
            price = item['price']
            line_total = quantity * price

            receipt_lines.append(f"{product_name}")
            receipt_lines.append(f"  {quantity} x {price:.2f} = {line_total:.2f} MAD")

        # Extra charges
        for charge in extra_charges:
            description = charge['description'] if 'description' in charge else 'Extra Charge'
            description = description[:22]  # Shorter to fit better
            amount = charge['amount']
            receipt_lines.append(f"{description}")
            receipt_lines.append(f"  1 x {amount:.2f} = {amount:.2f} MAD")

        # Totals section - left aligned to prevent cutoff - FRENCH
        receipt_lines.extend([
            "",
            "-" * 40,
            f"TOTAL: {total:.2f} MAD",  # Keep TOTAL in caps for clarity
            "=" * 40,
            ""
        ])

        # Footer (only if custom footer text exists)
        if self.settings['footer_text']:
            receipt_lines.extend([
                self.settings['footer_text'],
                ""
            ])

        return "\n".join(receipt_lines)

    def generate_summary_receipt(self, user_name, total, receipt_number=None):
        """Generate brief summary receipt with user, date, time, and total only"""
        current_time = datetime.now()

        if not receipt_number:
            receipt_number = f"S{current_time.strftime('%Y%m%d%H%M%S')}"

        receipt_lines = []

        # Minimal header - FRENCH
        receipt_lines.extend([
            self.settings['business_name'].upper(),
            "RÉSUMÉ DE TRANSACTION",  # French: Transaction Summary
            "=" * 32,
            "",
            f"Date: {current_time.strftime('%d/%m/%Y')}",
            f"Heure: {current_time.strftime('%H:%M:%S')}",  # French: Time
            f"Caissier: {user_name}",  # French: Cashier
            "",
            "-" * 32,
            f"TOTAL: {total:.2f} MAD",
            "-" * 32,
            "",
            f"Reçu #: {receipt_number}",  # French: Receipt #
            "",
            "Copie Interne",  # French: Internal Copy
            ""
        ])

        return "\n".join(receipt_lines)

    def generate_history_receipt(self, sales_data, filter_info, generated_by):
        """Generate concise sales history report with product breakdown"""
        current_time = datetime.now()

        receipt_lines = []

        # Header - NO LOGO for history reports - FRENCH
        receipt_lines.extend([
            self.settings['business_name'].upper(),
            "RAPPORT D'HISTORIQUE DES VENTES",  # French: Sales History Report
            ""
        ])

        # Cashier info - show the filtered user, not the one printing - FRENCH
        cashier_name = filter_info.get('user_filter', 'All Users')
        if cashier_name == 'All Users':
            receipt_lines.append("Caissier: Tous les Utilisateurs")  # French: All Users
        else:
            receipt_lines.append(f"Caissier: {cashier_name}")  # French: Cashier

        # Period info - format based on filter type - FRENCH
        date_range = filter_info.get('date_range', '')
        if 'today' in date_range.lower() or date_range == current_time.strftime('%d/%m/%Y'):
            receipt_lines.append(f"Période: Aujourd'hui ({current_time.strftime('%d/%m/%Y')})")  # French: Today
        else:
            receipt_lines.append(f"Période: {date_range}")  # French: Period

        # Table header - MINIMAL SPACING to prevent cutoff - FRENCH
        receipt_lines.extend([
            "_" * 38,  # Reduced from 45 to 38
            f"{'Produit:':<14} {'Prix':<6} {'Qté':<4} {'Total':<6}",  # Reduced spacing: 18->14, 8->6, 6->4, 8->6
            ""
        ])

        # Process sales data to get unique products - USE DATABASE DIRECTLY
        if sales_data:
            unique_products = {}

            # Get sale IDs from the sales_data
            sale_ids = [str(sale.get('id', 0)) for sale in sales_data if sale.get('id')]

            if sale_ids:
                # Query the sale_items table directly for accurate data
                from database import get_db_connection
                conn = get_db_connection()
                try:
                    c = conn.cursor()

                    # Get all items from sale_items table for these sales
                    placeholders = ','.join(['?' for _ in sale_ids])
                    query = f"""
                        SELECT product_name, quantity, price
                        FROM sale_items
                        WHERE sale_id IN ({placeholders})
                        ORDER BY product_name
                    """

                    c.execute(query, sale_ids)
                    items = c.fetchall()

                    # Aggregate products properly
                    for item in items:
                        product_name = item['product_name']
                        unit_price = float(item['price'])
                        quantity_in_sale = int(item['quantity'])

                        if product_name in unique_products:
                            # Add quantity from this sale to total quantity
                            unique_products[product_name]['total_quantity'] += quantity_in_sale
                            # Keep the same unit price (should be consistent)
                            unique_products[product_name]['unit_price'] = unit_price
                            # Recalculate total: unit_price × total_quantity
                            unique_products[product_name]['total_amount'] = (
                                unique_products[product_name]['unit_price'] *
                                unique_products[product_name]['total_quantity']
                            )
                        else:
                            # First time seeing this product
                            unique_products[product_name] = {
                                'unit_price': unit_price,
                                'total_quantity': quantity_in_sale,
                                'total_amount': unit_price * quantity_in_sale
                            }

                finally:
                    conn.close()

            # Calculate grand total from all product totals
            grand_total = sum(product['total_amount'] for product in unique_products.values())

            # Display products in table format - MINIMAL SPACING
            for product_name, data in unique_products.items():
                # Truncate product name to fit smaller column
                display_name = product_name[:12]  # Reduced from 16 to 12
                unit_price = data['unit_price']
                total_quantity = data['total_quantity']
                total_amount = data['total_amount']

                # Format: Product | Unit Price | Total Qty | Total Amount - COMPACT
                receipt_lines.append(f"{display_name:<14} {unit_price:<6.2f} {total_quantity:<4} {total_amount:<6.2f}")  # Matched to header spacing

            # Footer - FRENCH - COMPACT
            receipt_lines.extend([
                "_" * 38,  # Matched to header width
                f"Total: {grand_total:.2f} MAD",
                ""
            ])
        else:
            receipt_lines.extend([
                "Aucun produit trouvé.",  # French: No products found
                "_" * 38,  # Matched to header width
                "Total: 0.00 MAD",
                ""
            ])

        return "\n".join(receipt_lines)

    def prepare_logo_for_printing(self):
        """Prepare logo image for direct printing"""
        try:
            logo_data = self.settings.get('logo_image')
            if not logo_data:
                return None

            # Load image from binary data
            image = Image.open(io.BytesIO(logo_data))

            # Convert to grayscale first
            image = image.convert('L')

            # Convert to black and white for receipt printing
            image = image.point(lambda x: 0 if x < 128 else 255, '1')

            # Resize to appropriate size for receipt (1.5 inches wide max)
            max_width = 144  # pixels (1.5 inches at 96 DPI)
            max_height = 72   # pixels (0.75 inches at 96 DPI)

            # Calculate new size maintaining aspect ratio
            width, height = image.size
            aspect_ratio = width / height

            if aspect_ratio > 1:  # Wider than tall
                new_width = min(max_width, width)
                new_height = int(new_width / aspect_ratio)
            else:  # Taller than wide
                new_height = min(max_height, height)
                new_width = int(new_height * aspect_ratio)

            # Resize image with high quality
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            return image

        except Exception as e:
            print(f"Logo preparation error: {e}")
            return None

    def create_escpos_image_data(self, image):
        """Convert image to ESC/POS format for receipt printers"""
        try:
            if not image:
                return None

            width, height = image.size

            # ESC/POS image printing command
            # ESC * m nL nH d1...dk
            escpos_data = bytearray()

            # Center the image (assume 48 character width = 384 dots at 8 dots per char)
            receipt_width_dots = 384
            image_start_pos = max(0, (receipt_width_dots - width) // 2)

            # Add positioning command if needed
            if image_start_pos > 0:
                escpos_data.extend(b'\x1B\x61\x01')  # Center align

            # Process image line by line
            for y in range(height):
                line_data = bytearray()

                # Convert each line to bit data
                for x in range(0, width, 8):
                    byte_val = 0
                    for bit in range(8):
                        if x + bit < width:
                            pixel = image.getpixel((x + bit, y))
                            if pixel == 0:  # Black pixel
                                byte_val |= (1 << (7 - bit))
                    line_data.append(byte_val)

                # Add ESC/POS command for this line
                # ESC * 33 nL nH (bit image mode)
                nL = len(line_data) % 256
                nH = len(line_data) // 256

                escpos_data.extend(b'\x1B\x2A\x21')  # ESC * 33 (24-dot single-density)
                escpos_data.append(nL)
                escpos_data.append(nH)
                escpos_data.extend(line_data)
                escpos_data.append(0x0A)  # Line feed

            # Reset alignment
            escpos_data.extend(b'\x1B\x61\x00')  # Left align

            return bytes(escpos_data)

        except Exception as e:
            print(f"ESC/POS conversion error: {e}")
            return None

    def print_actual_logo_image(self, hdc, x_pos, y_pos, dpi_x, dpi_y):
        """Print the actual logo image directly to the receipt"""
        try:
            logo_data = self.settings.get('logo_image')
            if not logo_data:
                return 0

            # Load image from binary data
            image = Image.open(io.BytesIO(logo_data))

            # Convert to black and white for receipt printing
            image = image.convert('L')  # Convert to grayscale
            image = image.point(lambda x: 0 if x < 128 else 255, '1')  # Convert to pure B&W

            # Calculate optimal size for receipt (smaller, high quality)
            max_width_inches = 1.5  # 1.5 inches max width for receipt
            max_height_inches = 0.75  # 0.75 inch max height

            max_width_pixels = int(max_width_inches * dpi_x)
            max_height_pixels = int(max_height_inches * dpi_y)

            # Resize image maintaining aspect ratio
            width, height = image.size
            aspect_ratio = width / height

            if aspect_ratio > 1:  # Wider than tall
                new_width = min(max_width_pixels, width)
                new_height = int(new_width / aspect_ratio)
            else:  # Taller than wide
                new_height = min(max_height_pixels, height)
                new_width = int(new_height * aspect_ratio)

            # Resize image with high quality
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert PIL image to Windows bitmap for printing
            import tempfile
            import os

            # Save image to temporary BMP file
            temp_file = tempfile.NamedTemporaryFile(suffix='.bmp', delete=False)
            image.save(temp_file.name, 'BMP')
            temp_file.close()

            try:
                # Load and print the bitmap
                import win32gui
                import win32ui
                import win32con

                # Load the bitmap
                bitmap = win32ui.CreateBitmap()
                bitmap.LoadBitmapFile(temp_file.name)

                # Calculate center position for the image
                # Assume 3 inch receipt width
                page_width = int(3.0 * dpi_x)
                center_x = x_pos + (page_width - new_width) // 2

                # Create memory DC and select bitmap
                mem_dc = hdc.CreateCompatibleDC()
                mem_dc.SelectObject(bitmap)

                # Print the bitmap centered at the top
                hdc.BitBlt((center_x, y_pos), (new_width, new_height), mem_dc, (0, 0), win32con.SRCCOPY)

                # Clean up
                mem_dc.DeleteDC()
                bitmap.DeleteObject()

                return new_height

            except ImportError:
                print("Win32 libraries not available - cannot print image directly")
                return 0
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

        except Exception as e:
            print(f"Logo printing error: {e}")
            return 0

    def print_receipt(self, content, receipt_type="receipt"):
        """Print receipt content using configured printer"""
        try:
            # Get printer settings from receipt settings
            from receipt_settings import ReceiptSettings

            # Try to get connected printer from settings
            printer_name = getattr(self.app, 'connected_printer', None)

            # Check if this is a receipt printer that supports ESC/POS
            if self.is_escpos_printer(printer_name):
                return self.print_to_escpos_printer(content, printer_name, receipt_type)
            else:
                return self.print_to_printer(content, printer_name, receipt_type)

        except Exception as e:
            print(f"Print error: {e}")
            return False

    def print_actual_logo_image(self, hdc, x_pos, y_pos, dpi_x, dpi_y):
        """Print the actual logo image on the receipt - SIMPLE APPROACH"""
        print("=== SIMPLE LOGO PRINTING START ===")
        try:
            from PIL import Image
            import io
            import base64
            import win32ui
            import win32con
            import win32gui
            import os

            logo_data = self.settings.get('logo_image')
            if not logo_data:
                print("No logo data - printing text placeholder")
                hdc.TextOut(x_pos, y_pos, "[YOUR LOGO HERE]")
                return 25

            print(f"Logo data available: {type(logo_data)}")

            # SIMPLE APPROACH: Try to load image regardless of format
            image = None

            # Method 1: Try as file path
            if isinstance(logo_data, str) and ('\\' in logo_data or '/' in logo_data or logo_data.endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))):
                try:
                    if os.path.exists(logo_data):
                        image = Image.open(logo_data)
                        print(f"✓ Loaded from file: {logo_data}")
                except:
                    pass

            # Method 2: Try as base64
            if not image and logo_data:
                try:
                    if isinstance(logo_data, bytes):
                        logo_data = base64.b64encode(logo_data).decode('utf-8')
                    image_data = base64.b64decode(logo_data)
                    image = Image.open(io.BytesIO(image_data))
                    print(f"✓ Loaded from base64 data")
                except:
                    pass

            # If we still don't have an image, give up gracefully
            if not image:
                print("Could not load image - printing text placeholder")
                hdc.TextOut(x_pos, y_pos, "[LOGO ERROR]")
                return 25

            print(f"Image loaded: {image.size}, mode: {image.mode}")

            # Get logo size percentage from settings (default 100%)
            logo_size_percent = self.settings.get('logo_size', 100)
            print(f"Logo size setting: {logo_size_percent}%")

            # Base size for 100% (much larger than before)
            base_width, base_height = 300, 150  # Much larger base size

            # Calculate actual size based on percentage
            max_width = int(base_width * (logo_size_percent / 100.0))
            max_height = int(base_height * (logo_size_percent / 100.0))

            print(f"Calculated logo size: {max_width}x{max_height} ({logo_size_percent}% of {base_width}x{base_height})")

            # RESIZE with calculated size
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

            # SIMPLE FORMAT CONVERSION
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # SIMPLE BITMAP CREATION
            temp_path = "simple_logo.bmp"
            image.save(temp_path, "BMP")

            # SIMPLE PRINTING
            try:
                hbitmap = win32gui.LoadImage(0, temp_path, win32con.IMAGE_BITMAP, 0, 0,
                                           win32con.LR_LOADFROMFILE | win32con.LR_CREATEDIBSECTION)

                if hbitmap:
                    mem_dc = hdc.CreateCompatibleDC()
                    old_bitmap = mem_dc.SelectObject(win32ui.CreateBitmapFromHandle(hbitmap))

                    width, height = image.size
                    hdc.StretchBlt((x_pos, y_pos), (width, height), mem_dc, (0, 0), (width, height), win32con.SRCCOPY)

                    mem_dc.SelectObject(old_bitmap)
                    mem_dc.DeleteDC()
                    win32gui.DeleteObject(hbitmap)

                    print(f"✓ Logo printed successfully: {width}x{height}")

                    # Clean up
                    try:
                        os.remove(temp_path)
                    except:
                        pass

                    return height
                else:
                    print("Failed to create bitmap")
                    hdc.TextOut(x_pos, y_pos, "[LOGO BITMAP ERROR]")
                    return 25

            except Exception as e:
                print(f"Print error: {e}")
                hdc.TextOut(x_pos, y_pos, "[LOGO PRINT ERROR]")
                return 25

        except Exception as e:
            print(f"Error in print_actual_logo_image: {e}")
            # Always print something so user knows logo was attempted
            try:
                hdc.TextOut(x_pos, y_pos, "[LOGO ERROR]")
            except:
                pass
            return 25

    def is_escpos_printer(self, printer_name):
        """Check if printer is an ESC/POS receipt printer"""
        if not printer_name:
            return False

        # Common receipt printer names/models
        escpos_keywords = [
            'receipt', 'thermal', 'pos', 'epson', 'star', 'citizen',
            'bixolon', 'custom', 'tm-', 'tsp', 'ct-', 'rp-'
        ]

        printer_lower = printer_name.lower()
        return any(keyword in printer_lower for keyword in escpos_keywords)

    def print_to_escpos_printer(self, content, printer_name, receipt_type):
        """Print to ESC/POS receipt printer with image support"""
        try:
            # Process content and handle logo
            processed_content = self.process_content_for_escpos(content)

            # Send to printer (this would need actual printer communication)
            # For now, we'll use the standard Windows printing but with image processing
            return self.print_to_printer(processed_content, printer_name, receipt_type)

        except Exception as e:
            print(f"ESC/POS print error: {e}")
            return False

    def process_content_for_escpos(self, content):
        """Process content for ESC/POS printing, handling logo images"""
        try:
            lines = content.split('\n')
            processed_lines = []

            for line in lines:
                if line.strip() == "__PRINT_LOGO_IMAGE__":
                    # Prepare logo for ESC/POS printing
                    logo_image = self.prepare_logo_for_printing()
                    if logo_image:
                        # For now, add a placeholder that indicates logo should be printed
                        processed_lines.append("[LOGO IMAGE PRINTED HERE]")
                        # In a full implementation, you would send ESC/POS commands here
                    continue

                processed_lines.append(line)

            return '\n'.join(processed_lines)

        except Exception as e:
            print(f"Content processing error: {e}")
            return content

    def print_to_printer(self, content, printer_name=None, receipt_type="receipt"):
        """Print content to specified printer with proper line spacing"""
        try:
            import win32print
            import win32ui
            import win32con

            # Use specified printer or default
            if printer_name:
                printer = printer_name
            else:
                printer = win32print.GetDefaultPrinter()

            # Create device context
            hdc = win32ui.CreateDC()
            hdc.CreatePrinterDC(printer)

            # Get printer capabilities for better scaling
            try:
                # Get printer resolution
                dpi_x = hdc.GetDeviceCaps(win32con.LOGPIXELSX)
                dpi_y = hdc.GetDeviceCaps(win32con.LOGPIXELSY)

                # Scale font size based on DPI (default 96 DPI)
                font_scale = max(dpi_y / 96.0, 1.0)
            except:
                font_scale = 1.0
                dpi_y = 96

            # Start document
            hdc.StartDoc(f"POS {receipt_type.title()}")
            hdc.StartPage()

            # Get settings with proper line spacing
            font_size = int(self.settings['font_size'] * font_scale)
            line_spacing = self.settings.get('line_spacing', 3)

            # Calculate proper line height with spacing
            # Convert line spacing from pixels to printer units
            line_spacing_printer = int(line_spacing * font_scale * (dpi_y / 96.0))
            line_height = font_size + line_spacing_printer + 10  # Extra padding for clarity

            # Create font for better text rendering
            try:
                font = win32ui.CreateFont({
                    'name': 'Courier New',
                    'height': font_size,
                    'weight': win32con.FW_BOLD
                })
                hdc.SelectObject(font)
            except:
                pass  # Use default font if creation fails

            # Print content line by line with proper spacing
            y_pos = 100  # Start position
            x_pos = 20   # Reduced left margin for less empty space

            lines = content.strip().split('\n')
            for line in lines:
                # Check if this is the logo placeholder
                if line.strip() == "__PRINT_LOGO_IMAGE__":
                    # Print the actual logo image here - moved to the right
                    if self.settings.get('logo_image'):
                        # Move logo to the right by adding offset
                        logo_x_pos = x_pos + 100  # Move 100 pixels to the right
                        logo_height = self.print_actual_logo_image(hdc, logo_x_pos, y_pos, dpi_x, dpi_y)
                        if logo_height > 0:
                            y_pos += logo_height + 20  # Add space after logo
                    continue

                if line.strip():  # Only print non-empty lines
                    try:
                        hdc.TextOut(x_pos, y_pos, line)
                    except:
                        # Fallback for encoding issues
                        try:
                            hdc.TextOut(x_pos, y_pos, line.encode('utf-8', 'ignore').decode('utf-8'))
                        except:
                            hdc.TextOut(x_pos, y_pos, str(line))

                    y_pos += line_height
                else:
                    # Empty line - add half spacing
                    y_pos += line_height // 2

            # End document
            hdc.EndPage()
            hdc.EndDoc()
            hdc.DeleteDC()

            return True

        except Exception as e:
            print(f"Print error: {e}")
            return False

