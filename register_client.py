#!/usr/bin/env python3
"""
Register POS Client with Multi-Client Remote Management
"""

import sqlite3
import datetime
import os
import sys
from pathlib import Path

def register_pos_client():
    """Register this POS system as a client"""
    
    print("🏪 POS CLIENT REGISTRATION")
    print("=" * 40)
    
    # Check if pos_system.db exists
    if not os.path.exists('pos_system.db'):
        print("❌ Error: pos_system.db not found!")
        print("Please run this from your POS system directory.")
        return False
    
    # Get client information
    print("\n📝 Enter client information:")
    client_id = input("Client ID (unique identifier): ").strip()
    if not client_id:
        print("❌ Client ID is required!")
        return False
    
    client_name = input("Client Name (display name): ").strip()
    if not client_name:
        print("❌ Client Name is required!")
        return False
    
    database_path = os.path.abspath('pos_system.db')
    
    # Connect to master database
    master_db = 'master_remote.db'
    conn = sqlite3.connect(master_db)
    conn.row_factory = sqlite3.Row
    
    try:
        c = conn.cursor()
        
        # Check if client already exists
        c.execute("SELECT client_id FROM clients WHERE client_id = ?", (client_id,))
        if c.fetchone():
            print(f"⚠️  Client '{client_id}' already exists!")
            update = input("Update existing client? (y/n): ").lower().strip()
            if update != 'y':
                return False
        
        # Register/update client
        c.execute("""
            INSERT OR REPLACE INTO clients 
            (client_id, client_name, database_path, created_date, last_active)
            VALUES (?, ?, ?, ?, ?)
        """, (client_id, client_name, database_path, 
              datetime.datetime.now().isoformat(),
              datetime.datetime.now().isoformat()))
        
        conn.commit()
        
        print(f"\n✅ Client registered successfully!")
        print(f"   Client ID: {client_id}")
        print(f"   Client Name: {client_name}")
        print(f"   Database: {database_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error registering client: {e}")
        return False
    finally:
        conn.close()

def list_registered_clients():
    """List all registered clients"""
    
    master_db = 'master_remote.db'
    if not os.path.exists(master_db):
        print("❌ No master database found. No clients registered yet.")
        return
    
    conn = sqlite3.connect(master_db)
    conn.row_factory = sqlite3.Row
    
    try:
        c = conn.cursor()
        c.execute("SELECT * FROM clients WHERE is_active = 1 ORDER BY client_name")
        clients = c.fetchall()
        
        if not clients:
            print("📋 No clients registered yet.")
            return
        
        print("\n📋 REGISTERED CLIENTS:")
        print("=" * 50)
        for client in clients:
            print(f"🏪 {client['client_name']}")
            print(f"   ID: {client['client_id']}")
            print(f"   Database: {client['database_path']}")
            print(f"   Created: {client['created_date']}")
            print(f"   Last Active: {client['last_active']}")
            print()
            
    except Exception as e:
        print(f"❌ Error listing clients: {e}")
    finally:
        conn.close()

def manage_user_access():
    """Manage user access to clients"""
    
    master_db = 'master_remote.db'
    if not os.path.exists(master_db):
        print("❌ No master database found.")
        return
    
    conn = sqlite3.connect(master_db)
    conn.row_factory = sqlite3.Row
    
    try:
        c = conn.cursor()
        
        # List users
        c.execute("SELECT username, client_access FROM remote_auth WHERE is_active = 1")
        users = c.fetchall()
        
        if not users:
            print("❌ No users found.")
            return
        
        print("\n👥 CURRENT USERS:")
        print("=" * 30)
        for i, user in enumerate(users, 1):
            import json
            client_access = json.loads(user['client_access'])
            access_desc = "All clients" if not client_access else f"Specific clients: {', '.join(client_access)}"
            print(f"{i}. {user['username']} - {access_desc}")
        
        # Select user to modify
        try:
            user_choice = int(input("\nSelect user number to modify access: ")) - 1
            if user_choice < 0 or user_choice >= len(users):
                print("❌ Invalid selection.")
                return
            
            selected_user = users[user_choice]
            username = selected_user['username']
            
            print(f"\n🔧 Modifying access for: {username}")
            print("1. Grant access to all clients")
            print("2. Grant access to specific clients")
            print("3. Remove all access")
            
            access_choice = input("Choose option (1-3): ").strip()
            
            if access_choice == '1':
                # All clients access
                new_access = "[]"
                c.execute("UPDATE remote_auth SET client_access = ? WHERE username = ?", (new_access, username))
                print(f"✅ {username} now has access to all clients.")
                
            elif access_choice == '2':
                # Specific clients
                c.execute("SELECT client_id, client_name FROM clients WHERE is_active = 1")
                clients = c.fetchall()
                
                if not clients:
                    print("❌ No clients available.")
                    return
                
                print("\n📋 Available clients:")
                for i, client in enumerate(clients, 1):
                    print(f"{i}. {client['client_name']} ({client['client_id']})")
                
                client_ids = input("\nEnter client numbers (comma-separated): ").strip()
                try:
                    selected_indices = [int(x.strip()) - 1 for x in client_ids.split(',')]
                    selected_client_ids = [clients[i]['client_id'] for i in selected_indices 
                                         if 0 <= i < len(clients)]
                    
                    if selected_client_ids:
                        import json
                        new_access = json.dumps(selected_client_ids)
                        c.execute("UPDATE remote_auth SET client_access = ? WHERE username = ?", (new_access, username))
                        print(f"✅ {username} now has access to: {', '.join(selected_client_ids)}")
                    else:
                        print("❌ No valid clients selected.")
                        return
                        
                except ValueError:
                    print("❌ Invalid input format.")
                    return
                    
            elif access_choice == '3':
                # Remove access
                new_access = json.dumps([])
                c.execute("UPDATE remote_auth SET client_access = ? WHERE username = ?", (new_access, username))
                print(f"✅ {username} access removed.")
                
            else:
                print("❌ Invalid choice.")
                return
            
            conn.commit()
            
        except ValueError:
            print("❌ Invalid input.")
            return
            
    except Exception as e:
        print(f"❌ Error managing user access: {e}")
    finally:
        conn.close()

def main():
    """Main menu"""
    
    while True:
        print("\n🏪 POS CLIENT MANAGEMENT")
        print("=" * 30)
        print("1. Register this POS as a client")
        print("2. List registered clients")
        print("3. Manage user access to clients")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '1':
            register_pos_client()
        elif choice == '2':
            list_registered_clients()
        elif choice == '3':
            manage_user_access()
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == '__main__':
    main()
