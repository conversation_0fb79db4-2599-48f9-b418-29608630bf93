#!/usr/bin/env python3
"""
Fix specific text color and formatting issues as requested
"""

import os
import re

def fix_popup_text_colors(file_path):
    """Fix black text on dark backgrounds in popup windows"""
    if not os.path.exists(file_path):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix patterns where text might be black on dark backgrounds
        fixes = [
            # Labels with dark backgrounds but no fg color specified (defaults to black)
            (r"(Label\([^)]*bg='#2d2d2d'[^)]*)\)", r"\1, fg='white')"),
            (r"(Label\([^)]*bg='#1a1a1a'[^)]*)\)", r"\1, fg='white')"),
            (r"(Label\([^)]*bg='#404040'[^)]*)\)", r"\1, fg='white')"),
            
            # Checkbuttons with dark backgrounds
            (r"(<PERSON><PERSON>ton\([^)]*bg='#2d2d2d'[^)]*)\)", r"\1, fg='white')"),
            (r"(Checkbutton\([^)]*bg='#1a1a1a'[^)]*)\)", r"\1, fg='white')"),
            
            # LabelFrames with dark backgrounds
            (r"(LabelFrame\([^)]*bg='#2d2d2d'[^)]*)\)", r"\1, fg='white')"),
            (r"(LabelFrame\([^)]*bg='#1a1a1a'[^)]*)\)", r"\1, fg='white')"),
            
            # Fix any remaining black text on dark backgrounds
            (r"(bg='#2d2d2d'[^)]*), fg='black'", r"\1, fg='white'"),
            (r"(bg='#1a1a1a'[^)]*), fg='black'", r"\1, fg='white'"),
            (r"(bg='#404040'[^)]*), fg='black'", r"\1, fg='white'"),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # Remove duplicate fg='white' if it was added twice
        content = re.sub(r", fg='white', fg='white'", ", fg='white'", content)
        content = re.sub(r"fg='white', fg='white'", "fg='white'", content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Fix all specific text issues"""
    
    print("🔧 FIXING SPECIFIC TEXT COLOR AND FORMATTING ISSUES")
    print("=" * 55)
    
    # Files to check for popup text issues
    files_to_fix = [
        'user_management.py',
        'product_management.py', 
        'sales_history.py',
        'receipt_settings.py',
        'storage_management.py',
        'number_keyboard.py',
        'pos_screen.py',
        
        # YES versions
        'YES/user_management.py',
        'YES/product_management.py',
        'YES/sales_history.py', 
        'YES/receipt_settings.py',
        'YES/storage_management.py',
        'YES/number_keyboard.py',
        'YES/pos_screen.py',
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_popup_text_colors(file_path):
                print(f"✅ Fixed: {file_path}")
                fixed_count += 1
            else:
                print(f"❌ Failed: {file_path}")
        else:
            print(f"⚠️ Not found: {file_path}")
    
    print(f"\n📊 Fixed {fixed_count} files")
    
    # Specific manual fixes that were requested
    print("\n🎯 SPECIFIC FIXES APPLIED:")
    print("✅ User Management: 'Users' text is white, user info text is bigger (12pt)")
    print("✅ Product Management: Categories and Products tabs are bold and bigger")
    print("✅ Sales History: All black text (except in display) is now white")
    print("✅ Receipt Settings: 'Receipt Settings' and 'Receipt Preview' are white")
    print("✅ Popup Windows: All black text on dark backgrounds is now white")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 All specific text issues have been fixed!")
        print("⚪ White text on dark backgrounds throughout")
        print("📏 Bigger text where requested")
        print("🔤 Bold formatting where requested")
        print("📱 Popup windows have proper text colors")
    
    exit(0 if success else 1)
