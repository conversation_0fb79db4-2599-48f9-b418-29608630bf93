# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
