#!/usr/bin/env python3
"""
Simple Secure Launcher for POS System
Simplified version with better error handling
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, simpledialog

class SimpleSecureLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔒 Secure POS System")
        self.root.geometry("450x350")
        self.root.configure(bg='#1a1a1a')
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        self.security_manager = None
        self.setup_ui()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.root.winfo_screenheight() // 2) - (350 // 2)
        self.root.geometry(f"450x350+{x}+{y}")
        
    def setup_ui(self):
        """Setup the launcher UI"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#ff8c00', height=70)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🔒 SECURE POS SYSTEM", 
                              font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        title_label.pack(expand=True)
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Status
        self.status_label = tk.Label(main_frame, text="🔍 Checking security...", 
                                    font=('Segoe UI', 12), bg='#1a1a1a', fg='yellow')
        self.status_label.pack(pady=(0, 20))
        
        # Machine info
        try:
            import platform
            import uuid
            machine_info = f"System: {platform.system()}\nNode: {platform.node()}"
        except:
            machine_info = "System information unavailable"
            
        info_label = tk.Label(main_frame, text=machine_info, 
                             font=('Segoe UI', 9), bg='#1a1a1a', fg='#888888')
        info_label.pack(pady=(0, 20))
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='#1a1a1a')
        button_frame.pack(pady=(20, 0))
        
        self.launch_btn = tk.Button(button_frame, text="🚀 Launch POS System",
                                   font=('Segoe UI', 11, 'bold'), bg='#28a745', fg='white',
                                   padx=20, pady=8, state='disabled',
                                   command=self.launch_pos_system)
        self.launch_btn.pack(side=tk.TOP, pady=(0, 10))
        
        auth_btn = tk.Button(button_frame, text="🔑 Authorize Machine",
                            font=('Segoe UI', 11, 'bold'), bg='#007bff', fg='white',
                            padx=20, pady=8, command=self.authorize_machine)
        auth_btn.pack(side=tk.TOP, pady=(0, 10))
        
        exit_btn = tk.Button(button_frame, text="❌ Exit",
                            font=('Segoe UI', 11, 'bold'), bg='#dc3545', fg='white',
                            padx=20, pady=8, command=self.root.quit)
        exit_btn.pack(side=tk.TOP)
        
        # Start security check after UI is ready
        self.root.after(1000, self.perform_security_check)
    
    def perform_security_check(self):
        """Perform security check with error handling"""
        try:
            self.status_label.config(text="🔍 Loading security system...", fg='yellow')
            self.root.update()
            
            # Try to load security manager
            try:
                from security_manager import security_manager
                self.security_manager = security_manager
                
                # Perform security check
                security_ok, message = security_manager.security_check()
                
                if security_ok:
                    self.status_label.config(text="✅ Security checks passed - Ready to launch", fg='#28a745')
                    self.launch_btn.config(state='normal')
                else:
                    if "UNAUTHORIZED_MACHINE" in message:
                        self.status_label.config(text="🚫 Machine not authorized", fg='#dc3545')
                        self.show_authorization_prompt()
                    else:
                        self.status_label.config(text="⚠️ Security warnings detected", fg='orange')
                        result = messagebox.askyesno(
                            "Security Warning",
                            f"Security check failed: {message}\n\nContinue anyway?"
                        )
                        if result:
                            self.launch_btn.config(state='normal')
                            self.status_label.config(text="⚠️ Ready to launch (with warnings)", fg='orange')
                        
            except ImportError:
                # Security manager not available - allow launch with warning
                self.status_label.config(text="⚠️ Security system unavailable - Basic mode", fg='orange')
                self.launch_btn.config(state='normal')
                
        except Exception as e:
            self.status_label.config(text="❌ Security check failed", fg='#dc3545')
            result = messagebox.askyesno(
                "Security Error",
                f"Security system error: {e}\n\nLaunch in basic mode?"
            )
            if result:
                self.launch_btn.config(state='normal')
                self.status_label.config(text="⚠️ Basic mode - No security", fg='orange')
    
    def show_authorization_prompt(self):
        """Show authorization prompt"""
        result = messagebox.askyesno(
            "Authorization Required",
            "This machine needs authorization to run the POS system.\n\n"
            "Click Yes to enter authorization code."
        )
        
        if result:
            self.authorize_machine()
    
    def authorize_machine(self):
        """Authorize the current machine"""
        auth_code = simpledialog.askstring(
            "Machine Authorization",
            "Enter authorization code:",
            show='*'
        )
        
        if auth_code:
            try:
                if self.security_manager and self.security_manager.authorize_machine(auth_code):
                    messagebox.showinfo("Success", "Machine authorized successfully!")
                    self.perform_security_check()
                else:
                    messagebox.showerror("Error", "Invalid authorization code!")
            except:
                # Fallback authorization for testing
                if auth_code == "SECURE_POS_2024_AUTH":
                    messagebox.showinfo("Success", "Machine authorized (basic mode)!")
                    self.launch_btn.config(state='normal')
                    self.status_label.config(text="✅ Authorized - Ready to launch", fg='#28a745')
                else:
                    messagebox.showerror("Error", "Invalid authorization code!")
    
    def launch_pos_system(self):
        """Launch the main POS system"""
        try:
            self.status_label.config(text="🚀 Launching POS system...", fg='#28a745')
            self.root.update()
            
            # Log launch event if security manager available
            try:
                if self.security_manager:
                    self.security_manager.log_security_event("SYSTEM_LAUNCH", "POS launched via secure launcher")
            except:
                pass
            
            # Hide launcher
            self.root.withdraw()
            
            # Launch main system
            if os.path.exists("main.py"):
                try:
                    # Simple launch method
                    import subprocess
                    result = subprocess.run([sys.executable, "main.py"], 
                                          capture_output=False, 
                                          check=False)
                    print(f"POS system exited with code: {result.returncode}")
                    
                except Exception as e:
                    print(f"Launch error: {e}")
                    messagebox.showerror("Launch Error", f"Failed to launch POS system: {e}")
            else:
                messagebox.showerror("Error", "main.py not found in current directory!")
            
            # Show launcher again
            self.root.deiconify()
            self.status_label.config(text="✅ POS system closed", fg='white')
            
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch POS system: {e}")
            self.root.deiconify()
    
    def run(self):
        """Run the launcher"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            pass
        except Exception as e:
            print(f"Launcher error: {e}")

def main():
    """Main entry point"""
    try:
        # Check if we're in the right directory
        if not os.path.exists("main.py"):
            print("❌ Error: main.py not found!")
            print("Please run this launcher from the POS system directory.")
            input("Press Enter to exit...")
            return
        
        # Create and run launcher
        launcher = SimpleSecureLauncher()
        launcher.run()
        
    except Exception as e:
        print(f"❌ Launcher startup error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
