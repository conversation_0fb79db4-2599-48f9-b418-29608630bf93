#!/usr/bin/env python3
"""
Test that the Display.png icon is properly loaded and used in the display button
"""

import sys
import os
from pathlib import Path

def test_display_icon():
    """Test that Display.png icon is loaded and used correctly"""
    
    print("Testing Display Icon Integration")
    print("=" * 40)
    
    # Test main directory
    print("📋 Testing main directory...")
    sys.path.insert(0, str(Path(".").absolute()))
    
    try:
        # Test icon file exists
        icon_path = Path("assets/Display.png")
        if icon_path.exists():
            print("✅ Display.png found in main assets folder")
        else:
            print("❌ Display.png missing from main assets folder")
            return False
        
        # Test icon loading in pos_app
        from pos_app import POSApp
        
        # Create a minimal app instance to test icon loading
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        try:
            app = POSApp(root)
            
            # Check if display_settings icon was loaded
            if 'display_settings' in app.icons:
                print("✅ Display icon loaded successfully in main version")
                print(f"   Icon object: {type(app.icons['display_settings'])}")
            else:
                print("❌ Display icon not loaded in main version")
                print(f"   Available icons: {list(app.icons.keys())}")
                return False
            
        finally:
            root.destroy()
        
    except Exception as e:
        print(f"❌ Error testing main directory: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test YES directory
    print("\n📋 Testing YES directory...")
    
    try:
        # Test icon file exists in YES
        yes_icon_path = Path("YES/assets/Display.png")
        if yes_icon_path.exists():
            print("✅ Display.png found in YES assets folder")
        else:
            print("❌ Display.png missing from YES assets folder")
            return False
        
        # Clear module cache
        modules_to_clear = ['pos_app', 'database', 'translations']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # Change to YES directory
        original_dir = os.getcwd()
        os.chdir("YES")
        
        try:
            sys.path.insert(0, str(Path(".").absolute()))
            
            from pos_app import POSApp as YESPOSApp
            
            # Create a minimal app instance to test icon loading
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # Hide the window
            
            try:
                yes_app = YESPOSApp(root)
                
                # Check if display_settings icon was loaded
                if 'display_settings' in yes_app.icons:
                    print("✅ Display icon loaded successfully in YES version")
                else:
                    print("❌ Display icon not loaded in YES version")
                    print(f"   Available icons: {list(yes_app.icons.keys())}")
                    return False
                
            finally:
                root.destroy()
            
        finally:
            os.chdir(original_dir)
        
    except Exception as e:
        print(f"❌ Error testing YES directory: {e}")
        return False
    
    # Test YES_OBFUSCATED directory
    print("\n📋 Testing YES_OBFUSCATED directory...")
    
    try:
        # Test icon file exists in YES_OBFUSCATED
        obf_icon_path = Path("YES_OBFUSCATED/assets/Display.png")
        if obf_icon_path.exists():
            print("✅ Display.png found in YES_OBFUSCATED assets folder")
        else:
            print("❌ Display.png missing from YES_OBFUSCATED assets folder")
            return False
        
        # Clear module cache
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # Change to YES_OBFUSCATED directory
        os.chdir("YES_OBFUSCATED")
        
        try:
            sys.path.insert(0, str(Path(".").absolute()))
            
            import pos_app as obf_pos_app
            
            # Create a minimal app instance to test icon loading
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # Hide the window
            
            try:
                obf_app = obf_pos_app.POSApp(root)
                
                # Check if display_settings icon was loaded
                if 'display_settings' in obf_app.icons:
                    print("✅ Display icon loaded successfully in YES_OBFUSCATED version")
                else:
                    print("❌ Display icon not loaded in YES_OBFUSCATED version")
                    print(f"   Available icons: {list(obf_app.icons.keys())}")
                    return False
                
            finally:
                root.destroy()
            
        finally:
            os.chdir(original_dir)
        
    except Exception as e:
        print(f"❌ Error testing YES_OBFUSCATED directory: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 DISPLAY ICON TEST PASSED!")
    print("=" * 40)
    print("✅ Display.png exists in all asset folders")
    print("✅ Display icon loads correctly in all versions")
    print("✅ Icon mapping is configured properly")
    print("✅ Display button will show icon instead of text")
    
    return True

def test_icon_button_logic():
    """Test that the button creation logic handles display_settings correctly"""
    
    print("\n🔘 Testing Icon Button Logic:")
    print("-" * 30)
    
    try:
        # Test that the pos_screen.py has the special handling for display_settings
        with open("pos_screen.py", "r") as f:
            content = f.read()
        
        if "if icon_key == 'display_settings':" in content:
            print("✅ Special display_settings button logic found in main version")
        else:
            print("❌ Special display_settings button logic missing in main version")
            return False
        
        if "width=40, height=40" in content:
            print("✅ Icon-only button sizing found in main version")
        else:
            print("❌ Icon-only button sizing missing in main version")
            return False
        
        # Test YES version
        with open("YES/pos_screen.py", "r") as f:
            yes_content = f.read()
        
        if "if icon_key == 'display_settings':" in yes_content:
            print("✅ Special display_settings button logic found in YES version")
        else:
            print("❌ Special display_settings button logic missing in YES version")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing button logic: {e}")
        return False

def main():
    """Run all display icon tests"""
    
    print("🖼️ DISPLAY ICON TEST SUITE")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Display icon loading
    if test_display_icon():
        tests_passed += 1
        print("✅ Test 1 PASSED: Display icon loading")
    else:
        print("❌ Test 1 FAILED: Display icon loading")
    
    # Test 2: Icon button logic
    if test_icon_button_logic():
        tests_passed += 1
        print("✅ Test 2 PASSED: Icon button logic")
    else:
        print("❌ Test 2 FAILED: Icon button logic")
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Display icon is working correctly")
        print("✅ Display button will show icon only (no text)")
        print("✅ Icon is available in all versions")
        print("✅ Button sizing is optimized for icon display")
    else:
        print("⚠️ Some tests failed")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎨 Display icon integration is complete!")
        print("📋 Display button now shows icon only")
    else:
        print("\n❌ Display icon integration test failed!")
    sys.exit(0 if success else 1)
