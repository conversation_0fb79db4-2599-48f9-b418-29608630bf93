#!/usr/bin/env python3
"""
Test the final fixes: larger windows and empty database
"""

import sys
import os
from pathlib import Path

def test_window_sizes():
    """Test that dialog windows are large enough"""
    
    print("Testing Dialog Window Sizes")
    print("=" * 28)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_sized = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bulk product dialog size
            if "750x700" in content:
                print(f"   ✅ Bulk product dialog size (750x700)")
            else:
                print(f"   ❌ Bulk product dialog size missing")
                all_sized = False
            
            # Check for bulk category dialog size
            if "600x500" in content:
                print(f"   ✅ Bulk category dialog size (600x500)")
            else:
                print(f"   ❌ Bulk category dialog size missing")
                all_sized = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_sized = False
    
    return all_sized

def test_empty_database():
    """Test that databases are empty by default"""
    
    print("\nTesting Empty Databases")
    print("=" * 24)
    
    databases = [
        ("Main", "pos_system.db"),
        ("YES", "YES/pos_system.db"),
        ("YES_OBFUSCATED", "YES_OBFUSCATED/pos_system.db")
    ]
    
    all_empty = True
    
    for db_name, db_path in databases:
        if Path(db_path).exists():
            try:
                # Import database module from the correct directory
                if db_name == "Main":
                    sys.path.insert(0, ".")
                    from database import get_db_connection
                elif db_name == "YES":
                    sys.path.insert(0, "YES")
                    from database import get_db_connection
                elif db_name == "YES_OBFUSCATED":
                    sys.path.insert(0, "YES_OBFUSCATED")
                    from database import get_db_connection
                
                # Change to the correct directory
                original_dir = os.getcwd()
                if db_name != "Main":
                    os.chdir(db_name)
                
                conn = get_db_connection()
                c = conn.cursor()
                
                c.execute('SELECT COUNT(*) FROM categories')
                cat_count = c.fetchone()[0]
                
                c.execute('SELECT COUNT(*) FROM products')
                prod_count = c.fetchone()[0]
                
                conn.close()
                
                # Change back to original directory
                os.chdir(original_dir)
                
                print(f"\n📋 Checking: {db_name} database")
                
                if cat_count == 0:
                    print(f"   ✅ Categories: {cat_count} (empty)")
                else:
                    print(f"   ❌ Categories: {cat_count} (should be 0)")
                    all_empty = False
                
                if prod_count == 0:
                    print(f"   ✅ Products: {prod_count} (empty)")
                else:
                    print(f"   ❌ Products: {prod_count} (should be 0)")
                    all_empty = False
                    
                # Clear sys.path
                if "." in sys.path:
                    sys.path.remove(".")
                if "YES" in sys.path:
                    sys.path.remove("YES")
                if "YES_OBFUSCATED" in sys.path:
                    sys.path.remove("YES_OBFUSCATED")
                    
            except Exception as e:
                print(f"❌ Error checking {db_name} database: {e}")
                all_empty = False
        else:
            print(f"❌ Database not found: {db_path}")
            all_empty = False
    
    return all_empty

def test_dialog_structure():
    """Test that dialog structure is correct"""
    
    print("\nTesting Dialog Structure")
    print("=" * 25)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_structured = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for empty text areas
            if "Start with empty text area" in content:
                print(f"   ✅ Empty text areas")
            else:
                print(f"   ❌ Empty text areas missing")
                all_structured = False
            
            # Check for button visibility fixes
            if "side=tk.BOTTOM" in content:
                print(f"   ✅ Button visibility fixes")
            else:
                print(f"   ❌ Button visibility fixes missing")
                all_structured = False
            
            # Check for confirm buttons
            if "📥 Add All Products" in content and "📥 Add All Categories" in content:
                print(f"   ✅ Confirm buttons present")
            else:
                print(f"   ❌ Confirm buttons missing")
                all_structured = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_structured = False
    
    return all_structured

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/pos_system.db"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            if file_path.endswith('.py'):
                # Check if it's actually obfuscated
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Obfuscated files should have scrambled names
                    if len(content) > 100 and ('import' in content):
                        print(f"   ✅ File appears to be obfuscated")
                    else:
                        print(f"   ⚠️ File may not be properly obfuscated")
                except:
                    print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all final fix tests"""
    
    print("🔧 FINAL FIXES TEST SUITE")
    print("=" * 30)
    
    tests = [
        ("Window Sizes", test_window_sizes),
        ("Empty Database", test_empty_database),
        ("Dialog Structure", test_dialog_structure),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 35)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Dialog windows are large enough")
        print("✅ Databases start empty (no default data)")
        print("✅ Dialog structure is correct")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Final fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 Final fixes successfully implemented!")
        print("📐 Dialog windows are large enough to show buttons")
        print("🗃️ Databases start completely empty")
        print("🔘 Confirm buttons are visible and working")
    else:
        print("\n❌ Final fixes need attention")
    
    exit(0 if success else 1)
