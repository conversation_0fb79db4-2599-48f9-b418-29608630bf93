#!/usr/bin/env python3
"""
Test the responsive design improvements for product display
"""

import tkinter as tk
from pathlib import Path
import sys

def test_responsive_calculations():
    """Test the responsive grid calculations"""
    
    print("Testing Responsive Product Display")
    print("=" * 40)
    
    # Test different screen resolutions
    test_resolutions = [
        (800, 600, "Very Small"),
        (1024, 768, "Small"),
        (1366, 768, "Medium"),
        (1920, 1080, "Large"),
        (2560, 1440, "Extra Large")
    ]
    
    print("📊 Responsive Grid Calculations:")
    print("-" * 40)
    
    for width, height, description in test_resolutions:
        # Apply the same logic as in pos_screen.py
        if width >= 1920:  # Large screens
            columns = 6
            button_width = 120
            button_height = 120
            font_size = 8
        elif width >= 1366:  # Medium screens
            columns = 5
            button_width = 110
            button_height = 110
            font_size = 8
        elif width >= 1024:  # Small screens
            columns = 4
            button_width = 100
            button_height = 100
            font_size = 7
        else:  # Very small screens
            columns = 3
            button_width = 90
            button_height = 90
            font_size = 7
        
        # Calculate how many products fit in view
        min_height = max(400, int(height * 0.6))
        rows_visible = min_height // (button_height + 6)  # +6 for padding
        products_visible = columns * rows_visible
        
        print(f"{description:12} ({width}x{height}):")
        print(f"  Columns: {columns}")
        print(f"  Button Size: {button_width}x{button_height}")
        print(f"  Font Size: {font_size}")
        print(f"  Canvas Height: {min_height}px")
        print(f"  Visible Rows: {rows_visible}")
        print(f"  Products Visible: {products_visible}")
        print()
    
    return True

def test_pos_screen_import():
    """Test that the updated pos_screen can be imported"""
    
    print("📦 Testing POS Screen Import:")
    print("-" * 30)
    
    try:
        # Test main version
        sys.path.insert(0, str(Path(".").absolute()))
        import pos_screen
        print("✅ Main pos_screen.py imported successfully")
        
        # Check if the responsive method exists
        if hasattr(pos_screen.POSScreen, 'load_products'):
            print("✅ load_products method found")
        else:
            print("❌ load_products method not found")
            return False
        
    except Exception as e:
        print(f"❌ Failed to import main pos_screen: {e}")
        return False
    
    try:
        # Test YES version
        sys.path.insert(0, str(Path("YES").absolute()))
        
        # Clear module cache
        if 'pos_screen' in sys.modules:
            del sys.modules['pos_screen']
        
        import pos_screen as pos_screen_yes
        print("✅ YES pos_screen.py imported successfully")
        
    except Exception as e:
        print(f"❌ Failed to import YES pos_screen: {e}")
        return False
    
    try:
        # Test YES_OBFUSCATED version
        sys.path.insert(0, str(Path("YES_OBFUSCATED").absolute()))
        
        # Clear module cache
        if 'pos_screen' in sys.modules:
            del sys.modules['pos_screen']
        
        import pos_screen as pos_screen_obf
        print("✅ YES_OBFUSCATED pos_screen.py imported successfully")
        
    except Exception as e:
        print(f"❌ Failed to import YES_OBFUSCATED pos_screen: {e}")
        return False
    
    return True

def create_visual_test():
    """Create a visual test window to demonstrate responsive design"""
    
    print("\n🖼️ Creating Visual Test Window:")
    print("-" * 30)
    
    try:
        root = tk.Tk()
        root.title("Responsive Design Test")
        root.geometry("800x600")
        
        # Get screen dimensions
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        # Apply responsive logic
        if screen_width >= 1920:
            columns = 6
            button_width = 120
            button_height = 120
            font_size = 8
        elif screen_width >= 1366:
            columns = 5
            button_width = 110
            button_height = 110
            font_size = 8
        elif screen_width >= 1024:
            columns = 4
            button_width = 100
            button_height = 100
            font_size = 7
        else:
            columns = 3
            button_width = 90
            button_height = 90
            font_size = 7
        
        # Create info label
        info_text = f"""Responsive Design Test
Screen: {screen_width}x{screen_height}
Columns: {columns}
Button Size: {button_width}x{button_height}
Font Size: {font_size}"""
        
        info_label = tk.Label(root, text=info_text, font=('Helvetica', 12), 
                             justify=tk.LEFT, bg='lightblue', padx=20, pady=20)
        info_label.pack(fill=tk.X)
        
        # Create scrollable frame to simulate product area
        canvas = tk.Canvas(root, bg='white')
        scrollbar = tk.Scrollbar(root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # Create sample product buttons
        for i in range(24):  # 24 sample products
            row = i // columns
            col = i % columns
            
            button_text = f"Product {i+1}\n15.99 MAD"
            btn = tk.Button(scrollable_frame, text=button_text,
                           font=('Helvetica', font_size, 'bold'),
                           bg='white', fg='#2c3e50',
                           width=button_width//8, height=button_height//20,
                           relief='raised', bd=1)
            btn.grid(row=row, column=col, padx=3, pady=3, sticky='nsew')
        
        # Configure grid weights
        for i in range(columns):
            scrollable_frame.grid_columnconfigure(i, weight=1)
        
        scrollable_frame.bind("<Configure>",
                             lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        
        # Add close button
        close_btn = tk.Button(root, text="Close Test", command=root.destroy,
                             font=('Helvetica', 10, 'bold'), bg='red', fg='white')
        close_btn.pack(pady=10)
        
        print("✅ Visual test window created")
        print("   Close the window to continue...")
        
        # Show window for 5 seconds then close automatically
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create visual test: {e}")
        return False

def main():
    """Run all responsive design tests"""
    
    print("🔧 RESPONSIVE DESIGN TEST SUITE")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Responsive calculations
    if test_responsive_calculations():
        tests_passed += 1
        print("✅ Test 1 PASSED: Responsive calculations")
    else:
        print("❌ Test 1 FAILED: Responsive calculations")
    
    # Test 2: Module imports
    if test_pos_screen_import():
        tests_passed += 1
        print("✅ Test 2 PASSED: Module imports")
    else:
        print("❌ Test 2 FAILED: Module imports")
    
    # Test 3: Visual test
    if create_visual_test():
        tests_passed += 1
        print("✅ Test 3 PASSED: Visual test")
    else:
        print("❌ Test 3 FAILED: Visual test")
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Responsive design is working correctly")
        print("✅ Products will adapt to different screen sizes")
        print("✅ No more hidden products on small screens")
    else:
        print("⚠️ Some tests failed")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
