"""
User Management Screen
Provides user administration functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import hashlib

from database import get_db_connection

class UserManagement:
    """User management interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.users_tree = None

    def show(self):
        """Display the user management screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create interface
        self.create_interface()
        self.load_users()

    def hide(self):
        """Hide the user management screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the user management interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('user_management'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Users list
        self.create_users_list(content_frame)

        # Action buttons
        self.create_action_buttons(content_frame)

    def create_users_list(self, parent):
        """Create users list display"""
        # Users frame
        users_frame = tk.LabelFrame(parent, text=self.app.get_text('users'),
                                   font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white')
        users_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Treeview for users
        columns = ('ID', 'Username', 'Role', 'Color')
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.users_tree.heading('ID', text='ID')
        self.users_tree.heading('Username', text=self.app.get_text('username_field'))
        self.users_tree.heading('Role', text=self.app.get_text('role'))
        self.users_tree.heading('Color', text=self.app.get_text('button_color'))

        self.users_tree.column('ID', width=50)
        self.users_tree.column('Username', width=150)
        self.users_tree.column('Role', width=100)
        self.users_tree.column('Color', width=100)

        # Scrollbar
        scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_action_buttons(self, parent):
        """Create action buttons"""
        buttons_frame = tk.Frame(parent, bg='#1a1a1a')
        buttons_frame.pack(fill=tk.X)

        # Add user button with icon
        if self.app.icons.get('add_extra'):
            add_btn = tk.Button(buttons_frame, image=self.app.icons['add_extra'],
                               text=self.app.get_text('add_user'),
                               compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                               bg='#28a745', fg='white', padx=20, pady=8,
                               command=self.add_user)
        else:
            add_btn = tk.Button(buttons_frame, text=self.app.get_text('add_user'),
                               font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                               padx=20, pady=8, command=self.add_user)
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Edit user button with icon
        if self.app.icons.get('edit'):
            edit_btn = tk.Button(buttons_frame, image=self.app.icons['edit'],
                                text=self.app.get_text('edit_user'),
                                compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                bg='#007bff', fg='white', padx=20, pady=8,
                                command=self.edit_user)
        else:
            edit_btn = tk.Button(buttons_frame, text=self.app.get_text('edit_user'),
                                font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                                padx=20, pady=8, command=self.edit_user)
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Delete user button with icon
        if self.app.icons.get('remove_selected'):
            delete_btn = tk.Button(buttons_frame, image=self.app.icons['remove_selected'],
                                  text=self.app.get_text('delete_user'),
                                  compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                  bg='#dc3545', fg='white', padx=20, pady=8,
                                  command=self.delete_user)
        else:
            delete_btn = tk.Button(buttons_frame, text=self.app.get_text('delete_user'),
                                  font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                                  padx=20, pady=8, command=self.delete_user)
        delete_btn.pack(side=tk.LEFT)

    def load_users(self):
        """Load users from database"""
        # Clear existing items
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT id, username, role, is_admin, button_color FROM users ORDER BY username")
            users = c.fetchall()

            for user in users:
                role_text = self.app.get_text('admin') if user['is_admin'] else self.app.get_text('user')
                self.users_tree.insert('', tk.END, values=(
                    user['id'], user['username'], role_text, user['button_color']
                ))
        finally:
            conn.close()

    def add_user(self):
        """Add new user"""
        self.show_user_dialog()

    def edit_user(self):
        """Edit selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_user_to_edit'))
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]

        # Get user data
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            user = c.fetchone()
            if user:
                self.show_user_dialog(dict(user))
        finally:
            conn.close()

    def delete_user(self):
        """Delete selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_user_to_delete'))
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]

        if messagebox.askyesno(self.app.get_text('confirm'),
                              f"{self.app.get_text('confirm_delete_user')} '{username}'?"):
            conn = get_db_connection()
            try:
                c = conn.cursor()
                c.execute("DELETE FROM users WHERE id = ?", (user_id,))
                conn.commit()
                messagebox.showinfo(self.app.get_text('success'),
                                  self.app.get_text('user_deleted_success'))
                self.load_users()
            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"{self.app.get_text('failed_delete_user')}: {e}")
            finally:
                conn.close()

    def show_user_dialog(self, user_data=None):
        """Show user add/edit dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title(self.app.get_text('edit_user') if user_data else self.app.get_text('add_user'))
        dialog.geometry("550x500")  # Much bigger size
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(bg='#1a1a1a')  # Dark background

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (550 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"550x500+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_text = self.app.get_text('edit_user') if user_data else self.app.get_text('add_user')
        header_label = tk.Label(header_frame, text=f"👤 {title_text}",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Variables
        username_var = tk.StringVar(value=user_data['username'] if user_data else '')
        password_var = tk.StringVar()
        is_admin_var = tk.BooleanVar(value=user_data['is_admin'] if user_data else False)
        color_var = tk.StringVar(value=user_data['button_color'] if user_data else '#ff8c00')

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Username section
        username_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        username_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(username_section, text=f"👤 {self.app.get_text('username_field')}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        username_entry = tk.Entry(username_section, textvariable=username_var,
                                 font=('Segoe UI', 12), bg='#404040', fg='white',
                                 insertbackground='white', relief='flat', bd=5)
        username_entry.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Password section
        password_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        password_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(password_section, text=f"🔒 {self.app.get_text('password_field')}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        password_entry = tk.Entry(password_section, textvariable=password_var, show='*',
                                 font=('Segoe UI', 12), bg='#404040', fg='white',
                                 insertbackground='white', relief='flat', bd=5)
        password_entry.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Admin checkbox section
        admin_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        admin_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(admin_section, text="⚙️ User Permissions",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))

        admin_check = tk.Checkbutton(admin_section, text=self.app.get_text('admin'),
                                    variable=is_admin_var, font=('Segoe UI', 11),
                                    bg='#2d2d2d', fg='white', selectcolor='#404040',
                                    activebackground='#2d2d2d', activeforeground='white')
        admin_check.pack(anchor='w', padx=15, pady=(0, 10))

        # Color selection
        color_frame = tk.Frame(main_frame, bg='#2d2d2d')
        color_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(color_frame, text=self.app.get_text('button_color'),
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        color_btn = tk.Button(color_frame, text=self.app.get_text('choose_color'),
                             bg=color_var.get(), width=15,
                             command=lambda: self.choose_color(color_var, color_btn))
        color_btn.pack(side=tk.RIGHT)

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        save_btn = tk.Button(buttons_frame, text=self.app.get_text('save'),
                            font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                            padx=20, pady=8,
                            command=lambda: self.save_user(dialog, user_data, username_var,
                                                          password_var, is_admin_var, color_var))
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text=self.app.get_text('cancel'),
                              font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                              padx=20, pady=8, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def choose_color(self, color_var, color_btn):
        """Choose button color"""
        color = colorchooser.askcolor(color=color_var.get())[1]
        if color:
            color_var.set(color)
            color_btn.config(bg=color)

    def save_user(self, dialog, user_data, username_var, password_var, is_admin_var, color_var):
        """Save user data"""
        username = username_var.get().strip()
        password = password_var.get()
        is_admin = is_admin_var.get()
        color = color_var.get()

        if not username:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('username_required'))
            return

        if not user_data and not password:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('password_required'))
            return

        conn = get_db_connection()
        try:
            c = conn.cursor()

            if user_data:  # Edit existing user
                if password:
                    hashed_password = hashlib.sha256(password.encode()).hexdigest()
                    c.execute("""
                        UPDATE users SET username=?, password=?, is_admin=?, button_color=?
                        WHERE id=?
                    """, (username, hashed_password, is_admin, color, user_data['id']))
                else:
                    c.execute("""
                        UPDATE users SET username=?, is_admin=?, button_color=?
                        WHERE id=?
                    """, (username, is_admin, color, user_data['id']))

                success_msg = self.app.get_text('user_updated_success')
            else:  # Add new user
                hashed_password = hashlib.sha256(password.encode()).hexdigest()
                c.execute("""
                    INSERT INTO users (username, password, is_admin, button_color)
                    VALUES (?, ?, ?, ?)
                """, (username, hashed_password, is_admin, color))

                success_msg = self.app.get_text('user_added_success')

            conn.commit()
            messagebox.showinfo(self.app.get_text('success'), success_msg)
            dialog.destroy()
            self.load_users()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_add_user')}: {e}")
        finally:
            conn.close()
