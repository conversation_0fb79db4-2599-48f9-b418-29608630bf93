#!/usr/bin/env python3
"""
Test the product button sizing fix for text-only buttons
"""

import sys
import os
from pathlib import Path

def test_button_sizing_logic():
    """Test that button sizing logic is correct"""
    
    print("Testing Button Sizing Logic")
    print("=" * 28)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for character-based sizing conversion
            if "char_width = max(8, min(20, button_size // 8))" in content:
                print(f"   ✅ Character width conversion found")
            else:
                print(f"   ❌ Character width conversion missing")
                all_fixed = False
            
            if "char_height = max(2, min(6, button_size // 25))" in content:
                print(f"   ✅ Character height conversion found")
            else:
                print(f"   ❌ Character height conversion missing")
                all_fixed = False
            
            # Check that text-only buttons use character sizing
            if "width=char_width, height=char_height" in content:
                print(f"   ✅ Text-only buttons use character sizing")
            else:
                print(f"   ❌ Text-only buttons still use pixel sizing")
                all_fixed = False
            
            # Check for proper comments
            if "proper character-based sizing" in content:
                print(f"   ✅ Proper documentation found")
            else:
                print(f"   ❌ Documentation missing")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_sizing_calculations():
    """Test the sizing calculation logic"""
    
    print("\nTesting Sizing Calculations")
    print("=" * 28)
    
    # Test different button sizes
    test_cases = [
        (80, 10, 3),    # Small: 80px -> 10 chars wide, 3 chars tall
        (130, 16, 5),   # Default: 130px -> 16 chars wide, 5 chars tall  
        (160, 20, 6),   # Large: 160px -> 20 chars wide, 6 chars tall
        (200, 20, 6),   # Max: 200px -> 20 chars wide (capped), 6 chars tall
    ]
    
    all_correct = True
    
    for button_size, expected_width, expected_height in test_cases:
        # Apply the same logic as in the code
        char_width = max(8, min(20, button_size // 8))
        char_height = max(2, min(6, button_size // 25))
        
        print(f"\n📐 Button size: {button_size}px")
        print(f"   Expected: {expected_width}w × {expected_height}h chars")
        print(f"   Actual:   {char_width}w × {char_height}h chars")
        
        if char_width == expected_width and char_height == expected_height:
            print(f"   ✅ Calculation correct")
        else:
            print(f"   ❌ Calculation incorrect")
            all_correct = False
    
    return all_correct

def test_image_vs_text_buttons():
    """Test that image buttons still use pixel sizing"""
    
    print("\nTesting Image vs Text Button Sizing")
    print("=" * 36)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that image buttons still use pixel sizing
            if "width=button_size, height=button_size" in content:
                # Count occurrences - should be for image buttons only
                pixel_sizing_count = content.count("width=button_size, height=button_size")
                print(f"   ✅ Image buttons use pixel sizing ({pixel_sizing_count} instances)")
            else:
                print(f"   ❌ Image buttons missing pixel sizing")
                all_correct = False
            
            # Check that text buttons use character sizing
            char_sizing_count = content.count("width=char_width, height=char_height")
            if char_sizing_count >= 2:  # Should be at least 2 (fallback + text-only)
                print(f"   ✅ Text buttons use character sizing ({char_sizing_count} instances)")
            else:
                print(f"   ❌ Text buttons missing character sizing")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_size_ranges():
    """Test that size ranges are reasonable"""
    
    print("\nTesting Size Ranges")
    print("=" * 20)
    
    # Test edge cases
    edge_cases = [
        (50, 8, 2),     # Very small -> minimum values
        (1000, 20, 6),  # Very large -> maximum values
        (0, 8, 2),      # Zero -> minimum values
    ]
    
    all_reasonable = True
    
    for button_size, expected_min_width, expected_min_height in edge_cases:
        char_width = max(8, min(20, button_size // 8))
        char_height = max(2, min(6, button_size // 25))
        
        print(f"\n📐 Edge case: {button_size}px")
        print(f"   Width: {char_width} chars (range: 8-20)")
        print(f"   Height: {char_height} chars (range: 2-6)")
        
        if 8 <= char_width <= 20 and 2 <= char_height <= 6:
            print(f"   ✅ Within reasonable range")
        else:
            print(f"   ❌ Outside reasonable range")
            all_reasonable = False
    
    return all_reasonable

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/pos_screen.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all button sizing tests"""
    
    print("🔘 BUTTON SIZING FIX TEST SUITE")
    print("=" * 35)
    
    tests = [
        ("Button Sizing Logic", test_button_sizing_logic),
        ("Sizing Calculations", test_sizing_calculations),
        ("Image vs Text Buttons", test_image_vs_text_buttons),
        ("Size Ranges", test_size_ranges),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 35)
    print("📊 RESULTS")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Button sizing logic is correct")
        print("✅ Text-only buttons use character units")
        print("✅ Image buttons still use pixel units")
        print("✅ Size ranges are reasonable")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Button sizing fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔘 Button sizing fix successfully implemented!")
        print("📐 Text-only buttons now use proper character sizing")
        print("🖼️ Image buttons continue to use pixel sizing")
        print("📏 Reasonable size ranges enforced")
        print("🎯 No more massive text-only buttons!")
    else:
        print("\n❌ Button sizing fixes need attention")
    
    exit(0 if success else 1)
