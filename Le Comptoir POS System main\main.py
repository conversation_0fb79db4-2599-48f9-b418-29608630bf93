#!/usr/bin/env python3
"""
Professional Point of Sale System
Main Application Entry Point
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import sqlite3
import hashlib
import json
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from pos_app import POSApplication
    from database import DatabaseManager
    from license_manager import LicenseManager
except ImportError as e:
    messagebox.showerror("Import Error", f"Failed to import required modules: {e}")
    sys.exit(1)

class POSSystem:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.license_manager = LicenseManager()
        
    def check_license(self):
        """Check if the system has a valid license"""
        if not self.license_manager.is_valid():
            messagebox.showerror("License Error", 
                               "Invalid or expired license. Please contact support.")
            return False
        return True
        
    def initialize_database(self):
        """Initialize database connection"""
        try:
            self.db_manager.connect()
            return True
        except Exception as e:
            messagebox.showerror("Database Error", 
                               f"Failed to connect to database: {e}")
            return False
            
    def run(self):
        """Start the POS application"""
        # Check license first
        if not self.check_license():
            sys.exit(1)
            
        # Initialize database
        if not self.initialize_database():
            sys.exit(1)
            
        # Start the main application
        try:
            app = POSApplication(self.db_manager)
            app.run()
        except Exception as e:
            messagebox.showerror("Application Error", 
                               f"Failed to start application: {e}")
            sys.exit(1)

def main():
    """Main entry point"""
    try:
        pos_system = POSSystem()
        pos_system.run()
    except KeyboardInterrupt:
        print("\nApplication terminated by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
