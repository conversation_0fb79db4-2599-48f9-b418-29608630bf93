"""
Product Management Screen
Provides product and category administration functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from PIL import Image, ImageTk

from database import get_db_connection

class ProductManagement:
    """Product and category management interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.notebook = None
        self.products_tree = None
        self.categories_tree = None

    def show(self):
        """Display the product management screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create interface
        self.create_interface()

    def hide(self):
        """Hide the product management screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the product management interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('product_management'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button (keep button color)
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content with tabs
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create notebook for tabs with custom styling
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#1a1a1a', borderwidth=0)
        style.configure('TNotebook.Tab', background='#2d2d2d', foreground='white',
                       padding=[20, 10], font=('Segoe UI', 12, 'bold'))
        style.map('TNotebook.Tab', background=[('selected', '#ff8c00')],
                 foreground=[('selected', 'white')])

        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Categories tab
        self.create_categories_tab()

        # Products tab
        self.create_products_tab()

    def create_categories_tab(self):
        """Create categories management tab"""
        cat_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(cat_frame, text=self.app.get_text('categories'))

        # Categories list
        list_frame = tk.Frame(cat_frame, bg='#2d2d2d')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Treeview for categories
        columns = ('ID', 'Name')
        self.categories_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        self.categories_tree.heading('ID', text='ID')
        self.categories_tree.heading('Name', text=self.app.get_text('name'))

        self.categories_tree.column('ID', width=50)
        self.categories_tree.column('Name', width=200)

        # Scrollbar
        cat_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=cat_scrollbar.set)

        self.categories_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cat_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Action buttons for categories
        cat_buttons_frame = tk.Frame(cat_frame, bg='#2d2d2d')
        cat_buttons_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        # Add category button with icon (keep button color)
        if self.app.icons.get('add_extra'):
            add_cat_btn = tk.Button(cat_buttons_frame, image=self.app.icons['add_extra'],
                                   text=self.app.get_text('add_category'),
                                   compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                   bg='#28a745', fg='white', padx=20, pady=8,
                                   command=self.add_category)
        else:
            add_cat_btn = tk.Button(cat_buttons_frame, text=self.app.get_text('add_category'),
                                   font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                                   padx=20, pady=8, command=self.add_category)
        add_cat_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Edit category button with icon
        if self.app.icons.get('edit'):
            edit_cat_btn = tk.Button(cat_buttons_frame, image=self.app.icons['edit'],
                                    text=self.app.get_text('edit_category'),
                                    compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                    bg='#007bff', fg='white', padx=20, pady=8,
                                    command=self.edit_category)
        else:
            edit_cat_btn = tk.Button(cat_buttons_frame, text=self.app.get_text('edit_category'),
                                    font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                                    padx=20, pady=8, command=self.edit_category)
        edit_cat_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Delete category button with icon
        if self.app.icons.get('remove_selected'):
            delete_cat_btn = tk.Button(cat_buttons_frame, image=self.app.icons['remove_selected'],
                                      text=self.app.get_text('delete_category'),
                                      compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                      bg='#dc3545', fg='white', padx=20, pady=8,
                                      command=self.delete_category)
        else:
            delete_cat_btn = tk.Button(cat_buttons_frame, text=self.app.get_text('delete_category'),
                                      font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                                      padx=20, pady=8, command=self.delete_category)
        delete_cat_btn.pack(side=tk.LEFT)

        # Load categories
        self.load_categories()

    def create_products_tab(self):
        """Create products management tab"""
        prod_frame = tk.Frame(self.notebook, bg='#2d2d2d')
        self.notebook.add(prod_frame, text=self.app.get_text('products'))

        # Products list
        list_frame = tk.Frame(prod_frame, bg='#2d2d2d')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Treeview for products
        columns = ('ID', 'Name', 'Category', 'Price')
        self.products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        self.products_tree.heading('ID', text='ID')
        self.products_tree.heading('Name', text=self.app.get_text('name'))
        self.products_tree.heading('Category', text=self.app.get_text('category'))
        self.products_tree.heading('Price', text=self.app.get_text('price'))

        self.products_tree.column('ID', width=50)
        self.products_tree.column('Name', width=150)
        self.products_tree.column('Category', width=120)
        self.products_tree.column('Price', width=100)

        # Scrollbar
        prod_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=prod_scrollbar.set)

        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        prod_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Action buttons for products
        prod_buttons_frame = tk.Frame(prod_frame, bg='#2d2d2d')
        prod_buttons_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        # Add product button with icon
        if self.app.icons.get('add_extra'):
            add_prod_btn = tk.Button(prod_buttons_frame, image=self.app.icons['add_extra'],
                                    text=self.app.get_text('add_product'),
                                    compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                    bg='#28a745', fg='white', padx=20, pady=8,
                                    command=self.add_product)
        else:
            add_prod_btn = tk.Button(prod_buttons_frame, text=self.app.get_text('add_product'),
                                    font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                                    padx=20, pady=8, command=self.add_product)
        add_prod_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Edit product button with icon
        if self.app.icons.get('edit'):
            edit_prod_btn = tk.Button(prod_buttons_frame, image=self.app.icons['edit'],
                                     text=self.app.get_text('edit_product'),
                                     compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                     bg='#007bff', fg='white', padx=20, pady=8,
                                     command=self.edit_product)
        else:
            edit_prod_btn = tk.Button(prod_buttons_frame, text=self.app.get_text('edit_product'),
                                     font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                                     padx=20, pady=8, command=self.edit_product)
        edit_prod_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Delete product button with icon
        if self.app.icons.get('remove_selected'):
            delete_prod_btn = tk.Button(prod_buttons_frame, image=self.app.icons['remove_selected'],
                                       text=self.app.get_text('delete_product'),
                                       compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                       bg='#dc3545', fg='white', padx=20, pady=8,
                                       command=self.delete_product)
        else:
            delete_prod_btn = tk.Button(prod_buttons_frame, text=self.app.get_text('delete_product'),
                                       font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                                       padx=20, pady=8, command=self.delete_product)
        delete_prod_btn.pack(side=tk.LEFT)

        # Load products
        self.load_products()

    def load_categories(self):
        """Load categories from database"""
        if not self.categories_tree:
            return

        # Clear existing items
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT id, name FROM categories ORDER BY name")
            categories = c.fetchall()

            for category in categories:
                self.categories_tree.insert('', tk.END, values=(category['id'], category['name']))
        finally:
            conn.close()

    def load_products(self):
        """Load products from database with optimized caching"""
        if not self.products_tree:
            return

        # Clear existing items efficiently
        children = self.products_tree.get_children()
        if children:
            self.products_tree.delete(*children)

        try:
            # Use cached data for better performance
            from database import get_products_cached
            products = get_products_cached()

            # Batch insert for better performance
            for product in products:
                self.products_tree.insert('', tk.END, values=(
                    product['id'], product['name'],
                    product['category_name'] or 'No Category',
                    f"{product['price']:.2f} MAD"
                ))
        except Exception as e:
            print(f"Error loading products: {e}")
            # Fallback to direct database query if cache fails
            conn = get_db_connection()
            try:
                c = conn.cursor()
                c.execute("""
                    SELECT p.id, p.name, c.name as category_name, p.price
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    ORDER BY p.name
                """)
                products = c.fetchall()

                for product in products:
                    self.products_tree.insert('', tk.END, values=(
                        product['id'], product['name'],
                        product['category_name'] or 'No Category',
                        f"{product['price']:.2f} MAD"
                    ))
            finally:
                conn.close()

    def add_category(self):
        """Add new category with option for bulk entry"""
        # Create a choice dialog - BIGGER SIZE
        choice_dialog = tk.Toplevel(self.root)
        choice_dialog.title("Add Categories")
        choice_dialog.geometry("500x300")  # Increased from 400x200
        choice_dialog.configure(bg='#2d2d2d')
        choice_dialog.transient(self.root)
        choice_dialog.grab_set()

        # Center the dialog
        choice_dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # Main frame
        main_frame = tk.Frame(choice_dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # Title
        title_label = tk.Label(main_frame, text="Choose how to add categories:",
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(pady=(0, 30))

        # Single category button - BIGGER
        single_btn = tk.Button(main_frame, text="📝 Add Single Category",
                              font=('Segoe UI', 14), bg='#007bff', fg='white',
                              padx=30, pady=15, width=25,
                              command=lambda: [choice_dialog.destroy(), self.show_category_dialog()])
        single_btn.pack(pady=(0, 15))

        # Bulk categories button - BIGGER
        bulk_btn = tk.Button(main_frame, text="📋 Add Multiple Categories",
                            font=('Segoe UI', 14), bg='#28a745', fg='white',
                            padx=30, pady=15, width=25,
                            command=lambda: [choice_dialog.destroy(), self.show_bulk_category_dialog()])
        bulk_btn.pack(pady=(0, 15))

        # Cancel button - BIGGER
        cancel_btn = tk.Button(main_frame, text="Cancel",
                              font=('Segoe UI', 12), bg='#6c757d', fg='white',
                              padx=30, pady=10, width=15,
                              command=choice_dialog.destroy)
        cancel_btn.pack()

    def show_bulk_category_dialog(self):
        """Show bulk category entry dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Multiple Categories")
        dialog.geometry("650x550")  # Even bigger to ensure all content fits
        dialog.configure(bg='#1a1a1a')  # Dark background
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (650 // 2)
        y = (dialog.winfo_screenheight() // 2) - (550 // 2)
        dialog.geometry(f"650x550+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text="📁 Add Multiple Categories",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Text area for bulk entry
        text_frame = tk.Frame(main_frame, bg='#2d2d2d')
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        tk.Label(text_frame, text="Enter Categories (one per line):",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white').pack(anchor=tk.W, pady=(0, 5))

        # Text widget with scrollbar
        text_widget_frame = tk.Frame(text_frame, bg='#2d2d2d')
        text_widget_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_widget_frame, height=12, width=50,
                             font=('Segoe UI', 11), bg='#404040', fg='white',
                             wrap=tk.WORD, relief=tk.SUNKEN, bd=1,
                             insertbackground='white')
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(text_widget_frame, orient=tk.VERTICAL, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(yscrollcommand=scrollbar.set)

        # Add placeholder example text
        placeholder_text = """Drinks
Food
Snacks
Desserts
Hot Beverages"""
        text_widget.insert('1.0', placeholder_text)
        text_widget.config(fg='#888888')  # Gray color for placeholder

        # Function to handle placeholder behavior
        def on_focus_in(event):
            if text_widget.get('1.0', 'end-1c') == placeholder_text:
                text_widget.delete('1.0', 'end')
                text_widget.config(fg='white')

        def on_focus_out(event):
            if not text_widget.get('1.0', 'end-1c').strip():
                text_widget.insert('1.0', placeholder_text)
                text_widget.config(fg='#888888')

        text_widget.bind('<FocusIn>', on_focus_in)
        text_widget.bind('<FocusOut>', on_focus_out)
        text_widget.focus()

        # Buttons frame - ensure it's visible
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0), side=tk.BOTTOM)

        def save_categories():
            content = text_widget.get("1.0", tk.END).strip()
            if not content:
                messagebox.showerror("Error", "Please enter at least one category.")
                return

            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if not lines:
                messagebox.showerror("Error", "Please enter at least one category.")
                return

            categories_to_add = []
            errors = []

            # Parse each line
            for i, line in enumerate(lines, 1):
                name = line.strip()

                if not name:
                    errors.append(f"Line {i}: Empty category name")
                    continue

                categories_to_add.append(name)

            if errors:
                error_msg = "Found errors:\n\n" + "\n".join(errors)
                error_msg += "\n\nDo you want to continue with valid categories?"
                if not messagebox.askyesno("Errors Found", error_msg):
                    return

            if not categories_to_add:
                messagebox.showerror("Error", "No valid categories to add.")
                return

            # Add categories to database
            conn = get_db_connection()
            try:
                cursor = conn.cursor()
                added_count = 0
                skipped_count = 0

                for name in categories_to_add:
                    try:
                        cursor.execute("INSERT INTO categories (name) VALUES (?)", (name,))
                        added_count += 1
                    except sqlite3.IntegrityError:
                        skipped_count += 1  # Category already exists

                conn.commit()

                # Invalidate cache after category changes
                from database import invalidate_categories_cache, invalidate_products_cache
                invalidate_categories_cache()
                invalidate_products_cache()  # Products cache includes category names

                result_msg = f"Categories added successfully!\n\n"
                result_msg += f"Added: {added_count} categories\n"
                if skipped_count > 0:
                    result_msg += f"Skipped: {skipped_count} (already exist)"

                messagebox.showinfo("Success", result_msg)
                dialog.destroy()
                self.load_categories()

            except Exception as e:
                messagebox.showerror("Error", f"Database error: {e}")
            finally:
                conn.close()

        def clear_text():
            text_widget.delete("1.0", tk.END)

        # Clear button
        clear_btn = tk.Button(buttons_frame, text="Clear",
                             font=('Segoe UI', 10), bg='#ffc107', fg='white',
                             padx=20, pady=8, command=clear_text)
        clear_btn.pack(side=tk.LEFT)

        # Save button
        save_btn = tk.Button(buttons_frame, text="📥 Add All Categories",
                            font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                            padx=25, pady=8, command=save_categories)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Cancel button
        cancel_btn = tk.Button(buttons_frame, text="Cancel",
                              font=('Segoe UI', 11), bg='#6c757d', fg='white',
                              padx=20, pady=8, command=dialog.destroy)
        cancel_btn.pack(side=tk.RIGHT)

    def edit_category(self):
        """Edit selected category"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_category_to_edit'))
            return

        item = self.categories_tree.item(selection[0])
        category_id = item['values'][0]

        # Get category data
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM categories WHERE id = ?", (category_id,))
            category = c.fetchone()
            if category:
                self.show_category_dialog(dict(category))
        finally:
            conn.close()

    def delete_category(self):
        """Delete selected category"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_category_to_delete'))
            return

        item = self.categories_tree.item(selection[0])
        category_id = item['values'][0]
        category_name = item['values'][1]

        if messagebox.askyesno(self.app.get_text('confirm'),
                              f"{self.app.get_text('confirm_delete_category')} '{category_name}'?"):
            conn = get_db_connection()
            try:
                c = conn.cursor()
                c.execute("DELETE FROM categories WHERE id = ?", (category_id,))
                conn.commit()

                # Invalidate cache after category deletion
                from database import invalidate_categories_cache, invalidate_products_cache
                invalidate_categories_cache()
                invalidate_products_cache()  # Products cache includes category names

                messagebox.showinfo(self.app.get_text('success'),
                                  self.app.get_text('category_deleted_success'))
                self.load_categories()
                self.load_products()  # Refresh products too
            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"{self.app.get_text('failed_delete_category')}: {e}")
            finally:
                conn.close()

    def show_category_dialog(self, category_data=None):
        """Show category add/edit dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title(self.app.get_text('edit_category') if category_data else self.app.get_text('add_category'))
        dialog.geometry("600x500")  # Even bigger to ensure all content fits
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(bg='#1a1a1a')  # Dark background

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_text = self.app.get_text('edit_category') if category_data else self.app.get_text('add_category')
        header_label = tk.Label(header_frame, text=f"📁 {title_text}",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Variables
        name_var = tk.StringVar(value=category_data['name'] if category_data else '')
        selected_image_path = tk.StringVar()

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Category name section
        name_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        name_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(name_section, text=f"📝 {self.app.get_text('category_name')}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        name_entry = tk.Entry(name_section, textvariable=name_var,
                             font=('Segoe UI', 12), bg='#404040', fg='white',
                             insertbackground='white', relief='flat', bd=5)
        name_entry.pack(fill=tk.X, padx=15, pady=(0, 10))
        name_entry.focus()

        # Image selection section
        image_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        image_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(image_section, text=f"🖼️ {self.app.get_text('image')} (Optional)",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))

        image_frame = tk.Frame(image_section, bg='#2d2d2d')
        image_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        image_btn = tk.Button(image_frame, text=f"📁 {self.app.get_text('select_image')}",
                             font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                             relief='flat', bd=0, padx=15, pady=8,
                             activebackground='#0056b3', activeforeground='white',
                             command=lambda: self.select_category_image(selected_image_path, image_label))
        image_btn.pack(side=tk.LEFT, padx=(0, 10))

        image_label = tk.Label(image_frame, text=self.app.get_text('no_image_selected'),
                              font=('Segoe UI', 10), bg='#2d2d2d', fg='white')
        image_label.pack(side=tk.LEFT)

        # Show current image if editing
        if category_data and category_data.get('image'):
            image_label.config(text="Current image: Category has image", fg='#28a745')

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        save_btn = tk.Button(buttons_frame, text=self.app.get_text('save'),
                            font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                            padx=20, pady=8,
                            command=lambda: self.save_category(dialog, category_data, name_var, selected_image_path))
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text=self.app.get_text('cancel'),
                              font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                              padx=20, pady=8, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def select_category_image(self, image_path_var, image_label):
        """Select category image"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="Select Category Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            image_path_var.set(file_path)
            filename = file_path.split('/')[-1]
            image_label.config(text=f"Selected: {filename}", fg='#28a745')

    def save_category(self, dialog, category_data, name_var, image_path_var):
        """Save category data"""
        name = name_var.get().strip()
        image_path = image_path_var.get()

        if not name:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('category_name_required'))
            return

        # Read image file if selected
        image_data = None
        if image_path:
            try:
                with open(image_path, 'rb') as f:
                    image_data = f.read()
            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"Error reading image file: {e}")
                return

        conn = get_db_connection()
        try:
            c = conn.cursor()

            if category_data:  # Edit existing category
                if image_data:
                    c.execute("UPDATE categories SET name=?, image=? WHERE id=?",
                             (name, image_data, category_data['id']))
                else:
                    c.execute("UPDATE categories SET name=? WHERE id=?",
                             (name, category_data['id']))
                success_msg = self.app.get_text('category_updated_success')
            else:  # Add new category
                c.execute("INSERT INTO categories (name, image) VALUES (?, ?)",
                         (name, image_data))
                success_msg = self.app.get_text('category_added_success')

            conn.commit()

            # Invalidate cache after category changes
            from database import invalidate_categories_cache, invalidate_products_cache
            invalidate_categories_cache()
            invalidate_products_cache()  # Products cache includes category names

            messagebox.showinfo(self.app.get_text('success'), success_msg)
            dialog.destroy()
            self.load_categories()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_add_category')}: {e}")
        finally:
            conn.close()

    def add_product(self):
        """Add new product with option for bulk entry"""
        # Create a choice dialog - BIGGER SIZE
        choice_dialog = tk.Toplevel(self.root)
        choice_dialog.title("Add Products")
        choice_dialog.geometry("500x300")  # Increased from 400x200
        choice_dialog.configure(bg='#2d2d2d')
        choice_dialog.transient(self.root)
        choice_dialog.grab_set()

        # Center the dialog
        choice_dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # Main frame
        main_frame = tk.Frame(choice_dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # Title
        title_label = tk.Label(main_frame, text="Choose how to add products:",
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(pady=(0, 30))

        # Single product button - BIGGER
        single_btn = tk.Button(main_frame, text="📝 Add Single Product",
                              font=('Segoe UI', 14), bg='#007bff', fg='white',
                              padx=30, pady=15, width=25,
                              command=lambda: [choice_dialog.destroy(), self.show_product_dialog()])
        single_btn.pack(pady=(0, 15))

        # Bulk products button - BIGGER
        bulk_btn = tk.Button(main_frame, text="📋 Add Multiple Products",
                            font=('Segoe UI', 14), bg='#28a745', fg='white',
                            padx=30, pady=15, width=25,
                            command=lambda: [choice_dialog.destroy(), self.show_bulk_product_dialog()])
        bulk_btn.pack(pady=(0, 15))

        # Cancel button - BIGGER
        cancel_btn = tk.Button(main_frame, text="Cancel",
                              font=('Segoe UI', 12), bg='#6c757d', fg='white',
                              padx=30, pady=10, width=15,
                              command=choice_dialog.destroy)
        cancel_btn.pack()

    def show_bulk_product_dialog(self):
        """Show bulk product entry dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Multiple Products")
        dialog.geometry("800x650")  # Even bigger to ensure all content fits
        dialog.configure(bg='#1a1a1a')  # Dark background
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (650 // 2)
        dialog.geometry(f"800x650+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text="📋 Add Multiple Products",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Removed instructions - using placeholder text instead

        # Default category selection
        category_frame = tk.Frame(main_frame, bg='#2d2d2d')
        category_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(category_frame, text="Default Category (for products without category specified):",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white').pack(anchor=tk.W, pady=(0, 5))

        # Get categories for dropdown
        categories = self.get_categories_list()
        category_names = [cat['name'] for cat in categories]

        default_category_var = tk.StringVar()
        category_combo = ttk.Combobox(category_frame, textvariable=default_category_var,
                                     values=category_names, state='readonly',
                                     font=('Segoe UI', 10), width=30)
        category_combo.pack(anchor=tk.W)

        # Set first category as default if available
        if category_names:
            default_category_var.set(category_names[0])

        # Text area for bulk entry
        text_frame = tk.Frame(main_frame, bg='#2d2d2d')
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        tk.Label(text_frame, text="Enter Products (one per line):",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white').pack(anchor=tk.W, pady=(0, 5))

        # Text widget with scrollbar
        text_widget_frame = tk.Frame(text_frame, bg='#2d2d2d')
        text_widget_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_widget_frame, height=12, width=65,
                             font=('Segoe UI', 11), bg='#404040', fg='white',
                             wrap=tk.WORD, relief=tk.SUNKEN, bd=1,
                             insertbackground='white')
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(text_widget_frame, orient=tk.VERTICAL, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(yscrollcommand=scrollbar.set)

        # Add placeholder example text
        placeholder_text = """Cola, 5.50, Drinks
Milk, 3.00
Bread, 2.50
Coffee, 8.99, Hot Beverages
Sandwich, 6.75"""
        text_widget.insert('1.0', placeholder_text)
        text_widget.config(fg='#888888')  # Gray color for placeholder

        # Function to handle placeholder behavior
        def on_focus_in_products(event):
            if text_widget.get('1.0', 'end-1c') == placeholder_text:
                text_widget.delete('1.0', 'end')
                text_widget.config(fg='white')

        def on_focus_out_products(event):
            if not text_widget.get('1.0', 'end-1c').strip():
                text_widget.insert('1.0', placeholder_text)
                text_widget.config(fg='#888888')

        text_widget.bind('<FocusIn>', on_focus_in_products)
        text_widget.bind('<FocusOut>', on_focus_out_products)
        text_widget.focus()

        # Buttons frame - ensure it's visible
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0), side=tk.BOTTOM)

        def save_products():
            content = text_widget.get("1.0", tk.END).strip()
            if not content:
                messagebox.showerror("Error", "Please enter at least one product.")
                return

            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if not lines:
                messagebox.showerror("Error", "Please enter at least one product.")
                return

            default_category_name = default_category_var.get()
            if not default_category_name:
                messagebox.showerror("Error", "Please select a default category.")
                return

            # Get default category ID
            default_category_id = None
            for cat in categories:
                if cat['name'] == default_category_name:
                    default_category_id = cat['id']
                    break

            products_to_add = []
            errors = []

            # Parse each line
            for i, line in enumerate(lines, 1):
                parts = [part.strip() for part in line.split(',')]

                if len(parts) == 1:
                    # Only name provided
                    name = parts[0]
                    price = 0.0
                    category_id = default_category_id
                elif len(parts) == 2:
                    # Name and price provided
                    name = parts[0]
                    try:
                        price = float(parts[1]) if parts[1] else 0.0
                    except ValueError:
                        errors.append(f"Line {i}: Invalid price '{parts[1]}'")
                        continue
                    category_id = default_category_id
                elif len(parts) == 3:
                    # Name, price, and category provided
                    name = parts[0]
                    try:
                        price = float(parts[1]) if parts[1] else 0.0
                    except ValueError:
                        errors.append(f"Line {i}: Invalid price '{parts[1]}'")
                        continue

                    # Find category ID
                    category_name = parts[2]
                    category_id = None
                    for cat in categories:
                        if cat['name'].lower() == category_name.lower():
                            category_id = cat['id']
                            break

                    if not category_id:
                        errors.append(f"Line {i}: Category '{category_name}' not found")
                        continue
                else:
                    errors.append(f"Line {i}: Too many parts (max 3: name, price, category)")
                    continue

                if not name:
                    errors.append(f"Line {i}: Empty product name")
                    continue

                products_to_add.append((name, price, category_id))

            if errors:
                error_msg = "Found errors:\n\n" + "\n".join(errors[:10])  # Show first 10 errors
                if len(errors) > 10:
                    error_msg += f"\n... and {len(errors) - 10} more errors"
                error_msg += "\n\nDo you want to continue with valid products?"
                if not messagebox.askyesno("Errors Found", error_msg):
                    return

            if not products_to_add:
                messagebox.showerror("Error", "No valid products to add.")
                return

            # Add products to database
            conn = get_db_connection()
            try:
                cursor = conn.cursor()
                added_count = 0
                skipped_count = 0

                for name, price, category_id in products_to_add:
                    try:
                        cursor.execute("""
                            INSERT INTO products (name, category_id, price)
                            VALUES (?, ?, ?)
                        """, (name, category_id, price))
                        added_count += 1
                    except sqlite3.IntegrityError:
                        skipped_count += 1  # Product already exists

                conn.commit()

                # Invalidate cache after product changes
                from database import invalidate_products_cache
                invalidate_products_cache()

                result_msg = f"Products added successfully!\n\n"
                result_msg += f"Added: {added_count} products\n"
                if skipped_count > 0:
                    result_msg += f"Skipped: {skipped_count} (already exist)"

                messagebox.showinfo("Success", result_msg)
                dialog.destroy()
                self.load_products()

            except Exception as e:
                messagebox.showerror("Error", f"Database error: {e}")
            finally:
                conn.close()

        def clear_text():
            text_widget.delete("1.0", tk.END)

        # Clear button
        clear_btn = tk.Button(buttons_frame, text="Clear",
                             font=('Segoe UI', 10), bg='#ffc107', fg='white',
                             padx=20, pady=8, command=clear_text)
        clear_btn.pack(side=tk.LEFT)

        # Save button
        save_btn = tk.Button(buttons_frame, text="📥 Add All Products",
                            font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                            padx=25, pady=8, command=save_products)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Cancel button
        cancel_btn = tk.Button(buttons_frame, text="Cancel",
                              font=('Segoe UI', 11), bg='#6c757d', fg='white',
                              padx=20, pady=8, command=dialog.destroy)
        cancel_btn.pack(side=tk.RIGHT)

    def edit_product(self):
        """Edit selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_product_to_edit'))
            return

        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]

        # Get product data
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM products WHERE id = ?", (product_id,))
            product = c.fetchone()
            if product:
                self.show_product_dialog(dict(product))
        finally:
            conn.close()

    def delete_product(self):
        """Delete selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('select_product_to_delete'))
            return

        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]
        product_name = item['values'][1]

        if messagebox.askyesno(self.app.get_text('confirm'),
                              f"{self.app.get_text('confirm_delete_product')} '{product_name}'?"):
            conn = get_db_connection()
            try:
                c = conn.cursor()
                c.execute("DELETE FROM products WHERE id = ?", (product_id,))
                conn.commit()

                # Invalidate cache after product deletion
                from database import invalidate_products_cache
                invalidate_products_cache()

                messagebox.showinfo(self.app.get_text('success'),
                                  self.app.get_text('product_deleted_success'))
                self.load_products()
            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"{self.app.get_text('failed_delete_product')}: {e}")
            finally:
                conn.close()

    def show_product_dialog(self, product_data=None):
        """Show product add/edit dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title(self.app.get_text('edit_product') if product_data else self.app.get_text('add_product'))
        dialog.geometry("650x700")  # Even bigger to ensure all content fits
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(bg='#1a1a1a')  # Dark background

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (650 // 2)
        y = (dialog.winfo_screenheight() // 2) - (700 // 2)
        dialog.geometry(f"650x700+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_text = self.app.get_text('edit_product') if product_data else self.app.get_text('add_product')
        header_label = tk.Label(header_frame, text=f"📝 {title_text}",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Variables
        name_var = tk.StringVar(value=product_data['name'] if product_data else '')
        category_var = tk.StringVar()
        price_var = tk.StringVar(value=str(product_data['price']) if product_data else '0.00')
        selected_image_path = tk.StringVar()
        track_storage_var = tk.BooleanVar()

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Product name section
        name_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        name_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(name_section, text=f"📝 {self.app.get_text('product_name')}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        name_entry = tk.Entry(name_section, textvariable=name_var,
                             font=('Segoe UI', 12), bg='#404040', fg='white',
                             insertbackground='white', relief='flat', bd=5)
        name_entry.pack(fill=tk.X, padx=15, pady=(0, 10))
        name_entry.focus()

        # Category selection section
        category_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        category_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(category_section, text=f"📁 {self.app.get_text('category')}",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))

        # Get categories for dropdown
        categories = self.get_categories_list()
        category_names = [cat['name'] for cat in categories]

        category_combo = ttk.Combobox(category_section, textvariable=category_var,
                                     values=category_names, state='readonly',
                                     font=('Segoe UI', 11))
        category_combo.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Set current category if editing
        if product_data and product_data.get('category_id'):
            for cat in categories:
                if cat['id'] == product_data['category_id']:
                    category_var.set(cat['name'])
                    break

        # Price section
        price_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        price_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(price_section, text=f"💰 {self.app.get_text('price')} (MAD)",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        price_entry = tk.Entry(price_section, textvariable=price_var,
                              font=('Segoe UI', 12), bg='#404040', fg='white',
                              insertbackground='white', relief='flat', bd=5)
        price_entry.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Storage tracking checkbox
        storage_frame = tk.Frame(main_frame, bg='#2d2d2d')
        storage_frame.pack(fill=tk.X, pady=(0, 15))

        storage_checkbox = tk.Checkbutton(storage_frame,
                                        text=self.app.get_text('track_in_storage'),
                                        variable=track_storage_var,
                                        font=('Segoe UI', 11),
                                        bg='#2d2d2d',
                                        fg='white',
                                        selectcolor='#404040',
                                        activebackground='#2d2d2d',
                                        activeforeground='white')
        storage_checkbox.pack(anchor='w')

        # Unit selection (only shown when storage tracking is enabled)
        unit_frame = tk.Frame(main_frame, bg='#2d2d2d')
        unit_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(unit_frame, text=self.app.get_text('unit_for_storage'),
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))

        # Comprehensive list of units
        units = [
            "pieces", "items", "units", "pcs",  # Count-based
            "kg", "g", "lb", "oz", "ton",  # Weight
            "L", "ml", "gal", "fl oz", "cup",  # Volume/Liquid
            "m", "cm", "mm", "ft", "in",  # Length
            "m²", "cm²", "ft²", "sq ft",  # Area
            "box", "pack", "case", "carton",  # Packaging
            "bottle", "can", "jar", "bag",  # Containers
            "dozen", "pair", "set", "roll",  # Groups
            "sheet", "page", "slice", "portion"  # Portions
        ]

        unit_var = tk.StringVar(value="pieces")
        unit_combo = ttk.Combobox(unit_frame, textvariable=unit_var,
                                 values=units, font=('Segoe UI', 10), width=20, state="readonly")
        unit_combo.pack(anchor='w')
        unit_combo.set("pieces")  # Default

        # Function to show/hide unit selection based on storage checkbox
        def toggle_unit_visibility():
            if track_storage_var.get():
                unit_frame.pack(fill=tk.X, pady=(0, 15))
            else:
                unit_frame.pack_forget()

        # Initially hide unit selection if not tracking storage
        if not track_storage_var.get():
            unit_frame.pack_forget()

        # Bind checkbox to show/hide unit selection
        storage_checkbox.configure(command=toggle_unit_visibility)

        # Check if product is already tracked in storage (when editing)
        if product_data:
            conn = get_db_connection()
            try:
                c = conn.cursor()
                c.execute("SELECT unit FROM storage WHERE product_id = ?", (product_data['id'],))
                result = c.fetchone()
                if result:
                    track_storage_var.set(True)
                    unit_var.set(result['unit'])
                    toggle_unit_visibility()  # Show unit selection
            finally:
                conn.close()

        # Image selection (placeholder for now)
        tk.Label(main_frame, text=self.app.get_text('image') + ' (Optional)',
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(anchor='w', pady=(0, 5))

        image_frame = tk.Frame(main_frame, bg='#2d2d2d')
        image_frame.pack(fill=tk.X, pady=(0, 20))

        image_btn = tk.Button(image_frame, text=self.app.get_text('select_image'),
                             font=('Segoe UI', 9), bg='#007bff', fg='white',
                             padx=15, pady=5,
                             command=lambda: self.select_product_image(selected_image_path, image_label))
        image_btn.pack(side=tk.LEFT, padx=(0, 10))

        image_label = tk.Label(image_frame, text=self.app.get_text('no_image_selected'),
                              font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        image_label.pack(side=tk.LEFT)

        # Show current image if editing
        if product_data and product_data.get('image'):
            image_label.config(text="Current image: Product has image", fg='#28a745')

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        save_btn = tk.Button(buttons_frame, text=self.app.get_text('save'),
                            font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                            padx=20, pady=8,
                            command=lambda: self.save_product(dialog, product_data, name_var,
                                                             category_var, price_var, categories,
                                                             selected_image_path, track_storage_var, unit_var))
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text=self.app.get_text('cancel'),
                              font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                              padx=20, pady=8, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

    def get_categories_list(self):
        """Get list of categories for dropdown"""
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT id, name FROM categories ORDER BY name")
            return [dict(row) for row in c.fetchall()]
        finally:
            conn.close()

    def select_product_image(self, image_path_var, image_label):
        """Select product image"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="Select Product Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            image_path_var.set(file_path)
            filename = file_path.split('/')[-1]
            image_label.config(text=f"Selected: {filename}", fg='#28a745')

    def save_product(self, dialog, product_data, name_var, category_var, price_var, categories, image_path_var, track_storage_var, unit_var):
        """Save product data"""
        name = name_var.get().strip()
        category_name = category_var.get()
        price_str = price_var.get().strip()
        image_path = image_path_var.get()
        track_storage = track_storage_var.get()
        unit = unit_var.get() if track_storage else "pieces"

        # Validation
        if not name:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('name_category_price_required'))
            return

        if not category_name:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('select_valid_category'))
            return

        try:
            price = float(price_str)
            if price < 0:
                messagebox.showerror(self.app.get_text('error'),
                                   self.app.get_text('price_cannot_negative'))
                return
        except ValueError:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('invalid_price'))
            return

        # Get category ID
        category_id = None
        for cat in categories:
            if cat['name'] == category_name:
                category_id = cat['id']
                break

        if not category_id:
            messagebox.showerror(self.app.get_text('error'),
                               self.app.get_text('select_valid_category'))
            return

        # Read image file if selected
        image_data = None
        if image_path:
            try:
                with open(image_path, 'rb') as f:
                    image_data = f.read()
            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"Error reading image file: {e}")
                return

        conn = get_db_connection()
        try:
            c = conn.cursor()

            if product_data:  # Edit existing product
                if image_data:
                    c.execute("""
                        UPDATE products SET name=?, category_id=?, price=?, image=?
                        WHERE id=?
                    """, (name, category_id, price, image_data, product_data['id']))
                else:
                    c.execute("""
                        UPDATE products SET name=?, category_id=?, price=?
                        WHERE id=?
                    """, (name, category_id, price, product_data['id']))

                product_id = product_data['id']
                success_msg = self.app.get_text('product_updated_success')
            else:  # Add new product
                c.execute("""
                    INSERT INTO products (name, category_id, price, image)
                    VALUES (?, ?, ?, ?)
                """, (name, category_id, price, image_data))

                product_id = c.lastrowid
                success_msg = self.app.get_text('product_added_success')

            # Handle storage tracking
            self.handle_storage_tracking(c, product_id, track_storage, unit)

            conn.commit()

            # Invalidate cache after product changes
            from database import invalidate_products_cache
            invalidate_products_cache()

            messagebox.showinfo(self.app.get_text('success'), success_msg)
            dialog.destroy()
            self.load_products()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_add_product')}: {e}")
        finally:
            conn.close()

    def handle_storage_tracking(self, cursor, product_id, track_storage, unit="pieces"):
        """Handle adding/removing product from storage tracking"""
        # Check if product is currently tracked
        cursor.execute("SELECT id FROM storage WHERE product_id = ?", (product_id,))
        existing_storage = cursor.fetchone()

        if track_storage and not existing_storage:
            # Add to storage tracking with specified unit
            cursor.execute("""
                INSERT INTO storage (product_id, current_stock, min_stock_level, max_stock_level, unit)
                VALUES (?, 0, 5, 100, ?)
            """, (product_id, unit))
        elif track_storage and existing_storage:
            # Update existing storage with new unit
            cursor.execute("UPDATE storage SET unit = ? WHERE product_id = ?", (unit, product_id))
        elif not track_storage and existing_storage:
            # Remove from storage tracking
            cursor.execute("DELETE FROM storage WHERE product_id = ?", (product_id,))
            # Also remove stock movements
            cursor.execute("DELETE FROM stock_movements WHERE product_id = ?", (product_id,))

