#!/usr/bin/env python3
"""
Test the layout optimizations: categories width reduction, product button spacing, and receipt formatting
"""

import sys
import os
from pathlib import Path

def test_categories_width_reduction():
    """Test that categories area and buttons are reduced in width"""
    
    print("Testing Categories Width Reduction")
    print("=" * 35)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_reduced = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for 15% reduction in categories area (240 -> 204)
            if "minsize=204" in content and "15% less than 240" in content:
                print(f"   ✅ Categories area reduced 15% (240 -> 204)")
            else:
                print(f"   ❌ Categories area not reduced")
                all_reduced = False
            
            # Check for categories frame width reduction
            if "width=204" in content and "cat_frame" in content:
                print(f"   ✅ Categories frame reduced to 204px")
            else:
                print(f"   ❌ Categories frame not reduced")
                all_reduced = False
            
            # Check for canvas width reduction
            if "width=184" in content and "cat_canvas" in content:
                print(f"   ✅ Categories canvas reduced to 184px")
            else:
                print(f"   ❌ Categories canvas not reduced")
                all_reduced = False
            
            # Check for 10% reduction in category button width (180 -> 162)
            if "width=162" in content and "10% less than 180" in content:
                print(f"   ✅ Category buttons reduced 10% (180 -> 162)")
            else:
                print(f"   ❌ Category buttons not reduced")
                all_reduced = False
            
            # Check for text-only category button reduction (22 -> 20)
            if "width=20" in content and "10% less than 22" in content:
                print(f"   ✅ Text category buttons reduced 10% (22 -> 20)")
            else:
                print(f"   ❌ Text category buttons not reduced")
                all_reduced = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_reduced = False
    
    return all_reduced

def test_product_button_spacing():
    """Test that product button spacing is optimized"""
    
    print("\nTesting Product Button Spacing")
    print("=" * 32)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_optimized = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for removed extra newline
            if 'f"{product[\'name\']}\\n{product[\'price\']:.2f} MAD"' in content and "Removed extra newline" in content:
                print(f"   ✅ Extra newline removed for tighter spacing")
            else:
                print(f"   ❌ Extra newline not removed")
                all_optimized = False
            
            # Check for reduced internal padding
            if "pady=2" in content and "Reduced internal padding" in content:
                print(f"   ✅ Internal padding reduced (pady=2)")
            else:
                print(f"   ❌ Internal padding not reduced")
                all_optimized = False
            
            # Check for optimization comments
            if "MINIMAL EMPTY SPACE" in content:
                print(f"   ✅ Optimization documented")
            else:
                print(f"   ❌ Optimization not documented")
                all_optimized = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_optimized = False
    
    return all_optimized

def test_receipt_formatting():
    """Test that receipt formatting is more compact"""
    
    print("\nTesting Receipt Formatting")
    print("=" * 28)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_compact = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for reduced line width (45 -> 38)
            if '"_" * 38' in content and "Reduced from 45 to 38" in content:
                print(f"   ✅ Line width reduced (45 -> 38)")
            else:
                print(f"   ❌ Line width not reduced")
                all_compact = False
            
            # Check for compact column spacing
            if "{'Produit:':<14} {'Prix':<6} {'Qté':<4} {'Total':<6}" in content:
                print(f"   ✅ Column spacing reduced (18->14, 8->6, 6->4, 8->6)")
            else:
                print(f"   ❌ Column spacing not reduced")
                all_compact = False
            
            # Check for truncated product names (16 -> 12)
            if "product_name[:12]" in content and "Reduced from 16 to 12" in content:
                print(f"   ✅ Product name truncation reduced (16 -> 12)")
            else:
                print(f"   ❌ Product name truncation not reduced")
                all_compact = False
            
            # Check for compact data formatting
            if "{display_name:<14} {unit_price:<6.2f} {total_quantity:<4} {total_amount:<6.2f}" in content:
                print(f"   ✅ Data formatting matches compact headers")
            else:
                print(f"   ❌ Data formatting doesn't match headers")
                all_compact = False
            
            # Check for prevention comments
            if "prevent cutoff" in content.lower():
                print(f"   ✅ Cutoff prevention documented")
            else:
                print(f"   ❌ Cutoff prevention not documented")
                all_compact = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_compact = False
    
    return all_compact

def test_layout_consistency():
    """Test that layout changes are consistent across files"""
    
    print("\nTesting Layout Consistency")
    print("=" * 27)
    
    # Check that main and YES versions have same changes
    main_files = ["pos_screen.py", "receipt_generator.py"]
    yes_files = ["YES/pos_screen.py", "YES/receipt_generator.py"]
    
    all_consistent = True
    
    for main_file, yes_file in zip(main_files, yes_files):
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                main_content = f.read()
            with open(yes_file, 'r', encoding='utf-8') as f:
                yes_content = f.read()
            
            print(f"\n📋 Comparing: {main_file} vs {yes_file}")
            
            # Check key optimization markers
            optimization_markers = [
                "OPTIMIZED",
                "MINIMAL",
                "COMPACT",
                "minsize=204",
                "width=162",
                "width=20",
                "_\" * 38"
            ]
            
            for marker in optimization_markers:
                main_has = marker in main_content
                yes_has = marker in yes_content
                
                if main_has == yes_has:
                    if main_has:
                        print(f"   ✅ Both have: {marker}")
                else:
                    print(f"   ❌ Inconsistent: {marker} (main: {main_has}, yes: {yes_has})")
                    all_consistent = False
                    
        except Exception as e:
            print(f"❌ Error comparing files: {e}")
            all_consistent = False
    
    return all_consistent

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/pos_screen.py",
        "YES_OBFUSCATED/receipt_generator.py"
    ]
    
    all_updated = True
    
    for obf_file in obf_files:
        if Path(obf_file).exists():
            print(f"✅ Found: {obf_file}")
            
            # Check if it's actually obfuscated
            try:
                with open(obf_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
                    all_updated = False
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {obf_file}")
            all_updated = False
    
    return all_updated

def main():
    """Run all layout optimization tests"""
    
    print("📐 LAYOUT OPTIMIZATIONS TEST SUITE")
    print("=" * 36)
    
    tests = [
        ("Categories Width Reduction", test_categories_width_reduction),
        ("Product Button Spacing", test_product_button_spacing),
        ("Receipt Formatting", test_receipt_formatting),
        ("Layout Consistency", test_layout_consistency),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 36)
    print("📊 RESULTS")
    print("=" * 36)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Categories area reduced 15% (240 -> 204px)")
        print("✅ Category buttons reduced 10% (180 -> 162px, 22 -> 20 chars)")
        print("✅ Product button spacing optimized (removed extra newline, pady=2)")
        print("✅ Receipt formatting compacted (45 -> 38 chars, tighter columns)")
        print("✅ All versions updated consistently")
    else:
        print("⚠️ Some tests failed")
        print("❌ Layout optimizations may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📐 Layout optimizations successfully implemented!")
        print("📏 Categories: 15% narrower area, 10% narrower buttons")
        print("🖼️ Products: Tighter image-text spacing, minimal empty space")
        print("📄 Receipts: Compact columns to prevent printing cutoff")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Layout optimizations need attention")
    
    exit(0 if success else 1)
