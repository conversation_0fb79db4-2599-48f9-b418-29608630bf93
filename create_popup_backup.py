#!/usr/bin/env python3
"""
Create backup of current popup window designs before redesigning
"""

import os
import shutil
from datetime import datetime

def create_popup_backup():
    """Create backup of all popup window files"""
    
    print("🔄 CREATING POPUP BACKUP")
    print("=" * 25)
    
    # Create backup directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"POPUP_BACKUP_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        print(f"📁 Created backup directory: {backup_dir}")
        
        # Files containing popup windows to backup
        files_to_backup = [
            'user_management.py',
            'product_management.py',
            'sales_history.py',
            'receipt_settings.py',
            'storage_management.py',
            'number_keyboard.py',
            'pos_screen.py',  # Has some popup dialogs
        ]
        
        backed_up = 0
        
        for file_name in files_to_backup:
            if os.path.exists(file_name):
                backup_path = os.path.join(backup_dir, file_name)
                shutil.copy2(file_name, backup_path)
                print(f"✅ Backed up: {file_name}")
                backed_up += 1
            else:
                print(f"⚠️ Not found: {file_name}")
        
        # Create restore script
        restore_script = f"""#!/usr/bin/env python3
'''
Restore popup windows from backup {timestamp}
'''

import shutil
import os

def restore_popups():
    '''Restore all popup windows from backup'''
    
    backup_files = {files_to_backup}
    
    print("🔄 RESTORING POPUP WINDOWS FROM BACKUP")
    print("=" * 40)
    
    restored = 0
    for file_name in backup_files:
        backup_path = os.path.join('{backup_dir}', file_name)
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, file_name)
            print(f"✅ Restored: {{file_name}}")
            restored += 1
        else:
            print(f"❌ Backup not found: {{file_name}}")
    
    print(f"\\n📊 Restored {{restored}} files")
    print("🔄 Popup windows restored to original design!")
    
    return restored > 0

if __name__ == "__main__":
    success = restore_popups()
    exit(0 if success else 1)
"""
        
        restore_script_path = os.path.join(backup_dir, "restore_popups.py")
        with open(restore_script_path, 'w', encoding='utf-8') as f:
            f.write(restore_script)
        
        print(f"✅ Created restore script: {restore_script_path}")
        
        # Create README
        readme_content = f"""# Popup Windows Backup - {timestamp}

## What's in this backup:
- Original popup window designs before aesthetic redesign
- All files containing popup dialogs and windows
- Restore script to revert changes

## Files backed up:
{chr(10).join(f'- {f}' for f in files_to_backup)}

## To restore original popup designs:
1. Run: python {backup_dir}/restore_popups.py
2. Or manually copy files from this backup folder

## Backup created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        readme_path = os.path.join(backup_dir, "README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ Created README: {readme_path}")
        print(f"\n📊 Backup complete: {backed_up} files backed up")
        print(f"📁 Backup location: {backup_dir}")
        print(f"🔄 To restore: python {backup_dir}/restore_popups.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

if __name__ == "__main__":
    success = create_popup_backup()
    
    if success:
        print("\n🛡️ Popup backup created successfully!")
        print("✅ Original designs are safely preserved")
        print("🎨 Ready to proceed with redesign")
    else:
        print("\n❌ Backup creation failed")
    
    exit(0 if success else 1)
