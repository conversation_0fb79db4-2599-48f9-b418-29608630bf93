#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "main.py"
PROTECTION_DATE = "2025-06-03T03:26:12.798213"
ENCRYPTED_DATA = """eJxlVWtz2kgWpXZTmVk7MygehySOk2kJuYW7JaTm0Q2SWiIEU+Oa2nJcmCF+YDtmFYIxlhDgt6O/voANSc18U9WpPvfec869GpzfIcDOmIWDxmFlWV+HyvjzsNs7q79ZWAhWkaqA9Oh3mR4I3+rIIQphmzER8khT83nfdSAGnNGo+qmxnCGWOHzx7KdO2tEV1SSWafCtz5fBVn2/Wd7X5NDW6CaXXVFzPZrcMDNJRMSQaLVa4y8nW9w4/NJ6oeFz22E4LyoRU4lBiFHaXCoZgZQGWisD+TmFeSpZoA5s41aS8d67/T01D6y7kT+E9rGOCPmGTY3LaeMGqaulWol9y2pKXgJi+3ls0rPw7OLmbPOIguJSrbTtyEwwgOFy4MQK2pjZQMoKyv6CzcZO4+Pl9deNodcajtVYLYKCjAYWpSmR56P3n5pVnFWsobo2VAAwu0As2orab38+Cr1oqVL6r7cX+pO3Hb8XPcH5dV/RkKda4RI92Gl+UOeoUuwi9E3MZKh38jU8IdWXPztRMEPPoyLQ1jiv73qt0YRZ2KkaPHTzBBeA9qjGIJ4h+OjTBF1ajoK+/1KDTJowU+JyxYaklXOYWdv5c9lMWTagm1PmrnedQyCnoOzI9+K1yt4Kc110d9a67fQWzEJac91+ffBYd3dnmbjQf9Sqn+ZG0gKXN9HmQ1eTuuEMjS68/xDL6lto7MJZ9FYQbkk+wXkWXnSOz+/jAFmWrfe3Wj+8hRlPs7iPHurem1g1RLF38bP5wDxTo+vhvAU1vjWvqxY4B5CChKvjpPe/AFO1QNS18DQS7hrLq67rAoRgOwlyna3rwB9E44lYlth7frmOC2E4FKEiWaLePb0O/00jAaXXIKKldx/rOPy0Mav71ROoD5jNGFVIVDsYJyeHfcKRwu2TqJA2wMaUedwV3RHeHxgSVEWyKqcsONfKU5VQcaq1va5OoHIDLG5a/PIkqj12NZ2o3Cghyd44nM375fq1lDTEsYO2GJU2DubzdlTbRMRwxd3Z251yczfKApQncFFDPOei9bhtjd8CAD16VBonNiCcyIwkMFYBoUCYTVSfOcgYzHP03ErTjAySMaCpCoSo+uqNU5+4ACymumb0KzfzrkJt3MqJnvndo+PhLwBcuZ5LoBspGhxl8/okOf/oeVDMqDqcJ1biHLM7vRczXNAKntnIFVsWJ9F2uVHH+RATmgLQPVeTCMsqxB2vYOjti4L513YxcNMcd+8HG5Ns3Ia9d46JW4ZNtD+F8j51oJ9BZMgVHH2/ZpdBezovlvBornMeF0Xlsn/1eXTlnf7rdWGWOtmCALfak+TEVxOtp4uJxcv7pfbi89ibt4tPLo6ndSeoLqqaeNM+EYRXy6fHtz86+BunjgJahNFc9aj8XQ2YitaAnUMsusWqFSnZZm23SdjtPHUpZhOLxhgwHIj03oe98RUlJlhBmYSITR3wqy+78w39bek+A8bMnmRqPseIaYFlmYMXz56Gsl4+2G84sgXMrdkeudJ4UZPIWJh6dFQZJ9Z/siC8KLoSTclaEfxwc2oVPfD/hl7ezPe3roM7Lo5d+DBVw06pVFe8hYKEtOqoVDWkMK148PyD/6hVUjMxu8+1Ew+ZfMkhIuedHJY6Z/NdkBg9qKwk4jZLT6RTbEtLewU7OdBojtoAWEvN5kdkM3soE9JJFxxpvZBJ0n7/bH4ZsJRViSIx6bOjSViLI2zfPqCNP7a3dTlcUCGUORRJV0YB6syVLJWaRL5Zv4gROP7jqK6MZcgLUyUT8H2lsesF62i9fXr4eEUDS3QMIOZIul8u7+/hOGOs94BqKiNJF3bmWlW0om3dtX83X8feRq9sJCMnAhoVKUodCNv7GeAgAHrjXFWo5j2NT/4LK6sj/yoS/N6uU7SFxfb1lBnKhtO+qf8fFH4J/g=="""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
