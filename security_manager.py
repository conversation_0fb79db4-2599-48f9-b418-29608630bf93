#!/usr/bin/env python3
"""
Advanced Security Manager for POS System
Provides multiple layers of protection against theft and tampering
"""

import os
import sys
import hashlib
import platform
import subprocess
import uuid
import time
import base64
import json
from datetime import datetime, timedelta
import sqlite3

class SecurityManager:
    def __init__(self):
        self.security_db = "security_vault.db"
        self.machine_id = self._get_machine_fingerprint()
        self.init_security_database()
    
    def _get_machine_fingerprint(self):
        """Generate unique machine fingerprint"""
        try:
            # Combine multiple hardware identifiers
            machine_info = []
            
            # CPU info
            try:
                if platform.system() == "Windows":
                    cpu_info = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode()
                    machine_info.append(cpu_info.strip())
                else:
                    cpu_info = subprocess.check_output("cat /proc/cpuinfo | grep 'processor'", shell=True).decode()
                    machine_info.append(cpu_info.strip())
            except:
                machine_info.append(platform.processor())
            
            # MAC Address
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0,2*6,2)][::-1])
                machine_info.append(mac)
            except:
                machine_info.append("unknown_mac")
            
            # System info
            machine_info.extend([
                platform.system(),
                platform.machine(),
                platform.node(),
                str(uuid.getnode())
            ])
            
            # Create fingerprint hash
            fingerprint_data = "|".join(machine_info)
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
            
            return fingerprint
            
        except Exception as e:
            # Fallback fingerprint
            fallback = f"{platform.system()}_{platform.node()}_{uuid.getnode()}"
            return hashlib.sha256(fallback.encode()).hexdigest()[:32]
    
    def init_security_database(self):
        """Initialize security database"""
        try:
            conn = sqlite3.connect(self.security_db)
            conn.execute('''CREATE TABLE IF NOT EXISTS security_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                event_type TEXT NOT NULL,
                machine_id TEXT NOT NULL,
                details TEXT,
                hash_check TEXT
            )''')
            
            conn.execute('''CREATE TABLE IF NOT EXISTS authorized_machines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                machine_id TEXT UNIQUE NOT NULL,
                authorized_date TEXT NOT NULL,
                last_access TEXT,
                access_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active'
            )''')
            
            conn.execute('''CREATE TABLE IF NOT EXISTS file_integrity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT UNIQUE NOT NULL,
                file_hash TEXT NOT NULL,
                last_check TEXT NOT NULL
            )''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Security database initialization failed: {e}")
    
    def log_security_event(self, event_type, details=""):
        """Log security events"""
        try:
            conn = sqlite3.connect(self.security_db)
            timestamp = datetime.now().isoformat()
            hash_check = hashlib.sha256(f"{timestamp}{event_type}{self.machine_id}".encode()).hexdigest()[:16]
            
            conn.execute('''INSERT INTO security_log 
                           (timestamp, event_type, machine_id, details, hash_check) 
                           VALUES (?, ?, ?, ?, ?)''',
                        (timestamp, event_type, self.machine_id, details, hash_check))
            conn.commit()
            conn.close()
            
        except Exception as e:
            pass  # Silent fail for security
    
    def check_machine_authorization(self):
        """Check if current machine is authorized"""
        try:
            conn = sqlite3.connect(self.security_db)
            cursor = conn.cursor()
            
            cursor.execute('''SELECT * FROM authorized_machines 
                             WHERE machine_id = ? AND status = 'active' ''', 
                          (self.machine_id,))
            result = cursor.fetchone()
            
            if result:
                # Update access info
                cursor.execute('''UPDATE authorized_machines 
                                 SET last_access = ?, access_count = access_count + 1 
                                 WHERE machine_id = ?''',
                              (datetime.now().isoformat(), self.machine_id))
                conn.commit()
                conn.close()
                
                self.log_security_event("AUTHORIZED_ACCESS", "Machine authorized")
                return True
            else:
                conn.close()
                self.log_security_event("UNAUTHORIZED_ACCESS", "Machine not authorized")
                return False
                
        except Exception as e:
            self.log_security_event("SECURITY_ERROR", f"Authorization check failed: {e}")
            return False
    
    def authorize_machine(self, auth_code=None):
        """Authorize current machine (for initial setup)"""
        try:
            # Simple authorization - in production, this would be more sophisticated
            if auth_code == "SECURE_POS_2024_AUTH":
                conn = sqlite3.connect(self.security_db)
                
                # Check if already authorized
                cursor = conn.cursor()
                cursor.execute('''SELECT * FROM authorized_machines WHERE machine_id = ?''', 
                              (self.machine_id,))
                
                if cursor.fetchone():
                    conn.close()
                    return True
                
                # Add authorization
                cursor.execute('''INSERT OR REPLACE INTO authorized_machines 
                                 (machine_id, authorized_date, last_access, access_count, status) 
                                 VALUES (?, ?, ?, 1, 'active')''',
                              (self.machine_id, datetime.now().isoformat(), 
                               datetime.now().isoformat()))
                conn.commit()
                conn.close()
                
                self.log_security_event("MACHINE_AUTHORIZED", "New machine authorized")
                return True
            else:
                self.log_security_event("INVALID_AUTH_CODE", "Invalid authorization code")
                return False
                
        except Exception as e:
            self.log_security_event("AUTH_ERROR", f"Authorization failed: {e}")
            return False
    
    def calculate_file_hash(self, file_path):
        """Calculate SHA256 hash of a file"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except:
            return None
    
    def check_file_integrity(self, critical_files):
        """Check integrity of critical files"""
        try:
            conn = sqlite3.connect(self.security_db)
            cursor = conn.cursor()
            
            tampered_files = []
            
            for file_path in critical_files:
                if not os.path.exists(file_path):
                    tampered_files.append(f"{file_path} (MISSING)")
                    continue
                
                current_hash = self.calculate_file_hash(file_path)
                if not current_hash:
                    continue
                
                # Check stored hash
                cursor.execute('''SELECT file_hash FROM file_integrity WHERE file_path = ?''', 
                              (file_path,))
                result = cursor.fetchone()
                
                if result:
                    stored_hash = result[0]
                    if current_hash != stored_hash:
                        tampered_files.append(file_path)
                        self.log_security_event("FILE_TAMPERED", f"File modified: {file_path}")
                else:
                    # Store initial hash
                    cursor.execute('''INSERT OR REPLACE INTO file_integrity 
                                     (file_path, file_hash, last_check) 
                                     VALUES (?, ?, ?)''',
                                  (file_path, current_hash, datetime.now().isoformat()))
                
                # Update last check
                cursor.execute('''UPDATE file_integrity 
                                 SET last_check = ? WHERE file_path = ?''',
                              (datetime.now().isoformat(), file_path))
            
            conn.commit()
            conn.close()
            
            if tampered_files:
                self.log_security_event("INTEGRITY_VIOLATION", f"Tampered files: {tampered_files}")
                return False, tampered_files
            else:
                return True, []
                
        except Exception as e:
            self.log_security_event("INTEGRITY_ERROR", f"Integrity check failed: {e}")
            return False, ["INTEGRITY_CHECK_FAILED"]
    
    def get_machine_info(self):
        """Get machine information for display"""
        return {
            "machine_id": self.machine_id,
            "system": platform.system(),
            "machine": platform.machine(),
            "node": platform.node(),
            "python_version": platform.python_version()
        }
    
    def security_check(self):
        """Comprehensive security check"""
        try:
            # Check machine authorization
            if not self.check_machine_authorization():
                return False, "UNAUTHORIZED_MACHINE"
            
            # Check file integrity
            critical_files = [
                "main.py",
                "pos_app.py", 
                "database.py",
                "license_client.py"
            ]
            
            integrity_ok, tampered_files = self.check_file_integrity(critical_files)
            if not integrity_ok:
                return False, f"FILE_INTEGRITY_VIOLATION: {tampered_files}"
            
            # Check for suspicious activity
            if self._detect_suspicious_activity():
                return False, "SUSPICIOUS_ACTIVITY_DETECTED"
            
            self.log_security_event("SECURITY_CHECK_PASSED", "All checks passed")
            return True, "SECURITY_OK"
            
        except Exception as e:
            self.log_security_event("SECURITY_CHECK_ERROR", f"Security check failed: {e}")
            return False, f"SECURITY_ERROR: {e}"
    
    def _detect_suspicious_activity(self):
        """Detect suspicious activity patterns"""
        try:
            conn = sqlite3.connect(self.security_db)
            cursor = conn.cursor()
            
            # Check for too many failed attempts
            one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
            cursor.execute('''SELECT COUNT(*) FROM security_log 
                             WHERE event_type LIKE '%UNAUTHORIZED%' 
                             AND timestamp > ?''', (one_hour_ago,))
            
            failed_attempts = cursor.fetchone()[0]
            conn.close()
            
            if failed_attempts > 5:
                return True
            
            return False
            
        except:
            return False

# Global security manager instance
security_manager = SecurityManager()
