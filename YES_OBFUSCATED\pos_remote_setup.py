#!/usr/bin/env python3
"""
POS Remote Management Setup Integration
Asks for remote credentials during POS first-time setup
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import hashlib
import requests
import json
import os
from pathlib import Path

class RemoteSetupDialog:
    """Dialog for remote management setup during POS installation"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.result = None
        self.remote_credentials = None
        
    def show_setup_dialog(self):
        """Show the remote setup dialog"""
        
        # Create dialog window
        self.dialog = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.dialog.title("LeComptoir Remote Management Setup")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#1a1a1a')
        self.dialog.resizable(False, False)
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        # Make dialog modal
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
        return self.result
    
    def create_widgets(self):
        """Create dialog widgets"""
        
        # Main frame
        main_frame = tk.Frame(self.dialog, bg='#1a1a1a', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(main_frame,
                              text="🏪 LeComptoir Remote Management Setup",
                              font=('Segoe UI', 16, 'bold'),
                              bg='#1a1a1a', fg='#ff8c00')
        title_label.pack(pady=(0, 10))
        
        # Description
        desc_text = """Your LeComptoir POS system can be managed remotely from anywhere in the world.
Enter the LeComptoir remote management credentials provided to you:"""
        
        desc_label = tk.Label(main_frame, 
                             text=desc_text,
                             font=('Segoe UI', 10),
                             bg='#1a1a1a', fg='white',
                             wraplength=450,
                             justify='left')
        desc_label.pack(pady=(0, 20))
        
        # Credentials frame
        cred_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=1)
        cred_frame.pack(fill='x', pady=(0, 20), padx=10, ipady=15)
        
        # Username
        tk.Label(cred_frame,
                text="LeComptoir Username:",
                font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', padx=15, pady=(10, 5))
        
        self.username_entry = tk.Entry(cred_frame,
                                      font=('Segoe UI', 11),
                                      bg='#404040', fg='white',
                                      insertbackground='white',
                                      relief='flat', bd=5)
        self.username_entry.pack(fill='x', padx=15, pady=(0, 10))
        
        # Password
        tk.Label(cred_frame,
                text="LeComptoir Password:",
                font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', padx=15, pady=(0, 5))
        
        self.password_entry = tk.Entry(cred_frame,
                                      font=('Segoe UI', 11),
                                      bg='#404040', fg='white',
                                      insertbackground='white',
                                      relief='flat', bd=5,
                                      show='*')
        self.password_entry.pack(fill='x', padx=15, pady=(0, 10))
        
        # Example
        example_label = tk.Label(cred_frame,
                                text="Example: pos_1234 / Ab3$2x9K",
                                font=('Segoe UI', 9),
                                bg='#2d2d2d', fg='#888888')
        example_label.pack(anchor='w', padx=15, pady=(0, 10))
        
        # Test connection button
        test_frame = tk.Frame(main_frame, bg='#1a1a1a')
        test_frame.pack(fill='x', pady=(0, 10))
        
        self.test_button = tk.Button(test_frame,
                                    text="🔍 Test Connection",
                                    font=('Segoe UI', 10),
                                    bg='#404040', fg='white',
                                    relief='flat', bd=0, pady=8,
                                    command=self.test_connection)
        self.test_button.pack(side='left')
        
        self.status_label = tk.Label(test_frame,
                                    text="",
                                    font=('Segoe UI', 9),
                                    bg='#1a1a1a', fg='white')
        self.status_label.pack(side='left', padx=(10, 0))
        
        # Buttons frame
        button_frame = tk.Frame(main_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', pady=(20, 0))
        
        # Skip button
        skip_button = tk.Button(button_frame,
                               text="Skip Remote Setup",
                               font=('Segoe UI', 10),
                               bg='#666666', fg='white',
                               relief='flat', bd=0, pady=10,
                               command=self.skip_setup)
        skip_button.pack(side='left')
        
        # Continue button
        continue_button = tk.Button(button_frame,
                                   text="Continue with LeComptoir Remote",
                                   font=('Segoe UI', 10, 'bold'),
                                   bg='#ff8c00', fg='white',
                                   relief='flat', bd=0, pady=10,
                                   command=self.continue_setup)
        continue_button.pack(side='right')
        
        # Focus on username entry
        self.username_entry.focus()
    
    def test_connection(self):
        """Test the remote connection"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.status_label.config(text="❌ Please enter both username and password", fg='#ff4444')
            return
        
        self.status_label.config(text="🔄 Testing connection...", fg='#ffaa00')
        self.test_button.config(state='disabled')
        self.dialog.update()
        
        try:
            # Test connection to central server
            # In production, this would be your actual server URL
            server_url = "http://localhost:5000"  # Change to your server
            
            response = requests.post(f"{server_url}/api/v1/auth/login",
                                   json={"username": username, "password": password},
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('token'):
                    self.status_label.config(text="✅ Connection successful!", fg='#44ff44')
                    self.remote_credentials = {
                        'username': username,
                        'password': password,
                        'server_url': server_url,
                        'token': data['token']
                    }
                else:
                    self.status_label.config(text="❌ Invalid response from server", fg='#ff4444')
            else:
                error_data = response.json() if response.headers.get('content-type') == 'application/json' else {}
                error_msg = error_data.get('error', 'Authentication failed')
                self.status_label.config(text=f"❌ {error_msg}", fg='#ff4444')
                
        except requests.exceptions.Timeout:
            self.status_label.config(text="❌ Connection timeout", fg='#ff4444')
        except requests.exceptions.ConnectionError:
            self.status_label.config(text="❌ Cannot connect to server", fg='#ff4444')
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}", fg='#ff4444')
        
        self.test_button.config(state='normal')
    
    def continue_setup(self):
        """Continue with remote setup"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return
        
        if not self.remote_credentials:
            # Test connection first
            self.test_connection()
            if not self.remote_credentials:
                messagebox.showerror("Error", "Please test the connection first")
                return
        
        # Save credentials
        self.save_remote_credentials()
        
        self.result = {
            'setup_remote': True,
            'credentials': self.remote_credentials
        }
        self.dialog.destroy()
    
    def skip_setup(self):
        """Skip remote setup"""
        result = messagebox.askyesno("Skip Remote Setup", 
                                   "Are you sure you want to skip remote management setup?\n\n" +
                                   "You can set this up later, but remote access will not be available.")
        if result:
            self.result = {
                'setup_remote': False,
                'credentials': None
            }
            self.dialog.destroy()
    
    def save_remote_credentials(self):
        """Save remote credentials to local database"""
        try:
            # Create/connect to POS database
            conn = sqlite3.connect('pos_system.db')
            c = conn.cursor()
            
            # Create remote config table if not exists
            c.execute("""
                CREATE TABLE IF NOT EXISTS remote_config (
                    id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    password_hash TEXT NOT NULL,
                    server_url TEXT NOT NULL,
                    setup_date TEXT NOT NULL,
                    last_sync TEXT,
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            # Clear existing config
            c.execute("DELETE FROM remote_config")
            
            # Save new config
            password_hash = hashlib.sha256(self.remote_credentials['password'].encode()).hexdigest()
            c.execute("""
                INSERT INTO remote_config 
                (username, password_hash, server_url, setup_date)
                VALUES (?, ?, ?, ?)
            """, (self.remote_credentials['username'], 
                  password_hash,
                  self.remote_credentials['server_url'],
                  "2024-12-03"))  # Current date
            
            conn.commit()
            conn.close()
            
            print("✅ Remote credentials saved successfully")
            
        except Exception as e:
            print(f"❌ Error saving remote credentials: {e}")
            messagebox.showerror("Error", f"Failed to save remote credentials: {e}")

def integrate_with_pos_setup():
    """Integration function for POS setup process"""
    
    print("🌐 Starting remote management setup...")
    
    # Show setup dialog
    setup_dialog = RemoteSetupDialog()
    result = setup_dialog.show_setup_dialog()
    
    if result:
        if result['setup_remote']:
            print("✅ Remote management configured successfully")
            print(f"   Username: {result['credentials']['username']}")
            print(f"   Server: {result['credentials']['server_url']}")
            return True
        else:
            print("⏭️  Remote management setup skipped")
            return False
    else:
        print("❌ Remote management setup cancelled")
        return False

def main():
    """Test the remote setup dialog"""
    print("🧪 Testing Remote Setup Dialog")
    print("=" * 40)
    
    # Test the dialog
    result = integrate_with_pos_setup()
    
    if result:
        print("\n🎉 Remote setup completed successfully!")
    else:
        print("\n⏭️  Remote setup was skipped or cancelled")

if __name__ == '__main__':
    main()
