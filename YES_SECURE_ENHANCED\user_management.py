#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "user_management.py"
PROTECTION_DATE = "2025-06-03T03:26:12.889589"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
