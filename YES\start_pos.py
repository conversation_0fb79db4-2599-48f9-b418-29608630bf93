#!/usr/bin/env python3
"""
POS System Client Launcher
Protected version for client deployment
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the POS System"""
    try:
        print("=" * 50)
        print("    POS SYSTEM STARTING (CLIENT VERSION)")
        print("=" * 50)

        # Add current directory to path for imports
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))

        # Import and run the main module
        import main
        main.main()

    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please ensure all required dependencies are installed.")
        print("Run: pip install -r requirements.txt")
    except Exception as e:
        print(f"Error starting POS System: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\nPOS System shutdown complete.")

if __name__ == "__main__":
    main()
