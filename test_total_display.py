#!/usr/bin/env python3
"""
Test the total display functionality in sales history
"""

import sys
import os
from pathlib import Path

def test_total_display_creation():
    """Test that total display is created in the interface"""
    
    print("Testing Total Display Creation")
    print("=" * 31)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_created = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for total display creation in interface
            if "self.create_total_display(content_frame)" in content:
                print(f"   ✅ Total display added to interface")
            else:
                print(f"   ❌ Total display not added to interface")
                all_created = False
            
            # Check for create_total_display method
            if "def create_total_display(self, parent):" in content:
                print(f"   ✅ create_total_display method exists")
            else:
                print(f"   ❌ create_total_display method missing")
                all_created = False
            
            # Check for total label creation
            if "self.total_label = tk.Label" in content:
                print(f"   ✅ Total label created")
            else:
                print(f"   ❌ Total label not created")
                all_created = False
            
            # Check for centered display
            if "center_frame.pack(anchor=tk.CENTER)" in content:
                print(f"   ✅ Total display centered")
            else:
                print(f"   ❌ Total display not centered")
                all_created = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_created = False
    
    return all_created

def test_total_calculation():
    """Test that total calculation is implemented"""
    
    print("\nTesting Total Calculation")
    print("=" * 26)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_calculated = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for total calculation in load_sales
            if "total_amount = 0.0" in content and "total_amount += sale['total']" in content:
                print(f"   ✅ Total calculation implemented")
            else:
                print(f"   ❌ Total calculation not implemented")
                all_calculated = False
            
            # Check for update_total_display call
            if "self.update_total_display(total_amount, len(sales))" in content:
                print(f"   ✅ Total display update called")
            else:
                print(f"   ❌ Total display update not called")
                all_calculated = False
            
            # Check for update_total_display method
            if "def update_total_display(self, total_amount, transaction_count):" in content:
                print(f"   ✅ update_total_display method exists")
            else:
                print(f"   ❌ update_total_display method missing")
                all_calculated = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_calculated = False
    
    return all_calculated

def test_display_formatting():
    """Test that display formatting is correct"""
    
    print("\nTesting Display Formatting")
    print("=" * 27)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_formatted = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for proper styling
            if "bg='#2c3e50'" in content and "fg='white'" in content:
                print(f"   ✅ Professional styling applied")
            else:
                print(f"   ❌ Professional styling not applied")
                all_formatted = False
            
            # Check for proper font
            if "font=('Helvetica', 14, 'bold')" in content:
                print(f"   ✅ Large bold font used")
            else:
                print(f"   ❌ Large bold font not used")
                all_formatted = False
            
            # Check for padding
            if "padx=30, pady=10" in content:
                print(f"   ✅ Proper padding applied")
            else:
                print(f"   ❌ Proper padding not applied")
                all_formatted = False
            
            # Check for different display states
            if "No sales found" in content and "transaction" in content:
                print(f"   ✅ Different display states handled")
            else:
                print(f"   ❌ Different display states not handled")
                all_formatted = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_formatted = False
    
    return all_formatted

def test_positioning():
    """Test that total display is positioned correctly"""
    
    print("\nTesting Positioning")
    print("=" * 19)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_positioned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check order: sales list -> total display -> action buttons
            lines = content.split('\n')
            sales_list_line = -1
            total_display_line = -1
            action_buttons_line = -1
            
            for i, line in enumerate(lines):
                if "self.create_sales_list(content_frame)" in line:
                    sales_list_line = i
                elif "self.create_total_display(content_frame)" in line:
                    total_display_line = i
                elif "self.create_action_buttons(content_frame)" in line:
                    action_buttons_line = i
            
            if (sales_list_line < total_display_line < action_buttons_line and 
                sales_list_line != -1 and total_display_line != -1 and action_buttons_line != -1):
                print(f"   ✅ Total display positioned between sales list and buttons")
            else:
                print(f"   ❌ Total display not positioned correctly")
                all_positioned = False
            
            # Check for middle bottom positioning
            if "pady=(10, 10)" in content:
                print(f"   ✅ Proper vertical spacing applied")
            else:
                print(f"   ❌ Proper vertical spacing not applied")
                all_positioned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_positioned = False
    
    return all_positioned

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/sales_history.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all total display tests"""
    
    print("📊 TOTAL DISPLAY TEST SUITE")
    print("=" * 29)
    
    tests = [
        ("Total Display Creation", test_total_display_creation),
        ("Total Calculation", test_total_calculation),
        ("Display Formatting", test_display_formatting),
        ("Positioning", test_positioning),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 29)
    print("📊 RESULTS")
    print("=" * 29)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Total display created and positioned correctly")
        print("✅ Total calculation implemented properly")
        print("✅ Display formatting is professional")
        print("✅ Positioning is correct (middle bottom)")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Total display may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📊 Total display successfully implemented!")
        print("💰 Shows total amount of current sales view")
        print("📍 Positioned in middle bottom of page")
        print("🎨 Professional styling with dark background")
        print("📈 Updates automatically when filters change")
        print("🔢 Shows transaction count alongside total")
    else:
        print("\n❌ Total display needs attention")
    
    exit(0 if success else 1)
