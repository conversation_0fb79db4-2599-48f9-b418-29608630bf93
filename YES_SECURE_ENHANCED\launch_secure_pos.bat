@echo off
echo ========================================
echo    SECURE POS SYSTEM LAUNCHER
echo ========================================
echo.

REM Change to the script directory
cd /d "%~dp0"

echo Current directory: %CD%
echo.

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found!
    echo Please ensure you're running this from the POS system directory.
    pause
    exit /b 1
)

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python 3.7+ and try again.
    pause
    exit /b 1
)

echo.
echo Starting secure launcher...
echo.

REM Try GUI launcher first
echo Attempting GUI launcher...
python simple_secure_launcher.py
if errorlevel 1 (
    echo.
    echo GUI launcher failed, trying console launcher...
    echo.
    python console_launcher.py
    if errorlevel 1 (
        echo.
        echo Console launcher failed, trying direct launch...
        echo.
        echo WARNING: Launching without security checks!
        echo.
        python main.py
    )
)

echo.
echo POS system closed.
pause
