# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """eJzNWm9z1LwRf59PoZoX56MXP/BAKb3p0UlDeMpMCAy5PH2RyXgcW+ZUbOuw7ISUyXfvriTbkiVfEqDTehhytlar1f757WrtIAj2jllKK0HJYcFo1ZB9/WNfsIySQg9eJQXLkobxam+9YYJc0VrADeFVcdMNUtGRiwVJk6riDUlrCgOE16RMquQTJc2GlnsBLLvHyi2vGyK+FKyhz7rbTSI2BbvsbtuWZd3vfwlYPa95SXCxhpWU6JHufkHw/4wWTaIIt0mD3Dq6D3DbcdsWSZPzuuzuP9Fmmwixt5cW8IdorShdLPcIXCD1btVIbci9IXlGcxLHrGJNHIeCFvmCCFqD4uLsMkbBVie8onPFW/N/C9QM+P17YJ/KJZFrR/eoM1XGqahmvZJBiOQyEaAF2yr9RBQismUgq5FQaKpArx2LG9HQMsoujS2BnuKUl9u2gUkskzuzN/EbrWiNArUV+9LCDjQ1Aa1VDcsZrc3tDMyqnIM8efCts01U8YyG89v94YkSST7TFovgbwubgGc+phmw1E4VldmfQmu5iFapXGIebejXjH2iognn58unLy6idrtFpj3LGpapK5OzrRQlmWTrVQq4uqQgSFGXymPgV2/ppk7Sz6z6ZCpHL/qtfyDZddoIlr0bR92PcL4YEdc8pULw2qLuHrrkN82GV7EOcGuONeJM7BVTJSU15ykj9sS3g9o6F407h+N5XrCK6nDpnn6mN7Yyf9fzetVdMxCtbfoQIEmK2yMh3LCUKH2TdEPTz3NTvU19s7R28Yj83ZxhwJ5JxXKC4GbIh2FT0Co0RSZ/JU+f2OxNkwaSOSjqTVJg0Aa0rqWRAAXkUL875K8ECrTypgWJRJPUjUCFhMHx4X4w/+9KAGCEWpWOvEnqDMMpI78+3wd71KBfQLOOy1hyU32rFUFp//zy8OXhi5d/eXn47OB5MCm5BLLeeeQinQtZFvCLOixNclZQQr8y0UDu4mlSFDfWlI6ZpFv1wBgB82C8HUwuoTlhHinOoccEaB/Ct4bH4IwFmdWzOUkEyd0peImG1zRTGiN5BLifhXMwec22RoQZQpkTVqbG/fwdz1jXLTpGCcEE2Rtd49hOe2DsUEctKcH2c9dB3oBrmCQLFEwHGJQSKa9rmjYLAtrn1xC5DbtyQ+4RkXUHrZLLAgoNphIleELTbsn1hlZD7AMZBoWCAAbkFqNtzaomzIP3WqIhxJdd0reiX8sFuGyIFtjK/nG1QdllghTBxEYzS5n0a0q3DTmSfzB3gJvQ5W45xoGdj+VAPnJwSb7R28CHzXZ43ReZoc67Cw+IE/C7kFmJ0QWiJVQEK9rhmLbgU1UTyyJx1deHkIquw2lUUNJBsUAuKTgUFBUZ/AK7ONAlY32Q6H6RPtDfGeeKFD0apMe6Nyp4komwC/nRHsb7wEiBf2gDAcnYC8B6J8NKWEeFM8OyIOEfvgcznGyytqxeMgF+nm7GQDFpDPp1ywDEHGKZ6RwTY93PBFfRFA7bO58N9LMLFyxpkWzB4DH6qQCGoeVD+8Zq86jhDXAVNOUVGGVOfiHPXjx54m4HtmGzfbWCUPheNdpBBMUp45mpIJ9Ca1omgJXVp35fvz6HzVhSTdYHXjltjPOTDLiXj4wvIZRG5NtIsGX0NL8lSsZ+KHC431pPKCjIVSYkHFaLRh4FIYYRuKfLELysYPNv2gqLpRkTfg2Yvra00CgafHM+Mdc4YcBkWeuMD12u+3psP4E81zuRJ4+uaziPhxJ0srbcCiOGfLjzE50l8KcJnXZpFpG16fpSx/DU5yY/OWvaTnz/3Nk5CpoiVgdtN4MuzDOlcYPnKNkjWBDjbOnpGniPQ/pcP5RFIaBRcpWwAiuou05B9y7qoYSGYPvfl/br+oY0HKSphlPLUBFWpGyLhm2heMAyH01nw96Wq1Kxa4MgUp67GcdtoLhOHUTRL24DZYFCfkhq2bdhWPDy+sY7d3r6+prDIfMKYI+0W2cu1iTRhpdUJiRPD0fyOAPxCVK5ax8uf/nw/jQ+lRM8MuD0g0vBC/BO2VmzWFzYFhn6S+iv1hB6lRwDszh6dx2jK7dweFehZa/qyNddl1BBffaeovVkX0Y54YMz5bytIEyhOiSquzDRJMBLHTb6orvngcv1fDBDcec0ErgY7w2RcetkOki6k8+ZXHEcJABiWgG3o6WhyqmwT6gatRHegvuGmtqljWp+HeeJ9HBj3kd+bZPCmCSH9CgbUXee1vVBPYEQh+pWlRI2S3APmoJ7hrPZzFHf6dHx0eGaPCZvPr5/Z0KUWwH98x9HH49sFCN/Iwcnr2HhWC0MT55a82DJBTHVvxiny25MlxpplFOogyE8Rvl81NVBctcpleoKLsaz8bp/hwdikVV6R0a3Z6rNI0tNb0PMlPZ8pkpSgXdQbbvS6/Hpwn0XO2+nwz7jkVfdEn6g2Km9+2nweNDWrip8fC4buuHgwgUe526GCue7vZlVUA0VhSez4TXtznaf/OEubjIYu7sMV6zwTeHu8HvvHB8in21VxZNApS/gtO7Gx07t4XX24fXB+ugO1SlVr+VCMS6EahoVafjIO/HeendmK03bPm0dHWwJpuvJueveyvd5WbLG4/wPw5X7NLpEKztxeVsUk7gytNZgJyDYwwPh8P3ZyTp8LM82yn3hxNRCtfVD8fEjcG9JYbv9+cwcnF2Mw8Ca+mo1Atcy+RoPChM+fP2x7JA74AY4lW7AlrA0K9vSsJcg4bc7xLv1NIUPNOQN5wjIRBZAPsAF3p6cHn1cw5/1+zssPQleTkCZfGTeWQwYYB3IXP3+fnB8dnRKQsAJ69/8AWDqyLMDCzzYsws5TNmdokvjqhGP0gkfYAwNqzvrK0TUritiutLK+/SPo9DDyxuyDwzQaSCcDJ8HAGCf1CcB8Of0Jl53hwq7F4GX9ASaE5FcDQcF/Z7rrk7+KcwxQNx8E6g57GwhDM0n66XZzv5T13uyjjAebaDOH6LE7uBzhApCZRjnH6Uv7zJS1fY7frlz612I+5bfemeKH6HISfJV406N/b+9a9Ra2PmWUdNYR/wHGwT5388icp3eIPJN2Q5jvEtYNXxGoxpabZX23124X9Z4GmFo0aFkB8gc4YRZxt2jVTuuG6c+ksDLwGlk3qjWYTT+1sTF8KF5p1xvulVn+bO5A9vRnTLdGvaV531LUjqfEqPrNHjqEdEWTbf8zuap11MtaXZnUUNxDivvsVLJdq7x11NpqQ30uLToZ3Qp4cKZ4n9voRTXZQ9W6bfPNS35FTVAxDvR8Vzzkmhh4ck8aquCVZ8nDr8qhKcZyg/lfAMmcg6aULnqYuymJ9z2T5zwpaVwoJOwid90TcIyZECc3teObZUF35NYNbOh4FU4YaTSvb1HZI1C4dtUBRl7DL/sQ4eKY9nejmN8YxXHuq2tqMCfrY8IuwbXI3K64dceULHBRDGZgJO7YEQSjZqP+qNB3ItGV02wCshj8uyJ+SwPDjvxTmAF0IS14m0wQfz2tUnKMiTUm5Y6tNBYDmgv18Ey7NsGd3u5bkenTdK0AlacSbiZYbyqrhZGGJnpHtdsLO47tRhM1MsiwX8AoKtgGA=="""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
