#!/usr/bin/env python3
"""
File Protection System
Encrypts and protects critical Python files
"""

import os
import base64
import zlib
import marshal
import py_compile
import shutil
from datetime import datetime

class FileProtector:
    def __init__(self):
        self.protection_key = "POS_SECURE_2024_PROTECTION_KEY"
        self.protected_extension = ".pyx"
        
    def simple_encrypt(self, data, key):
        """Simple encryption using XOR and base64"""
        try:
            # Convert to bytes if string
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # XOR encryption
            key_bytes = key.encode('utf-8')
            encrypted = bytearray()
            
            for i, byte in enumerate(data):
                encrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            # Compress and encode
            compressed = zlib.compress(bytes(encrypted))
            encoded = base64.b64encode(compressed)
            
            return encoded
            
        except Exception as e:
            print(f"Encryption error: {e}")
            return None
    
    def simple_decrypt(self, encrypted_data, key):
        """Simple decryption"""
        try:
            # Decode and decompress
            decoded = base64.b64decode(encrypted_data)
            decompressed = zlib.decompress(decoded)
            
            # XOR decryption
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            return bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"Decryption error: {e}")
            return None
    
    def protect_file(self, file_path):
        """Protect a Python file"""
        try:
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}")
                return False
            
            # Read original file
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Create backup
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
            
            # Encrypt content
            encrypted_content = self.simple_encrypt(original_content, self.protection_key)
            if not encrypted_content:
                return False
            
            # Create protected loader
            protected_content = self._create_protected_loader(encrypted_content, file_path)
            
            # Write protected file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(protected_content)
            
            print(f"✅ Protected: {file_path}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to protect {file_path}: {e}")
            return False
    
    def _create_protected_loader(self, encrypted_content, original_file):
        """Create a loader that decrypts and executes the original code"""
        
        # Convert encrypted content to string for embedding
        encrypted_str = encrypted_content.decode('utf-8')
        
        loader_code = f'''#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "{os.path.basename(original_file)}"
PROTECTION_DATE = "{datetime.now().isoformat()}"
ENCRYPTED_DATA = """{encrypted_str}"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {{message}}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {{e}}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {{
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }}
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {{e}}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {{e}}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
'''
        
        return loader_code
    
    def protect_directory(self, directory_path, file_patterns=None):
        """Protect all Python files in a directory"""
        if file_patterns is None:
            file_patterns = ["*.py"]
        
        protected_count = 0
        failed_count = 0
        
        print(f"🔒 Protecting files in: {directory_path}")
        
        # Files to protect (excluding this script and security files)
        exclude_files = [
            "file_protector.py",
            "security_manager.py", 
            "secure_launcher.py",
            "create_secure_deployment.py"
        ]
        
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.endswith('.py') and file not in exclude_files:
                    file_path = os.path.join(root, file)
                    
                    if self.protect_file(file_path):
                        protected_count += 1
                    else:
                        failed_count += 1
        
        print(f"\\n📊 Protection Summary:")
        print(f"✅ Protected: {protected_count} files")
        print(f"❌ Failed: {failed_count} files")
        
        return protected_count, failed_count
    
    def restore_file(self, file_path):
        """Restore a file from backup"""
        try:
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, file_path)
                print(f"✅ Restored: {file_path}")
                return True
            else:
                print(f"❌ No backup found for: {file_path}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to restore {file_path}: {e}")
            return False

def main():
    """Main function for command line usage"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python file_protector.py <file_or_directory>")
        return

    protector = FileProtector()
    target = sys.argv[1]

    if os.path.isfile(target):
        print(f"🔒 Protecting file: {target}")
        protector.protect_file(target)
    elif os.path.isdir(target):
        print(f"🔒 Protecting directory: {target}")
        protector.protect_directory(target)
    else:
        print(f"❌ Target not found: {target}")

if __name__ == "__main__":
    main()
