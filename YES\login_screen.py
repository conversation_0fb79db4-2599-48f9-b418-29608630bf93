# PROTECTED CLIENT VERSION - DO NOT MODIFY
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

"""
Login Screen for POS System
Provides user authentication with horizontal user selection and number keyboard
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from database import get_available_users, authenticate_user
from number_keyboard import NumberKeyboard

class LoginScreen:
    """Login screen with horizontal user selection and modern design"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.selected_user_button = None
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.language_var = tk.StringVar(value='English' if app.current_language == 'english' else 'Français')

    def show(self):
        """Display the modern login screen"""
        # Set modern orange/black gradient background
        self.root.configure(bg='#1a1a1a')

        # Create main frame with modern orange/black styling
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create modern login interface
        self.create_login_interface()

    def hide(self):
        """Hide the login screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_login_interface(self):
        """Create the modern login interface"""
        # Create split layout - left branding, right login card
        layout_frame = tk.Frame(self.frame, bg='#1a1a1a')
        layout_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # Left side - Branding area
        left_frame = tk.Frame(layout_frame, bg='#1a1a1a', width=400)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 40))
        left_frame.pack_propagate(False)

        # Brand section
        brand_frame = tk.Frame(left_frame, bg='#1a1a1a')
        brand_frame.pack(expand=True)

        # Modern logo section with enhanced styling
        logo_brand_frame = tk.Frame(brand_frame, bg='#1a1a1a')
        logo_brand_frame.pack(pady=(0, 30))

        if self.app.logo_image:
            # Create a white frame for the logo as requested
            logo_container = tk.Frame(logo_brand_frame, bg='white', relief='flat', bd=0)
            logo_container.pack(pady=(0, 20))

            # Add subtle shadow effect
            shadow_frame = tk.Frame(logo_brand_frame, bg='#0a0a0a', height=2)
            shadow_frame.place(in_=logo_container, x=3, y=3, relwidth=1, relheight=1)
            logo_container.lift()

            logo_label = tk.Label(logo_container, image=self.app.logo_image, bg='white')
            logo_label.pack(padx=20, pady=20)

        # Brand title with orange accent - translatable with responsive font size
        title_text = f"💼 {self.app.get_text('login_title')}"
        # Use much smaller font for French to fit better
        font_size = 20 if self.app.current_language == 'french' else 28
        self.brand_title = tk.Label(brand_frame, text=title_text,
                                   font=('Segoe UI', font_size, 'bold'), bg='#1a1a1a', fg='#ff8c00')
        self.brand_title.pack(pady=(0, 10))

        # Modern tagline - translatable
        tagline_text = "Solution de Point de Vente Moderne" if self.app.current_language == 'french' else "Modern Point of Sale Solution"
        self.tagline = tk.Label(brand_frame, text=tagline_text,
                               font=('Segoe UI', 14), bg='#1a1a1a', fg='#b0b0b0')
        self.tagline.pack(pady=(0, 30))

        # Feature highlights with icons - translatable
        if self.app.current_language == 'french':
            features = ["✨ Rapide et Fiable", "🔒 Transactions Sécurisées", "📊 Analyses en Temps Réel", "🌐 Support Multi-langues"]
        else:
            features = ["✨ Fast & Reliable", "🔒 Secure Transactions", "📊 Real-time Analytics", "🌐 Multi-language Support"]

        self.feature_labels = []
        for feature in features:
            feature_label = tk.Label(brand_frame, text=feature,
                                   font=('Segoe UI', 11), bg='#1a1a1a', fg='#d0d0d0')
            feature_label.pack(pady=8, anchor='w')
            self.feature_labels.append(feature_label)

        # Right side - Modern login card
        right_frame = tk.Frame(layout_frame, bg='#1a1a1a')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Center the login card
        center_frame = tk.Frame(right_frame, bg='#1a1a1a')
        center_frame.pack(expand=True)

        # Modern login card with orange/black theme
        main_container = tk.Frame(center_frame, bg='#2d2d2d', relief='flat', bd=0)
        main_container.pack(pady=50, padx=50)

        # Add modern shadow effect
        shadow_frame = tk.Frame(center_frame, bg='#0a0a0a')
        shadow_frame.place(in_=main_container, x=6, y=6, relwidth=1, relheight=1)
        main_container.lift()

        # Modern welcome header with orange accent - translatable
        header_frame = tk.Frame(main_container, bg='#2d2d2d')
        header_frame.pack(pady=(40, 30), padx=40)

        welcome_text = "🔐 Bon Retour" if self.app.current_language == 'french' else "🔐 Welcome Back"
        self.welcome_label = tk.Label(header_frame, text=welcome_text,
                                     font=('Segoe UI', 24, 'bold'), bg='#2d2d2d', fg='#ffffff')
        self.welcome_label.pack()

        subtitle_text = "Connectez-vous à votre compte" if self.app.current_language == 'french' else "Sign in to your account"
        self.subtitle_label = tk.Label(header_frame, text=subtitle_text,
                                      font=('Segoe UI', 12), bg='#2d2d2d', fg='#b0b0b0')
        self.subtitle_label.pack(pady=(8, 0))

        # User selection section
        self.create_user_selection(main_container)

        # Password section
        self.create_password_section(main_container)

        # Modern login button with orange theme - translatable
        login_btn_container = tk.Frame(main_container, bg='#2d2d2d')
        login_btn_container.pack(pady=(0, 30), padx=40, fill=tk.X)

        self.login_btn = tk.Button(login_btn_container, text=f"🔐 {self.app.get_text('login')}",
                                  font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white',
                                  padx=50, pady=15, relief='flat', bd=0,
                                  cursor='hand2', activebackground='#e67e00',
                                  activeforeground='white', command=self.handle_login)
        self.login_btn.pack(pady=10)

        # Modern exit button (top right, enhanced styling)
        if self.app.icons.get('exit'):
            exit_btn = tk.Button(self.frame, image=self.app.icons['exit'],
                               bg='#cc0000', fg='white', width=50, height=50, bd=0,
                               relief='flat', cursor='hand2', activebackground='#990000',
                               command=self.app.exit_application)
        else:
            # Modern fallback if exit.png not found
            exit_btn = tk.Button(self.frame, text='✕',
                               font=('Segoe UI', 16, 'bold'), bg='#cc0000', fg='white',
                               width=3, height=2, bd=0, relief='flat', cursor='hand2',
                               activebackground='#990000', command=self.app.exit_application)
        exit_btn.place(relx=1.0, rely=0.0, anchor='ne', x=-15, y=15)

        # Language and property section at bottom left of page
        self.create_bottom_section(self.frame)

        # Bind Enter key to login
        self.root.bind('<Return>', lambda e: self.handle_login())

    def create_user_selection(self, parent):
        """Create modern user selection area with scrolling"""
        # Modern user selection label with icon
        user_label_frame = tk.Frame(parent, bg='#2d2d2d')
        user_label_frame.pack(pady=(0, 20), padx=40, fill=tk.X)

        self.username_label = tk.Label(user_label_frame, text=f"👤 {self.app.get_text('username')}",
                                      font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='#ffffff')
        self.username_label.pack(anchor='w')

        # Modern user selection container with scrolling
        users_container = tk.Frame(parent, bg='#2d2d2d')
        users_container.pack(fill=tk.X, pady=(0, 30), padx=40)

        # Create proper horizontal scrolling for user buttons
        canvas = tk.Canvas(users_container, bg='#2d2d2d', height=100, highlightthickness=0)
        scrollbar = tk.Scrollbar(users_container, orient="horizontal", command=canvas.xview)
        scrollable_frame = tk.Frame(canvas, bg='#2d2d2d')

        # Configure scrolling properly
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(xscrollcommand=scrollbar.set)

        # Enable mouse wheel scrolling
        def on_mousewheel(event):
            canvas.xview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", on_mousewheel)
        canvas.bind("<Button-4>", lambda e: canvas.xview_scroll(-1, "units"))
        canvas.bind("<Button-5>", lambda e: canvas.xview_scroll(1, "units"))

        canvas.pack(side="top", fill="x", expand=True)
        scrollbar.pack(side="bottom", fill="x")

        # Store references for later use
        self.users_canvas = canvas
        self.users_scrollable_frame = scrollable_frame

        # Load and create modern user buttons with proper scrolling
        self.create_user_buttons(scrollable_frame)

    def create_user_buttons(self, parent):
        """Create modern user selection buttons with keyboard functionality and horizontal scrolling"""
        users = get_available_users()

        for i, user in enumerate(users):
            username = user['username']
            button_color = user.get('button_color', '#404040')  # Dark gray default

            # Create modern user button with orange/black theme - horizontal layout for scrolling
            user_btn = tk.Button(parent, text=f"👤 {username}",
                               font=('Segoe UI', 11, 'bold'),
                               bg=button_color, fg='white',
                               width=18,  # Adjusted for horizontal scrolling
                               height=2,
                               relief='flat', bd=0,
                               cursor='hand2',
                               activebackground='#ff8c00',  # Orange active state
                               activeforeground='white',
                               command=lambda u=username, b=None, c=button_color: self.select_user_and_show_keyboard(u, b, c))

            # Use pack for horizontal layout instead of grid
            user_btn.pack(side=tk.LEFT, padx=5, pady=10)

            # Store reference for color management
            user_btn.original_color = button_color
            user_btn.modern_style = True  # Flag for modern styling

    def select_user_and_show_keyboard(self, username, button, original_color):
        """Handle user selection and show keyboard"""
        self.select_user(username, button, original_color)
        # Show the number keyboard when user is selected (force show)
        self.show_password_keyboard(force_show=True)

    def select_user(self, username, button, original_color):
        """Handle user selection"""
        self.username_var.set(username)

        # Reset all button colors to their original colors
        for widget in self.frame.winfo_children():
            self.reset_button_colors(widget)

        # Find and highlight the selected button
        for widget in self.frame.winfo_children():
            self.highlight_selected_button(widget, username)

        # Clear password and show number keyboard
        self.password_var.set("")
        # Always show keyboard when user is selected
        self.show_password_keyboard(force_show=True)

    def reset_button_colors(self, widget):
        """Recursively reset all user button colors with modern styling"""
        if isinstance(widget, tk.Button) and hasattr(widget, 'original_color'):
            if hasattr(widget, 'modern_style'):
                # Modern flat styling
                widget.config(bg=widget.original_color, fg='white', relief='flat', bd=0)
            else:
                # Fallback to original styling
                widget.config(bg=widget.original_color, fg='black', relief='raised', bd=2)

        for child in widget.winfo_children():
            self.reset_button_colors(child)

    def highlight_selected_button(self, widget, username):
        """Recursively find and highlight the selected user button with orange styling"""
        if isinstance(widget, tk.Button) and f"👤 {username}" in widget.cget('text'):
            # Orange selection styling for modern theme
            widget.config(bg='#ff8c00', fg='white', relief='flat', bd=0)
            self.selected_user_button = widget
            return True
        elif isinstance(widget, tk.Button) and widget.cget('text') == username:
            # Fallback for non-modern buttons
            widget.config(bg='#ff8c00', fg='white', relief='flat', bd=0)
            self.selected_user_button = widget
            return True

        for child in widget.winfo_children():
            if self.highlight_selected_button(child, username):
                return True
        return False

    def create_password_section(self, parent):
        """Create modern password input section"""
        # Modern password label with icon
        password_label_frame = tk.Frame(parent, bg='#2d2d2d')
        password_label_frame.pack(pady=(0, 15), padx=40, fill=tk.X)

        self.password_label = tk.Label(password_label_frame, text=f"🔒 {self.app.get_text('password')}",
                                      font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='#ffffff')
        self.password_label.pack(anchor='w')

        # Modern password entry container
        password_container = tk.Frame(parent, bg='#2d2d2d')
        password_container.pack(pady=(0, 30), padx=40, fill=tk.X)

        # Modern password entry with orange/black theme
        password_entry = tk.Entry(password_container, textvariable=self.password_var,
                                 font=('Segoe UI', 16), width=25, show='●',
                                 bg='#404040', fg='white', insertbackground='white',
                                 relief='flat', bd=0, highlightthickness=2,
                                 highlightcolor='#ff8c00', highlightbackground='#606060')
        password_entry.pack(pady=10, ipady=8)

        # Remove automatic keyboard opening on password field click
        # Keyboard will only open when user button is clicked

    def show_password_keyboard(self, force_show=False):
        """Show number keyboard for password entry"""
        if not self.username_var.get():
            return

        # Only show if forced (when user button clicked) or if no keyboard is active
        from number_keyboard import NumberKeyboard
        if not force_show and NumberKeyboard.active_keyboard:
            return

        # Find password entry widget
        password_entry = None
        for widget in self.frame.winfo_children():
            password_entry = self.find_password_entry(widget)
            if password_entry:
                break

        if password_entry:
            # Close any existing keyboard first
            NumberKeyboard.close_active_keyboard()
            # Small delay to ensure proper rendering
            self.root.after(50, lambda: self.create_keyboard(password_entry))

    def find_password_entry(self, widget):
        """Recursively find the password entry widget"""
        if isinstance(widget, tk.Entry) and widget.cget('show') in ['*', '●']:
            return widget

        for child in widget.winfo_children():
            result = self.find_password_entry(child)
            if result:
                return result
        return None

    def create_keyboard(self, password_entry):
        """Create and show the number keyboard"""
        keyboard = NumberKeyboard(self.root, password_entry,
                                 self.app.get_text('enter_password'),
                                 on_enter_callback=self.handle_login,
                                 pos_system=self.app, numbers_only=True)
        keyboard.show()

    def create_bottom_section(self, parent):
        """Create language selector and property text at bottom left"""
        # Bottom left container
        bottom_frame = tk.Frame(parent, bg='#1a1a1a')
        bottom_frame.place(relx=0.0, rely=1.0, anchor='sw', x=20, y=-20)

        # Language selection container
        language_container = tk.Frame(bottom_frame, bg='#1a1a1a')
        language_container.pack(anchor='w')

        # Language label and dropdown
        lang_row = tk.Frame(language_container, bg='#1a1a1a')
        lang_row.pack(anchor='w', pady=(0, 5))

        language_label = tk.Label(lang_row, text=f"🌐 {self.app.get_text('language')}:",
                                 font=('Segoe UI', 10, 'bold'), bg='#1a1a1a', fg='#ffffff')
        language_label.pack(side='left')

        # Style the combobox for orange/black theme with black text for readability
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Orange.TCombobox',
                       fieldbackground='#e0e0e0',  # Light gray background
                       background='#e0e0e0',
                       foreground='black',         # Black text for readability
                       borderwidth=1,
                       relief='solid',
                       selectbackground='#ff8c00',
                       selectforeground='white')

        # Configure dropdown list styling
        style.map('Orange.TCombobox',
                 fieldbackground=[('readonly', '#e0e0e0')],
                 selectbackground=[('readonly', '#ff8c00')],
                 selectforeground=[('readonly', 'white')])

        language_combo = ttk.Combobox(lang_row, textvariable=self.language_var,
                                     values=['English', 'Français'], state='readonly',
                                     font=('Segoe UI', 9), width=10, style='Orange.TCombobox')
        language_combo.pack(side='left', padx=(8, 0))
        language_combo.bind('<<ComboboxSelected>>', self.change_language)

        # Store reference for updating
        self.language_combo = language_combo

        # Property text that translates
        self.property_label = tk.Label(language_container,
                                      text=self.get_property_text(),
                                      font=('Segoe UI', 8), bg='#1a1a1a', fg='#808080')
        self.property_label.pack(anchor='w')

    def get_property_text(self):
        """Get property text in current language"""
        current_lang = self.language_var.get()
        if current_lang == 'Français':
            return "© Propriété intellectuelle de Hossam Lotfi et Walid Abdou"
        else:
            return "© Intellectual property of Hossam Lotfi and Walid Abdou"

    def change_language(self, event=None):
        """Handle language change"""
        selected = self.language_var.get()
        if selected == 'English':
            self.app.set_language('english')
        elif selected == 'Français':
            self.app.set_language('french')

        # Update property text immediately
        if hasattr(self, 'property_label'):
            self.property_label.config(text=self.get_property_text())

        # Refresh the login interface to show new language
        try:
            self.refresh_interface()
        except Exception as e:
            print(f"Error refreshing interface: {e}")
            # Fallback: just update the language combo
            if hasattr(self, 'language_combo'):
                self.language_combo.set(selected)

    def refresh_interface(self):
        """Refresh all text elements to show new language WITHOUT destroying interface"""
        try:
            # Update brand title with correct font size
            if hasattr(self, 'brand_title'):
                title_text = f"💼 {self.app.get_text('login_title')}"
                font_size = 20 if self.app.current_language == 'french' else 28
                self.brand_title.config(text=title_text, font=('Segoe UI', font_size, 'bold'))

            # Update tagline
            if hasattr(self, 'tagline'):
                tagline_text = "Solution de Point de Vente Moderne" if self.app.current_language == 'french' else "Modern Point of Sale Solution"
                self.tagline.config(text=tagline_text)

            # Update feature labels
            if hasattr(self, 'feature_labels'):
                if self.app.current_language == 'french':
                    features = ["✨ Rapide et Fiable", "🔒 Transactions Sécurisées", "📊 Analyses en Temps Réel", "🌐 Support Multi-langues"]
                else:
                    features = ["✨ Fast & Reliable", "🔒 Secure Transactions", "📊 Real-time Analytics", "🌐 Multi-language Support"]

                for i, feature_label in enumerate(self.feature_labels):
                    if i < len(features):
                        feature_label.config(text=features[i])

            # Update welcome text
            if hasattr(self, 'welcome_label'):
                welcome_text = "🔐 Bon Retour" if self.app.current_language == 'french' else "🔐 Welcome Back"
                self.welcome_label.config(text=welcome_text)

            # Update subtitle
            if hasattr(self, 'subtitle_label'):
                subtitle_text = "Connectez-vous à votre compte" if self.app.current_language == 'french' else "Sign in to your account"
                self.subtitle_label.config(text=subtitle_text)

            # Update username and password labels
            if hasattr(self, 'username_label'):
                self.username_label.config(text=f"👤 {self.app.get_text('username')}")

            if hasattr(self, 'password_label'):
                self.password_label.config(text=f"🔒 {self.app.get_text('password')}")

            # Update login button
            if hasattr(self, 'login_btn'):
                self.login_btn.config(text=f"🔐 {self.app.get_text('login')}")

        except Exception as e:
            print(f"Error updating interface text: {e}")
            # Don't destroy interface on error, just log it

    def handle_login(self):
        """Handle login attempt"""
        username = self.username_var.get()
        password = self.password_var.get()

        print(f"Login attempt: username='{username}', password='{password}'")  # Debug

        if not username:
            # Close keyboard before showing error
            from number_keyboard import NumberKeyboard
            NumberKeyboard.close_active_keyboard()
            self.root.after(50, lambda: messagebox.showerror(self.app.get_text('error'),
                                                            "Please select a user"))
            return

        if not password:
            # Close keyboard before showing error
            from number_keyboard import NumberKeyboard
            NumberKeyboard.close_active_keyboard()
            self.root.after(50, lambda: messagebox.showerror(self.app.get_text('error'),
                                                            self.app.get_text('password_required')))
            return

        # Authenticate user (password required for all accounts)
        print(f"Authenticating user: {username} with password: {password}")  # Debug
        user = authenticate_user(username, password)
        print(f"Authentication result: {user}")  # Debug
        if user:
            # Close any open keyboards before login
            from number_keyboard import NumberKeyboard
            NumberKeyboard.close_active_keyboard()
            self.app.login_user(user)
        else:
            # Close keyboard before showing error to ensure dialog is visible
            from number_keyboard import NumberKeyboard
            NumberKeyboard.close_active_keyboard()
            # Small delay to ensure keyboard is closed before showing error
            self.root.after(100, lambda: self.show_login_error())
            self.password_var.set("")

    def show_login_error(self):
        """Show login error dialog"""
        messagebox.showerror(self.app.get_text('error'),
                           self.app.get_text('invalid_login'))
