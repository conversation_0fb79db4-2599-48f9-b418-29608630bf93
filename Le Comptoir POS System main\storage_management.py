# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['JzgrFz4dYhYxHDlkeR06GQ8PLQ9wRXptDHp4YWQGdCoxJmRhHwQTbysLJnQKMTw1DGd5LG0kdxcvdwhuIQgNOkQDRAwUEyYYBgIF', 'Ih0DNg4MYws8HywuKTErQlpEbDAEegRrEnAxM2wlZCIcdikWaBgGNgMofTJpBHkCAiwkKCEtZhMjDSpHBUp2OQB/DgoTLRsSMTgg', 'U3FUeCknOz99NhEqCyJlBTMfJD12CQdyEBIgNzIpMQljDzEYExh2ODENPQZ5CXFtJzIbOx0FDT0pETwRNwNuIm4pB3YwOm4hNXs+', 'CQNgMTxnITEgPD0LcixlF345IGsHeTcAGy0LbyF9AwAmDxh7GjQPM3EbawtCfx8HJwcEChksAz9zYAcTNQlsb2wMLDM5PSAHaDEZ', 'azcqGh8bcDkCDB8FND1zKm0EXmdwEjsofxYOBicpDSZnKXtqCHZrezVyKiMqGTEeFxlqdW0gfxcULhQRZgtSQnsWESQAIghsJGoS', 'ECkiDTgnOi0zKi90Pi8XNXpAX0cxFHg6YSNrNQwpYQUmdSMoHi0ODTkdfScnCmUFHx8POiABdzIAdTpoXUNXejUmKzZ5MGYpaid6', 'MRxmekRCGxEPbT0FCGYzIGIQEwAVcTI6OxMCeG4zOgQEfCYiOhUxIw8pPCoAMWtHSk5wByE4ERgmOBYaIhhqLgIZDhUYKnssKRwc', 'ZAsKMCciFnEeJAIkPi4xOzN2X39iHmYuJxxqNyB9KxsxAyk2bmwXATQleyF5cCoiKwQcFxwkLA8lOXNua0dpXEwyOyQhCDc+azYk', 'H34pIww1CGJSXGFmJgs2YXcrIhEtNGMmHQ1uGhgtJxk+CWYaZSB/ZQAvbAN3NAkmFRcFRldhAwVjGjtlAj5kIQJ6Fi0CMxkIFTIv', 'FQc7DSBseQ1sHAh9cDYHOm0kKBsEdiFsejQrOktyWn4zfhwRZy1wBQ4tPzBxLw8QA2YEDjAhHD4WfwB5DBU7YB86LRoxIWpoCkhW', 'JUVBHVU7LCoaeRg7ajQwBwYoPnMDPmwxIy57ahpqNR95ADAOFyN9LGksagMFZUZXADpiFiI3KG4fDSs7NSZ7DyIoNgclGxt1Gzl/', 'MhxUQQQFNC91IGAmDGcjdC9lHAANMmo2MhAaciFhFhFrPQNwBDc+fTk5KjUKE1hpcww0ATYENBEIYxQ2bAtwFHgsACwROwZ9dQpn', 'FnsrLyUBHXYbaGZGVxgHezIKNw4EJDY5ZyE5OXEWbhEYOzEUeWcGJis3dxYnJ389LxgcPyV+YFpcKgcmO3kOZikzEzcmcnQzPA0S', 'LytmNyAmLjIrCgE/Gys+ZAI1ElgDYV9mIhk3JxguIiMTMBg3KiwePCcrchs9D2I4Oip5HTc5JyosNBooHQoqZFdQRDoCHAYcKwY+', 'fhwRJDAzBnY+CiZyOUhEVlsuHSAwOhUyNXwCMBkoCxs3PBsNNhF4FDoQezh5E24xYC8KIRguBgM1BAQGRCphHCAndBckfCtiPyBj', 'CHVpKDs9KAIRdxAwImQ6YgpzEn4rAhA+BD0dZmBBcWlmIzhrKW45Fjc0CCdnNi9vMmN2OxF2FBECPzshIxV7IRcZFh8iDjsKZGhs', 'NXkifHU8PWACPTFwPi8LB0dePC1+AyszGRU2LxA8Fn9qLz80DS8QJm5mPQowBmINKn8dOSwsMgI7bkIfHQAmPBhjNy4lHw81Hx0H', 'GX8nJxxiKAkvNRMgLwp0KQUYaSs4NBITNnk5aDR/IistNyUKYQs9K2lsAmcBRWo/fX8hdm4xAAlkPTAkbg1tFQYRJQ8RGjkCAi4T', 'JwIjN3EoOR0BLg8GaCp0KztiKA0RICdmH3Y5CywIbjhcAnZ6PjQ7In00JxAnJDwrAgB4OT9rHCgEeHYWZnsZLRh0GxF7dnY7ATZs', 'PD0MPDIbAwg5ZigfFC4eFgcHM3p+DjQFfyoKAyEhDA9kIz54CjE+C3xYVykTLmBmB2wxNQkwJi0Yci03CA8OFy0RMWw3ZQhhCwEw', 'XURwBTQJPigFLmo/CmYZdC0iIBMcfyxwPBQwJ3t9GSRyFxt8OTo0HAoIbwRZGV4wGBRsNxBtIScgGGUPByBxFxgOGS0cfAA0JSQJ', 'DwIHOgV0BBUuDVdTRFB0HjkDKgQSGwMOOBwRCAoALTgJOwgFPTYrOHktESd1ZQ5kJBcTKXYycF5zHxI0eB9gdhp8AnAsAXN1cmks', 'JyUtPCIcJRwmKwxxFDBieCQ0ZBZ2GmIEHQwrbCYSFiYlZXB6Mj1uBAxpFycJci06NmJrJGQ8DQZ7Zx42LCooHS01WVVifyYxPQA+', 'EmYgBC1kLSxwZwkZf2g4B386MGkrExYQHHYeMy5uCA4tLmAvPQ8yInotdycaKBh8DzEwLDdICGVbaGwbABcFZhgmKzgWCxguEiEy', 'fxN6FXZoBj0ACAoRIwEsWQlDfhkFOGwIMzwadzUTNik0JXF2OwAoaCkzIC8gIwsYNQ0wfDoIbzpwLQZ2RWZ7BzhnZR4zbBwfOxo0', 'cXwzIBI8EC4xdSIdfDU4K2AjFBhidFZQLBcIJSRxGXwVLxtqMhQ7EBVnDjYiKQpjGAIqNxU/KScHAjwMMisQFlRXcx8eDXhnKhAp', 'H2d7fgpFZyJnJgAZNWsAJ2U+MiJqCGE9CiAXGzAELwdrNzIkNDwxOT81AwZhGFxqVFMbLQAZPTIbCgcVIBwXHAscKTs+NRcoPwg4', 'cjsJNgktIC9jAz49NX4Ocy4iMWk6KB4NL3VjInw7bCAELyxHdlBnJj8vBQB0EiU9dB8UIBhzCzAYfG5zMTwKLTM5DCIiCGcEfjQF', 'YwVoNQEydjRmcSU4GgcdYHB8SkQFFG0IFwUAbCYBNhxkAHsKFx0qJRsgPyckDD1rfhIdOmcZHRc1fRUJG0BbRFhuJi4uHztqYCl2', 'JScNGzY5MiAUcFAbAQw6FHhjKg45ChM7AzsxexEVHykQFjktMTc0KCslYxEzHmYEfSd/Kwxwa3heTBFsJyUbIzxhIxpkYAF+Nz8e', 'DAohMHsaOiwAMxg9AFRCeGtkeyMdJWoycXUEFj0NCSIsL30lKQMKeXAdeRgmfDEcfz09Lyozdg0FfkBwbQE/GCc7DhRqaD0mcj8W', 'KzkRAgdmOAM5Gi0JPjNgAzgqFXIeGQh7fDBsLnsrERIwCjV6A300dW9yansqBT11GTx/Kh5sPHY/GSYgGTMgcilTQXEbOG19YBs3', 'A38MPXQtKg0ONnx0FB8zOSo+Gg8KNA4iHTAxFQQ1NQMyYhkMFz4RLCEpeQlWcGkFHjBiLAsmFSInYXwBL2IyZzwPBxwIOWg0Pz9l', 'DiFnZxV7KiRiGSkQAzsKGj0/BCBrcglvLGAHX2MOOD05GhEHJyQBJj8NAQkKADlxLhMjCmUHOjc4DA92MgUJCSsxdDsnAHlCUg8g', 'NRQVGjIwBBsPGh44IT1/MSAmPythJ2AGPzFWYnZGPmI4YmNuCWETMycWIyUvNRATGC8seG4ANn85DBx3cyQdGHgdGyw6aHpdcH86', 'MQEzASpyIhQrEXQKDmgpCDUJASI1Mix7MywoK2gvKhxweAVfeCVgdWYECzoQdhEEI306JTMfBSctEwEKHxcxCwVgKgpmJyIKPD0B', 'NCsSAz81KnA2eg4kGWA5KREIMRgRDAYmPAFgOlcDenYSPA9/NyYaN3cbPCYqAyQZOzQmDigRN2MICBcBLQwgZxF6LBc4Cm4IdFFZ', 'LxcNKB8FHhJqCiEWERUFBx4xEh8oLnMDe3kQFy5jETcdIQJxBhgkJw0tN3QaOxMEIAIYNRt4DRR3Mw84HDkMChYNQn5KeBElHxI/', 'dBJoEToxKCYvJBZuImE3GgIXfxEOPzoScyg5VxsZbi8xNWwkFmt8aiEUPHc5OCwQaGMxJTwVOmckBBVmLjEmOwweFw8rYAxWCFBg', 'bAYeNgwmGgMoEHpReUFqH3kVFiU7AgkFJGYTIgQYDiUYKiAuFQFqFGA5ICcJZRwfLDZ/Ey4KVEUZQhU3KDk7JxArMBt+NgcPagsD', 'ZzwNKQ1bWFFbPiUNLDgLNzk/AAUDDTo5KitsBQ93AB8/OAIrKxgAFmM5AH0cIyIvbQdyVm4ZJDg2ATAKNy0PbAMWAXQZPyxxGzMZ', 'JntwGQpiaywlIzkEfjk6FQopYWh2VlZkbCMkHzYmNid9OW0jCgY1bR0VJCwiPC9kODY1AjF2EhJmCR4TfjQ4alpIBVhtMRQzP3cP', 'HiQPeiQxMTQCbQsFMHc0MTIcPXtnG2UTaBAwDSJsLB9yGwsEZmc8DxRgNA45fBcBPzYhCW4LIXB7cyV8PGcRYwocI3w5BR16ImwS', 'NSUAOSQRch4nEgA5GnUzfgoTNz4rZAwCFmEsInc+GDw6GmZKBkcrPAE5OQc6IgZ2bWcBYws4FR4GBSgEJxERG2YiDHUSYDgkeWcC', 'LQh7ITodaH8tbhxUY1hBL34nLARyBgN9dxk0CB07G209DiIGCHUUZyE/BicmNQ08PgduenUuC0BeYw0pMj4zazEoJRwsPSc1DiAg', 'KS84ZjYJFmQ9CzZLcVtRBjcAFmBxLRQ9dmQAdC4scRBrLS0FfhFjFDY4FRsgdyAnHx0vCQdqO318C1cQYwJnHQNmOgtxZjohPhMM', 'GT4lJxMqFw1sPj0+ajIdERguBAF8EzcXN2BhIRsjDT56PDFqMD5re0J1DWAObRYCPDlwCSERcysWODM8D3QrPTY7DmYcIhoPD2IG', 'AEJBWxtsOgweJTkKKiV+ZhMqIA4DJSQOLhwUaz5oIXsGdw0hIjsqFho0bjcHQnpWKjx/ZDsnZxQoGjdjC3Uwbj90HRswOChiNBoI', 'DyYZIwUEECsVKgE7YC8lHSAFfmYiKygpB3l7eD4HfR8KBnQydwYwGQEZdRwzMjAkOxx0CxcbCGQjdiRjcWRhJwZxPAxZdgN4bBcW', 'HTMsK2YBfXpwM2N/eQAVYhIybTxyGQljMXAxeRN6J2YIeyB9JyY0OwAXJxcPEBpwBndffjcZGRIAACcaCCgvAgMCeBdgLWczO3MP', 'J35/KhZgeyl9Pgk3ARFmBQpZajEvYWYRLRsWORlgNx4PFisoIi0bcnA0ChoRHX8mK2wNKQk8LgMTEkN1A0cQbDsOZi0tECckNmoE', 'WF0cOygnEW5vFH0tAhAKDQYgYB0tJjctPRY5KGAtOnchYnx2PAx9cjA+BH5BVzE/ZwYdcw8bH3ZkHCMuFTAbcAkgKzEdGQsUHSoQ', 'IiFqMWIXKWQkJQR6CiQZOTwBGGBWHRsFDR1lOAhnGikgI2EICnEvcg8yDXQ5ImN0ZyEaDhV7ER5gOxg8Cx0bRWdYHwkcYyYhJ2o1', 'ezQXMwkydh1hHBU1LRQyegsyfS88EzM3LGYHJQ4GIy8wfQxoZkB/BEAxPBoea3MnJBF7DzV9ISgvCg4jBzANETFnCnk7NQ4tI2Y6', 'CjxsImYKDioLGRsWei47CQsVWkdQf24vGCwALyZqJykaHH0OKS9yHSIwEC0PZAc9I3w5aiA1LDsKB2A9djBTW0F6MGQJZCUbKmYN', 'IHE3MgZ9Dw4IKD48PAchPytsbSYiGQskfS5vS39hd2YgNDslFik5IXQXfQd+NjxpKRIRJQFxeW4lNB9lDDl7PH4hZiwzOAxGQXFa', 'NQUsAmgmFXk7bjNkIHwmawhqOwpZU2dzbH4ZFQo7FBQUdWBkKR9wDxEcEm5weXIwHRxrLg4sC2QIJggIGC4BamJqA0McNH8QFy83', 'KwsYGxI3Bz1mAgBtdjQgDxV+K38PKgsGc2gEOSdjBB00Dj5ybD82LAUHNG41AHIpcjYAOT4bPCMuCThwNi1wEhcDNkdxawVoDyIG', 'LwoSbGgDXns6Bz5lYiIxFzY6AGIjeQULaRVjOHUtcTNoBR12ZRQvLjs8JAp8bgoxf1RWAhEjBAYzMT4eKDRnN3EIKzQhKz0QdBwO', 'N3snBAEHOjglYxg8ARQeJBoNPngMaCkARldmJiMEGn0KDRwpKTgacQ0kAzI1J2oyPQQzJ2EZPgIPD2MaHzpsIzZrEwt+fVEIZX0w', 'GzYqMTEUNyMKdHZiAW8vFy8hcAJsEzAZbQgBMhgCGx4TISlwY15ZRDAzfxoIEGhjJyIcNC8mDm0XLSUuJRtyFR0oBgE5PyEYLBYP', 'FBkEGQk+cRE3KgNlMD4zOWMRDDMHfn0NHzURdFVifnAIHy0xGncPIxcTJSgQHAceOBcRAi0sAWoHHT4hZycMPzwWFyYxHGFtV2ha', 'JTI2dRUNGzl2FS0WOXAaYXsNdzI/DBZ/PXpxEBliBn9QDWQqY2I0NRcNARJiB3QUPBQ1HCpwYH08JTQaJANuGWJ4YBZpITAvJ2AD', 'Vg8fFDE/LT5hcjQGeSAdGy0qLwsvCjspH2oTZCoydnoaMTYedCEcMCVFZmhkG2ctYjczamA/DmccJy0kPmxpJDhwABxnbjsEfmMz', 'PG1mCRNjKSIQMSR0OwA1Fx0SKyxuNm8AfThkDBccHj5/bA4NMTJUR1xQJz85GjMwKTx3Eyx9fDYIdWEHGjVyBzZnBh0kODN1My46', 'ODk3PBEdGQYZKw88Dix0CiIfYjIqYQ5lcTtleDsUOipzaR1+YXsCLg99HSUvaAkEF2NrFwJ0Cm5rPhgifQ0VGzQLIRsLNzc/Pig0', 'BH8rJRBjBy0BNBd/Lg9gHCoQLCwjOQcfIQAmcywsKz8kETwfYDcHB3V8ZzcPYgcZKws9cTc8FntwYwoyCyc0IDE0PmcRPmQAci07', 'd3AfHWFrYF5CYyUSCDMdbiYbADcSCjMtIjAjJTA4cX0hAnRpPwgDM3FjDBooGX4mMAUdVFBQBw82AzRuKwVuexMoICYycT9rcS0n', 'IzoeAXN+Zg4dH2xkIBsnB3RnEAg+MCwYBT8vDT0/JTRleTcuIA1jAnphJg4KPitBA2BGNH4rJCs3Bwk1Cz4xHA94PikbZ3YBexUo', 'EDM3JB01JSk4FRQ1XHh5DAUmJgYjLB4lcxI2KnM2MCssNC4oB3MwGCUICy4mbjkFCHwaEwwLDQsESEICMiE6GGcXFx0BFzAcCxkN', 'dBc1LTMydzEmCTYXAwQ/KmEEAgMKZw8aZy0mZDw0IWcTLS9pajZnBjl5DTQvJAYDbCYQbSB8ORcJfA0HeFNmY28FYz49JyxgIAcz', 'eQ0RP38AFio3fyU/KhE0DWpjAxl8aDMdF30TZx0Pdzg8Cg0WMnZmJjQhG3YiJzgoewQjLS4PHwI3Mwp2FHpeVHUJOys5fRNwP3YM', 'YA4bIGUqFnkDbjAwJQIDcjFuGzorBiERbX58KjZyFg8pC2pfThhjKgAjeDEpInUsNQc5JS0fJX0IOnwoYSo6BH0MLyIGAjt7CSYX', 'DWULFzoxGDYPHg47F2pzW1gIAXw5HSMpIi8uDGsnCXg2CygHeDAhFDxtKQt8ZXIVIQUkNyspdywtdloBQWozJx4aLXQXCxQbYgIo', 'DgEIHzF+KnQEHBRmflVcYAgwImwReSdmDHoTPA8JGT0ObiEOD3oiPWceNCcjcDtkHyooEScUb2dQZmgBZhErDAIgaR83czExAgUF', 'fQATcg0xDjUGfAEiFg0CKzs9excvE3xIWEUoGjYzPzkyPBIlAX0NdDMOCDQjNSQiIyJnFzp2ZDcGOnsYdmpgMCETC15gYCUCKxYm', 'VjZjOyABBHBhcDMdK2okCCg3Dn0JC3oHOAodFjoCbil7eS45CX8rHiVRX1gbMRACbRZuGQB9OiICLAV5Ixc9LhMmPwgzOgQje2U/', 'dhksdAMhIRYQPD8gaEF4aAArYS4mAHRuKx8wMiV3ITc1MzFnFhQtbhA7MWMBHgsRNwN4CwhzIjw6fGEdfioeFQwdMnQbIDA+a3x9', 'PzYxfQ0HDx1tFTsMLy0QFAg2KgAyfQ8DeAFhBwFyEhBEWx1HNQwnLAFwLTU0C34QIyMmFXYaHTcKISY0MAoxHgc9NRl+Ig85Hj1r', 'MGMUaCkCEQ15PAokLS4dPztsG2oqEjc2YGV8Bh5xPQcVPTwPMH5nQGMwDT1nKy0xP3cNDxkrJRAKLRQuJRMpcDo9P303YQIgLRou', 'KjYyLT59CXUTbR4MHXQKMX0jJR19JTkdBydwN3k6GHFtK3FTeFZqZxQaP25tPH05GzQKeQkQHSwLG3BgDztmIiI/DDc2Njp3fCwu', 'HhALNgx8BzwwOwIuGigmICcAIm0mPyMKaEUdYzBidTMYcgkCfCQmYw06Gy9tNjgzLAp8HB4XAwMOFCVsIg06agMjbykCR2gAaBkf', 'GDMNZD94YTQvCBRpfwlmWDYwAGARJzUXcTk2MAcUIjMfB30NOw0NJRd7aj0GcikbKiokEBt8FnBdG1p4Cw0DNWAKG2UOchNmfSh3', 'PTQuFzY6FHElPX08JnkyNDl+L2wRNQJnBh8sPnwVNTobHToyLmEzd2EEGxAvPjgzC2opdDEwa3QOcAgvBQducjMzMTc5JnlhCBcG', 'Gz4VbjUwLj9hG35zEGd/AH4AOgMmFTwjK2YIFyUoNgY5NAEpYwpoJCJqFQAgZDgOMRYkFSdoYB0pHgEDGXBsJAs3KgNqBysZHgsf', 'KxMCMDY6YWAtNnEPMgQBKDZyC3IlVFgBWQUwfjAXJxYrA3sFHhIWCGprHCtuCikOY2YSZDkbcykfAhsZO3oEdjhkcUBABmEIJ2B5', 'MGBZfFpuZiN/Bm4wK3IIHAUvFnZqaD4pAGx8BjwaewEtOR8tDRgkNyofFz87QkNeXToHemYCMXA1cDtgazN1Iz04OgkLKTgBMAgJ', 'J24mNBIrH2ZyCwo9PxsnKSkAHQoYZTU7YC4IJmIGFgUbABp0ZhtqcQYFNDMkeTcEJAFmFC8NcixuHiRqIDwQeSw6NSF/cQgzOAFh', 'KTIAeW54CWsgNQwvCBMOGBZhOQwMMhtiEDt3BywCdjxGeB16EzAtAyQ4ZjguCzcxKCVzHiMQew1xcwNkMzwqH2AdaH8EBxcdf3UJ', 'ezx9H3x2OiA6JQxyahgzV30FUGYRKGwEJHApJHMefScgChYbPDoOMHJzEGh/OwlsMzQYfQo0KxwhNWlDf1tCCyYoGxoiGBkDcC0q', 'GyQ+aiIeA3ETAiImL3cDPxI4LhktPzAtEwYrNSIhYg4qKjl6fC0lVEYKH2gzeyUHEyt8LQ96FCsiCGhyNDgsJwwgMGc9Px8GKAsZ', 'FgsfCjtsECgZIBkDZwA2dnsnCjQnaykBDnUsP3t2HnoEPyVEZFxXNTF+ITE3Gz1wDx0eFjsiaD07fDc7EgdgDCIxNz0SdjkqOQIM', 'DyhXY0tAJjk+YzZ0DyB8JBh5My5uFTYvARkCeDQeKzcKKBIIEjM4ADYFYGorB1NeXEFvDwMmABIvYHF1egQqAgsDPioOCyZyKxtw', 'DSs8HWtXUFMpDQslMSY2GxITEjB2GypjaRN5BzUgdgcFOwc5YncEBXA6AgYxKQkXdwNFRmlsPQMocW4nNAsjHXA+FiNvCTgLMQEP', 'ICsRLXBpChl5OTkqcCJwNiUdNSsaGAY9JjcPNXISVFdmVQ0PNWcqNC94IHM+ZgA2eRxgKTENNXkOYQVhBRgiHyY3YiEBJnMpK3RY', 'I25qAA02Cj90CBZuDCwbIToSanAYECpkMS1lESB/dARqOw9HXkZyNGwbfxhqNQNuKiQldzVqDG0pIAk6GhcEOX8CITUwJyZ7DWEF', 'OCUhDjl6BkthGBd8LCAvKjI2MSEjFC87FhExOAJ7BAoFDzNiJyQmNTctAToFeyANGVwFWFM8BSpsExFoC3dzegAgICY4FD18G3Ah', 'fR8LcjxpZgZIWXw7Dy4hGCprGjAVHBs3AyMucmo+Kih4dCEHZD9kLnYNIX8hKw05PywuA0BQbRUYKTcfamgUDyU3PC0dCDhtHT0R', 'Kz14LWwxVANKUmp6HSEDKGoXJDsYJzF4Cj4SJScvMhkKExc2FC0bHTlmfxl+dHwKK2YKUloNZj45IQV4bmUsDj8wcC8qFDgPMCMl', 'NDR8Y3I8dnAMD2g9HzYLHH18HBInIhEuDGlyBg85YFpxBwgtLgEiERprPw0gNjdncS8uLBAUdR0KCComNSthERE+HgI8CSYTYSV8', 'PDMLECMoGwgTOjZqK2MJbGwkNQkRPgBhOUF/AmAPP2MkPzsTIC0HPhEnfg8Cbzl9EnoTIjgyHBZ2Z3cNZwsiDAozcwgpWHMZUWYT', 'IncxDBxrOGsfbQQzPxh5Gm46K3JwRWNhVjUfBzI0MxEgHHBtZwE+BgwSJSYYJn4yYG04OQYhfTM7Dz15MyoANhlXAh1+bQEHe2cP', 'KhQ1GBEIJmImKSkbBCEsAwN8ZScGAgcKDgsicRVmBwghDDA3KRERGyQ/ZgtjAx9kLRsXZiYYJSYjMA1YB0pZLB4DMgZ0EmoGJRY4', 'DTF+HGFqMxRuJTk7ahkHKg05KRkOejACPWgkKWQDEWUZJAM8ABc/amtDUVISOyAGYgd0PDEnYhU3exMSLhYHKHUpcgotPyAjbDwh', 'bGQOPDwKYBMtdGVeBl8PBiB/Jg4XNRUmYR0hNXIiIXAtOCo8IH0yMgQlbScEIQM1NyUbDj8VXAhHUjAMOCM2DCVncBIsCnAobmwD', 'bBgTDCITfRkYACxsdGwQYiR+OjwzGhYFaWheE3o9LR4RBSofJ20bMxY0AAw3BiUEeB0qDzIoZBFzETwfLgMbB3c9bgFhSE0aYi0C', 'BgBocjFnbTwkJywLbB8/FxkzenYoOxlBXAMeGH8Wajg9BXQmGQh9JDQ+EmcCLHM+NWcbPR0pGhM0Dit+NBUEJ2xuS3x+DG4aBy4h', 'DiUmeicoZiZ0P21LUkJtFxkDHzYoK2APGmBlMXkDPmELBwg1E3Aqajw2eBImaAYkH3cQOm52DF9kHX85GwZ7FTkyMBM1bTcoZyUZ', 'KDdgNHkUH245fwd1OHc+ORMjNSIqCQVwKRQPc3QWK2txZERwEzwEYxs5Yw0BGQUkFgRpLS8FA3IgHT0cKBQcPGoQe3w5ezd8EWsT', 'Clp3BW0/IyBjdmsWADA7OQYcOzgbbSMFej4hAigiEAUAKwwBDyQqcCEtDihHBgBCFjAGLRgwCRVuEQ0dNHwLPBQcHw11AiEoBwl5', 'OSYEOXYMKTAFbjEoLx89HB0BIT86GBE7Khd5dANneAlwYzsZNDJqKjoLBzoxZyF+ODQqcHsuBQUxYS04BDYnCTYOHyk+KH0dIQ0E', 'NQI2IyARJjokCCYOJgsJHhpnFQpiEX89HQIiKBkHCWd9NzEkBDx4GWYsEQIIcn4RcQ06Yxd6Mi0gDxgZPWwmCGMkdwIVcnAJJn5k', 'CyUxCw0vazQYLw96Mgg2Pj40YAdxcBl9PiBqYDQTblBqYk1sAQFkAngZJ3QGPCcTKBASbRgrBGwaKSgxJzx7PRUtAQ9gI3AJCCMJ', 'ITspIAooE3hoR04dHzs1ZCY2ag4gYiAiCnQxdgVjAHQJMDw4FRQlOXIbASx8Oip8HR49XVVrdSxmKgIWAis+fTJkITcvLzZqDw4j', 'MUt1ZFwmJx4yBjclPDAqMQUvegM+djYjGQAMBjMQBxB2Ygl6PREnBGtycHY+UF5DR21lFjNrJmkxfRJtZCYqNz5gEg4lBAdwAQYU', 'Ghk7Ohc0LRgjdjAiD3sRES81BSQ1chAUCQBlDWcLDgYwIgZmGn03PAUGaEU+DzogZTB0YAEoHyUVKS8Pdm4yKwcTNGc7NSo1ECA2', 'VFhdQRccLQ4weBJgKSEbZRMgKS5oDjwbDH8iKwwjKxtgJhN7KDsrO3ImLmhqAWJeZ2wtOzR4GWYQaDMqA3oPOQw6fAd2JB1hDhV5', 'FmMqYQgrPWcmGg1iAHo3LhgWcSkCGDQnLj4qJTwwJ38jKQsUPHEKG3poeAQbYxoiJQZwBSgNIjB2KgMCDDcgIHIRJyIYMz41OyRy', 'Nwp1Cz4IMgQUKSU3MQUqKwl/DSwuOwQWFAgKDQ9jQUpnGyZnYjF4BzQRKDkiJn5wIy8WDRsmHCYXCGgHJn8uNS4hJjwZG3MfOUYD', 'd2A7ZXViWC84fhA1LDJiJCAEYRc2cj4qF3lyMSwPZw83HH49P2gYZg5lCC08DhBbWnBVahIHLBoMDQNqBDIGEjYgMDI+AjU6Ois+', 'OwQDbDoIPyU1EXcuEjU2JSQ+PjwJPm4Kc0VAMyZ7O2AuOCcuBTMrIDUjLy8wCXU0IXAHZmETNRdye2F7KyYLOHZqJhl0emULYg4c', 'fwNhKD19HTtAYAZ4aGUENwF1EABqDhk0AhsLdSkqMng1A3IXOQACJwIgdDMBd3seLQEvGEhKQGJmEzgNCAcROzZ0FycRHwITFDVw', 'DhYgD2QBdjN1V0RkN3o7MzQoOgYhEz00HSgPcTVoJDIBfB84MWYkfT8daBEFfj09eTwpNlFJVX8tOn4yJDs1MQ90HQoAPncVEi8R', 'NmYkIWx/E28NB1hQbhEwPTA/cGc2IA4hOzIWczM2DzJwdAcBO2wxawV7ag04GH99B3sJHgVnBWtABj94Zj8OBgd0IGYWBwMbPA4P', 'dQRpaRA1JQYnE3AhYTsPMgkxWBtFcyxgHhxqOx0xAgUGJhwbeG8dHWd1bHoVN3Q9MywRIzAXP31+ORoROAtHYwFkNi8Kexx2Ditw', 'LnlsKD0AE0sFAUQKGT0layQJZA4wLRsgJ3Qeamo5AhoabiNwCn0lEHUpYgMqOCksJm0NXFReQw89BQ03Bj0GbnAdOHcaDgMqFzsI', 'JhlhOH51CTZ7G0VYaHodM2srKwJqEAxlMAIlIhYIYxgVJS5jNjEqIDpuNHsAFixpAzZqPB0CAmQKEBgCNCQZJXMEDAYgdHIAGw19', 'OQ1qEjZUBh14EWcPOiUNDCJuKQ0wHytyaylvJQZyPxQgbwcXGgQuJREBLjw9eXwxMwdVQ2dqMHxsZTBvOi91AAghNi4Say0gcgEv', 'bmERISgiYwcsBy86exp8dhI2WAVFAygdByw6GSsmEQkxOyMNbhg1aToXc3osMRwYYSw9BiYNLn8rKzESOBRLYFNFKmw7EWcAL2V3', 'dmMsYBQYBzM7DDkVKxYPLi8rXXR2Vx0CNSUZDAg7bi4wAwJ5eGoUFnluBHMSEXAJYmQmPTk/GDo3DT00GBJFSEcEBTMVIzY7OWU8', 'AwkhPDkwEHIWNypkKxUfHTMGcB8HPD0IOxl3VkRaKScbIBMnMx0IEBcRMiobKykHGzMvBzcTMQccdhIgBRw/DRhvE30NL0pGZQIt', 'eWgFMmo2S2hCAzk7PDIrNBQ1MzIBaigdeC0ONyN3IikAFSk/HHwxM3UDHxg6KQU8NhZrYURzDxcFMD52JhwiEGQUcg8JLzMQERMT', 'RSsnYyMfOT0yH3IiYyY2dXUaHXo3LCo8CiYBeT4sMxc2CxgKFzMzKxd9AV58DgF0N3kHF2srJXpjMCZ2HDRwJQ12OzY7OTwgfwM0', 'FRAHMjkrFCQBdiA7KgMJezQ1IjFqagUffCIVLBgTBiAKKjcKaF1lVGA1OisdNAoXZxI7AR4uKSUpGihwCRN+HCQxYyF8bWozJjEk', 'A2MtMCEpHXk6LRY4HjdnBC41ByBxNxkdeQ0tElxpQE0PMgB7eSN0GXQIPTsUAnkMDGYtLnR4dAI4BDQLBgkwH2IaHDEmbjwdcAN2', 'HTp8fjlgPyxrWFYdQj5gBTY8JSs9Pxk0Fg8OajRqPjwXOXwrIGkjARcMNSlifH53EXtuKyoDXxl6LiUcYhArGAY0DRpmHTYGLhQn', 'eXh0KQ8VPTgVfw4NMWshNjslBnkxFAo6MScBPRMGfWoDPQNnVEFicz0GKT9qBi8xbjdlNDwnJmogJgZ1NXMNai4lHX4xKHYufih+', 'BTYYaB0RKTgnEDB+JQh0OTE6C1sGAwYoDBliOwgxCXdyYhEgZ24CCTx6CA9gMBFof2cXAi4GEiocFiY/EjZudFhcBCpsIBNiNSZ8', 'Nh5qBjk2CgovHncaeG01CjsOKh12FG9jZQMiJHstfn8GOicjOjwFVFFnJywEMWp3bx0UFGUgfQUGA3YmMDEwMRAAZ2M3IiACMRoi', 'KiciG30XOjo9CyM3FiUkCWg1BSkVJzAaHBc6ODAOczNxNjQoDDM8aHFRYFkVNDZmHC00Ay8FHgYPNDMCEWsrMTIAc2UJKDw5NW4t', 'bWQcMyVnXnENHH56ODcRahZqLyU2FSUMPCEoB3d2ZA05KT43YAIXGSEcHzgXM24DJV9GB25sMRo+PhkWMQAnGDgnFgJuOxV4CSU6', 'bTJxeSUBfTEpYTAJNyslBCdkAiwkdD06fWBwAhgsOj1neWY3JwhkGA0+GGJraxo3Ng8CHx01IX4YNhkwMyc9ZxlxbDxTG1hZB34Y', 'Nz4ufEZac3ABYz06Ng8XDiI7ZRIUMgsrBwI2IHMwNzscYzUaJzIMewh8KgEWO2Z4ZVFNHAUoYB5uHj0xC2EDHBwgECMsGCUUHBUw', 'DmoaPnZKBQItOy85BHAmYig6bSYiBypjHSwKcgsfDQgpFRo5LW4QHz48KxAhH2sJB2hzbA8CPT40EhR4HSxiNwcfLx87PD0YG3IR', 'CWQ/KHRubQg3MHhyfAobKWkCJjZ1KS4ufCUYOCohPlYIXFAyZRQxZwQHJz0aBSRqAxULdiV/bhMPChwPYCE8Lm45Y3kJBTt/L2gp', 'fmoGF2IWPCVzHhEOF2YjbiIqMG0xIBUWBSNkCWkZPDgjEjI7eHYvBwgzbXcHRUBmLDoDAgAaJwF6LR4VFXQgaR1nICggJDsQNmQ3', 'YnQpFHFsBSYCOQ5xDSh9I3s7MH1sKQo6LjYhJQJ9HHAxFy8va3VoTBEPeyIhMGsmKHpiPX0tMQoTCzJudggnGyYSZzsmCQkYEz55', 'X2sbZ2B6MjUkaTAuFTwccCt1O2gtETcgDzYrEwAfd2wnCWRxOXs1PXAjCnt3RwExbX8gGxNpGjM0BTMRNjg0PzkKFXYECSYYGH0g', 'dh8nMQYEP25vDwkPM24KLj4heCVqGjYGNjoFCXwBC3B9CgRpNBUQeTAUPjYPGR0DPyZtIR0fDA99Fz0tezx3HSMsFzkmIG9zKzY+', 'KGQ6C2R0JhcDfhg5fgI3aVtKY2dwIg0RNDgJagkUZTEHfhgQADgGEQcPfBY8ABl4J3MnbDArdxx8EA0eAlJFGwsjJB8KKyweKWgd', 'HTMgbSJqFG4AIRc9OxckPCYqez4CNit7MXAaOGkSCiwOZQRmfC4neBkkOHAADTQZIxJ5ag1yMhJ1LA5wOSk+OTg/LglmIwRlESV0', 'bgkqAiQ1czJlaWE6OT4JCA0LHwQQKWohD314AGwufmc2NCI3HWoQfjYVLgZpHTk4AiUSHwhoPBwXJzcBAjopJ2YKMGobdwgHQRgF', 'ACU9CAUvCBcDW1BCOCV5PiAHCBkiMS0FHwlqNRQuJg46PgECGCU0NWcjbGwZKj82ByIIYg8=', 'MBV2aRxxd3NxYy4cHQYtHzQBYjd/MT00I2ZABwNTNTMoM2QyEwoCCwMgHHg3HAsGCTVoEgB9BzxnHT0POiY/KRkqGnE8MksDZQ1p', 'UmkaKiFlbj0hKjpmBHwWDh5gMCoMcgEgEWw/a3kYai0dPXd4JzktMSh/aktRDh8oEBgtah4jFXoLfR4pdRoJLzsJInYdOz0+YHsG', 'FwslGBc6LgkEJwQdHwJyCjE+PWU+Hy9qEwN8cRtmJxY6MSwLHBMtMQELBAYMA2xjInM+fWcKYjc7Zj0bPigDBhchcjRqXkpUfm5i', 'HXZ7EGMwPwh1bhYKGXASNAptA2sbZzUUOS0LYT1+ajEpSltUWGcMBC5mBRgaFCA6CxFjNyhvDAd5NRI0eT0jGhYSKzkWexgqKAd9', 'G2Y3Ew0HFRwZe3cbCwpKAgU4AR8FFnU2HQEuJjVuZwUINmoyCWgaCWAWHz99Fgg3YmYFDxc8BztufVpGAWlmLhIgNG0XLxIefXQN', 'fisXOjEObQc9BXEGaj45Jwo3excLAgwDFz4oEz8bVwl4WylnBwRqagc+IxAfJA56DC43dA8JIgk9FydlAighPzRlKip9NSEmKBdk', 'KQsgNTkeNXQ6NiMmaTVgYgkpDi4hBhE8NgpwZ3t7eA0QGX8xdjgCFhIQGnw8NxJtaXslFQVxNzIxCzgODHAEPDsqPmA8ExF3X1dZ', 'LCo7GS1sFSYoYWMrNDI5LycycQ8SKhg8Mjk2AnIOIAUIJwcKCGosRAlqURhmAGVkODQrdxQ6BnY7A3ESLw4kIXgwNAZjNAEYAioF', 'MjsOBiF3NTEjE24qCAE6KAwQBzY+aSgVDmxjUkF9NzJ9bDc0Dhx0BSYdCjkLLQ4mBnYiPycnDGM4KjINDgFxf3soAjQ8EmMEUEwZ'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
