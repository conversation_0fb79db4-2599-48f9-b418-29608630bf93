#!/usr/bin/env python3
"""
POS System Main Launcher
This file launches the main POS application
"""

import sys
import os
import traceback

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Hardware lock enforcement - MUST be first
try:
    from hardware_lock import enforce_hardware_lock
    if not enforce_hardware_lock():
        sys.exit(1)
except ImportError:
    print("SECURITY ERROR: Security module missing. System cannot start.")
    sys.exit(1)
except Exception:
    print("SECURITY ERROR: Security check failed. System cannot start.")
    sys.exit(1)

def create_desktop_shortcut_if_needed():
    """Create desktop shortcut if none exists"""
    try:
        # Only try on Windows
        if os.name != 'nt':
            return

        from pathlib import Path

        # Check if shortcut already exists
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "OneDrive" / "Desktop"
            if not desktop.exists():
                return

        shortcut_path = desktop / "Le Comptoir.lnk"
        batch_path = desktop / "Le Comptoir.bat"

        # If shortcut already exists, skip
        if shortcut_path.exists() or batch_path.exists():
            return

        # Import and run shortcut creation
        from create_desktop_shortcut import create_desktop_shortcut
        create_desktop_shortcut()

    except Exception as e:
        # Don't fail the app if shortcut creation fails
        print(f"Note: Could not create desktop shortcut: {e}")

def main():
    """Main entry point for the POS System"""
    try:
        print("=" * 50)
        print("    POS SYSTEM STARTING")
        print("=" * 50)

        # Create desktop shortcut if needed (non-blocking)
        create_desktop_shortcut_if_needed()

        # Import and start the POS application
        from pos_app import POSApplication

        app = POSApplication()
        app.run()

    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please ensure all required modules are available.")
        traceback.print_exc()
    except Exception as e:
        print(f"Error starting POS System: {e}")
        traceback.print_exc()
    finally:
        print("\nPOS System shutdown complete.")

if __name__ == "__main__":
    main()
