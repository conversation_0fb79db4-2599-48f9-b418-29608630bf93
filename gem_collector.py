import pygame
import random
import math
import sys
import os

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = 20
        self.speed = 5
        self.color = BLUE
        self.trail = []
        self.max_trail = 10
        
    def update(self):
        keys = pygame.key.get_pressed()
        old_x, old_y = self.x, self.y
        
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.x -= self.speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.x += self.speed
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.y -= self.speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.y += self.speed
            
        # Keep player on screen
        self.x = max(self.size, min(SCREEN_WIDTH - self.size, self.x))
        self.y = max(self.size, min(SCREEN_HEIGHT - self.size, self.y))
        
        # Add to trail if moved
        if old_x != self.x or old_y != self.y:
            self.trail.append((old_x, old_y))
            if len(self.trail) > self.max_trail:
                self.trail.pop(0)
    
    def draw(self, screen):
        # Draw trail
        for i, (tx, ty) in enumerate(self.trail):
            alpha = int(255 * (i + 1) / len(self.trail) * 0.3)
            trail_color = (*self.color, alpha)
            pygame.draw.circle(screen, self.color, (int(tx), int(ty)), 
                             int(self.size * 0.5 * (i + 1) / len(self.trail)))
        
        # Draw player
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, WHITE, (int(self.x), int(self.y)), self.size, 2)

class Gem:
    def __init__(self, x, y, gem_type="normal"):
        self.x = x
        self.y = y
        self.size = 8
        self.type = gem_type
        self.pulse = 0
        self.collected = False
        
        if gem_type == "normal":
            self.color = YELLOW
            self.points = 10
        elif gem_type == "rare":
            self.color = PURPLE
            self.points = 50
            self.size = 12
        elif gem_type == "epic":
            self.color = CYAN
            self.points = 100
            self.size = 15
    
    def update(self):
        self.pulse += 0.2
        
    def draw(self, screen):
        pulse_size = self.size + math.sin(self.pulse) * 2
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), int(pulse_size))
        pygame.draw.circle(screen, WHITE, (int(self.x), int(self.y)), int(pulse_size), 1)

class Enemy:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = 15
        self.speed = 2
        self.color = RED
        self.direction = random.uniform(0, 2 * math.pi)
        self.change_direction_timer = 0
        
    def update(self, player):
        self.change_direction_timer += 1
        
        # Change direction occasionally or follow player
        if self.change_direction_timer > 120 or random.random() < 0.01:
            if random.random() < 0.3:  # 30% chance to follow player
                dx = player.x - self.x
                dy = player.y - self.y
                self.direction = math.atan2(dy, dx)
            else:
                self.direction = random.uniform(0, 2 * math.pi)
            self.change_direction_timer = 0
        
        # Move
        self.x += math.cos(self.direction) * self.speed
        self.y += math.sin(self.direction) * self.speed
        
        # Bounce off walls
        if self.x <= self.size or self.x >= SCREEN_WIDTH - self.size:
            self.direction = math.pi - self.direction
        if self.y <= self.size or self.y >= SCREEN_HEIGHT - self.size:
            self.direction = -self.direction
            
        # Keep on screen
        self.x = max(self.size, min(SCREEN_WIDTH - self.size, self.x))
        self.y = max(self.size, min(SCREEN_HEIGHT - self.size, self.y))
    
    def draw(self, screen):
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, WHITE, (int(self.x), int(self.y)), self.size, 2)

class PowerUp:
    def __init__(self, x, y, power_type):
        self.x = x
        self.y = y
        self.size = 12
        self.type = power_type
        self.duration = 300  # 5 seconds at 60 FPS
        self.pulse = 0
        
        if power_type == "speed":
            self.color = GREEN
        elif power_type == "shield":
            self.color = CYAN
        elif power_type == "magnet":
            self.color = ORANGE
    
    def update(self):
        self.pulse += 0.3
        
    def draw(self, screen):
        pulse_size = self.size + math.sin(self.pulse) * 3
        pygame.draw.rect(screen, self.color, 
                        (self.x - pulse_size//2, self.y - pulse_size//2, 
                         pulse_size, pulse_size))
        pygame.draw.rect(screen, WHITE, 
                        (self.x - pulse_size//2, self.y - pulse_size//2, 
                         pulse_size, pulse_size), 2)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Gem Collector - Addictive Fun!")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        self.reset_game()
        
    def reset_game(self):
        self.player = Player(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.gems = []
        self.enemies = []
        self.power_ups = []
        self.score = 0
        self.level = 1
        self.gems_collected = 0
        self.gems_needed = 10
        self.game_over = False
        self.paused = False
        
        # Player power-ups
        self.speed_boost = 0
        self.shield_active = 0
        self.magnet_active = 0
        
        self.spawn_gems()
        self.spawn_enemies()
        
    def spawn_gems(self):
        for _ in range(15 + self.level * 2):
            x = random.randint(20, SCREEN_WIDTH - 20)
            y = random.randint(20, SCREEN_HEIGHT - 20)
            
            # Determine gem type based on level
            rand = random.random()
            if rand < 0.7:
                gem_type = "normal"
            elif rand < 0.9:
                gem_type = "rare"
            else:
                gem_type = "epic"
                
            self.gems.append(Gem(x, y, gem_type))
    
    def spawn_enemies(self):
        enemy_count = min(3 + self.level, 10)
        for _ in range(enemy_count):
            x = random.randint(50, SCREEN_WIDTH - 50)
            y = random.randint(50, SCREEN_HEIGHT - 50)
            self.enemies.append(Enemy(x, y))
    
    def spawn_power_up(self):
        if random.random() < 0.3:  # 30% chance
            x = random.randint(30, SCREEN_WIDTH - 30)
            y = random.randint(30, SCREEN_HEIGHT - 30)
            power_type = random.choice(["speed", "shield", "magnet"])
            self.power_ups.append(PowerUp(x, y, power_type))

    def check_collisions(self):
        # Check gem collection
        for gem in self.gems[:]:
            distance = math.sqrt((self.player.x - gem.x)**2 + (self.player.y - gem.y)**2)
            collect_distance = self.player.size + gem.size

            # Magnet effect
            if self.magnet_active > 0:
                collect_distance *= 2
                if distance < collect_distance * 1.5:
                    # Pull gem towards player
                    dx = self.player.x - gem.x
                    dy = self.player.y - gem.y
                    gem.x += dx * 0.1
                    gem.y += dy * 0.1

            if distance < collect_distance:
                self.score += gem.points
                self.gems_collected += 1
                self.gems.remove(gem)

                # Chance to spawn power-up
                if random.random() < 0.1:
                    self.spawn_power_up()

        # Check power-up collection
        for power_up in self.power_ups[:]:
            distance = math.sqrt((self.player.x - power_up.x)**2 + (self.player.y - power_up.y)**2)
            if distance < self.player.size + power_up.size:
                if power_up.type == "speed":
                    self.speed_boost = 300
                    self.player.speed = 8
                elif power_up.type == "shield":
                    self.shield_active = 300
                elif power_up.type == "magnet":
                    self.magnet_active = 300

                self.power_ups.remove(power_up)

        # Check enemy collision
        if self.shield_active <= 0:
            for enemy in self.enemies:
                distance = math.sqrt((self.player.x - enemy.x)**2 + (self.player.y - enemy.y)**2)
                if distance < self.player.size + enemy.size:
                    self.game_over = True

    def update(self):
        if self.game_over or self.paused:
            return

        # Update player
        self.player.update()

        # Update power-up timers
        if self.speed_boost > 0:
            self.speed_boost -= 1
            if self.speed_boost == 0:
                self.player.speed = 5

        if self.shield_active > 0:
            self.shield_active -= 1

        if self.magnet_active > 0:
            self.magnet_active -= 1

        # Update gems
        for gem in self.gems:
            gem.update()

        # Update enemies
        for enemy in self.enemies:
            enemy.update(self.player)

        # Update power-ups
        for power_up in self.power_ups:
            power_up.update()

        # Check collisions
        self.check_collisions()

        # Check level completion
        if self.gems_collected >= self.gems_needed:
            self.level += 1
            self.gems_collected = 0
            self.gems_needed += 5
            self.spawn_gems()
            self.spawn_enemies()

            # Bonus points for completing level
            self.score += self.level * 100

    def draw(self):
        self.screen.fill(BLACK)

        # Draw gems
        for gem in self.gems:
            gem.draw(self.screen)

        # Draw power-ups
        for power_up in self.power_ups:
            power_up.draw(self.screen)

        # Draw enemies
        for enemy in self.enemies:
            enemy.draw(self.screen)

        # Draw player with shield effect
        if self.shield_active > 0:
            shield_size = self.player.size + 10 + math.sin(pygame.time.get_ticks() * 0.01) * 3
            pygame.draw.circle(self.screen, CYAN, (int(self.player.x), int(self.player.y)),
                             int(shield_size), 3)

        self.player.draw(self.screen)

        # Draw UI
        score_text = self.font.render(f"Score: {self.score}", True, WHITE)
        level_text = self.font.render(f"Level: {self.level}", True, WHITE)
        gems_text = self.small_font.render(f"Gems: {self.gems_collected}/{self.gems_needed}", True, WHITE)

        self.screen.blit(score_text, (10, 10))
        self.screen.blit(level_text, (10, 50))
        self.screen.blit(gems_text, (10, 90))

        # Draw power-up indicators
        y_offset = 120
        if self.speed_boost > 0:
            speed_text = self.small_font.render(f"SPEED: {self.speed_boost//60 + 1}s", True, GREEN)
            self.screen.blit(speed_text, (10, y_offset))
            y_offset += 25

        if self.shield_active > 0:
            shield_text = self.small_font.render(f"SHIELD: {self.shield_active//60 + 1}s", True, CYAN)
            self.screen.blit(shield_text, (10, y_offset))
            y_offset += 25

        if self.magnet_active > 0:
            magnet_text = self.small_font.render(f"MAGNET: {self.magnet_active//60 + 1}s", True, ORANGE)
            self.screen.blit(magnet_text, (10, y_offset))

        # Draw instructions
        if self.level == 1 and self.gems_collected < 3:
            instructions = [
                "Use WASD or Arrow Keys to move",
                "Collect gems to score points",
                "Avoid red enemies!",
                "Collect power-ups for abilities"
            ]
            for i, instruction in enumerate(instructions):
                text = self.small_font.render(instruction, True, WHITE)
                self.screen.blit(text, (SCREEN_WIDTH - 250, 10 + i * 25))

        # Game over screen
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill(BLACK)
            self.screen.blit(overlay, (0, 0))

            game_over_text = self.font.render("GAME OVER!", True, RED)
            final_score_text = self.font.render(f"Final Score: {self.score}", True, WHITE)
            restart_text = self.small_font.render("Press R to restart or ESC to quit", True, WHITE)

            self.screen.blit(game_over_text, (SCREEN_WIDTH//2 - 100, SCREEN_HEIGHT//2 - 50))
            self.screen.blit(final_score_text, (SCREEN_WIDTH//2 - 120, SCREEN_HEIGHT//2))
            self.screen.blit(restart_text, (SCREEN_WIDTH//2 - 150, SCREEN_HEIGHT//2 + 50))

        pygame.display.flip()

    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_r and self.game_over:
                        self.reset_game()
                    elif event.key == pygame.K_SPACE:
                        self.paused = not self.paused

            self.update()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
