#!/usr/bin/env python3
"""
POS System Dependency Installer
Automatically installs all required Python libraries for the POS system
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("⚠️  POS System requires Python 3.8 or higher")
        print("📥 Please download and install Python 3.8+ from https://python.org")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_pip():
    """Check if pip is available and upgrade it"""
    print("\n📦 Checking pip package manager...")
    
    try:
        import pip
        print("✅ pip is available")
    except ImportError:
        print("❌ pip not found")
        print("📥 Please install pip: https://pip.pypa.io/en/stable/installation/")
        return False
    
    # Upgrade pip to latest version
    print("🔄 Upgrading pip to latest version...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip upgraded successfully")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Warning: Could not upgrade pip: {e}")
        print("   Continuing with current pip version...")
    
    return True

def install_package(package_name, description=""):
    """Install a single package with error handling"""
    print(f"\n📥 Installing {package_name}...")
    if description:
        print(f"   Purpose: {description}")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            package_name, "--upgrade"
        ])
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}")
        print(f"   Error: {e}")
        return False

def install_requirements():
    """Install all required packages for the POS system"""
    print("\n" + "=" * 60)
    print("🚀 INSTALLING POS SYSTEM DEPENDENCIES")
    print("=" * 60)
    
    # Define required packages with descriptions
    packages = [
        {
            "name": "Pillow>=9.0.0",
            "description": "Image processing library for product images and logos"
        }
    ]
    
    # Add Windows-specific packages
    if platform.system() == "Windows":
        packages.append({
            "name": "pywin32>=300",
            "description": "Windows API access for printing and desktop shortcuts"
        })
        print("🪟 Windows detected - including Windows-specific libraries")
    else:
        print("🐧 Non-Windows system detected - skipping Windows-specific libraries")
    
    # Install each package
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package["name"], package["description"]):
            success_count += 1
        else:
            failed_packages.append(package["name"])
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 60)
    
    print(f"✅ Successfully installed: {success_count}/{len(packages)} packages")
    
    if failed_packages:
        print(f"❌ Failed to install: {len(failed_packages)} packages")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n⚠️  Some packages failed to install. The POS system may not work correctly.")
        print("   Please try installing them manually:")
        for package in failed_packages:
            print(f"   pip install {package}")
        return False
    else:
        print("🎉 All dependencies installed successfully!")
        return True

def verify_installation():
    """Verify that all required packages can be imported"""
    print("\n🔍 Verifying installation...")
    
    # Test imports
    tests = [
        ("tkinter", "GUI framework (should be included with Python)"),
        ("sqlite3", "Database support (should be included with Python)"),
        ("PIL", "Pillow image processing library"),
    ]
    
    # Add Windows-specific tests
    if platform.system() == "Windows":
        tests.extend([
            ("win32print", "Windows printing support"),
            ("win32ui", "Windows UI support"),
            ("win32com.client", "Windows COM support"),
        ])
    
    failed_imports = []
    
    for module, description in tests:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - {description}")
            print(f"   Import error: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n⚠️  {len(failed_imports)} modules failed to import:")
        for module in failed_imports:
            print(f"   - {module}")
        print("\nThe POS system may not work correctly.")
        return False
    else:
        print("\n✅ All required modules can be imported successfully!")
        return True

def create_requirements_file():
    """Create or update requirements.txt file"""
    print("\n📝 Creating/updating requirements.txt...")
    
    requirements_content = """# POS System Requirements
# Install with: pip install -r requirements.txt
# Or run: python install.py

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (automatically installed on Windows only)
pywin32>=300; sys_platform == "win32"

# Note: The following are included with Python:
# - tkinter (GUI framework)
# - sqlite3 (database support)
# - datetime, os, sys, platform, hashlib, getpass (standard library)
"""
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        print("✅ requirements.txt created/updated")
    except Exception as e:
        print(f"⚠️  Could not create requirements.txt: {e}")

def main():
    """Main installation process"""
    print("🏪 POS SYSTEM DEPENDENCY INSTALLER")
    print("=" * 50)
    print("This script will install all required Python libraries")
    print("for the POS System to run properly.\n")
    
    # Step 1: Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Check and upgrade pip
    if not check_pip():
        sys.exit(1)
    
    # Step 3: Install requirements
    if not install_requirements():
        print("\n❌ Installation completed with errors.")
        print("   Please resolve the issues above and try again.")
        sys.exit(1)
    
    # Step 4: Verify installation
    if not verify_installation():
        print("\n⚠️  Installation verification failed.")
        print("   Some modules may not be working correctly.")
        sys.exit(1)
    
    # Step 5: Create requirements file
    create_requirements_file()
    
    # Success message
    print("\n" + "🎉" * 20)
    print("✅ INSTALLATION COMPLETED SUCCESSFULLY!")
    print("🎉" * 20)
    print("\n📋 Next steps:")
    print("   1. Run the POS system: python main.py")
    print("   2. Or use the launcher: python start_pos.py")
    print("   3. Create desktop shortcut: python create_desktop_shortcut.py")
    print("\n💡 If you encounter any issues:")
    print("   - Check that all dependencies are installed: python install.py")
    print("   - Verify Python version is 3.8+")
    print("   - Contact your system administrator")
    
    print("\n🏪 Ready to run your POS System!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Installation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during installation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
