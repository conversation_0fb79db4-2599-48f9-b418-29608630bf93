#!/usr/bin/env python3
"""
Test the modern login screen implementation
"""

import sys
import os
from pathlib import Path

def test_modern_background():
    """Test that login screen uses modern dark background"""
    
    print("Testing Modern Background")
    print("=" * 26)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_modern = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern dark background
            if "bg='#1e293b'" in content and "modern gradient background" in content:
                print(f"   ✅ Modern dark background implemented")
            else:
                print(f"   ❌ Modern dark background not implemented")
                all_modern = False
            
            # Check for removal of old white background
            if "bg='white'" not in content or content.count("bg='white'") < 3:  # Some may remain in fallbacks
                print(f"   ✅ Old white background mostly removed")
            else:
                print(f"   ⚠️ Some white backgrounds may remain")
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern = False
    
    return all_modern

def test_split_layout():
    """Test that login screen uses modern split layout"""
    
    print("\nTesting Split Layout")
    print("=" * 21)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_split = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for split layout structure
            if "left_frame" in content and "right_frame" in content:
                print(f"   ✅ Split layout structure implemented")
            else:
                print(f"   ❌ Split layout structure not implemented")
                all_split = False
            
            # Check for branding area
            if "brand_frame" in content and "Brand section" in content:
                print(f"   ✅ Branding area implemented")
            else:
                print(f"   ❌ Branding area not implemented")
                all_split = False
            
            # Check for login card
            if "login card" in content.lower() and "glassmorphism" in content:
                print(f"   ✅ Modern login card implemented")
            else:
                print(f"   ❌ Modern login card not implemented")
                all_split = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_split = False
    
    return all_split

def test_modern_typography():
    """Test that login screen uses modern typography"""
    
    print("\nTesting Modern Typography")
    print("=" * 26)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_modern_fonts = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern fonts
            if "Segoe UI" in content:
                print(f"   ✅ Modern Segoe UI font implemented")
            else:
                print(f"   ❌ Modern Segoe UI font not implemented")
                all_modern_fonts = False
            
            # Check for modern welcome text
            if "🔐 Welcome Back" in content:
                print(f"   ✅ Modern welcome text with icon")
            else:
                print(f"   ❌ Modern welcome text not implemented")
                all_modern_fonts = False
            
            # Check for brand title
            if "💼 POS System" in content:
                print(f"   ✅ Modern brand title with icon")
            else:
                print(f"   ❌ Modern brand title not implemented")
                all_modern_fonts = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern_fonts = False
    
    return all_modern_fonts

def test_modern_buttons():
    """Test that buttons use modern styling"""
    
    print("\nTesting Modern Buttons")
    print("=" * 23)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_modern_buttons = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for flat button styling
            if "relief='flat'" in content and "bd=0" in content:
                print(f"   ✅ Modern flat button styling")
            else:
                print(f"   ❌ Modern flat button styling not implemented")
                all_modern_buttons = False
            
            # Check for cursor styling
            if "cursor='hand2'" in content:
                print(f"   ✅ Modern cursor styling")
            else:
                print(f"   ❌ Modern cursor styling not implemented")
                all_modern_buttons = False
            
            # Check for modern user buttons with icons
            if "👤" in content and "modern_style" in content:
                print(f"   ✅ Modern user buttons with icons")
            else:
                print(f"   ❌ Modern user buttons not implemented")
                all_modern_buttons = False
            
            # Check for modern login button
            if "🔐" in content and "#3b82f6" in content:
                print(f"   ✅ Modern login button with icon and color")
            else:
                print(f"   ❌ Modern login button not implemented")
                all_modern_buttons = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern_buttons = False
    
    return all_modern_buttons

def test_modern_colors():
    """Test that modern color scheme is implemented"""
    
    print("\nTesting Modern Colors")
    print("=" * 22)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_modern_colors = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern color palette
            modern_colors = ['#1e293b', '#334155', '#475569', '#60a5fa', '#3b82f6', '#ef4444']
            colors_found = sum(1 for color in modern_colors if color in content)
            
            if colors_found >= 4:
                print(f"   ✅ Modern color palette implemented ({colors_found}/6 colors)")
            else:
                print(f"   ❌ Modern color palette incomplete ({colors_found}/6 colors)")
                all_modern_colors = False
            
            # Check for shadow effects
            if "shadow_frame" in content and "#0f172a" in content:
                print(f"   ✅ Modern shadow effects implemented")
            else:
                print(f"   ❌ Modern shadow effects not implemented")
                all_modern_colors = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern_colors = False
    
    return all_modern_colors

def test_feature_highlights():
    """Test that feature highlights are implemented"""
    
    print("\nTesting Feature Highlights")
    print("=" * 27)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_features = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for feature highlights
            features = ["✨ Fast & Reliable", "🔒 Secure Transactions", "📊 Real-time Analytics", "🌐 Multi-language Support"]
            features_found = sum(1 for feature in features if feature in content)
            
            if features_found >= 3:
                print(f"   ✅ Feature highlights implemented ({features_found}/4 features)")
            else:
                print(f"   ❌ Feature highlights incomplete ({features_found}/4 features)")
                all_features = False
            
            # Check for modern tagline
            if "Modern Point of Sale Solution" in content:
                print(f"   ✅ Modern tagline implemented")
            else:
                print(f"   ❌ Modern tagline not implemented")
                all_features = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_features = False
    
    return all_features

def test_functionality_preserved():
    """Test that all original functionality is preserved"""
    
    print("\nTesting Functionality Preserved")
    print("=" * 32)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_functional = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for preserved functionality
            essential_functions = [
                "create_user_selection",
                "create_password_section", 
                "handle_login",
                "show_password_keyboard",
                "change_language"
            ]
            
            functions_found = sum(1 for func in essential_functions if func in content)
            
            if functions_found == len(essential_functions):
                print(f"   ✅ All essential functions preserved ({functions_found}/{len(essential_functions)})")
            else:
                print(f"   ❌ Some functions missing ({functions_found}/{len(essential_functions)})")
                all_functional = False
            
            # Check for keyboard integration
            if "NumberKeyboard" in content and "show_password_keyboard" in content:
                print(f"   ✅ Number keyboard integration preserved")
            else:
                print(f"   ❌ Number keyboard integration missing")
                all_functional = False
            
            # Check for language support
            if "language_combo" in content and "change_language" in content:
                print(f"   ✅ Language switching preserved")
            else:
                print(f"   ❌ Language switching missing")
                all_functional = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_functional = False
    
    return all_functional

def main():
    """Run all modern login screen tests"""
    
    print("🎨 MODERN LOGIN SCREEN TEST SUITE")
    print("=" * 35)
    
    tests = [
        ("Modern Background", test_modern_background),
        ("Split Layout", test_split_layout),
        ("Modern Typography", test_modern_typography),
        ("Modern Buttons", test_modern_buttons),
        ("Modern Colors", test_modern_colors),
        ("Feature Highlights", test_feature_highlights),
        ("Functionality Preserved", test_functionality_preserved)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 35)
    print("📊 RESULTS")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Modern dark gradient background")
        print("✅ Split layout with branding area")
        print("✅ Modern typography with Segoe UI")
        print("✅ Flat buttons with modern styling")
        print("✅ Modern color palette and shadows")
        print("✅ Feature highlights and branding")
        print("✅ All functionality preserved")
    else:
        print("⚠️ Some tests failed")
        print("❌ Modern login screen may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 Modern login screen successfully implemented!")
        print("🌟 Eye-catching split layout with branding")
        print("🎯 Modern colors, typography, and styling")
        print("⚡ Enhanced user experience with icons")
        print("🔧 All original functionality preserved")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Modern login screen needs attention")
    
    exit(0 if success else 1)
