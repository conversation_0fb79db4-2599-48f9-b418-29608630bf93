#!/usr/bin/env python3
"""
Test that all remaining black text in sales history has been fixed
"""

import sys
import os
import re

def test_sales_history_black_text():
    """Test that no black text remains in sales history"""
    
    print("Testing Sales History - No Black Text Remaining")
    print("=" * 48)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Count white text instances
            white_text_count = content.count("fg='white'")
            print(f"   📊 White text instances: {white_text_count}")
            
            # Look for labels with dark backgrounds but no fg color specified
            problematic_patterns = [
                r"Label\([^)]*bg='#2d2d2d'[^)]*\)(?![^)]*fg=)",
                r"Label\([^)]*bg='#1a1a1a'[^)]*\)(?![^)]*fg=)",
                r"Label\([^)]*bg='#404040'[^)]*\)(?![^)]*fg=)",
            ]
            
            problems_found = []
            for pattern in problematic_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    problems_found.extend(matches)
            
            # Check specific labels that were mentioned as problematic
            specific_checks = [
                ("Today starts at", "Today starts at.*fg='white'"),
                ("Date Range", "Date Range.*fg='white'"),
                ("From:", "From:.*fg='white'"),
                ("To:", "To:.*fg='white'"),
                ("User:", "User:.*fg='white'"),
                ("Sales History title", "sales_history.*fg='white'"),
                ("Items Sold", "items_sold.*fg='white'"),
                ("Item labels", "item_label.*fg='white'"),
            ]
            
            all_specific_fixed = True
            for check_name, pattern in specific_checks:
                if re.search(pattern, content):
                    print(f"   ✅ {check_name} is white")
                else:
                    print(f"   ❌ {check_name} not white")
                    all_specific_fixed = False
            
            if white_text_count >= 40 and not problems_found and all_specific_fixed:
                print(f"   ✅ All black text fixed!")
            else:
                print(f"   ❌ Black text issues remain")
                if problems_found:
                    print(f"      Found {len(problems_found)} labels without fg color")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_all_files_black_text():
    """Test all files for remaining black text issues"""
    
    print("\nTesting All Files - No Black Text on Dark Backgrounds")
    print("=" * 54)
    
    files_to_check = [
        "user_management.py",
        "product_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "number_keyboard.py"
    ]
    
    all_clean = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Count white text
            white_count = content.count("fg='white'")
            
            # Look for problematic patterns
            problems = []
            patterns = [
                r"Label\([^)]*bg='#2d2d2d'[^)]*\)(?![^)]*fg=)",
                r"Label\([^)]*bg='#1a1a1a'[^)]*\)(?![^)]*fg=)",
                r"Label\([^)]*bg='#404040'[^)]*\)(?![^)]*fg=)",
                r"LabelFrame\([^)]*bg='#2d2d2d'[^)]*\)(?![^)]*fg=)",
                r"Checkbutton\([^)]*bg='#2d2d2d'[^)]*\)(?![^)]*fg=)",
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                problems.extend(matches)
            
            if white_count > 0 and not problems:
                print(f"   ✅ Clean ({white_count} white text instances)")
            else:
                print(f"   ❌ Issues found ({len(problems)} problematic elements)")
                all_clean = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_clean = False
    
    return all_clean

def main():
    """Test all remaining black text issues"""
    
    print("🔍 REMAINING BLACK TEXT TEST SUITE")
    print("=" * 36)
    
    tests = [
        ("Sales History Black Text", test_sales_history_black_text),
        ("All Files Black Text", test_all_files_black_text)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 36)
    print("📊 RESULTS")
    print("=" * 36)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL BLACK TEXT ISSUES RESOLVED!")
        print("✅ Sales history completely fixed")
        print("✅ All files have white text on dark backgrounds")
        print("✅ No remaining black text issues")
    else:
        print("⚠️ Some black text issues remain")
        print("❌ Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔍 All remaining black text completely fixed!")
        print("⚪ Perfect text visibility throughout")
        print("📊 Sales history labels all white")
        print("🔧 No black text on dark backgrounds anywhere")
    else:
        print("\n❌ Some black text issues still need attention")
    
    exit(0 if success else 1)
