# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['dgMmHUVLUjQsHwcaDRMmHXYfJxY+IGoICnomLBEjYiofCysRAzkje3onBwpybil1UWZnC2A5IwUkHis/MREUahUZNRoQEBQ2czU9', 'dDg0cRQwcQElDSQmJnB5Oxlnf20vJW1xHgQ0eG4MDGh8f1luLClkMHUaKjY3ZQh8GA1vNTAPGQQ+dARoOQI6E24KGjA4ADJ+dCw6', 'BAA4Bg9xIwthG3Babhp/Eic7b2AALABlbggwG2klZ3klMXFjPmZnLR5ucgceKjc6EiQ7ZkcIWUA2GSsMBQIuZ3R6Eh8CKQ04aTp+', 'IWozZwAgLz4cfBYsEit+EAszFTgeCGZ8LAETBhEtfTAmICw3A2AKbWggfhI9OC18MmxgHCQmCBdhPgEkKSZwGTwaMHwuCgU6J3w/', 'YBp+YWVWdBEBMjZ1MWU9KX42MCgpbQoyBBt2IzcUbn8lJyZ2cmENDWUIOQE8PH0IXHBvYi4CeSBuZxMaYX0BLSMUIxwdABM8AR0l', 'ahQubD47IgJ9YWk4ahY5cwlnLjUMFC8GPDx4BQF/EiACZ2J5a2duOiZgPSQMHHYKMXcKfjx9E2MEBhYoEgwQCQYcIioRbHdDfmRm', 'NioUFGgRAzkHKQZqHjolH39yIS4ALjYMDnI0L1UDBEYOIhxhJgUHHnFwM2UoOiAOKm0RLnofCioMMjcVDS51Oh0LLzwFEGxnBGl1', 'd38nKSEPJnUELgdqOWU0eiUUEBkQAwpmLhISKHsHBlJwbA0SICYwJAMLAQoHYwozDRwuNy8pHwcxZAI8HHMTADEiHiYhcGkWBx8A', 'ATcgIQobczl6HB8NBzBkNzMpYQ4HHzMxBAMzBh9Uc2cYO20kDCcGMnYNBgkvNjc8KREXOQUdBm0IMz8YFCcnOT0UNA8vHmp5H1Eb', 'LnYFZnIZIg4FMDEcfHUCBiQVeRlqKm9/EgY9dyQlKj4AMg11exF6fWlgKS8IaAJUQAsGOjIKDBU/J3Ehaig2ExQyOz8ZaAATfTI8', 'AhIxMwR4IAsncycRJ1dmXG1menVtAngKECMNZhp2Fg8QCQorJGgmKhMzJwYVHDMCeyMXeTE4EWsSQGpQDRllPBszGCo9NiE8Oh8Y', 'J3sFaxE8FRQbF30XFyExPGYCJzZ/cgoxKANlHXMoISpgRQFxDCAWJWMPBWscEH4dcAoMOC5reCtoGHRgPgRjHDgdbB5iBnk+LhUS', 'DiEoJgUUEScDNXUfZhYnEwcgEGwDCEBdKSQFZgVwLDBqFT00MzwDMwB0PjI2cgc6aAo6Fy4/NCR5I3weeW4JakdIV0AGMAlsIDMU', 'FWUVByRmAS9wChUpJhEEf3Axbyo4DQ4TewEzFxszOAZrKHl3AWUVLDx7AQ4MZiwUACU8DwgZDhUcdnsbDXlpI2F9BnYkZB4VNA08', 'H24DMUIHAkIeBSQ+fTZwOicTLBAjCng1K2p7Fwk7MhcPGBcNZgkNJSs7FB4/DBhwanxUZCwbGTgKN2ljAgcjKmoddh8IPCl1KCl9', 'ChUDVkZcEQYqDSQpCCsVdg19Dzx0HQtwey1sIw5kHDIbJH8OFGwadiYnfhQvN0NnZAQNZBkGNiotHgdsBSg0PS0fD2gwADUEPWI6', 'NmAhcRobfH9nJXVoGkV8ClE8Lx4gP3kaEQgOD2UuKXdvdj16cRklNxgGZztgYSg5PBk5GRdyAXIGW0VqWjkPIgJ5DBALKSViBRIu', 'KAAqfxkufg4nbxc+JhwHOR0qFQNrAyxuOQtxaBs1OmNgZjITPj1yPBACInhjYXAyDiByPyQbNTE1IDQoEwwABxs7AGEwXkMAA2sF', 'DW4jEmcwLid3GHA5YAhhdzU4HR8jFS8/Lxp9H2VuHi87ZiUQCmZ9MWx5ETgSbhISK3UQGycYawMwewwADRgTKRQuKAk2FlgGeX41', 'YEdtMQ11MSYCOzc3ByExDTQVKGBtODF7JB9qDmV5ImYINWJ5ZHtufiQrZmQJZUYcIwUEfRQbYm4vAGYhBisCDRgaLBp6AzULMjEl', 'DjN/dgcRJyYqGFx0aEQbN2MjOTYmZTMgARApOjMXED0AGWwzc2s6CCUKNnwqfzMgLQY5CyMmGQd5ARFsPhsnGRd8NnsCNG4ocwA7', 'JT9zKDV7MDYSahozeCE2cDJ8LnRkQXxgLTkvIhoKPCILBAd5CXgmKio9ESBxYBQ1CBcaKWwcNWVwHRYnOQQRMFcJfkYSLSURCHQU', 'FDU0ahwYCjRiDAtlKR8fbhdmUn5ONmImPSJyMTkgCyZjPXkubz0HIWoMPg8iaQcFFxM3IToBGTo4LjEBNwVZYgMFJhgtBTJsEQRy', 'KDgrGC18HyoANCQUNxFgOz4QdSgNCx1gAiMdCGgxHmAvCRsCYBFFRHRhF2w+YCgSBzgvN3owFBkVO28pHSR1JjYedDRlZBknGzg6', 'FGI8ADklfHYWHm4eMCA0PBhzLiYSJyoqYR5jCC17ExoaCS01IxlrX0tzFSAbACsNBR0mCHoDHR4pbTw3LHIOfi1qaBg2JQQBemM7', 'cRswejIAGTM9CjMYNn0NahQubgwOFn5xBRkmNhYsfGwtfQc6OC0qFjxKSENjZwM9YigKEzAWcGwIKRQpAgEuAC0ucjMTJTkKNRl1', 'KzZnLxcUCAR4AgcvJycyOH5nHTEDeHoDCBt8GCZFYFhNERobGn02NTYCFWcmNzYMLCMTBggwIQ8nOTV9KSIjcyJiCwUpMhI9GgEB', 'HycreDk/bhohAWo+OWhgGyFyACwMISYSIHg6fWgTexgABi0MLBgBSXRmKw0qZT4TZgMUczIYL3kqICo6KwxzKSY0DToweTgibGZ/', 'BnQoIDw6e2lHBw0BPA1lGRwwciQcfWo7KTM9ETEVJQAKYxsWAS4nFQd7Lg17NS0dcjMKV3EfFxwqFRQWZnwJejI8cRp5HGB0ETcL', 'NxcOERh2OzQadhAmV3lCAWoNNjJjNzsgFhlnM3w7JHEPBgoDJz0LMGseGD4xdwc8CBp3ahw3P29Hd0sMcBk2PhZ2BykUcQ07JAso', 'BXIMaj1vaAJ2AS90AyU5ZXQ8MT4rQVgGRwU5exMFcwsHFTEiAgAiNC1sPC0iBWAQGD1hISwufCc3BwkCcB1wAXRUCEAfBQNjPDAl', 'FR8OLAxjH3wjGi0AKDpDYEpjD2x+H3l5OgBqcg03DzkNHj4tPxMwOxB9EiUzABs3GTkCOR9qJiZpB0RKZ0R0AQQTPDERGwcEOGcn', 'JX13VlA7ZAsxGnI9BQknD2oifW4LPWodcRZgLn0TMjQjByskOR0KFmc8H2kJX2FIXywvIxdjDAg1MwYQBCAILT4NG343CgRuOxFp', 'NmliKiE8NiodJAR8FwUMMD5IcmoGCjMbDDAZJRwUAjEkMC4jIw8nDyUvKCJ9byYLGGQLAjI6KmV0BgMtEWJTBHJnIiA/NgwLNxwI', 'LWllNTozLjsrFQQ+B3xsEXRnBXcnDCguGQ4SPA17FzQifycSH2o9aiAFfWBwaDZkBiALMS0bfikbdwoRUGReHwUzIxoGcRcUJic+', 'eREUIiMiHiUVfBM8HiYwDCUMCzg7GR53MBJ0Eg0+eGcPHz4SC2EdHw8EDzUWKysbCXo8eQsUFzczFDx1AT0PCGoSfSB7djYGfAYb', 'Ew0bESsAdWpqCi82IH1uaz5iG3cQcA4lGGQYGC19DWsGc1NuEX44bDEuLyUnFwUrHDUZMzMFDHgGeB0wMyYBJRIgdTcMeB0tORcx', 'Bg9jP2g4JCoDDmU0H2MALDdyDmYpCy0TDAk5enYdeSgEFg43LSggEXQvEwd7CBxoOycSdDMLYDg3EDwsNSoXPSgvMgc9LhEZX0hH', 'RBsIZRkACCIcGWo0ZRQNfnY/FjFnA3oNcDgbMjcYMXNsDSt6OmYcMhIGBUZKASwYLTEkLWY+dCc4KBQkcD46En07MQ8/OzkpOBoM', 'aj4vXAFfe2xteTYHBggHLCY0G3ZnLzwzOiIXOzl9aywmC3tscDAmMwYLBy81EDYLYEJdJTt6fxYSdB9zDxExIS9xAwkZPhsQARMm', 'dWsoVQNXfz4NAhdlCSobC3szFyk/G2gqEXwgOwonN2hgYx8CfHM3Ih15KQN9PSYGd1QHbxp4DXkUCBILJBM/agEYcQ46LwohciEa', 'aAF9NxVlYH0OJhtmLD8ANR9uOBNmdn4DGmUgI2IlBiZwLRtkBHkjFjEXHnZ3eDckNTMkOXsLJmV6BTs1PCsuJQtkRm0rGX4dfTBv', 'CGYpAgIgZgJgLws7Imo0GX9GXBYeexciFh14bhINZDAKGTl2NiIoNjgtIjY6awNmHzEBCCgYKQJ2OnRGBAFSKTABGWYYCB8BJjd9', 'Zyc0ODgfKiAvEAV+FHchN2ExHDV4BhV3HjAtBG4iMAg0fWRXUw9tLTModzxgNhM2Cw4CKBg7O2MrDwAINjcBNHtmD2gxH393Mgkj', 'bzkLDRl5W1gEPD51OiE3HCEyBxo1cjU1FDMpIyVoOSglEBZ9CGcQFHt/AQQtGRUvBgpHewYGDXgdKi1vODNsHhNwLhcMay8gGTEA', 'AAM/ZgQndmQCEX9xJToZPTQTBWVcX3EILA57fXEQNXcQYBF2B3Q2Pzg+NiYDMQoNGRl4EnJoID82KDs9cDtvc39qXD0hPy0oeRd8', 'EXE/JQYrOzEMBxwHHxYsEQUhA3gAOGQyYDFfG2FTGBkONSA4KxoLNGcENB45FBcoCnQEDQ00ChYfBX9zDxkNCChtBDM+FURFd1E9', 'dRZqYgcmBz12ADwOeW4pcTc7fkpBZm4cOAdhMQ8lHGhlAQgLCW0RdAUUIRkGOhMWAh0eCAF/ADklDxE9ERUZUWUACSAWIX1xCWoN', 'CiodJg9wIyogG2oIbjsSRwQDdScleCMhAHA+P3AGATxnBTQsFCIAGxEkKCsDB3gaFBZgEDkdMywiYTNCBmhFbB4mPx4bKAV3EAMB', 'RwBwE3scGBsPYBYTBWYNeHkJcmkFGSEzFSYOI2UkDD0lPH14J2secylvCxtee2kZPAMIcCVrExN+M3V0dDguNnF1dgoLajMmERce', 'NSAHLysMHCgbHCRzYmZ4FhQzFSkOcGB1AREwKAAzGyoePS4ANBwCMWM3MjkEMQcqAg5nYn8tFgtsP3t9MBx1aTdYd3sFbDwobH0C', 'Ggd4DWw4CBs3dWYdTGpmGi0VbiwbHyYMJCoLJiARD38TJ2R3Hm8aJX17HxtmPAl+ajhzHj4CUnZEMTM2MxwvbTU0ChM6fToTLh0U', 'eWlpByIAMx81Jiw3PzYiETAkBSxlNy4NOCYHflRGMi0CNgsRCgAWGyYLFzYIEj0bHRl2BxUDJTY0K2ERbBx5ORQSETxvE0RSZQMd', 'LiEhIWMpKGA+PzIMYA8HOD45ICseeHdfZXQzHi1lMgUidCZjKBU4IBcbC3wEe310MDIyGzk6IhAyPjkkOWAjAQhzakhsFGUZLTB5', 'KDsfIyclJDJuCxlRc04mMQgmOCcIECpsYRxzHQ0xLG4ReXVyKBQ2HwAuFRU6exAoeGwoN28pAkJbYCk0Pj8cGBBqdAc2ZCwDGTg+', 'Nxh7PQctMDsIG3N2PSVlGx1dEDIrYmEoCTICATE5d2cINWE0CTcsJmoFPmcLJRwxMRY8IAcyfQRrKXxXWXwxYQRlFgUrMTUMbBE9', 'aGQLJQo0ZAQbZn1BChsQEi0YNzIpO240MzkBFCBrNxQ7cSUzNgZqZGd4EhUzLgEuCRwiIxApCkJUWwkZHAZhcRcVMXIfYjd9OAhy', 'YBMsdA1+FChsAyUIeww2bTAANikEAhFqZXV5Umk2FjxlC20XCgBnGBw9JWIROwcbdQh2PT5nGCYnK3cCcS53JXhwIG0KdFMGbjkq', 'Mw81Y3hZcSw4ejkYdTlmJjQYHXB0GCAhNjE3aH4oCDBnaggxCTsdBAJ/NBNxbT5HCAEBNBscGGMtMDhyGjF5NHx1CiswEWoOJS02', 'GAE4ODsVGBcWdXpmCgIpKy5wCSIpAwQaGAc6HxMddR8HLHoQfhw1aAV6GUNoZR5jJwJmIzMUETQreWoTNDwhNgwoNHkOByoKES9o', 'aiYPMAAIcgYRe2YjIXcNMxoIHGl4CjwGfXsdZBgYGWMmCWYUaioRFXcgAD8XHiwRFycGJg97Py0tIhdkCj4HZi82dhJXfHBECRs/', 'IXkVfwoLfxYuBAF6HDUaHBoPW2EBUhAfPwELFXQ3cSk7FC0UNC4aKSMgNCx3ZDgiKyQcKzYaHzcMJzhybT58QAtQCid0PD4TCTIw', 'd1NFYykDGxxjDRR8JnsAHzU2CTlsbB0ncXgLMzECJmQOIhstKxkoKiJuEWhnBAFQNwZnE2sEMgBqc2xjDnkNcQtuADA5EXQYEhl9', 'NDE6OnJwIQ0uBzshNx8sdQd7cnQCNWUbNyphBDs4ZW4aI28eDh0nAgh3MWcIKCYYahImIn4nNRF0dmxdcWpbHQ0rYHkrPBsddmYY', 'OzR3HjJiCCsBMnYTNQRsATorLih0M2YFfiwPamAFaARmBhljZxoXFgxkJAxneRI5cjwtMSsndClxeTwGYwk5LC0hBSE5ZgwmIxNz', 'By0FNRwIMjIyNAUnDRgcKGkYAhohPAQACi14By09bSVXSH5AamIDHT10NSUpdC80CSgrHjYwOyoMMSo+NTUfHWZuIRERO38Scy8x', 'IRMcAT0jASMvZwdFZSZ+fhpnEBlqPSoRKgc6NRgBLS40F2AtfSgfGBsGCDslHX0aCnsndm1DeFUBFT8eexxwJgRuESM9LyYMOG8q', 'MgQnFzF9Pg0VCgYNfQI2Jh8rDB8cDxVqV1haKAF0eygxaWsPOQUUAxlubmgeLnF7eSYZcGQ/LA4BMSIjCnkJDXI3MlEGdXoIIHx7', 'ZDQIIwwHDTEtcXp+MDIRMj1cQEBjFj0pbTU3azAGOTNnMSARNDETBTIXEgliCCMgfW0mLjJ/G2U0KAg3MX1YRG06PzkwMRkpGXIg', 'HjsJBGoqc1lEYR4/JWdgcxkXfSxhJQopDGsJdCQ7cRMteQ0qKyJtPHIkCi17DhsoFDILSVRTJQA+ZjF4HDURDmccDwEbPQ88MHkP', 'KQsgLTkdLTl/NiNtZzV3YiI6Igh+DWsMIW0ed3FVWRsWeBMXcR4kLhQvASx+JSweCBg7dT98MBQEHhokIiYiHCYaOHhxPm17e0ZH', 'DgAzPxJhagZnCgMFLQ4jJHEeBXx7MhR8dDduHQkBbncMPzUuaAACIzN6ZRl2CQkiMzUOflMEUGs/HmM7BGcAKiw7agd6IjkYOwFw', 'BhVsCQ8HGyY8H2YoYisiECQ6DDAOOz4/BxJqB1tlcTY6AThgcw0xLHZjIzd5KSAqKyoUDz4iCggoJwYQCCgjeAs8NGQVFzREBUEM', 'fW5nESM6ODsyfwo4AA4ycQgfBABnaB8fOzU5BgMVNnQHMzsPV2gAUTxmNBUoBRAkIzsjES8YCT4dBR41BRg8BDoRJiMdMw0OeXYj', 'EwQMLxoBPSsZPmAmOwZHYAVsKzgoZRluMGQwAXo+IiZwbTIXfiRzD3wdK2VmBm0mOhxmPAsrMjU6OAVWYgMwL2dsOAM2fHAwMiFu', 'FGcQIAx0eQs6IXYkMwdjDnoPdCIPa2ZKalRYDSAUZDoZZh10dgJkLAIWIHIUYygNenw/dAA/FgA0dhslInprESIVa352RmEmJQtg', 'GSY8LgUtcB5xKn0NJXY7cB19GQwvYnknIS82MWpwHjchHm4paBwOMHEuDWoxCjo6PBV7AyEhBiUKEx48HWRfbBodNmUwLRMVARAD', 'ax90dgpmG2dYGCx6EQAwbAMQOWRlNX8DHDMIHycGKRwaGDQRHCd9GzxmKXtueHURO2RDQAcWDXQXMCYXeHU5NhUMGS8gNQUQCQcd', 'aB1YRh8oGyExFxEuCg5sGyIcBng9PQURAAVyNRoLJgJ4DR06BjE8ezt6FzY5BWp6ZBsjIDY+DxoCaiYxJT0qCnE3Hh4YDx0JBzEp', 'LSkgHXx8Ph0jNzAZCF5OJxoILAozFDZqIDw0HXUUGC9qfwAFeRY8CAo8IxUoOgw5AWERI3wbKV9Wex9qMDg3AzsYJ3QyPgMzGjU0', 'GyM8FW0rBCx9LSQTKh0ILV8GRF4wfmdhZQxnJzB6ZzAhf3c5MxAFDGgyKSgSBwE9HxcXPBkkBTQuagF0WRthdwszJzYgczxqKg4G', 'BHURaH5RVVAlMSobZhdpHx00EQI1BC0qLwUecCw9KzYeaWN2FQd0IzwJCSsdDgw5Blh+BG8ABDEkCm0BdDY3ZiAtcD8jORkwEBww', 'AiB6MBsiDQ81dhgOMRItCmYWJmV3BWp0MSp2fh05anIQBwBjBxAcHQBkNxc+P3QlBTEtGRMaOhAjcxIGeQ0SZxYhIy83cGQHOQQt', 'LwgVBjkXBzwHHjRlZA0NdwJnPgdhCGALCAZUXAFhLSANOjQbKxcADDA9DnQiOQh0BC8wG3MkJwk0OCRuaH8sIS1sIShsCQcAaFol', 'b30ufg==', 'NxcxeTYOETo6EQBlHCMdPjlRBVVMajsDJGsvCWMBOR4Vdx5xGyMqY3gWAnA4LQUlDWAKMjkzPQgrAzUtPHYEBVpnZwR7HnA2FBEk', 'aEhTEyMqG2AWLQcVDhg3dnwZGwotGScPL3Q4cGE2KWYVdAY9I3lpAw0jNlRUABs9Zjo+FiMpOzczIBlqBW4fag55DzV4MB4taBo2', 'cW89fAoCFCB4FzEoJTAvDTh2NC4bPwQYDjdjIyt+aSBuOgcBBXlTNAM0Fmd4BmEhIgMZFRsZHD8WYy4SDSkkGD4hfCQ2egYdGXkW', 'CgYNZnkoFSQnMh5faURNDGIABz53bRsjE2AIJyQzbCovJnQrPSMkazUBLH8TM2EFAh43OhNgan5dfW1wOQsBIXc1ZBQEfiYtPCRi', 'AhQueCYYHGIBRGJwIiInFQ0UIz07NxUyOnlsbQljCQckPyppaQQfESwHG3t/OgU4Ix1tfFJWenBiH2Z9chgVcQsCAiM0NyJsOCx3', 'DQoFIh0pEnknPAJ9LCo6CSEMdwEkdhodKRMzblxmdgdsHRxsHzklJnIUBwcNPA01MTQMDyp4PRlwJ2oOYCI3Ln0iJQYsF3YGBElw', 'cQMPPzkBMzd/CSRtJ30FETtzGHB8ZEobaw8mZCE7aT8wOS8YPykQOTg1BjsUfgQ9JhIheBoXOyYjLHkPHB8vEn5WR1Fnfh9lYzIo', 'HyxrAiUBCn1ma3s1KAMCBCEffxo9fAwJbV4Jf3gyBT8mHHcnIwp2JQdxG3Yqa3A6OCQ7HTc1CmM2ew57ITN3fS4cMwo5dkoHTDpm', 'PDczG3gRMWQeEAYkLwEHHm4oOnMAbR4wKhA9NRklPmEYLilyEFdacQETIAUyYBA+GCMyIz10IC8yCnQDJBsME2AOJwM6BzMBDTwm', 'Lz8DbhclMiUaJQF9N2cCKzkmehoQDi8ZZABpOlF6ZmY1ZXUkKCRuGBd2Zn0NIHMdCQdwdHMzNCsqPH0OIXAvICFgPAwGEzQmY19l', 'c1d4BRktZxwZDGIMLSw1NyMHNz4WYwIxBDArMTchewR2LB4kOws5ZAAsJUZIC3g7PAZ7JBEvMGoRYwFyJgVsMRJjBnMkKzEGIgQb', 'BHQwKXBAAUIKYxViOXMrNHQ2LDF2ehk3ExE+ODcYMAg0NXkuPS8uMnAtCjcmAwEIdwN2ARFmIHsVKD0/czYPHgoJJzQBMCZ4IjwU', 'KgZ/IGg5B2Y4GBd5ex9yPGYNeTgIcBEed358QWssFBIAGyd4InRhKC0IDRE9HgkPaCwIKjclAGQaPTUkJRV2Dj4ybQ9IdlduLgwW', 'Dgs1Zxt2Mg0iIzV2aCMsZzQVMxAeChRifAUKOyIvKisRYAgjLR1adxtoEThhGnJvYyAZbGAzeyAJPAscCXZ7dx1nKGopNRA5YQUf', 'NgUtGi8KJ2APOi8/CDkfbhp9fQMbPWsZIHIiAXAfOBovFRNnAAZjBQ5jHDEINwUgASAGMwl1EjsAZz5qCjlzNAd/BmA8cGw4GWQI', 'fngkHjs3FjRTR0cbKWEPNnkoOwYdB21mDxYSFgANCTM0eigzET0aIzwxFxp5CysqKiMUFhkHYBsFE3k5eTsrHxwtbDonPy0cNgdx', 'HR8aBW8RNA9qOSwUHGk7AgM7cjA/fHt8BzkRFh5Ifhl7MAx+Lih5CSIcKWVgcHUxEGoLAjd1DB0HG2cWCH8PdRozBwA0fxU9G3h6', 'A2UBJGN/ZB0yLS5qFnAAB04PFnQ2GBMwBCYzIgMrHw8DMhAqNSghNxMHFDAMNhwnYQA8dio/Iz8mc0RoUydlPT4wGCp8ARQeOnY8', 'IGthdikbCBwIJTABJSwFAH1qAhAiKnoAIy01CB8bGgQQPmthAVtaaAw6ZSUDLDoVDCEzKg1xcRIxLwoaG3UDZhIHPyYJNzcNFWEr', 'CBIRIgAQGBwyD1dqWAVvOiQ7ZDs6EQA0Mx0VFXMRITs6CzkaEmUdYjwJIixobSABJDU+PGg+fHtnDTYhJTkmN24fHXcbNXEcdhst', 'M3w/NQERPz8jbxsvfBktchImCB8nPBhqciAsC3gLMzEqbARFdh8rOCdgZSAWGQs6ZyMXASwUYTkZIwYZcAApPHk4PjA5MAw7PHQn', 'eXU2eTc0MwkjBCcDM2YFAiwcIhwSHFQBCnonYw5kZDILFQwtbDZ3NDICCBV8cnIkNxonOn05BQM2exo6JmYMdm0nanMZUDgRAhph', 'f1pqTmxgGGF5MAgpcA5iYnYfcmo3CntqDDgOeRpjK2ASdhkDfQQBLCE8PysDZ0dZaR8IZCESbCISeyMoHxssLQsTfBZ2KQszLBhj', 'JjE1PkdUZn0YHT8uBCgpKS8MEytzei8PYDYLCjUucRg7HgctNQwlJi58fwkcPzw4YXR1dQ8XOjsccCkqFScjECI2dAIIGGMDJhww', 'AG4Oemc/AjN1YA0mJA1yMyMRHQY6HBYmES03BSxzLS8pe15rYBI5OBAXbhsefCwzHispFT8BMWNxNxsffRo/Fy02cio4fh15EykA', 'KBQ0aC05C3ANNCcNGyQdEmoLNT0XeDE9HyMYX3hFQnACAGN9KwwBIxMYPwspCgkvbBsMeg8NZCU4JQAmLxoHeDULDj0EbhtqQVZt', 'Cxt5f3AiYyRhMCc5MiEPKCI4Jj42LiMIBHIxJx4feTg1KBYuP3wXFGBuADl4d39+HS8dOhYAGz8kGmc3NAJ0PxVpCnI0DjYiMGAV', 'EglqaTknLBAVKAB8NiAsKQwQaBl0dW0uYwg9A3EHACsREyYgCjs1OjBjcRd6ajAGCjA4HiBoEAAuOCoRDRQ1eEIDfykZeh4wLDor', 'YWV8Yyc3YDwqNHAjCiErBWFRBT0EAhs+LxYeN3ZsJCR/CC8RPCM2BWQSHBIxFn8jNCU3BXdldDMzH2l1U0t/LAN4YxklCWVqNgFl', 'ey8lDzQ3EB8gF2cNGwM4fwxuOBQxdAt9AUAHfg8mHzgYJTQwDDZ2OQ4eYAYFcC97NCc3KiErByASMH49C2osPzsbagABdWcFexNl', 'ATB3BnISCQoeMzkxIx46BiYlABYGIH4HNHAOamomQVtQVhUvOhw2OA8wPWgYYXN+Iw48PTEZMzJqAmg0CnoZdjQ6LQQZLn1qNRx8', 'AmQ4f3VsLnoCBT09DWkqGWNLBQ4EBw4hGTIwbjBhNj82dg4/Dip2Ai0AGAgWagtiEC4QLH4hByI8ERtXAx18NTMqJx8uM2QzIWIQ', 'MRY0EjQCExwZLnAuAxslIDgcBnhQAQs5ICEEahAeDnd+BhIiagsKZioqDBlyPxNiFjsDDxs7Jwp7cC0Wawd5VAFmbB4+IAtyM2EW', 'YC4eBmMHLREBdDh4JiQyJnIgL1hHW2AQOXQ4KhMTfHYBIWYjJXZvbGkLEQQMMDAsNWMnIRwzIhEBNBBkEx8JV3x8UxtjAyAVLGs9', 'TjptOicCcSkGCRszCxU1CA9hDTk0OhpuZTkaZgZtdQk9Yns8LXooPAtldmIHFGJ9PgYXMD0/Di15bmNqEjYvLygwJjAGDwYcLhY9', 'PnsQJiUcMHQiCmolcCMdGjJ0KQ8VChgVJwAOLjM9JWQKaBoOKTsdUmtxOwE/AmsuJQYuExkIahhyCD83DAsSZGoRBSZ9LC1yaCc/', 'HgEEPgN1ZjYfCCY3dWN2Oz0VLHcLLgx9DWA0ADwmbAEeG3g5HXEsO15jXG0bDAdhZipmYDMgLT4RDS8DEW05LDoIICRtBGQ4e3E0', 'HygKGmohaggvVQNfYj5iIWIQGwdrcCk8GnMrAzMOBmMwNgcHZw5hHiRjEhFhDyo8Z3lydil+UxlDJTE7NgIgdGsULzFnCwENFztv', 'dBgzORE/IXF6JAR0KyITKyQADAAMYwczaykbAwszEyo0CywKPxwZf2JTNw00F2EAcB1wFT8KPRURADwqOBExfAIkNxcwDRY9ehAE', 'KGEqMw1uMzwsCSMPLRIAMRk+dwR3BhMIKnslBC5tDFwIXHosHxhgJyA9agA6DzgxKXJsbwkYeGw4bn0yGWEVPjAUNXp5CDk6LBEa', 'Kz8VCAxbeHUCGj96PhwMHj0MAmdgETUAKwswfAU7JRIBKwcCOxJ8BRxwfBclLXFrFgFKfAEKYxkjCjkFZRAJN2MWORs/bDkpbgt4', 'AXk6Iz80BQACAW0vOTgSZzZoJjEPGARwL3MKEwcpeHYtNX0yZj4JYT8IHBA6ewk4C2EnC3hfdTwvKRF5DxIiKSlmPjEbEQ9uJgAl', 'FRg+Mwt1KDIhemEWDzoIKGEHDhgxJ24wHCcqJ20ONCU+PXgdBXwuNldDWAEbOQFtYBFuIyR6ITZ9GRs0diV6My8tKCQSMT53Pz0k', 'Ck0bORVlPglmKSA2AgUNNCZpIxYFMDoDITQVexw9EDEmLB45GBB5PTQaXF9lYwYEYzAkNRYeLjAPYH0YIDUrLy4OJgYRYD0gNAAc', 'YjMBP3sBAB4tNSwQUUZIfi0yAGQwcxF4JAB+Y3AfJhcPJWMHdHl0Gg4DHS4YIwoFYhk6Lg8fHSVGXHhNcGd+GDwNagYhLRMxNiAP', 'M2dwGCA5CWpxKXUNN3lsJjFgZSYXJAI3emtgCHYPVHxGBRUbAjgcE24jcXAkYy0IIxs3HBFwGh8gNDYlAS0ZMBEbZicFORp1FTBF', 'IiYhIXITES87IGExfjRqPDExLRRyFwcZEWU7PiIhZyABfG0BDSNsSmIZemcBNgYaLzticHoNKCI5ICI4MAVzJzkiJWgJG3gzKSdm', 'di9dSB1fHnofGzENa2EHJTgWdwoPHANoKnF1AAc3HSgqPQcdExZ5YCc2KRAzaF9gZmwxLxg2YC4NFW47MiQMHHkjDyoPCjUoEmUp', 'Gxo1OWEzJzYQezRtATQOBVtgXlcnGzoBEzE9FDENegN2BhESPBABcgUdMTk1GCB2Jgx7IgUGdhwoPBRmRUpGZmw/OWE1cBEaHBoG', 'fwQaYgM3FCZ+JxJ0Z0NgfBsHGgJhIhcbcxEjeT8eCD8bKQwLAXI3ASgCOgsnBzYjLTwPO3IgCAxWckpeBxw2JBluFQAkJjkoLXt4', 'MHh8C2dhIQY3GR8zPSkSJAQkDCliIAciehl7LgISejYbBV5VZXdtBQIzJw9wAQ0BYB5uFBF1NBA9LycHKnkPCQg1JAN2fxlkAG4h', 'Ki0Ybh0jDyk4IQQNMCZjG3clHQJtcWB6NGACKzteYlMbGzQuHyQiaBE3LyZjfA8JEW0eLjAyPhQIahEgPiYwGh5wDgQrehxhBWpb', 'JTkiEAhlBj8xMzpmK38cMWAHD20KaVBcFAZ1MQsvMj48JWMKHCoqOwE8MnkVeXR9DD4Kfm0VLwEgCnwNCg0aDQZ2SHJwMmMtPRk4', 'PGESBTsOEnQTaRcoPWc/DS45FwYcYDYXdHkCBWdtNyIEKzQLZXUsZwcJNCw9cmsaOxdyMhcpJh96BCgmMhMVNjN4CmEeegh8TgsP', 'Mn0YGRghIi0bJDAXJwZ7dnJoaggKAjINJGQgOxhrKy1gGgQfJg87LjE7ISgvCGc2JDUhHxsfMyYfaAN3NRFAV1pdGTA9BAZqGwRu', 'FAElHDUzBgAFISNjGn89Czd3C2QQIn0WDycyFmJJVGEyJXQaFwMzZAF7NyU3KAwfNwYDIiF7CgZoPSYCBRV1GwJkCWoIanIqeVZn', 'ej9wKCMAF2RFXQdpMyIWMTgQGgASYystFnI8LQoKcAd7H2QUJWQpPx0vJHsNfGsgMgt0aEUBfzsbdC4KCCVrEWgyAAkqIjQRbH81', 'GAgQECA3d3B6LA0UB2RYAhgjLjIlDywRCSohOjcoMWIqJxklGiVxPxs2IRUxdXofEAF2EgYSPypqCWNfPgwFYD51ZzYsFx4gKB45', 'BEM5MwMWYAp0OwY1HjQhFm4RLzsnbgU9KSUsJxUnMxVzDAMHIjk+Jw4wfgULbTBjKCExOTQicWwaPz8DcA8raBs1Ngk9IQ1mGXgw', 'RkFabCxnOGEAGTYROQYkARwrDW8FGjkPfBQaFRgRJgR3cGx8DTwPPgEtO2sFWA0LDzkxOTQ7fCRoEBV8Fm44DhMtBTkmEj0mGGAs', 'aF5WYEcoMz85JwY2IyYlLBwxAC0KYW09JgohFWoVCmcfYBwFGi8sIw8xNDc7U0FFbCgDJ38YJgV8A3RlEB14DyM0MRonJHMXGzkC', 'fwZjMhg/E2UocBIgew0Qbi8HNSAXeREuKD0AbzgfZB82exMQGjsKcjU8Jwt8YAITGj5hISJoBn0CYzQXfCQ4Egl/bgAhMCAKe2Qd', 'DHV3B0U9AXl7MA4vAi91PmUBBicrDQUAIyAaFWsGJGIOEy56Zw4CGj0aAGwoXAVGBjYzOj9gGToHdjIhYDU9Ag0pGCsMCiETZjky', 'FCAaMGgdJRpyJno/NDkjMycXFQx/ezg9DmACPhpXRlZAExIcNwEnBx0iG2FhMn8uCDxmZzYwYAcaaDE/eDYmcCdmeS1vKTQ1O2JF', 'PjV1D2I3PXYPEWgDBjMiJ2dnJQQBJhFsGnkAJWwmMnIvVwcKUTU0CWMIeCVjfHM4eRx4eRR2bRgOAg4/OSsAOj4cPCUFDD4BOA0M', 'EwAzJwJuai0dNBYDHSkePC0fZwJ0Mw8LZnZeLW00eyN1agcHGzd9IQh3ERQafy16fBIecDgZCBA2JyAqJXccew1hDV9GBkwdMSYT', 'ARUAOSYjPQtfdwpQaxF5MXl0JWEvEQ1qKTsqAjBsCzF0HSonJRE6JGIzMzoBFggZfhdgJ3plRlFtMAAlHCtvZxMPLQoNfwYcdih6', 'BAZ6QywELXt9Miw+Ci96IgcpIy0oBiAmLydqJz4qPAg2PyUnBTU+Dj0mAQ1jXFtBKB0+F2EqG2oqG3o2MgYsGC85LHESHXcGbDgk', 'cx41cxcxKj8YJzkODGMrdSQRJisKajgTAi45C3h8LjgjaRB9ckZYOz88f2p4bRcwDz8ZdQZyFGo8CnMgIj83PAUYZA4uEQ0sHhYG', 'LRk5PQgPIQM3GXw1MjwfOmhgKRQnS3ZHBXAyAjAwd21gLSkcFysULS4ObTBuKGASKDQAIwMuEAoweQ4MZhsyG2kCA0JTEiMPMTQE', 'CmYbbhIBEhtsZWo9ZBMJMi0nKC0pIQo7BlYEbiUEGHsnCitkLnoUJAknd3VpJRwDbBpwBCk9Z3w2AxcQLXgfcH4OAxUZAAp+bRsN', 'CHhmMDMwMT12KzEmOC0CQX9nGBljJjQqNzwGDAU0B3xwOG0aKzBoMnZgJwhiGz8ccmIfJGUsEy4AMmJDYkYeBCt/JTM9FnQzLxZ3', 'NAU0dDEcJipqKBw3fAxxPHA8aDNmGDAoMD0LeSRuAiMREgF2X0cVJjZjAXMtZnVsOhZ9LQgMcig9CTcuITQZCDsKNyZyMS0EFGZ4', 'IAUpJntiEwInDWIvFSwqKRwgHA1/BRt/MSJ7ByojFz4pIxBjZwtCKjkpPDwGFjJqDhxkAgMKO2x0HjYTOzYeKRg9ABkjczEtKBZq', 'f3Y9fikgbWZWAnsSYDY+KiQ2JSAnY2EhIXAtKmZ9N2h8HCopOQIhLRBsbBE2HRgbbjsWSgdac2YvHwB5MBcAMA9hOC10bjgWHRgC', 'MA8wZysaPygVCSQ1NgU/J3UtJyc2eyB+GgAuMAR7NGpzNzdsdwhFBBs2eRgTCTUmKRksOAstIigtaSolOQV2GW8BJAEBAAdsOiI2', 'LQNlFzp5Bg4PMWIdIQYmEStqan1VcwMaJHwAHQIHag4gB30Ue3kWIBwRExMIMQtmPRg2Iwk6BywqZRV4Fi9vQQBCRD48YzAZODki', 'ODMBAzBqJgkNHgUjcHs5aisqMht+bConYgxkNnRkLR10dhtFcWw8CSI3AmwcMTEMBXEcdD8UaDEOGR91YjkkNwQcM3YiPA03MgQi', 'OSp7DxssXF0KQA5ldBkjdwpqLzNmBm58eBghLR4RewMkYW4ENwFhBxkkOAYrPQFqEiZHdgZ1Gz84BSg1NTcdBCEfNX4RaA8pGw0y', 'BwMMexkJPg0eByk/DRs9GX5lJnN1DTQGeGZFFzl5Hx9qHmcVMGV5NCE7K2ocIC4zMQ4cJTUoeAduKBVmLS1pCgxra1hJVk4uMB8l', 'JRIAa3B4MgtkFSgTBTx5EzEuYih3djgycGosHURwQQg8OmQBdRcmAzU0IxQdFyIsHBg0E392KjIEYj1sKxYfBjYGFXkQKyl9anpQ', 'EDcnBzN9MgoNbw5rDglxCCczHTlhKA4gCDYbOh0lCXIzBURAS0UsAzphJzceahcRJSELdRs5PS59MykfCChqYyIhNjYlbSw7ZTsm', 'AWQ0YxwyIwUZcjoVJC0JaAQsERBrSll3PC9jJwQUESkAaC0DbhkCbTt0ADUZeA03FH8AI2QHbAcYKhoPBwIea1wBQnAGLSIZPg8d', 'OCVqMgsUMilsFnE9Fy0rJRgiGSh3OycGJR8NF2giHiIeOggGDHBrSQBMCgUpNRoOEyUXKQU9BDQDD28yLix0CXw6NTsCAzV9aDIN', 'FXQvEw9jEQIOGXskGjIVMCcEeRcTfAEdLSM9MCUuMQ9UQWJ6E2wGP2IZaQoqcS19BAgRIHJqPG4TPyYkMDQwHmM0JTUlCBxuHCxq', 'AzQRPwwmBDc8E3ocFnAbcXUPOnkAMS5qHi01F3gEFw4+ISx7FjkLHilYYHZuOBl9ECF2Bz1qMzZldic5AgBwAgsgIAErZgUmexVz', 'MW5nER8ZczQmPxoJHhsLLx1WXV9SbTB0IRQ0ORQncTBqAxkgGGpmOzQsOHEcJSVhJ2wBewEzKyRnKgJtEVl0R25pOh5/a3MLBCY0', 'YGwKNAQ7Cn0LMj0IDmhHAVxuAiUaNHMveAcnGWY0egg+KQs9IxQyLWI+GyUEbXQhYgs1FhkRCyxuZgleWTgnZ2QgGBw4anA/awR5', 'fHYqLj07bCEhOD43dRg/fzg4fxE2CFcFdlkeEgoaIRV0B3wMIDAQfXUOMGcCDwQZHGJqewYdJ3QvISI5fGkEDj1nYFRkRm07IRMF', 'H2Z3IX8zKQMmeTI3K2hzeA1sehQ9M3cVPhFxYydydSMdMDM/djssCx9qN2U4ZHByIhgZHW8dJnYaWEldWhcjACwFbjkhNXN+fQg+', 'KSwhH30kOSYiMDQicB54Ox4uADsbIAo+aSIrGCwWJxh4KR4tE2ogFh1Df3UvfmM1PDgYAy4gM2QVIgRuOm86cwQjJyIRP30DIREg', 'NBF9CGUACGYHNx54MTJwLWw0FRUtNB9wECl3ZAMgdCkfGxYDcDkoHSBoAjcvD3wDVE0JIDUaJDAWAC8MJT0fKAwYLHR+ADkyHGQN', 'Iw4+NHU2cDUpD2cndSkhPzo8JCZ/KzMxCyt8Gy0rFGt8XkZHNzMpGDM5JyIRGWwfKGctLxomCzd1Iic4DxJ5JTgwBB9iKDsNfjwD', 'dSUGKx9oLioYEAZFBnFpAAsmMBF0awd2Ywd3JXQLNC0NF3QpfRdrIyY4PQssNQ8YCzwZbgw8Ax9bAmcgNRxkDWk7C2hsfWoOeSpu', 'DAQcGjImeBVxDDskORYRYQ0mCjUDITAoZGF8InUQESsafyp+cSEdWAl3WBIeJWM8c28jLCkSPhVjKhEUDTwzCycmIRI+JjsBcy03', 'JG8kJAk2DjUmYnw6HmQGFQx5fVl1JjM6NwYiOzc0BgYTIzojLz41YwVsO3UWMjpmLC4tKTo6HgZwHyAIHn1SBHxwNz5lJSIeZgQK', 'ARM9KDcIdmc0dj8COz80HwJpEEVnZXY5YRp7FwsWPHZ1LB0xBSMDACgYLgIScQtwFBx+Fx00Li56KzgxKB0XHUlXQRwsHjUBCgkj', 'MDkvZSM3KwQoMAEFdxswDh5pfhgbYCRhFTU2ficnejUoADgSHC5rNV5BXUYNEXoYYzYGPh0xIwo1JGo/MSt6KQtkLhQHABosNwcs', 'BA4nAT1xFnkgNXASGw0IARg2JQsnISMrGiIEAD4ZHBVqC0J+ATVmAWEQczgLcwo9Kwd0F20sFXByCg8rB2YlFAN/KC17BH0oKB0g', 'eTQxJgUMBTx/AHYRMy08FigycioPfVZEUh0NJSFmN2w8AWhmFQxnJy8XNXALIiUEFm09NCMTfAZgPC0jBSRybG9BAwR6ZmI+Dj0Z', 'JgcPBy0zIRcbJmJlWwEuMyBjajR0NB0mMWU/OXEXDG97FXUPIQoIIjweMghwLWIae2YuLhUxaAhQDTInOhhjIG0jJ3IbfQJ+Kj5t', 'AlpdEiQpMiQlByBuFBcachwnOy0sGXYTc3QTNgFqO2woMTkBA31nEzErNUgbX3E+MCozeQ4vFCc7Gjt2KjItaRFjeTpzMzQ8FGYr', 'AR55KTEcPWZQH2AAJQEcFmpxFGoiBGRmfQAlI281K3YWeC4xKTomew4/dgwuI3grDi5vNFYGdQM1JTUHJxYLeBIwAgccNAoAYQ0M', 'GQIbMDgkL28ZejcrBQphFzIzCwQ9LjckYCVnMjYRaVR4BllsJglsPnY0fCsoGwsnJRcjMBwiNCl8M2UJYxYjO3AkEHABeBRgIBMx', 'JT40FHwxLRgxbjQ1OABrfSwxMhEcBQhgIRMNcgB5FioHAXUWD0sGcXZ0BCRnYTU1ZCoLZQA3GysZPRFwIysFDR4yHWU6IHd3ZjEl', 'C3YcMAwmKyUrFG9YX2ZOHWU8LiIvERIocxoafTULcQg1MRZ0f300JgMlLSQjED45AHcseHwfGVUfa2FoMAYYFAw3HSQQGmUQGCRj', 'f20+OjYhCyo5MAkhHWd8DTADYAksFA8RKCQVAB4FDDV3OgR6JBYvJmBmZmcCUho7eA4+DRIAIBFkJyYcGHVoLTsHAAQkBG8ZNytl', 'HCMWey16AyA2MzJjPhgTew5wG2UzMT0pB2ZqU1UpGD4gKAYQfBd6HyAqLjgVYHBwdnsfMAFwMiQBMTA7HX4eJWw9cTIqSnx8BSsj', 'CyIVcyk5Pj4XJRY5DiY8Zh8AAmATaCA9DRY9KncxbR15HQAWIgsOKiwmBy8kNmUNIQJ1bxgZO3Nydzc6YXkjPTM2Owp4AmYGJy0p', 'OwVsCDc3N2MhJCEfOQAoGCluGg03CWIEA1g1IH8fPAQrY3YHPWAyCSQSAxpwMyoNJwILMxh/Y24BOnt+Dz4vfBgISwhdWgsFPCAw', 'PDR2ITYwMzMBbhoONwI6Gwg9BBF7H2YaGGt4MzU5W3VIVm0+O2cbDWkjNwpmPyZ4FAoDEA8AMXgDMxsbAgMYIy4YLgoqNGAIDzQK', 'FSJuD3hwPBIcFDpnCScnMD93GQ4rIz1dXnFEaD8kFT8HECYzenonDgMAKBIacQ0FAjdmJTIKFn8GE38jIRk7KTMPGUZ4RWUJbCos', 'NmojET18KgUbbioLIiw0JjU1HBZia2BhImIxB2wlfD0YCHwBPgdWQnpvemM4MXMIMX0BPwpuJQ8JDhZxNBUoNgQJYTUsIjUrOy9/', 'AxJiDBQCPjQ7c2JvZyoOLnwoFxEVNDgYcnE6GDllKhwxDzMBZAVSMScbJSFyHj19IRkLCS90LS8ZeWoRA3MnMWUddyMzKXsmNwcY', 'OCIgECBoIRdsZCcMJ3ZuMhEkbnUSJysRZ2MsHTdsFjM6GHQtHWAoHWZcUR5jfT0wMzofIwkdFR18LzAWHSx2ejMoHykCGSkNAjoM', 'Yy4sIztgIQVgCgknGRsFR2ljK3sbdS4HEhQsMwkqGCIjKhIOKD8iEC04Oyc6AhNkECd7cD0oDycGSmJaOSM4YgY5CmEiM2cIdTs5', 'Pzk5IRp2bDgdbGdbVQBEFww2IAVqZgkICwBrKz0saS9mGiUhex0mEAB9IGUHcjs7PT8rAiRsPlVEV0E1YjYBeTkRPAk2F309fy4A', 'aQAVMBtoVQVkdCUqfz0RazV2Kxg8dHgDIygIAw8MZBEbOWgaCWwALm0gGyoaPHVyPEIABFBqOQE1ESobfAQnMmcfHAkXPjNwLRkT', 'DxsrDRVBcUReFAQmbBMwcBQrLhM5Cj90CCETO3EFcwgnOmZhNTMMJgQMNyYVeh9gGlcIARsYZzpjHycIOXUHGicNOycJMCYABwst', 'Dw0oGFd1aFIJABhmK2onfCQleiEBHm4gdioOLjA6EH0WaGoHbH0HDGYfBS0cECEJBnpRQHAGeT4GahIfMCACZw0+eDIVBSIrDCVq', 'Vy4PFCEfOWhlMBBgNBc4GBxsDX52dQcoI2cXHRYndHU9JHZ9FzoROh58SnVSBQQ6bHlwLSIfBBEnci04PjJoBng1MyZnCAMANhU9', 'ZBpqO3h8Dn4IMSMlLRx0IS85O24kHhg+JTsPeyUGJn0yeHMfO2NoWXMYORsBFXQ4HQsAEDQUAzEgahZxDnpyKRQ2YX0NDRUOGgQj', 'EC8LCXl8L2J0MTx5ZyMEE2EwcD54HDAbfyATJCdjKz9lABYxJCJiNS8UZD09bVh7Clc7bTUMPRYSEAFoZgc3OQsZdj1+NDI/Lxsn', 'dBExcWBXYwYFHi4UcgkdLXAceX1jNBhrPQ8WaAYwJQ9hPwcHdikFIDwCGhkwdj5cfVBCLTx7FWB0PCkQLS80MnoQPAANIjQQIzIc', 'Myoye3YKPT40DGdEUx1COmY8DiEbFBchdyB9MCIFcQ1uGzc6GCE8ayYbGwRzejICFS0yciMODQZXVxs1JjsNYnMqJxI6EAF0GW4q', 'd2hifCd5cxYUbjsDKBYaFzgWbTwJDzECZDAzYn0wd3BmcAF5DykUdhZEdGR8EDA+EHkKBWcBLR4cdmcFCiArOjVyEnEwKQg8KCEK', 'MRM9IhseAWIzIWIrOH0QLn8pPwpkBDcOZwFqQScjOQQABwgrAnU+JjAeC2pqPTskbCAWZyk0FzUVLRYEBXksKBEwIypraENSaBY2', 'J2o3BHVtGwICOT0XMw8zJggpOxIxPDgFfHQRMX0+O3o8I3BeVGZWDRccLHkKMycREwJlKH4INCppOXF2LXdjEBc4eD02IgR4Nzpm', 'BA4xDHRiGXgsEHscKxgFQGtCaSF6OwV0aRQ9Lht5F3xqEx9nMCI3Dh99DAVhPiQ8NxojZCgtEmpgbkJlQQ0+Mzk1JDIpBgoUBWR2', 'EhQKKXV1AzMREnYndWASIQcpH3M5Z3wlYQ8lfDZ0BgdIVhUxdAAxDy0jMxFnMwkaNz0RGj4wKD4MZjQ2OR17CnRnHnsWdAV2IRkL', 'FAUZX3FQHj08bCUbLAcCdR0IMmMjLWo0fxgmHgNrJ2kgKTUzMhYPGCcFEX1tBQtyXU43IR0aC3kSJSobDX0GOjhpaRgbNhMgFhkt', 'GAEnFAQycjh/SFxFOCEmORxqCBEgFWwALXQrAhMMMCc5Hwo8BjIQHWUpcAYqIwIYJwIbPUdnBXYwO2McMXIHBzEoIzodNBI+Dgkf', 'CGo1GSwnATE8HCYPERdqfH9fATsBegQiNisjKxkCYAIucjY/GWcKAXs/Jy4xCAMYd3pgEGQFBnItNmZacXEfNjN8PhsTOzEnMTQi', 'JgUGbj8VZ0kBRSgGC2M1FgwEbjYQKzM1eQkOKiQyCwAvYXAyH3Z7IiEMEGQtJx4hKTtDAxlfPTMpLT53bgEvcWIrfQIHHwk4J3Qx', 'LBYRCisoAD4GGAQSd300FGEGOhN3IwcoPGkDH28VYn9qDTJmOGwZOytnFCUCEBUDDwANZyIPKhp8OWckN3l7Bi0feiQ/aAkTMHBE', 'MA9kYS0kDXYMAAMHTCxlCzIQJwg6KSBtGj94bigtLCR3AjEPZ24eZXY5IgAgCCk6Jy9yaRxBfgRzZj56ODErJhwfNSxrEik4Hm4y', 'ewEsKX0SKnFRBnxsHQUWPyVpJwd2fh4LOzMXDjE7DXtyfAclZxopJzAgFzs4fDNgEWAPYkpHXCkbOSEWeDpkC3IfJ2pnBTURHmc3', 'Ej5kDWojHyMbVgV+Agc9dWEELDo2dQ82JDF4M24ycCQzDXILNj1kZQZiIQ0VARo+LTk0FzBHRlVjEy0rMTEWCyYxF2AkPCEzCCsc', 'OCg5Gy83Jmw6KQoFRXIZQTciHCQaJmxkAWwRIR8mNAoJFjpzBx8LOjlkAAATHTJtZioqaXl8bypLdGhBPBc2Jxp0GhQ/Bw0ndHQY', 'bghnJBpzCX8LP3wRD31pdGZFVgQGMh1kZ3IwY2oJP2EiIHYubiwNCWgiMyswEWU2exAUJiwqORYpHC5mBgRjBTQsDhshEj0pP2gT', 'LWIlPiMubQYnMQELPiYxfCE0QQJcQmtnfW0+LysndSQWZhUrDGMIaicHMAUuFHQmZwE3ADEjPH98DzIxAw1CeUF6BzEUIiUDGCcH', 'QQ9jfBw8NBhmdQc8JBQIEm0ICxwACiE/OXQqPDVgIXIcei0LNz5wPTsdYVBfKBQgJDh2a2tuLzohIgE0FygIIyJ6GxAXZhgKI2It', 'LXwVKhsJJHgrKQ4QEz0fPDwyNm0qIzcMLQppbAoDVH84ZnsuZxQvFBU5Ngg/ITgeFR14AA88CGotFAonGz8COCIDA2gzbggbS2ZV', 'BiYUBhd9FWIzHSEQISAkZCs4CD9rEXdEWXtqEy4bKzMlJjFzBB0neCwzPBQraip+HSseJgsLZhMOOgY/FjkIMGEzZWJqG2kcHCYT', 'cT46fhR6ZAgmbiNgBBsSACEGOi8PLjcSKnNoQ3s8MT4tfQhrAwEWBDQmFC8oAD5/dykoAzxwOBZ+fwgaYzl/NG8SKB5mV2hIZB4t', 'WVZ1Bh4DfGMgdDc1EnYPFSEICQggOQx3IBt1FQc+EC4sajlsJDd6Bj59anRfBQsFDjQiGgcnF2QoMBpnAQklYxIPZzEGJWo3Oz4+', 'NCsxKycOeHECH30VJhssbW0YEAwzFCEGFDshPQ0lHXogFmY+FCMyAkd+QRZkfBYdCnQnEi9+Nn0FJWoKBx9uAjsiBjw2ABgNDgwh', 'IBV0JwIQDzhkLBMcKxMcJS0bZBIRZ2dlZwZ0LQY2G3A5B3YWGjYVCiUrKzAQMWgZETkIPGoVEDMNBywtAAhgdgM5Z1xHYi16dRJg', 'ZQwXeR0bcB4PVx8GWDYaFmEVDgcXHBMsPSY8GQs8KTs4GyQkBS0KPxohPTITDSIBKnk1FWlEZn4fbTAoAWANHGozciNidSk3LDRn', 'B2oyP20WfwJ9e2p+GBF5dmkDfQAeNA9/eT8xNRw4cCgHAAsBNTsCfTAhBDg5PQIqbg9gBgpSNQIKET95bQUJbDsqHw4xNgoqGxYR', 'PXYBCz8+ZxEEehJyEXAVHz9mLCdhITkZdAI/am0CYUt/JX4dDH0OKjsJGjcUAyUrYhg1MDguJBwfahM5DBw3BX8QeQc6Iyc+EQF7', 'eAZtPV9XVARwBQYmHQkxAGowLzQxAQsoCRAKdQ4odh8vPgA6GzMUMyYdGGkSEBEnA19kexIyFBBnCWoaJyVtBiB8Mi0DawQKdhJy', 'BCEQIw55GQEOcQ0zLAQfHSUoehUQcBZiFXYdKA9rZkBAXFxoPgEcMDRuHSo2AjMIdC8CA2wFAmgtEgYvOSQWF3x7DjMJLR0ddjQG', 'MTchIgc3bxQPJT0KczUZaHYXJTskJSNgNRIbKzl2Km04eDoPYCAwFwBlXF4wDRUVEDAaay46Zx8PGCgsIBt9CxMGcB8bJ2E+IRYl', 'HjwXCRIaCj0IDTZhNSQzFjcZejowJzA0JUt3YU4oBSICKnBsJwIRIxxudDgQCTU+FzQdCRQyEjwOBXYkZgEYAjsyI2loXUcBfCg4', 'Fy4VEGpoYxINHB4HdDQUH2dweHB+PDg0Pz4VGj00OGIICjY8F2xuAWRKeWk7A38KcDVgPWwBPCt6IjM2ORx4JAdzASUjJWANPxYw'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
