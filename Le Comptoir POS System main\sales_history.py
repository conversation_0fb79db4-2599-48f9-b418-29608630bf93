# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['JCwbNwYuLycqCQYgGwkxAgs3ZDUFMwJ3ET0cODMzNWAdVnpcfhtsGyAUdQ4VKS05MGoOAzQsEzkyMTwDCg4cAwJlJzcDMSVhKAQJ', 'LhNtaFVdbBI7AhVjKWkUKAU6GnZ0FGMgaxAtJR4RCGhpGgpjB3RgDy59JmA/AQZBXgRaG2A5ZyoOBQYqdnoIbjkXAmhrOyR3MW4x', 'On4nLWgaBxtqZFoCfHRiBi4iCRApdmwlNCcIMjc8MXgkBxwofW8yNCJ/BzkzPSEWPTEXIRZ2YnYGNi0LERxqLGV9bBQ+fQg0LT42', 'MgM8BDI/LWA5Yh93QC40YzplNmk8DTYnCgI+KBIDGyIzOnkfawwIJToTEjszGTceMycjIzEBAQNYPgx9DgIRKDkgDAN9MgNqHwsF', 'DAAkNGZ3A3h6MiALAj8iCSouOQYmbjYyOSobDHgSBXwWax95LCRyDxk+Gn03OgAzZwECBGAtbRo6NSA9ZgAWAmMkCTMUMjkmajIe', 'JnsQCyAmcXRwKGohaBE1fA0BbGEdHBQWBXQIbHtHcw1pIzQSIQYSKXILLDwIOiVpIwZnIwwidGVuExomf30XOwIBPhAyMwEXflsK', 'GxN2ZGsiF2RnEhllIyc9OXIgH2poAFFRMGYkbR8DLzc/JmQAJgcwGz0zDDU0P3AEOjo0BTEWezwnORZtPi82LX52akIYNiIZYwwF', 'JRkEQAQYJg5/BQwNfC0hGAYyJhEccic5DzQlNgUvZRgOez03Yn4BChEHBC4pZVJLUTtiOzFmLGdlIAhjIik5Bw43GBs5di0wZgxn', 'DTIdLTsXOjxmKTg0MTIeEVZkYGQYNjkTCGozOhEwJWp8eS4/GxcgCjJgL2doYzgbZhNxFSMoH2kIfCsHRAMZUD0gCyQaCRA/MBQn', 'HDAIIwgfEjU9GXMPIC87Nn4vNTM2YygFeT8QdxUsAGQMK3I2YRQAQwdnPgR+ewIiaGY9MSEWBwcgHD8TPClyGT09PTodOwcnKDk4', 'CCUQewYdB35zKHI5DgcmfA8oJWkRHWcnNxt+Nng8Mj9yOUVqR0AQBwhjOmoHNTUPNjxyACVqCBVwbi8sCghvNyp4YhUGLg4feg8l', 'ATodeSoiNTIpLGdzdiAfFmgbJjgYJCsOHgEPKg18IA91f1paEgF1NiUKbAZ3EBs6MyYbLGs0cXMsBXU2bTYoBDUTExI/Liw+Bigb', 'NjQmODQXKgoiHyYocCYKOmYBDmwTKwAqPz9qNAJoeFwrOmMNYHIyMSstO2syFBk8KzY4dC0lBjQnaX13LWp1bAU5AzEFHwktUEZ2', 'J30KbAgbem4JMTwGFx0+CiJzej0vAzgbPRNtHGJqa2YRJQ5nHggTPgMMJgICBjdxLjd7JQwuIjZmCD8DYQQ2LX4gDSsGFms7RkZf', 'DSk3eDNmcG58KGwCYxN1N28/MQwiNSlqG28/NxwyEXowLzV8Oxs8MhpEB1NNJxsIGhswLxsOOy85PXUwDmkGDhN7Giw6cBNgFWJz', 'GD8YF2YkdzMoJioKaAgvLQslI3wLLT04AgE2KT8TFiUFBw4xKVl3V1JpICk8aic8Iz0wfj1xKHhrNBJ5Eht5Mz0PCDoqGhUUIh0I', 'NTs1CnoSBggOcCxsO3w1dnoBPS0mKzk2PTIjcRcDaABqCmcBQV1WJRY6LWBuERExEQVhfCoPA3YyMXBxcjUXa2VhJTYLESEZKCoy', 'W2Q6PTYiGCQOZgFoFyp1KHYiN3B9OHYddWESPDF2ZHAUITgrdjEEfSNodGhbWBczOmABOyp8IBMjHD8FACM/En8CEmQ1fRAcEx02', 'ZmtuHRhiCBIsDwAGfR8zFWosFnYrIXwcIGslLiEzahtBbjF+IzAZNw42ESU4PAAocT8dDBwmJh80OwcqAj9ldg4ue356NSgSci5f', 'KQsgDw8dLWx/N2QMGGAoGxw6EHk5NDEGDAseZV0CcBwPPxdhcxpkCBoyHTMPEBIjaSkkMgELPwwcMAA/MHp/cQAoGzkLECxBXGVE', 'KGk8bmhsBGEDWy1gPXsgLwYJKxkdPDR7KgADCj8DcAgcaw0yJSEGcDQbcT9lDjgzFghbdGJEbBYoNR87EmMrLGJjEnsTChYKLwt1', 'DmUwOxwqKiMYG2N2AnEyGQJjKw4cI24rYhAAOhcOFTZ4F2gFH2YrYCUCG30lAC4ZKT4JahtvfGBBUih6Bh0KEQgHLww9YysgN20O', 'ECkDEAofM25yDH10BSYIYCknNH5iLwk1ejY9JwgNNnB+SnZTLGYiIWANKDkLATQ+P2MSKz1weCB6CQcjDxdqOzEjciQdDn9qJHwj', 'CxRqHX5ARzcnITwEFjYLBAc0KzM9Byg3PSkIBxgdZSgXaykWbgsVZhkABSx8ERdQBwpBMTseJxhzcBoqLWUnLAN4YmoYfTNyB2og', 'GGUkLTY4Ejk3IGBvEQonAHxgcAckPDkmLgcoHwN0Dz0MJkpGBnUyHXkiYQsyPDYKOxwxNDltKAcyEjUgIyIPIxo5OQghIxF/DW8o', 'IAY3ESwPF3I6Jhc2ADhjDBAeOSIsDWY3YzMrHzJ3GAEcKA0BI2kqAmBRGzUgOgw+OWt4Ky9nBHx6BGMdOA0qAAB8EzsBCHYldAAw', 'QlVHaWM/JQhwBmMsCzQ8cRwCMSovPSc3OiExJjUVdwN8FwQIBWUmKQAPNXVHC1A4YApnNXJsBAYTMTA/KQsIASUmBgd+JgAqCiQt', 'dy4zHBJmBggAHRoDcSc9Mnk9A28HH1VYZmIlYiElPAM2cmcIMAgzFygWfRVyHitkJQo8JhZ9dQU/fy0wLwo7NXQfYgMcHQcnICJm', 'ciw9ADECMDYHYSV2Mhx3bSQrAyogP3IqZwhfQzIZBHsrNzM4MSIyZTMPBixsLyEkEgQ8HgcHagQeLwpmGno8LyoyEHRKVkFYEjYU', 'PQUHETJ8NCQdfXIAIhYJORszKyYqYycxBSh/C2gmISp6OyQwDgUBQnh9JR44YCgubms3JDshCQInP24GPGo5G24GKTQAOmIVGWZi', 'CwUPbT17JnIHGTcTBiEJOTk2PhsmGDIkcCEoZmZ2DW4JEwd8YWwyH3IGGR9qDBViCDIwCBdkIxkjCH0VLDwAESoTKRgdNnQBKiIF', 'IGplNCIoByMtejUnNwsPZBAfaUV9X0EFOyM6eXU2fAgBMBorByAtM2t/DhUzHCUKZjg5FyQkLR0iKQsadQ4sVVUFcgcAOA4FMT09', 'Jj0OaBwIARllBC12IzYrDQUgPwgzD2g0ACUNLBwGJB4SZB0RLV5Vf3sYDAc2NzI8PCMABTMyIDgKGB4mczcuHSI9PmM9EAgNDHo6', 'f3w0cgAtMQJJYEMSJTpsIiRvIS40YjRyFnh1bzQ4eCd+M2Y0ZyBgbCMkeycYf2Z6fS07XRtBYnAgfXtnMz54NhMYPC4tKxNvLSES', 'Bj18JiozYw96GjEYEyEYRAFBUC4kJi4FdghiAxcSCyZjFCBraH8Ad3J0GC0fZwEyBi0iCyAabyJxPmYLekAfORECFSYsNAZzLS9l', 'YiMvE2skAzM1HG55NXYzbggLIj0iIgglZ3gCeBoHchcQZVcLXzMNZxocO2xiLg08FgE+IxQoEjAndjkrPR0qNCoDIwJiOSkYERIQ', 'IiJ6GwQHPxNmYWJCGRp0OysCbGZqITtiEHoOCx88BCk6HysKERd5eSU0ISc7PCtnJHEMOlFUWUxrIB5kFBFrB3w3AgM8PwcqaRc6', 'ICoMHmEwPCQOD3UzLDQHAiIoAi06PwhrPAxvKn9/UXMeZHwWKDEzGXYJITMjIHApEQ96MCclPzE3aQd4GHAGJjF9BAl/HTYpBAda', 'fG0MFDAdMm58PDc9Bg4OCCgXZ3F5CiUyMBB7ZCYOEWwZLxsHOjIiCjtYYAtyaRcbZRp2ECYAJQwqJiEtAjsUOnINBwsALWIwNRwq', 'Hn8kKzctej84KwQHHx1hEmJgAEYZZH8FABNqZAMSOyE2eCscETcRdXI4NmUaNWECYQ0nYjwbNwt4PQ4wHVFjDAdsLhshFSYQIQgP', 'PiURJXMfJxNqGXk5JmoSJDwkAnAaajUxdkUdcD1tA38jA3Q1dyciZRx5IHUzbCZxcw8BBmpjOiIDcDkaOw19Nn4HaGh7UmVOEGU1', 'Dxk9fQkkBh8mAnMWHRkoCgYpazN4QF8GHhd/LCcvZmokNwFhMiQYanY9H3IGAyQgb38DJmMxFjYGfgcxIygPKlVfBAYLYSB7NS1v', 'eX82LSEpYG5BQgAHNhoYYyUkKgAvFidndCI1YnY4G3dsczQxMT84CWwBMGNwLD45cxEQZ3lSXV5nYR4MPxYtORcOGx1yDxkXD3Rx', 'LnggPBwcMicbKzoCExk4ZCNsB3RyOFoGWgI+eiE8MTI4NyhzZhtxYyYCMzQ8FCB5KAsoExwqHSM6LHo8CjM4EQAlV2VAZDpjJjkr', 'ZzksH25rBjwZOTACKQA2bjF9eBV7PQIraRsYI3UOYT0/PXQAcDQSXX1TAD1+JSZieG1lKQtsZC99eHErJWckKRM0B3BpOBdiaghk', 'Cn0uMQZEZGRrFB0uBQYLOTFwGGIydQIQNxIbeBMOBiA7KDoFex13MnA1FxERNDsFSF8LdwY5FGEnNz0YfCdgCA0rBHVgbQ1zJhp8', 'DDA4CSIAJWR1KQQKBygMGSIXMl0Ce2QpIQQbOwQPAA87ZXkAOnExaGk8IwAzCzg6Khw/I2pyJAQrexAHAmANQFFcAjB6GBUkc282', 'PCgLEjQtYCc7biMdBWUTcDg8FwZwMgwxKX19RVo8NBsiPA0qJTYPMjBwOTYJOgcqJg5yNgg9CBV9LgpyIgA1OT19PB1nRHRIDBdi', 'AGUqYDgsHHwxC1ZdWl4bNwYzI25tJSo5LAN8H24JCS4tA3YqCQswZAEtHhITEix4GhY4LzE2QEcDTQkSA2NidRI2KAU5MQ42LT4X', 'fBElPygdJRgKeAduLGZ/DhRwPT8BBkIJcURmDQQEIhc4Hz1sMwR9AiYiMRt9bgw6CWclCmEXDXw6Fj59J24SIG43SEAHeWYFDSJ5', 'HykVNnYIcRguMwc/Pj0wOj0SJzYiB3YpBhMgCFRka1AyMzUhJSp0MXYsD2o9OBJiIGdwCnYDKwdqGiE7Y3wsOHx9dzsmICg5YnRR', 'IAElFSU6HzRhInALBD8YFWMUBDoNE3AzPjkmD2w2CAVhJXh3dg4BSlBcFmAgBDYGCAMROWYcKXRxIjcZEQ0pOx0raXsceWB1MgIT', 'DTASKToAIiVhNAQsNwZnGjl/PS8HcixQe31MJ2UhZAEJCmo3MmZ5AQouLDYMJHAlKnEhEyYjYBswMTM8d2VoGSkOaQV1A2FsNj8h', 'bgk7Dj5zAX8oBRscMSZ7A3QQJ2QWb3wDECleRlVQLSwLBjsjMHwfbDI5JGMpcQgHeyAkICZjJzYLBm0VbGd7G2EVIw4rDloIe0Qu', 'JSMxBAslCTsbMTIUJj5tByESdBIwJSkRNBxhMiEhejYmDWQyNy1jdGh5GT8FMQsrCj0UJGYhAWMvKhRoIjgNOj0KDjQHChkjdyYx', 'KjYuNSktDGUdEzQtJid6BCgjJikhHCwmAWVbBDVgFAAkFBB8dxo7FSAcNx41GT4uJw0xZxgqfSoRCQAHEH00CApuODRVRANDZn42', 'NmcmDi0gFxc3EWATMC8NfQR6PXokFAxTckUbMQAIEmUMLmR2AWACByQycS8wMgw7Az99NSZrKDY9KmcNFT4LGywvL0toQ1kJGSgu', 'BzU5Hn1+On4BNmhISgZROBZ/ICsKOTYgICcxFzw5ODw0PSU7Ly04PAYKeQdwEmUKJTYWMwE6JnR6XEcIMAgaMAZ0F3MKZTYdLwds', 'DDt7CwspBSEyGCVVRGd7PTI6ARsCFnhzJSEYcxQGLQNocDtsIwEjByYWLB4tMBMwLmVmGzYhF1NUU1c7YwJlHjQUNzMTPmojeHYu', 'Ci4uNG0Wb3gQeil0MHBoOSQTFnRjHRh9EnhyIyxiSBl6Mg8YYmAtNmskaDx9DH5uIBcPA3V1YDETESkwKjk9NzknP3kQeTcsb1d4', 'ZxgVCCZjLDAACCA5ARx/AyYIZHA3eSYuKi1wV0deR2sNdCY0OSd8Ig9nCnAcMC4SMX0qNnkhOW5/BSpmDyp7Ohs4ayI8aXRXRUps', 'CyYfYTw9KHFtBzx6GT5yKTsZSQtNDDR7ZBY3NWJyKwUjLAo3NHYsfxUxKTAqDwcRZGZxMRceNwI6CQ0uD0FRHVIKMSQMPAosamoz', 'AhcuBHp2NDgoE21XSWJjGhEmBSsYJjl2KCYnMCFqajUvYxUmfTMcO2U0OGEoLTl5FhoxfHYsCQdhB3MMA3w/YCkFAAElEjFqdAkj', 'YjEfDTVyJTM+fRAPNRNpeEZ/DBQtPxoaKAVrLSk3JAgpMzVgLS0Ddn4RP2wkMAoMD2x7Zhk9DC4JKmZVdHZQKWMNGSYNKCpqNQAT', 'YyErAgptAwV+GDZ/NmdqEmUpITxyOCh4GQ5gHHYqXF14ew5hPxZmBmgqHXQDIgoAeQA8GnoUMxk/YQ07Ywo5agAtPCE3OxEuKTFq', 'dCEIC3sEHC0xeA0yHW4qGSg+fW0JdX8/J3kufGoqJ2VCe0E5GCB/IXERMAcrPiQ8dXE4DxUKIDN7ah8pYTYXMnAvbScDfQcuMGAH', 'KBA5KHIdZBtgBxsaPDoAfiI4DhsrAG1/Rnx4BiwiIBAOJhk3aGMidjQkLTI0OnkkB3Q7ET4Tfi0zaAcKHz8PLwl2HlgHeXMmZGdk', 'ERkpAihmHz8fFxwvCXF0KT0qIRMxZycednIhOSl7bwwPbA1TakVtCz45HBgOC3gmMDpiPS8QHTc9AHFzZHVjDjMedmx0KhcrJnk5', 'KykHCT4tZx4AMCMLMBN6KGs8LjosXhtxHzEzKSMQLDp4NzswIDAeLTwxDRJ5KTkNOhAqMC0ccDNtHB4scC4QC29xRxlyORwAMGMy', 'AgcoFSg7Cy44cAd9fRg7NAQDMiQefBt9KnMuPDwZenp7PiY+LQR5ODQWEyY2KCoqMAlwACcILiQlKzwVHm0ocWcGeT0Qcw08CF1U', 'BGQiPRUKIGQoDhx2cWkde3R0CiliCGQrNiBuC2BiKgw7Gz0JLQNbf1ooZWNhYXccYAkWYiV3P3AcDzJjCHEmNyVoNih9YQY7YXot', 'XgZidmcnKSJmECx8HBQgNhEnajExNHBuJhwJCzMnPQEMNCIZLSYBOykyFzAKRBlAHRx0BGUsFCZ0K3oCA3ovCg1mcCwEEQkVahY3', 'Ay8xAxdqAwUXLzwGNQIJPQc9NR0GPyx2N34oBG4ycCAtQWpiUwsRDhwnaixrdikdCzx1dxF2bHALdDwrIR4+PjcZHTk5YgQBbicH', 'FBQTfzIPD3sMDRooGRUyPXVRA35nBAhkG248YSJoYRYKe3UZPGkOCiZzCWQWZSgHbA4xERA8ASgxcWsscVJ9RR4HeH8UIjcRPRsF', 'd39WQG02NB0fEHQndBMXGXU1cwwJFixuLAMQFik4Anw9MxIMBRYqDz4BPG5aYwdZJwUpJwR4LgUPGT0dEh90Yy90EC1yeysCCjVk', 'dRlsMGAnG38RCip1BwZ9dDQ/MRF4NjYTGgwqdhYqPy0qBjUBZHFmOyY5eDo8bDECPD5nLRUAGwNIClwKbQM9JhkMIAcLDyV8OwId', 'L3sDCjB/M30CdgV/GiR6EjNyKStCQUhTbQ85JDgYKDh9D20wAgEzMQ8NKghofnUdKAgcGyYLKDM/Bio4OyEBJkpWeG0wZgAsYwM0', 'JCMuLC4ufHYxAQ1hPX5YVUMQAnQXMAYPNiZ7HwEXNCcSHxA4NwIEcGIWZyR8HncFJSUpOzYeJG8XaENbBgoBGRVhJCcDcQcaKnwm', 'HUNGZxsFOAwUBDc+JDBiFDAbGDwpFC0qMHoWAHACagsjCy0zJnctKR4vbAwLBQMDMj8kDQp2bmM/Oy8FDSZqFmFpHjtzKQMrCzIX', 'czQdMQ56BHE6aXxuJCQOPycfagkTDhcgE3w/GzojLChHB3hmbSdjOxoTNBYIbBM3Kwt1axhmLxJ6ehELBiQhJAQDIhkZCzhpJzAQ', 'Fn8PCiYtPXspOw9qKEZGVEBoOwk7Z3MvOCB6IDodGG5pMzAKNCkDKgFpExx4LHAnPid5GSxgDQFqRV11Xg8wIGwCECkyc3YdN3B8', 'BlwERjcaYx0gLhMLMC16MQl4FjlhNzIHICQ/YRN7FwgkFggwPRt3PnojLTBiRARRBxN8ZhkRDjIvJzZiJABwNz00CicbGHczJWk9', 'ODkEaj8YVUB5cjMFFGV5CWhncDYyCCcOLC0VNQtqcHI0ZjQxKBxgAnEdcCosamApLxJEVGAFKyMGexQDdGQiMmcHExp2YwEsfSwH', 'JyAEdRoPH2oNYyguPjcEMhYLKTg1aAY6HCsZOQwNMmACcVgFIxU+axkrHA0EOx0TfjUwNzYicS0tETAyJGAZPScaDH4gODEAd2kT', 'NAM5GDUUFmQfengoIz0qFjMNdz0UPGEgNm4iDC09Bid8dDYtVXZXUG8jJD4FKDsrBi5gNjYLMCMJPAw1Kw4qHQt7FzUlA3ICcHkg', 'Oj46HXImZD8kEBt9Ci8wawsQOwx7HTEmHGQFGnwoOwIZegUtJixmB3RaDDJmfn8xMQoJcBkdHTEiMTQRB312NTwdYWsAGRU2PS0m', 'JjZxJTQeEQZwDmEaKhQkLTY0PB0gOWENMyIiFj5uc3EsKUJIVhtubC8+Z3g2NiImAiF8DngzYAs9BA4MKWoPZzB3bTwTFiAAI2d+', 'Cz1yChgLAAAfKRwoZgQWEh59IiMZMh0KYx8qMA00Bg89GjNgFTIEM2AaBz4PGysBN3NFSlUbJysyZDEoPn0EemI2IQ0UFnQiLwos', 'amYnYyI9cHU8eHYMLhsLOxJ8H0gBKjJnACgVNWp1cXodAQp4DXI5GAd2JD0jaGUIPRYrMAIsLR5uOnAcCkZXcAVnJ305FAxvfDw5', 'HCpsKBUKHSNoMCIWPik7CBo9UFVFUiomOAQGOBs9CgJnBSEGbmlyESZuNngNAhhoeSQkfAwYf2ApbiB1CghoewBeOwcaJCQNFxoy', 'Mx8BbCt0Pic2FyU7Khl3FQM3DCkHEwM3Cyk/LjMQFXsqDGErGTMoOXRGCk0SHBwcNggtYggVLQIiIHAuGBJ6KxsJdSFoewA7Pi82', 'HDNxLzovFjZlFD0KM2oFGDUIfjAeKGcrBCAxID4RIyt0GwIOB19qGVh0Z3QGajZsZSp2IRQ1KXQUCDV7OTkCdyMVGCR3GCRwFQgb', 'dzA7FX4nczQNOlF/cHoGFH4VZicXNCEuZTRuKG4MaGwLNi8EDzw4OQsYewFoDSsIfQwMdzIYX1VocG4wDm0eN3BjImgSOAEiNmsb', 'Jhp3ei12YQoaMTc2dDYwfwl6OTEINClAelwBOSAhbDQiCBEQCQ8/BjkoHmE5Iwt2ASQ7FwgiGiYBE21ie380HH0XO2J8BQM3AikB', 'bBsgHX8GH3ISNB1fekIyNhwTIw0uISg1HhAVGjMPHW8mORM6EWYbZwsDLBcHBHo5CzV4bhpsC0YAcBFidQAKDDtrfGg7YQcYNWxg', 'cBhwezR9DigkA3kGKyszBgpqemEKTDE8KGYZczEeJnssOCsrAD8BBhJyOX98OTppajpiAjEgG3w0EnI9citIf0ZMM3ouOgtyLHwu', 'GyJwMAoUaggUISQGPytsC2A9awZ9HhN1U3NcM20EBDkpbGU3ESBrdgtubgpnGQd3fANlNzprZBUddX8hJxhmfiMvBXkfan06Mz4y', 'Dn12OxhyfGgXC1NIAisZPC0rbjo/CDJlCwF9Kz8dPS4lNxwvI2kWMTwjCnsaLy1hHGAIIxB2G1NDB34DZWtwZj8cG35kKi4zIw8o', 'cnguZzthATsYIHoRHSJlEgd8LxdxXl5OCAccACQEEwQMNh5rci8FIzcQZwloARc0ZjQCKSB9EWwTCSQ4PQI8C3tgClpmDBhsJW49', 'DRZCcnBDFQUUOjYPMWszMR4EMg52NCApDyMtfCQjbztrBRwLCj0tBXwrPz0MOwZGQAUlLCsiOCULJGoIIDQVPhdqYBYhbjE/NWA7', 'IhNwGgIuBwUtLjpnC11LezxgBjkXJxNmLyx6ZBUYDjwJGH8YJnkHOW4AOSl/LzU4PgF2ZhkOCQxAYx16LhY/fwt5PgUjLh0fEiQ3', 'JXMSZG09HDsdcDsxcCgYBgx8LD10d10HCQJ+ARx5FAMmNAIoahZ3NBY5Pw00Ph8nKgc0dmA1KCIwGAILPTMgKFRhdFdtBwtnZRlr', 'ZXVwFXB6AzgUY3ARPCx4DDl6Fn0QFzMbYQ8OOQR/JgwuMwMFSn1TASwYKGQ7LA0wdgZ6Oh07LTcXNX95AhEiEy17FioQdXobM3oo', 'fTwpNQltPionNxk0GGwyGAgMfBYyOzkeaB43MG1zZAcHMRg1MRcnaDYqB2I1Ez1xK3IVJwAbc3Y5L2k1HDcxJQ0DLCYTIBxrLAdy', 'DxEiHHRVVAB8cGZ6ADQDOzczNxlgAjwLPDBrcQx7ZHYqMyYnPxMoMjkoJyttHQgjEFdfe3oHZjVjEzdvZBdoIiAzI3YiDGgNbhYn', 'CSEqA1NFTQU/KAJ5FBAiPAACCy8/EWsRD39udTgTATUEMCMzc3t7LC4MbHl1amt0egRYMhwCfzgTLgUBGQ0IAQEwPhwzezUZDgsk', 'AGdkCXBMaxl+BDQANisTJQwgAwkVEG8SYw8JOzwZZmY6PyIAKRMRd307OXYWaEdnalE9BSUmYBc+OREHMTN0NRAyK2Z6IA8CfAVq', 'Axk4CD9wQVt8WRMbdR02JGgWIDY6EHZ8dTE0FSUsBS0iNm9/BRhtBgkwECVlJhl8bQ94CEF9OhIrOn0HBx8CCG0lfHR4EmoXO243', 'amxmXmhaLRM9bQYpbGANGTxkAnU1O2sJenk7OD9gC2gqF2FzdD8sNiNsYAkbdGQfUXtvHTokMQwNZ3QCAyAGHC0uEj0sLQpyFD5u', 'YQxGQ0B9DzEJNz4bMz8DEwQadiEOYyEZGA1yLhxqNQAqZDZxLWElGxYmByMPOWpHAF8TGz8MEQNsY3JxAgIBNnkICykCcy4/PQJ0', 'IW4pMx4hPwIZCTB9EQI7PDs5GSgEewIrezspNAgPfGkvWnxBeD4nKDweNxNrcHMUPCoDA2IDBwZwcxF9MzlhOwlmAgBtYiJhPSAr', 'Cx48cXcBBCU7bhg8Og8KaldOHR04OQsMJyssJT8VATsUCA0WcHQmYC8iECo9OzY1CCAMAzg+GxQfZ2cbaHwcExQhNyc4ZjQKAXkX', 'KQg6LXc/CzsxN30WHzwVIHh8d20SfQglQVUZZ2w4fSIRNyhgBCc0CjUuFC5oGAl2LCwHZQkKMw0gL3RgESF3BwM3NTFiZgoAaGcl', 'FwkcEwIAEQYsegVzKx0nGykxcwscKxh4aHh9GzJRagNdPgM7NSpzDjsKcA8XHw8RaTAuJG4IOTQaD2MFC2wUExIBFgloPGo+b0dx', 'BHc0Nw4BE3sOFRRvBCAXAycEYS06KT0cFGA+HUZ/BhEbLSVkJxEqdXQ+ER85cgsxZn14MB9zOTI4IXxmETUaJgo0LiEVFj0GBF5N', 'B3YEeXwQZnkQJxM7A2obJihwOhY0NBR5NzE8dQo9Ex0iPzEVbAYKBmYpahUyWldUYWgZPCdnKnQnAzswEAsoBA4gMXFzBxErCBhk', 'HGYSC20tA3ULZS0jASZhLCwXLzcnKiR9IAMqagsXOz0mYSYyYnwFdCcNCzYAMzwdKgYZe1gEbX59AwYjaXwJcmMFJjUIAA9rPg8r', 'NwY6FyNoI2EPAxEMMQsuMXQpDwA4IggZNgkyEjw7Ij0EFzMLR1YGdzoUIh95K2gCITY+IQM6ajdhLH8LLz4AJhATZ39/fSRmICct', 'MwhuO3IzDGhjJXIPEAUqIzY1BDwpFiQrBDdyDSoHY3kdbmwmABM/JiZqNyI7Kh91LSszOTE3cCJqYA4yPnZnADUtPgU9Bhs8cmtk', 'fQMBDXMRbGoDVmRmaWV8LQYOGBpzL2RkLRRyABY7EiAFOAQdKGlmBmwMdyIZP3c5BXEgLlMFSgM2EQg5ISIJIgAZPyQ1NhMZLjIQ', 'Pwo0DBEwHWc/AQEfdBoxI3k5IG4+a0VCBWZvNHpjJAQQEgpsHRszFgYWGz17JmgRFzoUJTgDMxwwZCY2IAgNciBmQkZcYTVmNCdj', 'BTsdKS8jMWgfKREldhgvGDwGZhFsNgArKDwiKzwMYl5hbDUEeSM/Im54EXE9Fgo8dwlyGSovaGB2K3QZMAIsLRNsKyo2PDk2cm1I', 'FQMIESt0IwlrGR1jeS5qNgQFdn9qJyM4Bn4HAQQqYBU8ADMzEWonFhQVPSkUCmhjKyAhFTsOKiQfPiYkYhgqGzwEdRd0a1VRG21i', 'fwRURwcMLgY1NDE1dBJsETwKcGJtDXgYLjsdPTdnAztkdnobE3s5bCMPASVEBHZSJjA5FRMbHCk1MSY4KwN5GD8QEXUAD3xraB02', 'G3UtO0h4WFVqMR0kPwQ4PW4SNwEveBMMOG0xJxkpcyEYKmF/YAYLYh8hIxAZcRcyQVtHdQVhOjELdWgyH3cWGiINNxcNMhEWcSEc', 'BicDYgh0DRQzez89A3gRGwM9CTg0CnYQGAZ9GhkmMgQKLnoUeCwBaXl+YXAtEAtlBS8eMBx7HWYOFTRiEQcnDAV+KH0wABd2H2om', 'EAFtc1VgBnQ7CCc+Oy0ANhVlEw1+CygtPAIZO3NwFjkgHSc3biVnJHgZFnp2NwZ5XmZmOX4mNX0lNSYIKx0qImcrPCwsEC4uGycl', 'OXEuYB97PQp6JmsRWkZiVhM/GWEEImk+DzECKBwiEhcvOGc0djxuCDIjJCoDfCYOJjc0Pgg/PClme2tiCRZ0bSQzKBIwdWw0Ew1u', 'YzY0MAgfMS9kBgEFETkSKQkkCwEjPmkjAS0+cnYjJzU+aCUvYGxQc1xmbn4NZyQHORlqIGYxK3kgIxdpeyQbegYnEjciAzsIG2QZ', 'DyU7JnB6CgZ6Ij0QBxAmKwd9IGsmZXcNdjckfxhhPCIVYBdAW1RSCyIZZWArERVyCj8gcTgrcThnDQ0sBxIdbxgHDhsxDgQYGBw0', 'CmJVRXA7GzgRFgc5ZxByBiN8HS9oaw4ManoCMSQKHgoYfzUWBy8FHDIKCzcLVwFIcTQFNS4oKQprBjAENHADEwsRHhorFxwgYjY1', 'ER8PAQIXGG4pbRp4IRQYHGkOGVsLAxMvAyxqICkeIBM+MyQLM2ocMX0qe34zOQ5lFzoAHHdtD3ZlHiQwChJrRB1mZm0jMDY7GxsM', 'flwuPWcMYCIbNWo1ESRqKiQtLzo4CXZzCScnGzl+ID13Gh4BZQs8FiwqX3Z2ACw2CgMZFQ4FcAsMfXAONhUSMHw2Jht9YTIVAnwh', 'WB0GEx4iOwgmcGA8NRBnfBg7aWg8JiB7cy4oNBF9dmxwC2YLGGUefDNpHFQDUAERfn0gGDdmIS1xJBsCJjcPKjA8LnQtB2UeEwoi', 'CBkodRwGHgJsCjY3a3gAY0IxZycjI3AwJjIpYmAQCA4TbDB8FxovfWtvHTgJezEXFSQGeWktfDUuHWhEUT4lGT8GGGwJMBRtKi8Y', 'JRAYKA0aPQUAYSAeCxE/FiYIDhwEKyw+IRwlNjE/LhoiJgU9dh18W2VkHQw6EDETOAF9EwwfMBQDE2wGHA0OfSM3Mwc4FhwMDCJm', 'EgsQKgYAZRE0ORkZFnQ5dzQsVR9KTiYbOxA2NTIiDzUDPCYLcz8QOQpwcgo1KjMiAgo+********************************', 'CBccdEZUQTknFhMkDWsXMwoRIW47GANhEAUvGQ5qKjVpKDUHfTQNEzU7FAQhFRZwaWtjbSQFf3ktEjRqdX5mJhYLGBsPJ3QIZHF9', 'IRoLMAJxMgQ8AGsgPy4TX3RXRRkQCx9rBSs5NzIWIQwUBSszPQYFBQ8yASoWMR0nLGw1EXsNC2QzPypLBkYMaAJ6DiV4NT1wERgD', 'biM5MX0tHWkGfT0+KQRkI3xlOiA/NAV/CEBsai8IEGclajcqERphPwENETYvPxUpcm46Cx1nLjNwOzw/JwQpeSJgOl9GZwZnPx5m', 'IQUYPDkyEgoHBABEUBFgNjkHERVkFCgCIwgKCSAyKS0vcz0UOBoHM3hlCxEccCUeZhxydg14WEZuGhF4DjENCzI3aGweFAIOKCAn', 'MQt+d3gyGQMzCCw9JBMTESQ9O3UTPBgGNQ54NR4Ke2IDPQ85HCRgZQ89DT5oSFlaBWhgGiUxOzkwHQ47GgQpLHE6cAMqJz1uNi86', 'bD8yL3lqKA5wAgUYIxNpISBkYHAUJhAMBQ0lCzctcH1zRmwxC382NzAVdgI7ERV5eBdybD4PBHN0KBscA3cMcnANPSohEXwmDj1l', 'WVxyOGB8Nh1waSk2Oy8rIxkPbitwfjk0OXIjZjIfDGYSFDJ+diUqMRY8HURkUAwJMAViPQtuAXJ7GCRxNQ4dEmYqdw0MdiAeIyo4', 'FTgwEhY7LXkxAjwWK1hxYlYZET97G3geOisNBD8NfTIYNmYQEHYgBzxrBn1/OwETZzt3JSUFfHJ0d115eRF+BAMBcGo1dwAFMDIA', 'NH1yECdTeXt6HjEmPTYuEQknEzMZan12Ajx0DAYLfHcfMmdjFh0AdQ0vHDg8DggxDUUJVXcoIyN/J3gHGjMmAAg8ODs8FSk5DgIS', 'KBkpJAJuICgBbA4gPDl4ejseBGwcSFdhUykXZ3tnKXA9dSYaNXwcEDgzDCk3diEXOm8zA3ljKCwacHh5LRNqHjcBBQFYJx1jGgg7', 'Nm4dEAgkeRcCcBgmLBwhaDYdHnceLRVoaHdIC0N0OQgDFXYsJzQWHzkHezYRAzsQKSYbLQE6En0cAg82JTMXIgsqMD0Zd1loRx1i', 'FTcNBGY4PGsJP245XGJgUmttJDw3cTs+IwE+MQsUKWkAbDkrbAQjBQ0WIBcgN3Y8GXx+GC51CWd0fXxcDmIqPj5uNGY8Gy1lcCYx', 'cXQxNAYoJA5qMishER8DFS43NyIsGQcDMTdhMAAHFScCCCk7bHIkPDFVR2p6Fjk7MTEYLThzBBR9NAUgIxsVMAk3LBBgNykGIgV9', 'Zhk1LQh+CFRDKW0iMRxyD2d2cRJqcDkqKCA0eRMnOWoIEyo0CGISdXsOBwJpHQggbWtYAWMNEzo7ZgclBwAMOiMneBkZKzkSCRIu', 'LjsnLxR9D2UdZT42N2oGYQApPBQHFSw0HUdGHyYZNWZ9GCUfcGwjO3Q7NRQ+Fx4sJnhwY3AZNABjHTYZCyx2ZhoHNBV9BFVENBEC', 'YjV8Zw18KztGfHRFC2wZAGNwBRsjdjg0B2czAgssDywTJgp5LiZrH2MGNnsOBR0ZMTYXaX4HSlpqFnUVN3kQO3AmDBU9NiwidhYQ', 'Fzo7Bzw7OAVwCw8AchUVAGotfhEJGj0YbGliKWV0ORwAHTYXMQcjOFUfS1kdJz5ieRsNYAYPYiQCdSY8CTkgETE8IGoNGHknN2oH', 'Fj8tGHg0CBELNycGASIjdRcvJ3crBCghaHV9V300ASQQOXZnZgMtLQoNDXYOYGwdMAh/cz4POR0ZHz8LPyoNIyciKTcrZ2BHBmcs', 'CyoTITwROWpiXl4UbBkFMSVpfBI2GghudTs1a288Mwkyah0PYBcWER0THHp8BTtgPAtmAl9WbhYjKxk3LyYrJ2xiI3QEKzYBEns5', 'GAhxYFZMHTolGzMbbTUTE2VqNBsncREqOmoyPAkmHCZleCd2OhNwBHwqDyJuJlRTelsHIQk3YnIUZRNsHAEKBHAdFhctGy9kAiUM', 'NA8aIXw1AiIdJzY4KjgNNRYdWl1uDyY4OgFqDxd0ICVkfD0PYj9mOC0sEx83MRZhHBUzGTkTI3guOzNrEABSB3M5EiohPBcHPHZ1', 'BCV6ASFrG2YcfSISEWx/dnZtCSc6HQdEHVtoZAAMOmpqOy83MxMsHG4LAA8fNXs4BmEHGzgBMRUQJyc5DC0/LAwnVkp9YzwUYzxr', 'HwcUcwE2GkRoCgcJHho+JCktOQQJEmorOC0zFBcfDAIbDzEaMxx/LBMLZAd3OXA4AWw5QlJ1eDkiIA0gczRqLHJmEHMUcjM9Jz8R', 'cgFOBzAGJ2cuOT8uNBMjMToXPA5oCxQiBzATBRhlCzoEei4fJ3YQLQoKKgFmVXA5GCYhGjJsEgcBOBhqKw0XC2gPLzEdLyUQYH0a', 'ETo8IRcTfhAZZmBrd2ERbAQCKhYwEyBpCkNFen41BTsiGRRtZB0HOigCKndxcml5MDU8bmQwAzw7PjMmEyYFeQ8kfGgbQHxTWjkT', 'PR42BSNtKC5/ORMHPCIGIygNAxAyLA0rAB4+NmoOBEBWdxE3BC4FJ2oRChUYA2onEj4xGy9yIhgUATk5YTdkHw0CC2B8B3gxLDRG', 'KRMlKy9wP31sHgp5RQBtOjs6Ngo7NnwlEiccdSQMIQcRJykicRVqHWp3BnUlBAYlLCc/BioqQH9nYzRifjZrCwk1HQ9gGhV5Gzho', 'B3ANMDkdIzAeEyl6ASwmOQcNJBgpSwNBBxUjLRx5KWxmAHcfJHBjGCluHXE4JicqKDwdawcDETYMPncHDxE/bm5beANsEwcif2Mb', 'NzoINgkPEjMbMkEAV3pmGAEZBHk4CzYXZDQEOXdtEho8BxccagY8BR9gBiFsIT05C2YDLj0Pa1sFRAkCfhcCDi81dDI6PBQONjNr', 'ZBgfHnYNESomeww6BmQgbScZSGJbFDsiGX0HFCATCXpgNxR0KwEOGRsWYDUVD380PxwRJWUqG31sLycWGFcJfmF0ATgeKAltOidy', 'HmchCBgVLw8tLDM7Pio3PSE9EGgYHRYBGwcIPBoPejNpHFxEZ1c1P2dgF3QPPygHJyUGeBsWGiYmOBohP2A1Omt2JApzEA0pKQl7', 'MToGeScTc3IaeQYjPA0fbzFZVmtEbGN/P2oVKxwQdz8AIgB3HHY6Di4qeHIgOGILI3sQLDYPHAUROTQVHkEEWVpqOAYlO3EmPm46', 'IHUWcEtdeVBsYD1sHhQFMix0PihxH3YtFWl9KAEZMBM8KRV3YB0uIHkbKAUqKDpsZQJBcTwjBj5lNighLip6EHYNJ2gxPR8kGiEd', 'f3QBID8bN2d5IC5qClRHXDIjNhozA2lqCXIcPxx/JjxvaDs0OXshKDRmCCptEAVteBoaah8vODEKZHAAbBkOPxA5Lzw2LHoiKTg3', 'GSIeFX95IH4HBwk2JTotDDAuLikXFSEsMyZ5dh1edHoiARVqKCoBKxAzchhuGSoVZxEaMSAcPDYoexoBcjh9ewYqCnJ2E0hgRHFm', 'Njs/FTAUEW0nOw9vESMPNWUAdFw0bRU6JSsQPRUAYRozBxRoASwmKSkHAmNpGiYmGgwNf3E1HBt+Ij8YS35zQiokGhc8MGZlLgFh', 'YBEoYw0wABEbMBwhBgwDLygHfGtwKxchITRxLykLAj0lPXgCbBJmODcmCSZrNQo4GmQ8EyIiFwgbADVgb3B0CmY8OXR7ZyMSESwt', 'CAcNKSQdexEbHSEwJCM4ADQEdWkYAFRDZTgleSw/cm4UCgZ+Hio8JzcYKQduFR8tHT0VJB8YNzJkIzklO391LQdcRGQCPjsZPCh1', 'BXQ9Hx80FmcsOyx7IzMhHRl5Ai1qfQNVeWcWZy4jDBQbH3AdYyl5DSpvETEvDDswIRIUYCgEKiwjE3cKCHwyHSlzRkMfHBkLHmp0', 'ES8ePH0rCj8POA4YAgMSKHtiLXgvKXJ8HDYHVQdEaywZewMlcDFxLi0DDSgjNy4cEXN0DC8/GgALLiN0cnsaIntwOQAqOVdbBQ1v', 'Ggd1EXk3NCAQdic4MDEYPzgNG2oOHnAgIxMfGDkOci8reGBGdSVjAgFieScFdSIeEHY2BxJtazoMdyxyNxE6CnxhPAF7O3coMjMs', 'IH0EJhkwDn47ahsdIQ92B1UEMXoVOgh3DWAgMz01J3s5ajYxPxUpAj8VbB4oCSIjcgUlfHkLB3AXCmRWXlopAQINYyRmGDQTDGAL', 'CiQ8OX13Ix0UJjsADQgADGwSHDAIewp+PHZ2InEpIBFgcSM9e19XTWY9egUGDTUBdnswPAhnLD8UGyoGIhMKPTsSMC4YfCYBAyEA', 'ewAhPRsYNm8WPQkTLjZlLGgXAhYrJiY9IBo+MRc/GQF8fAMOFzQuGzk1fCoBAQsgNCgzHzUSKyAAIxkpFjcnPikJNycrDAc+DWAH', 'M3kcMT0qLQgFEmILfSshMy47V0B7ejstOnsXdRUUFwkiGx06BTMxbnE2OQMUYSsUIAcEFHYiDT8jbT02ajdxf1psBg9nYmI5OWUj', 'DxUBcAwJJX02Fw80OQcsMCxiEBodaCwMKgphR3tVDTMIIWclDHwIMBIDcR0FHTYeYzUZBC4aa38xYHsEL2YKfxdndg==', 'HRMPIyMFYgMWNSt7LRY7KgttYWBzGyglAA5gcRgxACU3Cz0IbhwjHnB5MAImeW1kPHhkdTNhESEvNn4wCBliWlRaCnp9Fis4DScE', 'P3EGF2Y4Iy04QAlhbB16DWBmJWYbNgAbFREcDx4gbQl2AiEVMTohCwIxdCkwCAF4aCInaRwZB1hOEREPOx07Jzk/DCFnKCgWFx50', 'PwwlECczNiUINgAsbQwQcwsnAyItM2oBJSsMAic5IytzLwAPQlNxBzMMf2Y+JxU5AysBOAoWchdsbRAvEx13JxkgHDgcNRU1AGRl', 'D3p2KTdoeX0NEW0lbARqaGMUdGV9NwsiKh9rDHMwBzIaLiABfSMDGyETIiZsBDcjHHpyAGYtBTwwOQALIQF2MSUSIAcuLnB5agwF', 'Mh02LR8FbWAqNTRiE355bSMmGHcGDW4ZDzckOxkrNzYQYBQSZBY8HlgBGVEHOX8SFjZoJS4lMWorJnNxGmZjBRotdj1vNTwjYHY6', 'dS4jEC0DZh8PLTtFXEZ4aCcKMiAwB2QnLCFmbn4LFz5rIig3fywgbAh9CTchCmMjYH8zJT9gK0tARVtnfn4jeSwOMS8xLzg3CzQp', 'dwY2MDJtBAp9cTBvN385DSN8OikzNwgCFXt/EwgjCCpyPWp8VgNkNzAdOj0sKGoHLWcVP34WGwkFPREtDX1mLQIqfSxuNAU+Ljgb', 'RQdbZxYCGAY2JyEkBjoWAS0NLA0xLQZoJTEebBY1GWcyJj8FISk8eQ06GwR2eVVtFgMsYXM1NiYIMTgBJAU8amsmcQBgMiA4IDQK', 'IS80Jgc4bQFhJAAJMBsjFg8Vcz1sMnViBQwvMy1iMCAmNjMkYiIVHzsibjIHNg0uHxAKYGR5JhAnYCJ6OR0RLAkWW15jDRcFHjYB', 'ejUKGCc6KzkMMwUeHGQrPjd3FScHfjg+ITJrHUprbQcxAxxjcTwbcQEdKisWKGMYbD12ezE1IDoRKnsefAUGLyoCLXwpNWhzCGQD', 'Fx8tci0WFQtbVHcnPmNtYBhveDc1YhEVPjMsaXAicXMEciMHCj4BYSIXLn4sKXAgbnY3SltGexN+FQQLMG0hdTZ6BA0abixraC8A', 'A2NxKQE9LAM7JApvG1dnQ3c1FhZsGnglBAQHBX0zYw1tLjMnagEhMDM5M2UkOy8Lex0pHxtkBC03dVd4Hy0hNjZkEQ4cCy0vCi0J', 'Yih4AxQBYC0/JzA+J3IZClEZZjV6BD8aM2ZiJDUPBDQeeRsDdANzEwUJAjM2FX0McBllewA0CygxKhNEV3BXEGx4Yhw0GBgDIT4d', 'MCMTK3cKGw8YaR0LGCcfNxY6Ax5pGR8eEVMCSl9qJwA1KHUOFSBsIighfCJjLhA+C3EDcwJuHSMfZ3JsPAQ2ASdzHXIIB11GRBoh', 'BRYYEjkAeBIGMhoyMiYhYBAWM2YpejF4HzpqY0lGfhdjPmwVGTMdKCYWCnEgD3EhNAslDX4/HCUTYTc+cnNsJwEdMmAhNx5wXAJz', 'Dzc1eQ09GTcNeSczPCtqFAoIWXBtBHRgChA9BgIILWtyOSwZchFwcjl+DTsvaT5kAnA6ZWIHZTx9KC8ycHxiGyssPCwQcCweKiJg', 'FzclHnM2egsfIwsvPAw9dnMCJBczCTQfZggSezgEOiUNIAAWfAgDemgAPS0iJDopNXs4MT85Ai42HT0JCXwvJDsnajkAIxsgLgE6', 'KzAVOj4rM303eRM0GCIRC3UfBzomMzgrYTl8VgZYbiQIYmoMbiR9LWAncS55HgknfhRsOSBlZxQkGT0zOj4dKglmM3UcEXloAF45', 'HzohPxJlCwYdHRdsJgEAchAoBToPFgM/CzokfQxhAyliPi1hCgQLI2xTQgdDLTx/Lh0gah0hKDo8L3Ruby48CSVoGhYqbRoaeBgc', 'IH0AEyQ7Lwx+KCkWSwlHeTkSITo2cDocKDYdZR0fai8XZgAzNCMLBAYTfX8DIHoceA47LQ0BKwV1XkYfNG0CMgIbPHwdNDA4Lj80', 'CSkqEiIMOkpRC3AXAmcZY3c4NDZwbBNyC3gsK2YEciUSA2UmFB08FRIZYDh/Bz5+KWsxXV9bAjxtDzUBOz4CdgkFOhQ/JmMvai0w', 'BB8tIRUtBAUKARImGA8vMgJnCgcBLR85GwR4BQolagtwWVYGTmoUHD5hGBYydHI+ND0qMQIXLQY4FXgKOgwYYQB7c3NtGWACKj8u', 'Fws2JwJtfQgpcQoLPTEmcBhuI2gCIC0jCwctfhUqaS0uPztCV3p7Jxo5HwE7MTFwKC85A34tMG43fzsLHwBqa2hgGSR8aBF4NTsd', 'ejAZOhNlIBU8DTAYKD8HLCwOEHBaBl9nJHoAahc6GBcRGTc/P3Ucay1jBiYhIRsTJzU5G3AxMAsLPQt4cQATeFRkdTljAzwmczMi', 'YEQKThM8NW0kEwshHDsBKxcLczg2LjpzMgYzKhllAQISChBjPXk+OQIRIRNiZR1jKmFjDTNyaGYoB2ETIXULEjYbKTg2fwMhZhQ7', 'LWNrcgImEDc+GSAHIysbdDwiPCwpNRsieWFnHnBtbFNTdwwdbBg5axYUNxF1Gx0WeysgCxodEmgsfBAJZ2ZgLgM2Jwo1GCl9J3J0', 'DnE3MgMFDChyKxQVZhd/NAUgMGQMCHItLG5Kf2gfBTljBSY4PAkMaAdlExkzCy9qCQkmIHcCByk3GjcmKyEwF2EWeR8aPF5+Wk1o', 'eBUzMjxmLRlqHBJoFx0CW34GHhxgJic9Ki92NxlxGy4eaBwadnIkKWAaYhoBDjICICM9ZTAGDSMsS2dIYxICfhAmNhE+H2gDJisl', 'MWssCT4mfR0KLQ0GDDkCfCA5L2crfD5zdjA+Ky09cgcpOwt7V0NoHwsyMHgsFTUPBWRwBC4sHSkBMyJ5KjZoEgIiHyYiPgcMd3AP', 'Jwd1BHAKORk/eQMVKT4ZHgpwOy8BFh1SBkEMGSsgZDE1HnwhIwd9Hw87bDlndy17KgI3MyYnMHQoGwN6JTNgFQw5ZHxIDBBhADk9', 'AmA7Gh13GHEIKDFzEyoZWEVWBj1nehorFTMbNy0nYmo4FmIdZikWFS8CZxkqZX09FmgGJzUCD34XbxVESQFwLWUKIwcOJQJyOSZk', 'AisnMRM0Kg85fyIKJT0HNTokGy0md3sxDQ8DM31XRwELGHQ+FS4RBX0bHGcvDiNvDQs8DDANcDw1IzEnMAB0HC4DPDk/KzgHdFNa', 'aDA6LGQrN3wvDyAIKAIuECMrJCATfiQXCiIrHzkGChY6CxRoISsuF11jCnElYydnPDltHgcpETQKOBEDExAKJGwhPTMNNBEqe3Yq', 'Kj9lP3sGBn8AOBEGNHRVVX9BLBQ4fxxuDxEKbABlKA5uKmgKIDQzGB0IBjlhfH8NETk6fnYHPnAhLGFSWgYFIzUNOCsnBGobFgtu', 'eTE7IxAgGRIudR4sEgYlHXw7ID13JC4KHz8VCx9mejE6Jm0bNBIyfS8jPzZjeS8VCSo0FWAuH28WJyMiPDYFHWQcHTEMATdWA35t', 'OhsUCy1uMDUreTICMjwaFio4PTswKTt8ISslLSYaKXZYVkEuJxsRNBNqEQdyAmYndQYCPmxnABBkBmtsZSUfAXJ1NXgOdycTPDxn', 'P3wueQk6BQtyYRASDQAAKXARIC4jLh4WEyMVPgYRfwwFJHQbbmkRe1hIBxEHezhqBxc5EnUACCc+CjcJGRIHDBI3AmkaPy4YCBE8', 'GSwQM3QAIBQLPyk6ODAoFxkCLAQMBQQifAMvOBUsDn1UZFcXYA8QMw5nfBETJjg2YyhxIz0SJREtbggmJwcLey4wMDB5CBglFD1s', 'dzonCDICKzw3EQtnARAaEAM8bgp0MD43EQ81NxsCNw88AQIPBRIyajlDW2p5bRsiYWcyDh4cIRcDcStzIGttfiU0HSgwMRU8NR4O', 'EjweDGRiQVJ0PDY/FiUSCRAtbWUEPjQOayc7cSU9HRowHWV2DAIsfxx7Fyszbj1pa0gGQBg/eSIqMjMydik5Pm40LyluCgl0OT00', 'Ng9KWUVWESIOLDhuEgUHcSQ3HyUNGDQYHwwODTUnC2F9DTJ2CjsdOjoPIGppbHN1HQFwDWdkInIYCTIXZzQ9dQAsITc7diY+NwcY', 'BRltbQYtY2E3FTk3aBw/cC9zcRdtcSU5O3Q7NRV5Gzp1ExBwIBooHCMra2AfdwI7IRYiFCI6Kj0vYHk9Iw4OEy4yES9kdH00KBAA', 'fgYoFAgefhd3IWEGFTxtByMzC34JB3MwbH41fTs7MQYFGgEyHwkKbGoKOAx4CCV0GQo7ZQsTGSE3GhAnbi0VWUpeWiYCfWY2ajQH', 'bDBsCjpmIBdjMQVteQx1dB5pHX51NhomZColZyA1Kw03JDV6Mjh3FRpIVkZwNBR1MBQjbQd1AiI3cD9xOApofwcmLGoFFjkHB38Q', 'NSwkfTthLBplLAduOiwCCV5aOh10ZD8QGyITeiQCCCZxDDotLQp7KTBiO2gaIxcTDhsoIT4PJSFoD2IGQ3FvEHlnIhIdPCEgGBY0', 'HBEEFR4oFiAJJQ1qPzQgHiINEgoiKxgBGxEBIwtUW19EMiZ1bDo0awBuCn4LFCEqYxESLDdzGnIrMWcILAF0OyAuI30nIw1ob3RU', 'OS8tA2cjFgd8KzEndzZzACMsLgwyPDxrKSMlezlwG2M6GwFuDwxvJXpSQWwFGio8ZylseDMaYQQgOTk4bmguBSQzFQUZFyV9FzFw', 'Jh4ZNh8gLnAnA3AjCTQtXlp9ADc2AGETDycSPRkeYCMcdhItMgQ5NRwXAhsyNT0wCyoEEWQebmQIIztwG3tQCBI6bQMCcB50dDMe', 'f2kMExopdwUZbW5hCGZmNm0/AzMzPCsfDG0PHAAjE30UKCoVGhs3MnohOmA3CTIKbip8fXR6JSQBLAN1BwsXAGdhMwk0CR8+Om4m', 'PHszIytsJn1lMnh3A2tfSVZFKC88N2U1OxQCcGA9N3t4MCsdBxkme3MCLiJifGYdewMsLQIpeCwWNx17GUQRGy4BOXc7Zx8xHjwg', 'AnY/MioGYSoZZwVgbC8GYmIOBhkyB2UWPygyDi8rMnc6YHNjNzdqLDUTBXsMNiMYfmoXNEhxWwE3Mz4WGCQbMDJ0FzdyLRhxEjx4', 'KTdoJVBGAgcVByA7ZBVnMTUtPwVqK25xPid6anUkcSVwIBEbLjUNIDo8eScocC8WSglUDTBhO2cKIBgaMjItfSE2KysAEgYsaD0E', 'alkRLyQ1BnUaFW4JZjl8HisvaWwvJzoICWsLAx53LXIWAAh9ZR1+Hxc8Y1p1VwkFHDETeAgnMy4RJAsWeDwBBRAyeyIPJRU1CGQz', 'CWorNQt6AjY4dRtuPAJuMS0UPWgICHcecCR/DSktET0NPBxiH2EHJi18HTMmdGsBCzBnEyo5LCEYKXNsIiEfbWkrImQVNxNmGwpu', 'BCAXbT4NdiwjdhI5amBbBzoQKnszBGY7LyJ+agQUKihtOQJ0GzkLEQUAJgwwcBQRDAAIChENFj1eAlV8bSEcEjwXKzsxFGAkHScj', 'BCcmZR8+cTQsDxEyGX4zAi5ofQcSD3AzLHc7aDwrPGlgCWtxODt5NjsoJjcjBxliFToRIghrCSQkLi0wDmhiDhwXK3sMOH8+OXBr', 'PEJJfHsmOWcFKHR0YDYvPxodeQkULg98BjB6fGEyHWolZQQ7Nj8md2ozMiB0BmcdZ2ZiLiIZFR5ldDsiCiN1dRIPBg1qcQ13IDoj', 'ejoPIwdtOQdEWEI4IAJgNyksZTw1O2AnCDIXLDhwETYZJhdqJRshZQQtPhoNHjUDDDIlBltZZnAdOTA0JBI1PAsheQR9KQ5hCWMp', 'dnUXNnkcKBEJKTYuD2E8dAksMi8QIDciahw3Nj47DBwbJi4FKispPBY8W3cLThIsDQRgJA0Kbi4aJwljGQwJCRosJRkPZTIEMBpi', 'IAYhO2knDTA9YVkdQ3AZGjMCdi58dHUwNjUKLwsPDyxyL3IXFwkYYXltLzYXEwMqZyh3NQZ+U3FkMCU6AmU7GDAODD80JD0PFCMP', 'cTJsCHILaiMSGz4MM38MKxFkPjcVdTcCKzUGayUCbAcEQxlsKywvBj8QJWonMhsKExk5MyMYGgdsMwJ9EzU+ATpyJjYMeHYHP3I7', 'cx1pB19CXV86FCl7EDEuPzEtPzFuJWovDRtwFAQmJ2IWITMrJCcxZgMpPGwIdhdwWVpbAzJ6Ph5kdmgBdgAbFQItNxc3cB53cigq', 'PB9ibjkCfGVqDSM7dhRnJjNsB0hbVlkbJSNgKHEQBg0FGhpyAC4+Ixg6LjZ7DBwFPjg6BBYQA3suIi4fLRUwfAJ1fDQ4ej0cJg4x', 'OhZ5aj4BIDowNCAHMWEyBysvATU9LTcfLDEhDCw+BSAmAmoXMl9bQEATfjhkESMROiE6fgMddAsZETcAJzsECAtmHxQIY3wnYQ8L', 'DQVmITtnJiIEJiN9ezAWbQV7C2h4IwBoZHkHP3QBJRwNIRcEEW03Bgl2VS47Oiwid2sRdAUgeS8jbjA8PTl2cz4PBCcoOiIaLycj'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
