INFINITE DUNGEON CRAWLER - ASSET INFORMATION
===========================================

This roguelike RPG is designed to work perfectly without external assets, using ASCII-style 
characters and colored rectangles for a classic roguelike aesthetic.

CURRENT VISUAL STYLE:
- Classic ASCII roguelike appearance
- Color-coded entities and items
- Clean, readable interface
- Retro gaming feel

VISUAL ELEMENTS USED:
===================

PLAYER:
- Character: '@' (Yellow)
- Represents the hero/player character

MONSTERS:
- Goblin: 'g' (Green)
- Orc: 'o' (Dark Green) 
- Skeleton: 's' (White)
- Troll: 'T' (<PERSON>)
- Dragon: 'D' (Red)
- Health bars above monsters

ITEMS:
- Weapons: '/' (Color by rarity)
- Armor: '[' (Color by rarity)
- Potions: '!' (Color by rarity)
- Scrolls: '?' (Color by rarity)
- Treasure: '$' (Color by rarity)

RARITY COLORS:
- Common: White
- Uncommon: Green
- Rare: Blue
- Epic: Purple
- Legendary: Gold

TERRAIN:
- Walls: Dark Gray rectangles
- Floors: Gray rectangles
- Stairs Down: '>' on gray background
- Stairs Up: '<' on gray background

OPTIONAL SPRITE ASSETS (if you want to upgrade graphics):
========================================================

PLAYER SPRITES (32x32 pixels):
- hero_idle.png - Player character standing
- hero_attack.png - Player attacking animation

MONSTER SPRITES (32x32 pixels):
- goblin.png - Small green humanoid
- orc.png - Larger green warrior
- skeleton.png - Undead warrior
- troll.png - Large brown creature
- dragon.png - Massive red dragon

ITEM SPRITES (32x32 pixels):
- sword_common.png, sword_uncommon.png, etc.
- armor_common.png, armor_uncommon.png, etc.
- potion_health.png
- treasure_gold.png, treasure_gem.png

TERRAIN SPRITES (32x32 pixels):
- wall_stone.png - Stone wall texture
- floor_stone.png - Stone floor texture
- stairs_down.png - Downward staircase
- stairs_up.png - Upward staircase

UI ELEMENTS:
- health_bar_bg.png - Health bar background
- health_bar_fill.png - Health bar fill
- inventory_panel.png - Inventory window background

SOUND EFFECTS (optional):
========================
- footstep.wav - Player movement
- sword_hit.wav - Melee attack
- monster_death.wav - Monster defeated
- item_pickup.wav - Item collected
- level_up.wav - Character levels up
- potion_drink.wav - Using potion
- stairs.wav - Using stairs
- ambient_dungeon.ogg - Background ambience

MUSIC (optional):
================
- dungeon_theme.ogg - Main dungeon exploration music
- combat_theme.ogg - Combat encounter music
- victory_theme.ogg - Level completion
- death_theme.ogg - Game over music

IMPLEMENTATION NOTES:
====================
The current ASCII implementation is intentionally chosen for:
1. Classic roguelike authenticity
2. Fast rendering performance
3. Clear visual distinction between elements
4. No dependency on external files
5. Easy to modify and extend

To add sprite support, modify the draw_entity() and draw_tile() methods
in the Game class to load and display images instead of rendering text.

The game is fully functional and engaging without any additional assets!
