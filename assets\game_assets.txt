UNDERGROUND TALE - ASSET INFORMATION
====================================

This Undertale-inspired RPG uses simple geometric shapes and text for a clean,
minimalist aesthetic that focuses on storytelling and gameplay mechanics.

CURRENT VISUAL STYLE:
- Minimalist geometric design
- Classic text-based storytelling
- Color-coded UI elements
- Clean, readable interface
- Focus on narrative over graphics

VISUAL ELEMENTS USED:
====================

PLAYER:
- Red square (8x8 pixels)
- Represents the human child's SOUL
- Classic Undertale heart symbol representation

BULLETS:
- White circles (various sizes)
- Colored based on attack type:
  * White: Standard bullets
  * Orange: Tori<PERSON>'s fire magic
  * Cyan: Sans' bone attacks
  * Custom colors per monster

BATTLE INTERFACE:
- White bordered battle box
- Black interior for contrast
- Classic Undertale-style layout
- Menu options: FIGHT, ACT, ITEM, MERCY

UI COLORS:
- FIGHT: Red
- ACT: Blue
- ITEM: Green
- MERCY: Yellow
- Selected options highlighted

MONSTERS (Text-based):
- Training Dummy
- <PERSON><PERSON> (motherly goat monster)
- San<PERSON> (skeleton)
- <PERSON><PERSON><PERSON> (skeleton)

OPTIONAL SPRITE ASSETS (for visual upgrades):
===========================================

CHARACTER SPRITES (64x64 pixels):
- frisk_overworld.png - Player character for overworld
- frisk_soul.png - Red heart for battle mode
- toriel_battle.png - Toriel battle sprite
- sans_overworld.png - Sans standing sprite
- papyrus_battle.png - Papyrus battle sprite
- flowey_neutral.png - Flowey's normal face
- flowey_evil.png - Flowey's scary face

BATTLE BACKGROUNDS (800x600 pixels):
- ruins_battle_bg.png - Stone ruins background
- snowdin_battle_bg.png - Snowy forest background
- generic_battle_bg.png - Default battle background

BULLET SPRITES (8x8 to 16x16 pixels):
- bullet_white.png - Standard white bullet
- bullet_orange.png - Orange fire bullet
- bullet_blue.png - Blue bone attack
- bullet_cyan.png - Cyan bone attack
- bone_horizontal.png - Horizontal bone sprite
- bone_vertical.png - Vertical bone sprite

UI ELEMENTS:
- battle_box.png - Battle area border
- hp_bar_bg.png - Health bar background
- hp_bar_fill.png - Health bar fill
- menu_cursor.png - Selection cursor
- dialogue_box.png - Text dialogue background

SOUND EFFECTS (optional):
========================
- menu_select.wav - Menu navigation
- menu_confirm.wav - Menu confirmation
- bullet_fire.wav - Bullet creation sound
- damage_taken.wav - Player takes damage
- monster_hurt.wav - Monster takes damage
- heal.wav - Healing item used
- spare.wav - Monster spared
- victory.wav - Battle won
- text_advance.wav - Dialogue advancement

MUSIC (optional):
================
- ruins_theme.ogg - Peaceful ruins exploration
- battle_theme.ogg - Standard battle music
- toriel_theme.ogg - Toriel's battle music
- sans_theme.ogg - Sans encounter music
- determination.ogg - Emotional story moments
- menu_theme.ogg - Main menu music

FONTS (optional):
================
- determination_mono.ttf - Undertale-style monospace font
- determination_sans.ttf - Undertale-style sans-serif font

STORY BACKGROUNDS (800x600 pixels):
==================================
- intro_mountain.png - Mt. Ebott introduction
- underground_entrance.png - First view of Underground
- golden_flowers.png - Flower bed where you land
- ruins_corridor.png - Stone hallway
- toriel_home.png - Toriel's house interior

IMPLEMENTATION NOTES:
====================

CURRENT DESIGN PHILOSOPHY:
The game intentionally uses simple geometric shapes and text to:
1. Focus attention on story and character development
2. Maintain fast performance and smooth bullet-hell gameplay
3. Keep the classic indie game aesthetic
4. Ensure accessibility across different systems
5. Allow players to use their imagination

BULLET HELL PATTERNS:
Each monster has unique attack patterns:
- Training Dummy: Simple left-right bullets
- Toriel: Gentle fire attacks from above
- Papyrus: Bone attacks (vertical and horizontal)
- Sans: Chaotic multi-directional attacks

STORY SYSTEM:
- Text-based narrative with typewriter effect
- Choice-based branching dialogue
- Multiple endings based on player decisions
- Character development through interactions

BATTLE MECHANICS:
- Turn-based menu selection
- Real-time bullet dodging
- Mercy system for non-violent solutions
- ACT system for unique monster interactions

The current implementation captures the essence of Undertale's gameplay
while maintaining simplicity and focus on the core experience.

To add sprites, modify the draw methods in each class to load and display
images instead of drawing geometric shapes. The game structure supports
easy asset integration without changing core mechanics.
