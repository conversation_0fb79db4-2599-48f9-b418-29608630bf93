#!/usr/bin/env python3
"""
Test the orange theme login screen with all requested features
"""

import sys
import os
from pathlib import Path

def test_orange_color_scheme():
    """Test that login screen uses orange/black/dark gray color scheme"""
    
    print("Testing Orange Color Scheme")
    print("=" * 29)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_orange = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for orange colors
            orange_colors = ['#ff8c00', '#e67e00']  # Orange and darker orange
            orange_found = sum(1 for color in orange_colors if color in content)
            
            if orange_found >= 1:
                print(f"   ✅ Orange colors found ({orange_found} instances)")
            else:
                print(f"   ❌ Orange colors not found")
                all_orange = False
            
            # Check for black/dark gray colors
            dark_colors = ['#1a1a1a', '#2d2d2d', '#404040', '#0a0a0a']
            dark_found = sum(1 for color in dark_colors if color in content)
            
            if dark_found >= 3:
                print(f"   ✅ Dark colors found ({dark_found} instances)")
            else:
                print(f"   ❌ Insufficient dark colors ({dark_found} instances)")
                all_orange = False
            
            # Check for removal of old blue colors
            old_blue_colors = ['#1e293b', '#334155', '#3b82f6', '#60a5fa']
            old_found = sum(1 for color in old_blue_colors if color in content)
            
            if old_found == 0:
                print(f"   ✅ Old blue colors removed")
            else:
                print(f"   ❌ Old blue colors still present ({old_found} instances)")
                all_orange = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_orange = False
    
    return all_orange

def test_scrolling_functionality():
    """Test that user selection has scrolling functionality"""
    
    print("\nTesting Scrolling Functionality")
    print("=" * 33)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_scrolling = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for scrolling components
            scrolling_elements = [
                "tk.Canvas",
                "tk.Scrollbar", 
                "scrollable_frame",
                "users_canvas",
                "xscrollcommand"
            ]
            
            scrolling_found = sum(1 for element in scrolling_elements if element in content)
            
            if scrolling_found >= 4:
                print(f"   ✅ Scrolling functionality implemented ({scrolling_found}/5 elements)")
            else:
                print(f"   ❌ Scrolling functionality incomplete ({scrolling_found}/5 elements)")
                all_scrolling = False
            
            # Check for horizontal scrolling
            if "orient=\"horizontal\"" in content:
                print(f"   ✅ Horizontal scrolling configured")
            else:
                print(f"   ❌ Horizontal scrolling not configured")
                all_scrolling = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_scrolling = False
    
    return all_scrolling

def test_keyboard_on_user_click():
    """Test that keyboard shows when user name is clicked"""
    
    print("\nTesting Keyboard on User Click")
    print("=" * 32)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_keyboard = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for keyboard functionality
            if "select_user_and_show_keyboard" in content:
                print(f"   ✅ User selection triggers keyboard")
            else:
                print(f"   ❌ User selection doesn't trigger keyboard")
                all_keyboard = False
            
            # Check for show_password_keyboard call
            if "self.show_password_keyboard()" in content:
                print(f"   ✅ Keyboard show method called")
            else:
                print(f"   ❌ Keyboard show method not called")
                all_keyboard = False
            
            # Check for NumberKeyboard import and usage
            if "NumberKeyboard" in content and "numbers_only=True" in content:
                print(f"   ✅ Number keyboard properly configured")
            else:
                print(f"   ❌ Number keyboard not properly configured")
                all_keyboard = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_keyboard = False
    
    return all_keyboard

def test_language_selection():
    """Test that language selection between French and English works"""
    
    print("\nTesting Language Selection")
    print("=" * 27)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_language = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for language options
            if "['English', 'Français']" in content:
                print(f"   ✅ English and French options available")
            else:
                print(f"   ❌ Language options not properly configured")
                all_language = False
            
            # Check for language change handler
            if "change_language" in content and "<<ComboboxSelected>>" in content:
                print(f"   ✅ Language change handler implemented")
            else:
                print(f"   ❌ Language change handler missing")
                all_language = False
            
            # Check for language variable
            if "language_var" in content and "language_combo" in content:
                print(f"   ✅ Language selection components present")
            else:
                print(f"   ❌ Language selection components missing")
                all_language = False
            
            # Check for orange theme in combobox
            if "Orange.TCombobox" in content:
                print(f"   ✅ Language selector uses orange theme")
            else:
                print(f"   ❌ Language selector doesn't use orange theme")
                all_language = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_language = False
    
    return all_language

def test_orange_button_styling():
    """Test that buttons use orange theme properly"""
    
    print("\nTesting Orange Button Styling")
    print("=" * 31)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_orange_buttons = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for orange active states
            if "activebackground='#ff8c00'" in content:
                print(f"   ✅ Orange active background for user buttons")
            else:
                print(f"   ❌ Orange active background missing")
                all_orange_buttons = False
            
            # Check for orange login button
            if "bg='#ff8c00'" in content and "login" in content.lower():
                print(f"   ✅ Orange login button")
            else:
                print(f"   ❌ Orange login button missing")
                all_orange_buttons = False
            
            # Check for orange selection highlighting
            if "widget.config(bg='#ff8c00'" in content:
                print(f"   ✅ Orange selection highlighting")
            else:
                print(f"   ❌ Orange selection highlighting missing")
                all_orange_buttons = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_orange_buttons = False
    
    return all_orange_buttons

def test_modern_styling_preserved():
    """Test that modern styling elements are preserved"""
    
    print("\nTesting Modern Styling Preserved")
    print("=" * 34)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_modern = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for modern elements
            modern_elements = [
                "relief='flat'",
                "cursor='hand2'",
                "Segoe UI",
                "shadow_frame",
                "glassmorphism"
            ]
            
            modern_found = sum(1 for element in modern_elements if element in content)
            
            if modern_found >= 4:
                print(f"   ✅ Modern styling preserved ({modern_found}/5 elements)")
            else:
                print(f"   ❌ Modern styling incomplete ({modern_found}/5 elements)")
                all_modern = False
            
            # Check for icons
            icons = ["💼", "🔐", "👤", "🔒", "🌐"]
            icons_found = sum(1 for icon in icons if icon in content)
            
            if icons_found >= 4:
                print(f"   ✅ Modern icons preserved ({icons_found}/5 icons)")
            else:
                print(f"   ❌ Modern icons incomplete ({icons_found}/5 icons)")
                all_modern = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_modern = False
    
    return all_modern

def main():
    """Run all orange theme login screen tests"""
    
    print("🧡 ORANGE THEME LOGIN SCREEN TEST SUITE")
    print("=" * 40)
    
    tests = [
        ("Orange Color Scheme", test_orange_color_scheme),
        ("Scrolling Functionality", test_scrolling_functionality),
        ("Keyboard on User Click", test_keyboard_on_user_click),
        ("Language Selection", test_language_selection),
        ("Orange Button Styling", test_orange_button_styling),
        ("Modern Styling Preserved", test_modern_styling_preserved)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Orange/black/dark gray color scheme")
        print("✅ User selection scrolling functionality")
        print("✅ Keyboard shows on user name click")
        print("✅ Language selection (English/French)")
        print("✅ Orange button styling and highlighting")
        print("✅ Modern styling elements preserved")
    else:
        print("⚠️ Some tests failed")
        print("❌ Orange theme implementation may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🧡 Orange theme login screen successfully implemented!")
        print("🎨 Eye-catching orange/black/dark gray color palette")
        print("📜 Horizontal scrolling for user selection")
        print("⌨️ Number keyboard appears on user name click")
        print("🌐 Language switching between English and French")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Orange theme implementation needs attention")
    
    exit(0 if success else 1)
