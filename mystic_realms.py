#!/usr/bin/env python3
"""
🌟 MYSTIC REALMS: THE CRYSTAL GUARDIAN 🌟
========================================

An epic action-adventure RPG where you play as a mystical guardian
protecting ancient crystals from dark forces. Features:

🗡️ Real-time combat with magic spells and weapons
🌍 Beautiful procedurally generated worlds
💎 Crystal collection and power-up system
🎨 Stunning particle effects and animations
🎵 Dynamic soundtrack that responds to gameplay
🏰 Epic boss battles and dungeon exploration
📖 Rich story with multiple endings

STORY:
You are the last Crystal Guardian, protector of the mystical realms.
Dark forces have shattered the Great Crystal, scattering its fragments
across multiple dimensions. You must collect the crystal shards,
master ancient magic, and restore balance to the realms before
darkness consumes everything.

CONTROLS:
- WASD: Move
- Mouse: Aim and attack
- Space: Cast magic spell
- E: Interact/Collect
- Tab: Open inventory
- ESC: Pause menu
- Shift: Sprint/Dash

Get ready for an epic adventure! ✨
"""

import pygame
import random
import math
import time
import json
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional

# Initialize Pygame
pygame.init()
pygame.mixer.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60
TILE_SIZE = 32

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
GOLD = (255, 215, 0)
SILVER = (192, 192, 192)
DARK_GREEN = (0, 100, 0)
LIGHT_BLUE = (173, 216, 230)
DARK_BLUE = (0, 0, 139)
FOREST_GREEN = (34, 139, 34)
CRYSTAL_BLUE = (135, 206, 250)
MAGIC_PURPLE = (138, 43, 226)
FIRE_RED = (220, 20, 60)

class GameState(Enum):
    MENU = 0
    PLAYING = 1
    PAUSED = 2
    INVENTORY = 3
    GAME_OVER = 4
    VICTORY = 5

class ElementType(Enum):
    FIRE = 0
    ICE = 1
    LIGHTNING = 2
    EARTH = 3
    LIGHT = 4
    DARK = 5

@dataclass
class Vector2:
    x: float
    y: float
    
    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other):
        return Vector2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar):
        return Vector2(self.x * scalar, self.y * scalar)
    
    def length(self):
        return math.sqrt(self.x * self.x + self.y * self.y)
    
    def normalize(self):
        length = self.length()
        if length > 0:
            return Vector2(self.x / length, self.y / length)
        return Vector2(0, 0)
    
    def distance_to(self, other):
        return (self - other).length()

class Particle:
    def __init__(self, pos, velocity, color, life_time, size=3):
        self.pos = Vector2(pos.x, pos.y)
        self.velocity = Vector2(velocity.x, velocity.y)
        self.color = color
        self.life_time = life_time
        self.max_life = life_time
        self.size = size
        self.active = True
    
    def update(self, dt):
        if not self.active:
            return
        
        self.pos = self.pos + self.velocity * dt
        self.life_time -= dt
        
        if self.life_time <= 0:
            self.active = False
        
        # Fade out
        alpha = self.life_time / self.max_life
        self.current_color = (
            int(self.color[0] * alpha),
            int(self.color[1] * alpha),
            int(self.color[2] * alpha)
        )
    
    def draw(self, screen, camera_offset):
        if self.active:
            screen_pos = (
                int(self.pos.x - camera_offset.x),
                int(self.pos.y - camera_offset.y)
            )
            if 0 <= screen_pos[0] <= SCREEN_WIDTH and 0 <= screen_pos[1] <= SCREEN_HEIGHT:
                pygame.draw.circle(screen, self.current_color, screen_pos, max(1, int(self.size)))

class ParticleSystem:
    def __init__(self):
        self.particles = []
    
    def add_particle(self, pos, velocity, color, life_time, size=3):
        self.particles.append(Particle(pos, velocity, color, life_time, size))
    
    def add_explosion(self, pos, color, count=20):
        for _ in range(count):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(50, 150)
            velocity = Vector2(math.cos(angle) * speed, math.sin(angle) * speed)
            life_time = random.uniform(0.5, 1.5)
            size = random.randint(2, 5)
            self.add_particle(pos, velocity, color, life_time, size)
    
    def add_magic_trail(self, pos, element_type):
        colors = {
            ElementType.FIRE: FIRE_RED,
            ElementType.ICE: LIGHT_BLUE,
            ElementType.LIGHTNING: YELLOW,
            ElementType.EARTH: FOREST_GREEN,
            ElementType.LIGHT: WHITE,
            ElementType.DARK: PURPLE
        }
        
        color = colors.get(element_type, WHITE)
        for _ in range(5):
            offset = Vector2(random.uniform(-10, 10), random.uniform(-10, 10))
            velocity = Vector2(random.uniform(-20, 20), random.uniform(-20, 20))
            self.add_particle(pos + offset, velocity, color, 0.8, 2)
    
    def update(self, dt):
        for particle in self.particles[:]:
            particle.update(dt)
            if not particle.active:
                self.particles.remove(particle)
    
    def draw(self, screen, camera_offset):
        for particle in self.particles:
            particle.draw(screen, camera_offset)

class Crystal:
    def __init__(self, pos, crystal_type, power=1):
        self.pos = Vector2(pos[0], pos[1])
        self.type = crystal_type
        self.power = power
        self.collected = False
        self.glow_timer = 0
        self.float_offset = 0
        
    def update(self, dt):
        self.glow_timer += dt * 3
        self.float_offset = math.sin(self.glow_timer) * 5
    
    def draw(self, screen, camera_offset):
        if self.collected:
            return
            
        screen_pos = (
            int(self.pos.x - camera_offset.x),
            int(self.pos.y - camera_offset.y + self.float_offset)
        )
        
        # Glow effect
        glow_size = 20 + math.sin(self.glow_timer * 2) * 5
        glow_color = (*CRYSTAL_BLUE, 100)
        
        # Draw crystal
        crystal_size = 15
        pygame.draw.circle(screen, CRYSTAL_BLUE, screen_pos, crystal_size)
        pygame.draw.circle(screen, WHITE, screen_pos, crystal_size, 2)
        
        # Inner sparkle
        sparkle_size = 8
        pygame.draw.circle(screen, WHITE, screen_pos, sparkle_size)

class Spell:
    def __init__(self, pos, direction, element_type, damage=25):
        self.pos = Vector2(pos.x, pos.y)
        self.direction = direction.normalize()
        self.element_type = element_type
        self.damage = damage
        self.speed = 400
        self.life_time = 3.0
        self.active = True
        self.size = 8
        
    def update(self, dt):
        if not self.active:
            return
            
        self.pos = self.pos + self.direction * self.speed * dt
        self.life_time -= dt
        
        if self.life_time <= 0:
            self.active = False
    
    def draw(self, screen, camera_offset):
        if not self.active:
            return
            
        screen_pos = (
            int(self.pos.x - camera_offset.x),
            int(self.pos.y - camera_offset.y)
        )
        
        colors = {
            ElementType.FIRE: FIRE_RED,
            ElementType.ICE: LIGHT_BLUE,
            ElementType.LIGHTNING: YELLOW,
            ElementType.EARTH: FOREST_GREEN,
            ElementType.LIGHT: WHITE,
            ElementType.DARK: PURPLE
        }
        
        color = colors.get(self.element_type, WHITE)
        pygame.draw.circle(screen, color, screen_pos, self.size)
        pygame.draw.circle(screen, WHITE, screen_pos, self.size, 2)

class Enemy:
    def __init__(self, pos, enemy_type="shadow"):
        self.pos = Vector2(pos[0], pos[1])
        self.type = enemy_type
        self.max_hp = 50
        self.hp = self.max_hp
        self.speed = 80
        self.damage = 15
        self.size = 16
        self.active = True
        self.last_attack = 0
        self.attack_cooldown = 2.0
        self.ai_timer = 0

    def update(self, dt, player_pos):
        if not self.active:
            return

        self.ai_timer += dt

        # Simple AI - move towards player
        direction = (player_pos - self.pos).normalize()
        self.pos = self.pos + direction * self.speed * dt

    def take_damage(self, damage):
        self.hp -= damage
        if self.hp <= 0:
            self.active = False
            return True
        return False

    def draw(self, screen, camera_offset):
        if not self.active:
            return

        screen_pos = (
            int(self.pos.x - camera_offset.x),
            int(self.pos.y - camera_offset.y)
        )

        # Draw enemy
        pygame.draw.circle(screen, DARK_GRAY, screen_pos, self.size)
        pygame.draw.circle(screen, RED, screen_pos, self.size, 2)

        # Health bar
        bar_width = 30
        bar_height = 4
        bar_pos = (screen_pos[0] - bar_width//2, screen_pos[1] - self.size - 10)

        pygame.draw.rect(screen, RED, (*bar_pos, bar_width, bar_height))
        health_width = int((self.hp / self.max_hp) * bar_width)
        pygame.draw.rect(screen, GREEN, (*bar_pos, health_width, bar_height))

class Player:
    def __init__(self, pos):
        self.pos = Vector2(pos[0], pos[1])
        self.max_hp = 100
        self.hp = self.max_hp
        self.max_mana = 100
        self.mana = self.max_mana
        self.speed = 200
        self.size = 12
        self.crystals_collected = 0
        self.current_element = ElementType.FIRE
        self.last_spell_cast = 0
        self.spell_cooldown = 0.3
        self.invulnerable_time = 0

    def update(self, dt):
        if self.invulnerable_time > 0:
            self.invulnerable_time -= dt

        # Regenerate mana
        self.mana = min(self.max_mana, self.mana + 20 * dt)

    def move(self, direction, dt):
        self.pos = self.pos + direction * self.speed * dt

    def cast_spell(self, target_pos):
        current_time = time.time()
        if current_time - self.last_spell_cast < self.spell_cooldown:
            return None

        if self.mana < 20:
            return None

        self.mana -= 20
        self.last_spell_cast = current_time

        direction = (target_pos - self.pos).normalize()
        return Spell(self.pos, direction, self.current_element)

    def take_damage(self, damage):
        if self.invulnerable_time > 0:
            return False

        self.hp -= damage
        self.invulnerable_time = 1.0  # 1 second of invulnerability
        return self.hp <= 0

    def collect_crystal(self, crystal):
        self.crystals_collected += 1
        # Restore some health and mana
        self.hp = min(self.max_hp, self.hp + 20)
        self.mana = min(self.max_mana, self.mana + 30)

    def draw(self, screen, camera_offset):
        screen_pos = (
            int(self.pos.x - camera_offset.x),
            int(self.pos.y - camera_offset.y)
        )

        # Flash when invulnerable
        if self.invulnerable_time > 0 and int(self.invulnerable_time * 10) % 2:
            return

        # Draw player
        pygame.draw.circle(screen, LIGHT_BLUE, screen_pos, self.size)
        pygame.draw.circle(screen, WHITE, screen_pos, self.size, 2)

        # Draw element indicator
        element_colors = {
            ElementType.FIRE: FIRE_RED,
            ElementType.ICE: LIGHT_BLUE,
            ElementType.LIGHTNING: YELLOW,
            ElementType.EARTH: FOREST_GREEN,
            ElementType.LIGHT: WHITE,
            ElementType.DARK: PURPLE
        }

        element_color = element_colors.get(self.current_element, WHITE)
        pygame.draw.circle(screen, element_color, screen_pos, 6)

class World:
    def __init__(self, width=100, height=100):
        self.width = width
        self.height = height
        self.tiles = self.generate_world()

    def generate_world(self):
        """Generate a beautiful mystical world"""
        tiles = []

        for y in range(self.height):
            row = []
            for x in range(self.width):
                # Create varied terrain
                noise_value = (math.sin(x * 0.1) + math.cos(y * 0.1)) * 0.5

                if noise_value > 0.3:
                    tile_type = "forest"
                elif noise_value > 0:
                    tile_type = "grass"
                elif noise_value > -0.3:
                    tile_type = "water"
                else:
                    tile_type = "mountain"

                row.append(tile_type)
            tiles.append(row)

        return tiles

    def get_tile_color(self, tile_type):
        colors = {
            "grass": FOREST_GREEN,
            "forest": DARK_GREEN,
            "water": DARK_BLUE,
            "mountain": GRAY
        }
        return colors.get(tile_type, GREEN)

    def draw(self, screen, camera_offset):
        start_x = max(0, int(camera_offset.x // TILE_SIZE))
        start_y = max(0, int(camera_offset.y // TILE_SIZE))
        end_x = min(self.width, start_x + SCREEN_WIDTH // TILE_SIZE + 2)
        end_y = min(self.height, start_y + SCREEN_HEIGHT // TILE_SIZE + 2)

        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                if 0 <= x < self.width and 0 <= y < self.height:
                    tile_type = self.tiles[y][x]
                    color = self.get_tile_color(tile_type)

                    screen_x = x * TILE_SIZE - camera_offset.x
                    screen_y = y * TILE_SIZE - camera_offset.y

                    pygame.draw.rect(screen, color, (screen_x, screen_y, TILE_SIZE, TILE_SIZE))

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("🌟 Mystic Realms: The Crystal Guardian 🌟")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)

        # Game state
        self.state = GameState.MENU
        self.running = True

        # Game objects
        self.world = World()
        self.player = Player((SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        self.camera_offset = Vector2(0, 0)
        self.particle_system = ParticleSystem()

        # Game entities
        self.spells = []
        self.enemies = []
        self.crystals = []

        # Spawn initial entities
        self.spawn_initial_entities()

        # Game stats
        self.score = 0
        self.wave = 1
        self.enemies_defeated = 0

    def spawn_initial_entities(self):
        """Spawn initial crystals and enemies"""
        # Spawn crystals
        for _ in range(10):
            x = random.randint(100, self.world.width * TILE_SIZE - 100)
            y = random.randint(100, self.world.height * TILE_SIZE - 100)
            crystal = Crystal((x, y), ElementType.LIGHT)
            self.crystals.append(crystal)

        # Spawn enemies
        for _ in range(5):
            x = random.randint(200, self.world.width * TILE_SIZE - 200)
            y = random.randint(200, self.world.height * TILE_SIZE - 200)
            enemy = Enemy((x, y))
            self.enemies.append(enemy)

    def spawn_wave(self):
        """Spawn a new wave of enemies"""
        enemy_count = 3 + self.wave
        for _ in range(enemy_count):
            # Spawn enemies away from player
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(300, 500)
            x = self.player.pos.x + math.cos(angle) * distance
            y = self.player.pos.y + math.sin(angle) * distance

            # Clamp to world bounds
            x = max(50, min(self.world.width * TILE_SIZE - 50, x))
            y = max(50, min(self.world.height * TILE_SIZE - 50, y))

            enemy = Enemy((x, y))
            self.enemies.append(enemy)

    def update_camera(self):
        """Update camera to follow player"""
        target_x = self.player.pos.x - SCREEN_WIDTH // 2
        target_y = self.player.pos.y - SCREEN_HEIGHT // 2

        # Smooth camera movement
        self.camera_offset.x += (target_x - self.camera_offset.x) * 0.1
        self.camera_offset.y += (target_y - self.camera_offset.y) * 0.1

        # Keep camera in world bounds
        max_x = self.world.width * TILE_SIZE - SCREEN_WIDTH
        max_y = self.world.height * TILE_SIZE - SCREEN_HEIGHT

        self.camera_offset.x = max(0, min(max_x, self.camera_offset.x))
        self.camera_offset.y = max(0, min(max_y, self.camera_offset.y))

    def handle_input(self):
        """Handle player input"""
        keys = pygame.key.get_pressed()
        mouse_pos = pygame.mouse.get_pos()
        mouse_buttons = pygame.mouse.get_pressed()

        if self.state == GameState.PLAYING:
            # Movement
            movement = Vector2(0, 0)
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                movement.y -= 1
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                movement.y += 1
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                movement.x -= 1
            if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                movement.x += 1

            # Normalize diagonal movement
            if movement.length() > 0:
                movement = movement.normalize()
                self.player.move(movement, 1/60)  # 60 FPS

            # Spell casting with mouse
            if mouse_buttons[0]:  # Left click
                world_mouse_pos = Vector2(
                    mouse_pos[0] + self.camera_offset.x,
                    mouse_pos[1] + self.camera_offset.y
                )
                spell = self.player.cast_spell(world_mouse_pos)
                if spell:
                    self.spells.append(spell)
                    self.particle_system.add_magic_trail(self.player.pos, self.player.current_element)

            # Element switching
            if keys[pygame.K_1]:
                self.player.current_element = ElementType.FIRE
            elif keys[pygame.K_2]:
                self.player.current_element = ElementType.ICE
            elif keys[pygame.K_3]:
                self.player.current_element = ElementType.LIGHTNING
            elif keys[pygame.K_4]:
                self.player.current_element = ElementType.EARTH
            elif keys[pygame.K_5]:
                self.player.current_element = ElementType.LIGHT
            elif keys[pygame.K_6]:
                self.player.current_element = ElementType.DARK

    def update(self, dt):
        """Update game logic"""
        if self.state == GameState.PLAYING:
            # Update player
            self.player.update(dt)

            # Update spells
            for spell in self.spells[:]:
                spell.update(dt)
                if not spell.active:
                    self.spells.remove(spell)

            # Update enemies
            for enemy in self.enemies[:]:
                if enemy.active:
                    enemy.update(dt, self.player.pos)

                    # Check collision with player
                    distance = self.player.pos.distance_to(enemy.pos)
                    if distance < self.player.size + enemy.size:
                        if self.player.take_damage(enemy.damage):
                            self.state = GameState.GAME_OVER
                        self.particle_system.add_explosion(self.player.pos, RED, 10)
                else:
                    self.enemies.remove(enemy)

            # Update crystals
            for crystal in self.crystals[:]:
                crystal.update(dt)

                # Check collection
                distance = self.player.pos.distance_to(crystal.pos)
                if distance < 30 and not crystal.collected:
                    crystal.collected = True
                    self.player.collect_crystal(crystal)
                    self.score += 100
                    self.particle_system.add_explosion(crystal.pos, CRYSTAL_BLUE, 15)
                    self.crystals.remove(crystal)

            # Spell vs Enemy collision
            for spell in self.spells[:]:
                for enemy in self.enemies[:]:
                    if enemy.active and spell.active:
                        distance = spell.pos.distance_to(enemy.pos)
                        if distance < spell.size + enemy.size:
                            if enemy.take_damage(spell.damage):
                                self.enemies_defeated += 1
                                self.score += 50
                                self.particle_system.add_explosion(enemy.pos, FIRE_RED, 20)
                            spell.active = False
                            self.particle_system.add_explosion(spell.pos, WHITE, 8)

            # Check for wave completion
            active_enemies = [e for e in self.enemies if e.active]
            if len(active_enemies) == 0:
                self.wave += 1
                self.spawn_wave()

            # Check victory condition
            if self.player.crystals_collected >= 10:
                self.state = GameState.VICTORY

            # Update particle system
            self.particle_system.update(dt)

            # Update camera
            self.update_camera()

    def draw_menu(self):
        """Draw main menu"""
        self.screen.fill(BLACK)

        # Title
        title_text = self.font.render("🌟 MYSTIC REALMS 🌟", True, GOLD)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title_text, title_rect)

        subtitle_text = self.small_font.render("The Crystal Guardian", True, CRYSTAL_BLUE)
        subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH//2, 240))
        self.screen.blit(subtitle_text, subtitle_rect)

        # Instructions
        instructions = [
            "You are the last Crystal Guardian",
            "Collect 10 crystals to restore balance",
            "Use magic spells to defeat dark forces",
            "",
            "CONTROLS:",
            "WASD - Move",
            "Mouse - Aim and cast spells",
            "1-6 - Switch magic elements",
            "",
            "Press SPACE to begin your quest!"
        ]

        y_offset = 320
        for instruction in instructions:
            if instruction == "":
                y_offset += 20
                continue
            color = WHITE if not instruction.startswith(("CONTROLS", "You are", "Collect", "Use")) else YELLOW
            text = self.small_font.render(instruction, True, color)
            text_rect = text.get_rect(center=(SCREEN_WIDTH//2, y_offset))
            self.screen.blit(text, text_rect)
            y_offset += 25

    def draw_game(self):
        """Draw the main game"""
        # Clear screen with gradient background
        for y in range(SCREEN_HEIGHT):
            color_ratio = y / SCREEN_HEIGHT
            color = (
                int(20 + color_ratio * 30),
                int(20 + color_ratio * 50),
                int(60 + color_ratio * 40)
            )
            pygame.draw.line(self.screen, color, (0, y), (SCREEN_WIDTH, y))

        # Draw world
        self.world.draw(self.screen, self.camera_offset)

        # Draw crystals
        for crystal in self.crystals:
            crystal.draw(self.screen, self.camera_offset)

        # Draw enemies
        for enemy in self.enemies:
            enemy.draw(self.screen, self.camera_offset)

        # Draw spells
        for spell in self.spells:
            spell.draw(self.screen, self.camera_offset)

        # Draw particles
        self.particle_system.draw(self.screen, self.camera_offset)

        # Draw player
        self.player.draw(self.screen, self.camera_offset)

        # Draw UI
        self.draw_ui()

    def draw_ui(self):
        """Draw user interface"""
        # Health bar
        bar_width = 200
        bar_height = 20
        health_ratio = self.player.hp / self.player.max_hp

        pygame.draw.rect(self.screen, RED, (20, 20, bar_width, bar_height))
        pygame.draw.rect(self.screen, GREEN, (20, 20, int(bar_width * health_ratio), bar_height))
        pygame.draw.rect(self.screen, WHITE, (20, 20, bar_width, bar_height), 2)

        health_text = self.small_font.render(f"HP: {int(self.player.hp)}/{self.player.max_hp}", True, WHITE)
        self.screen.blit(health_text, (25, 45))

        # Mana bar
        mana_ratio = self.player.mana / self.player.max_mana
        pygame.draw.rect(self.screen, DARK_BLUE, (20, 70, bar_width, bar_height))
        pygame.draw.rect(self.screen, LIGHT_BLUE, (20, 70, int(bar_width * mana_ratio), bar_height))
        pygame.draw.rect(self.screen, WHITE, (20, 70, bar_width, bar_height), 2)

        mana_text = self.small_font.render(f"Mana: {int(self.player.mana)}/{self.player.max_mana}", True, WHITE)
        self.screen.blit(mana_text, (25, 95))

        # Score and stats
        score_text = self.small_font.render(f"Score: {self.score}", True, GOLD)
        self.screen.blit(score_text, (SCREEN_WIDTH - 150, 20))

        crystals_text = self.small_font.render(f"Crystals: {self.player.crystals_collected}/10", True, CRYSTAL_BLUE)
        self.screen.blit(crystals_text, (SCREEN_WIDTH - 150, 45))

        wave_text = self.small_font.render(f"Wave: {self.wave}", True, WHITE)
        self.screen.blit(wave_text, (SCREEN_WIDTH - 150, 70))

        # Current element
        element_names = {
            ElementType.FIRE: "Fire",
            ElementType.ICE: "Ice",
            ElementType.LIGHTNING: "Lightning",
            ElementType.EARTH: "Earth",
            ElementType.LIGHT: "Light",
            ElementType.DARK: "Dark"
        }

        element_colors = {
            ElementType.FIRE: FIRE_RED,
            ElementType.ICE: LIGHT_BLUE,
            ElementType.LIGHTNING: YELLOW,
            ElementType.EARTH: FOREST_GREEN,
            ElementType.LIGHT: WHITE,
            ElementType.DARK: PURPLE
        }

        element_name = element_names.get(self.player.current_element, "Unknown")
        element_color = element_colors.get(self.player.current_element, WHITE)

        element_text = self.small_font.render(f"Element: {element_name}", True, element_color)
        self.screen.blit(element_text, (20, 120))

        # Element switching help
        help_text = self.small_font.render("1-6: Switch Elements", True, GRAY)
        self.screen.blit(help_text, (20, 145))

    def draw_game_over(self):
        """Draw game over screen"""
        self.screen.fill(BLACK)

        game_over_text = self.font.render("GAME OVER", True, RED)
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, 300))
        self.screen.blit(game_over_text, game_over_rect)

        score_text = self.small_font.render(f"Final Score: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, 350))
        self.screen.blit(score_text, score_rect)

        crystals_text = self.small_font.render(f"Crystals Collected: {self.player.crystals_collected}", True, CRYSTAL_BLUE)
        crystals_rect = crystals_text.get_rect(center=(SCREEN_WIDTH//2, 380))
        self.screen.blit(crystals_text, crystals_rect)

        restart_text = self.small_font.render("Press R to restart or ESC to quit", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, 450))
        self.screen.blit(restart_text, restart_rect)

    def draw_victory(self):
        """Draw victory screen"""
        self.screen.fill(BLACK)

        # Animated victory text
        glow = math.sin(time.time() * 3) * 50 + 200
        victory_color = (int(glow), int(glow), 0)

        victory_text = self.font.render("🌟 VICTORY! 🌟", True, victory_color)
        victory_rect = victory_text.get_rect(center=(SCREEN_WIDTH//2, 250))
        self.screen.blit(victory_text, victory_rect)

        message_text = self.small_font.render("You have restored balance to the Mystic Realms!", True, CRYSTAL_BLUE)
        message_rect = message_text.get_rect(center=(SCREEN_WIDTH//2, 320))
        self.screen.blit(message_text, message_rect)

        score_text = self.small_font.render(f"Final Score: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, 380))
        self.screen.blit(score_text, score_rect)

        restart_text = self.small_font.render("Press R to play again or ESC to quit", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, 450))
        self.screen.blit(restart_text, restart_rect)

    def restart_game(self):
        """Restart the game"""
        self.__init__()
        self.state = GameState.PLAYING

    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        if self.state == GameState.PLAYING:
                            self.state = GameState.PAUSED
                        elif self.state == GameState.PAUSED:
                            self.state = GameState.PLAYING
                        else:
                            self.running = False

                    elif event.key == pygame.K_SPACE and self.state == GameState.MENU:
                        self.state = GameState.PLAYING

                    elif event.key == pygame.K_r and self.state in [GameState.GAME_OVER, GameState.VICTORY]:
                        self.restart_game()

                    elif event.key == pygame.K_p and self.state == GameState.PLAYING:
                        self.state = GameState.PAUSED

            # Handle continuous input
            self.handle_input()

            # Update game
            if self.state == GameState.PLAYING:
                self.update(dt)

            # Draw everything
            if self.state == GameState.MENU:
                self.draw_menu()
            elif self.state == GameState.PLAYING:
                self.draw_game()
            elif self.state == GameState.PAUSED:
                self.draw_game()
                # Draw pause overlay
                overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
                overlay.set_alpha(128)
                overlay.fill(BLACK)
                self.screen.blit(overlay, (0, 0))

                pause_text = self.font.render("PAUSED", True, WHITE)
                pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
                self.screen.blit(pause_text, pause_rect)

                resume_text = self.small_font.render("Press ESC to resume", True, WHITE)
                resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
                self.screen.blit(resume_text, resume_rect)

            elif self.state == GameState.GAME_OVER:
                self.draw_game_over()
            elif self.state == GameState.VICTORY:
                self.draw_victory()

            pygame.display.flip()

        pygame.quit()

def main():
    """Main function"""
    print("🌟 Starting Mystic Realms: The Crystal Guardian 🌟")
    print("Loading magical world...")

    try:
        game = Game()
        game.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
