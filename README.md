# 💖 Underground Tale - An Undertale-Inspired RPG 💖

A heartfelt RPG inspired by the beloved game Undertale, featuring story-driven gameplay, meaningful choices, and the innovative bullet-hell combat system that made the original so special.

## 🌟 Game Features

### 📖 Rich Storytelling
- **Branching Narrative**: Your choices shape the story and determine multiple endings
- **Memorable Characters**: <PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and other unique personalities
- **Emotional Depth**: Themes of friendship, mercy, determination, and compassion
- **Typewriter Text**: Classic text reveal system for immersive dialogue

### ⚔️ Innovative Combat System
- **Bullet Hell Mechanics**: Dodge attacks in real-time during enemy turns
- **Turn-Based Strategy**: Choose between FIGHT, ACT, ITEM, or MERCY
- **Unique Monster Patterns**: Each enemy has distinctive bullet patterns
- **Mercy System**: Spare enemies through understanding and compassion
- **ACT System**: Interact with monsters in unique ways to resolve conflicts peacefully

### 🎭 Character-Driven Gameplay
- **Moral Choices**: Every decision matters - fight or show mercy?
- **Character Development**: Learn about each monster's personality and motivations
- **Multiple Endings**: Different story outcomes based on your choices
- **Consequence System**: Your actions affect how characters react to you

### 💝 Core Themes
- **Determination**: Never give up, even in the darkest moments
- **Compassion**: Understanding others can resolve conflicts without violence
- **Friendship**: Build relationships through kindness and empathy
- **Choice**: You decide what kind of person you want to be

## 🎮 How to Play

### Basic Controls
- **Arrow Keys/WASD**: Navigate menus and dodge bullets
- **ENTER**: Confirm selections and advance dialogue
- **ESC**: Quit game

### Combat System
1. **Your Turn**: Choose from four options:
   - **FIGHT** (Red): Attack the monster
   - **ACT** (Blue): Interact with the monster in unique ways
   - **ITEM** (Green): Use healing items or equipment
   - **MERCY** (Yellow): Attempt to spare the monster

2. **Monster's Turn**: Dodge their bullet patterns!
   - Move your red heart (SOUL) to avoid white bullets
   - Different monsters have unique attack patterns
   - Getting hit damages your HP

3. **Victory Conditions**:
   - Defeat the monster by reducing their HP to 0
   - OR spare them by fulfilling their unique conditions
   - Sparing often leads to friendship and better outcomes!

### Story Progression
- Read dialogue carefully - characters reveal important information
- Make choices that reflect your values
- Some decisions have immediate consequences, others affect the ending
- Explore different dialogue options to learn more about characters

## 🚀 Quick Start

### Option 1: Easy Launcher
```bash
python run_underground_tale.py
```
The launcher will automatically check for pygame and install it if needed.

### Option 2: Direct Run
```bash
python underground_tale.py
```

### Requirements
- Python 3.6 or higher
- Pygame library

### Install Pygame
```bash
pip install pygame
```

## 👥 Characters You'll Meet

### 🌸 Toriel
- A motherly goat monster who protects you in the Ruins
- Gentle and caring, but will fight to keep you safe
- **Battle Tip**: She doesn't really want to hurt you...

### 💀 Sans
- A laid-back skeleton with a mysterious past
- Loves jokes, especially puns
- **Battle Tip**: He's stronger than he looks

### 🍝 Papyrus
- Sans' energetic brother who loves puzzles and spaghetti
- Dreams of joining the Royal Guard
- **Battle Tip**: He believes in you, even during battle!

### 🌻 Training Dummy
- Your first encounter in the Underground
- Perfect for learning the battle system
- **Battle Tip**: Try talking to it!

## 🎯 Battle Strategies

### For Pacifist Players
- Always try ACT options first
- Look for clues in monster dialogue
- Some monsters can be spared immediately, others need specific actions
- Healing items help you survive while finding peaceful solutions

### For Combat Players
- Use FIGHT to deal damage
- Monitor monster HP with the health bar
- Some monsters are stronger than others
- Victory gives EXP, but at what cost?

### Mixed Approach
- Each monster is unique - adapt your strategy
- Some situations may require different approaches
- Your choices define your character's journey

## 🏆 Multiple Endings

The game features different endings based on your choices:

- **Pacifist Route**: Spare every monster you encounter
- **Neutral Route**: Mix of fighting and sparing
- **Other Routes**: Discover them through your choices!

Each ending provides a different perspective on your journey and the Underground's inhabitants.

## 🎨 Game Design

### Visual Style
- Minimalist geometric design focusing on gameplay
- Classic text-based storytelling
- Color-coded UI for intuitive navigation
- Clean, readable interface

### Audio Design
- Currently uses visual feedback for all interactions
- Designed to support future sound effects and music
- Focus on visual storytelling and character development

## 🔧 Technical Features

### Performance
- Smooth 60 FPS gameplay
- Efficient bullet-hell system
- Fast text rendering
- Minimal system requirements

### Code Quality
- Clean, modular Python code
- Object-oriented design
- Easy to modify and extend
- Well-documented for learning

## 🌈 Future Enhancements

The game is designed to be easily expandable:
- **Sound Effects**: Menu sounds, battle effects, dialogue beeps
- **Music**: Character themes and atmospheric tracks
- **Sprite Graphics**: Character and environment art
- **More Characters**: Additional monsters and NPCs
- **Extended Story**: More areas and plot development
- **Save System**: Continue your journey across sessions

## 💡 Tips for New Players

1. **Read Everything**: Dialogue contains important clues
2. **Experiment**: Try different ACT options with each monster
3. **Stay Determined**: Don't give up if you take damage
4. **Show Mercy**: Sometimes the peaceful path is more rewarding
5. **Pay Attention**: Character reactions change based on your choices

## 🐛 Troubleshooting

**Game won't start?**
- Ensure Python 3.6+ is installed
- Install pygame: `pip install pygame`
- Run from the correct directory

**Controls not working?**
- Make sure the game window has focus
- Try both arrow keys and WASD
- Check if you're in the right game state (story vs battle)

**Performance issues?**
- Close other applications
- Check Python version compatibility
- Ensure pygame is properly installed

## 📁 File Structure

```
Game/
├── underground_tale.py         # Main game file
├── run_underground_tale.py     # Easy launcher
├── README.md                   # This documentation
└── assets/
    └── game_assets.txt         # Asset information and upgrade guide
```

## 🎵 Inspiration

This game is inspired by Toby Fox's masterpiece "Undertale" and aims to capture:
- The innovative blend of traditional RPG and bullet-hell mechanics
- The emphasis on player choice and moral decision-making
- The power of showing mercy and understanding others
- The importance of determination in the face of adversity

## 💖 Message

Remember: In this world, you don't have to kill anyone. You can choose to show mercy, to understand, to befriend even those who seem like enemies. Your choices matter, and they define not just the story, but who you are as a person.

Stay determined! 💖

---

**"Despite everything, it's still you."**

*Experience a tale of friendship, determination, and the power of choice in the Underground.*
