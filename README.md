# 🏰 Infinite Dungeon Crawler - Roguelike RPG 🏰

An epic turn-based roguelike RPG with infinite replayability! Descend into procedurally generated dungeons, fight monsters, collect loot, and see how deep you can survive!

## 🎮 Game Features

### 🗺️ Procedural Generation
- **Infinite Dungeons**: Every playthrough features completely unique dungeon layouts
- **Room-Based Design**: Connected rooms with corridors for tactical gameplay
- **Progressive Difficulty**: Deeper levels = stronger enemies and better rewards

### ⚔️ Combat System
- **Turn-Based Strategy**: Think before you act - every move matters
- **Equipment Matters**: Weapons and armor directly affect combat effectiveness
- **Monster Variety**: 5 different monster types with unique stats and behaviors
- **Scaling Enemies**: Monsters get stronger as you go deeper

### 📈 Character Progression
- **Level Up System**: Gain EXP from defeating monsters
- **Stat Growth**: Increase HP, Attack, and Defense with each level
- **Equipment System**: Find and equip weapons and armor
- **Infinite Scaling**: No level cap - grow as strong as you can survive!

### 🎒 Rich Loot System
- **5 Rarity Tiers**: Common → Uncommon → Rare → Epic → Legendary
- **Multiple Item Types**: Weapons, Armor, Potions, Scrolls, Treasure
- **Smart Loot Scaling**: Better items appear in deeper levels
- **Inventory Management**: Collect, use, and equip items strategically

### 🏆 Infinite Replayability
- **Roguelike Elements**: Permanent death with fresh starts
- **Procedural Content**: No two runs are ever the same
- **High Score Challenge**: How deep can you go? How much treasure can you collect?
- **Strategic Depth**: Multiple viable playstyles and approaches

## 🎯 How to Play

### Basic Controls
- **WASD** or **Arrow Keys**: Move your character
- **I**: Open/Close inventory
- **UP/DOWN**: Navigate inventory items
- **ENTER**: Use/Equip selected item
- **ESC**: Close inventory or quit game
- **R**: Restart when dead

### Combat
1. **Attack**: Move into an enemy to attack them
2. **Damage Calculation**: Your Attack + Weapon Bonus - Enemy Defense
3. **Turn Order**: You move, then all monsters move
4. **Health Management**: Use potions to heal when needed

### Progression
1. **Explore**: Move through rooms and corridors
2. **Fight**: Defeat monsters to gain experience
3. **Loot**: Collect weapons, armor, and treasures
4. **Equip**: Better gear = better survival chances
5. **Descend**: Use stairs (>) to go deeper for better rewards

### Survival Tips
- **Equipment First**: Always equip better weapons and armor
- **Potion Management**: Save healing potions for emergencies
- **Strategic Positioning**: Use doorways and corridors tactically
- **Know Your Enemies**: Different monsters have different strengths
- **Risk vs Reward**: Deeper levels have better loot but stronger enemies

## 🚀 Quick Start

### Option 1: Easy Launcher
```bash
python run_dungeon_crawler.py
```
The launcher will automatically check for pygame and install it if needed.

### Option 2: Direct Run
```bash
python dungeon_crawler.py
```

### Requirements
- Python 3.6 or higher
- Pygame library

### Install Pygame
```bash
pip install pygame
```

## 🎨 Game Elements

### 👤 Player Character
- **Symbol**: @ (Yellow)
- **Starting Stats**: 100 HP, 10 Attack, 5 Defense
- **Growth**: Gains stats each level up

### 👹 Monsters
- **Goblin** (g): Weak but numerous
- **Orc** (o): Balanced fighter
- **Skeleton** (s): Undead warrior
- **Troll** (T): Strong and tough (appears level 5+)
- **Dragon** (D): Legendary boss monster (appears level 10+)

### ⚔️ Equipment Types
- **Weapons** (/): Increase attack damage
- **Armor** ([): Increase defense
- **Potions** (!): Restore health
- **Scrolls** (?): Future magic items
- **Treasure** ($): Valuable collectibles

### 🌈 Rarity System
- **Common** (White): Basic items
- **Uncommon** (Green): Decent upgrades
- **Rare** (Blue): Significant improvements
- **Epic** (Purple): Powerful equipment
- **Legendary** (Gold): Game-changing items

## 🎯 Game Mechanics

### Experience & Leveling
- Gain EXP by defeating monsters
- Level up increases HP, Attack, and Defense
- EXP requirements increase each level
- Full heal on level up

### Equipment System
- Equip one weapon and one armor piece
- Equipment bonuses stack with base stats
- Better equipment found at deeper levels
- Old equipment returns to inventory when replaced

### Dungeon Progression
- Each level has stairs down (>) and up (<)
- Going deeper increases difficulty and rewards
- Can return to previous levels if needed
- Dungeon level affects monster strength and loot quality

## 🏆 Scoring & Goals

### Primary Goals
1. **Survive**: Stay alive as long as possible
2. **Explore**: Reach the deepest dungeon levels
3. **Collect**: Gather valuable treasure and equipment
4. **Grow**: Achieve the highest character level

### Challenge Modes (Self-Imposed)
- **Speed Run**: Reach level 10 as fast as possible
- **Treasure Hunter**: Collect maximum gold value
- **Minimalist**: Use only common equipment
- **Pacifist**: Avoid combat when possible (good luck!)

## 🔧 Technical Features

### Performance
- Smooth 60 FPS gameplay
- Efficient ASCII-style rendering
- Minimal system requirements
- Fast dungeon generation

### Code Quality
- Clean, modular Python code
- Object-oriented design
- Easy to modify and extend
- Well-commented for learning

## 🎵 Future Enhancements

The game is designed to be easily expandable:
- **Sound Effects**: Combat, movement, and UI sounds
- **Music**: Dynamic background music
- **Sprite Graphics**: Replace ASCII with pixel art
- **Magic System**: Spells and magical abilities
- **More Monsters**: Additional creature types
- **Boss Fights**: Special challenging encounters
- **Quests**: Objective-based gameplay
- **Save System**: Continue previous runs

## 🐛 Troubleshooting

**Game won't start?**
- Ensure Python 3.6+ is installed
- Install pygame: `pip install pygame`
- Run from the correct directory

**Performance issues?**
- Close other applications
- Reduce window size if needed
- Check Python version compatibility

**Controls not working?**
- Ensure game window has focus
- Try both WASD and arrow keys
- Check if inventory is open (press ESC)

## 📁 File Structure

```
Game/
├── dungeon_crawler.py          # Main game file
├── run_dungeon_crawler.py      # Easy launcher
├── README.md                   # This documentation
└── assets/
    └── game_assets.txt         # Asset information and upgrade guide
```

---

**Prepare for an epic adventure! How deep into the infinite dungeon will you survive?** ⚔️🏰

*This roguelike combines classic gameplay with modern polish - perfect for both newcomers to the genre and veteran dungeon crawlers!*
