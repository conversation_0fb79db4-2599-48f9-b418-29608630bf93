# 💎 Gem Collector - Addictive Fun Game! 💎

A simple yet incredibly addictive arcade-style game built with Python and Pygame!

## 🎮 Game Features

### Core Gameplay
- **Smooth Movement**: Control your blue player with WASD or arrow keys
- **Beautiful Trail Effects**: Leave a glowing trail as you move
- **Progressive Difficulty**: Each level adds more enemies and gems
- **Multiple Gem Types**: 
  - 🟡 **Normal Gems** (10 points)
  - 🟣 **Rare Gems** (50 points) 
  - 🔵 **Epic Gems** (100 points)

### Power-ups
- 🟢 **Speed Boost**: Move faster for 5 seconds
- 🔵 **Shield**: Become invulnerable to enemies for 5 seconds
- 🟠 **Magnet**: Attract gems from a distance for 5 seconds

### Smart Enemies
- 🔴 Red enemies that move randomly
- 30% chance they'll follow you directly
- Bounce off walls realistically
- More enemies spawn each level

## 🎯 How to Play

1. **Move**: Use WASD or Arrow Keys
2. **Collect Gems**: Touch gems to collect them and increase your score
3. **Avoid Enemies**: Red circles will end your game if you touch them (unless you have a shield!)
4. **Collect Power-ups**: Square power-ups give you temporary abilities
5. **Complete Levels**: Collect enough gems to advance to the next level
6. **Survive**: Try to get the highest score possible!

## 🎮 Controls

- **WASD** or **Arrow Keys**: Move player
- **SPACE**: Pause/Unpause game
- **R**: Restart game (when game over)
- **ESC**: Quit game

## 🚀 How to Run

### Option 1: Easy Launcher
```bash
python run_game.py
```
The launcher will check for pygame and offer to install it if needed.

### Option 2: Direct Run
```bash
python gem_collector.py
```

### Requirements
- Python 3.6 or higher
- Pygame library

### Install Pygame
```bash
pip install pygame
```

## 🎨 Game Mechanics

### Scoring System
- Normal gems: 10 points
- Rare gems: 50 points  
- Epic gems: 100 points
- Level completion bonus: Level × 100 points

### Level Progression
- Start with 10 gems needed to complete level 1
- Each level requires 5 more gems than the previous
- More enemies spawn each level (up to 10 max)
- More gems spawn each level for higher scores

### Power-up Effects
- **Speed Boost**: Increases movement speed from 5 to 8
- **Shield**: Glowing cyan circle protects from enemies
- **Magnet**: Doubles collection radius and pulls gems toward you

## 🎯 Tips for High Scores

1. **Prioritize Epic Gems**: They're worth 10x normal gems!
2. **Use Power-ups Strategically**: Save shields for dangerous situations
3. **Learn Enemy Patterns**: They change direction every 2 seconds
4. **Stay Mobile**: Keep moving to avoid getting cornered
5. **Magnet + Epic Gems**: Best combo for massive points

## 🔧 Customization

The game is designed to be easily customizable:
- Modify colors in the color constants section
- Adjust game speed by changing FPS
- Tweak difficulty by modifying spawn rates
- Add sound effects by uncommenting audio code sections

## 📁 File Structure

```
Game/
├── gem_collector.py      # Main game file
├── run_game.py          # Easy launcher with pygame check
├── README.md            # This file
└── assets/
    └── needed_assets.txt # List of optional graphics/sounds
```

## 🎵 Future Enhancements

The game is designed to be easily expandable:
- Add sound effects and background music
- Create custom sprites and animations
- Add more power-up types
- Implement high score saving
- Add different game modes
- Create boss enemies

## 🐛 Troubleshooting

**Game won't start?**
- Make sure Python 3.6+ is installed
- Install pygame: `pip install pygame`
- Run from the Game directory

**Game running slowly?**
- Close other applications
- Lower FPS in the code if needed

**Controls not working?**
- Make sure the game window has focus
- Try both WASD and arrow keys

---

**Have fun and try to beat your high score!** 🏆

*This game is designed to be simple to understand but hard to master - perfect for quick gaming sessions that turn into hours of addictive fun!*
