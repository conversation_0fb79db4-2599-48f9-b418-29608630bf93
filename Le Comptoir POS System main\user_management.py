# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['bRgcBA8hCXR1D3IleSMtOQhrOAkzegx3Kh9mdjdsBWoJEUBnRVA7bB0TNAAaJhFyMGUhAigeajM/eTYFamcrOgQOIhAPf3x6NjEM', 'GhpmBzwOElgJUVcOOXgZNjlnFAd7AWZyFhsDLg9xdBV+HCZ0KH0qLBZyIA98fBMhFAMoYEdmbmktBmEnIzkdMjclJyMcOQA6GAEY', 'O34ePxxqcWsaDBZ2Bx0ZFAAVBx11ET4RJx4dKCI/JmgDQWMPHmcNJ2pmYy4gD2QmemoeFDx6MCBzdjFrIhQYMSsOAjMGOy05PBEt', 'a2BUXxIwDSY0eAoDahknH3QUKjAWPXBwe30pMA9/Ahd/dzEkYgFlDx0DNxRRal5OCiYAYn0yMzcPLCE0PzgwdR44BgQFLXAENx8n', 'Og4LDjoKREF3GActbDMMaycrFhQxMnsrLiA2DxEOHRcoPXsZABsEJn8QOXpoCHdqO3dJc3kMfi0ENW46MiYkIWMuARUzYC8nDBZk', 'fkBgZhMgGgQvNyQdM2ELEz4SdR8xBXAlDQoGLRYcNSZwJy4iYAtmYDxrLgVpRAcsYjs7HTITfDcnfh4jIDd1YBIidBQ+DCAmYn0i', 'dhVhOwYsMTMEfRkiNAQwNgx7HT8aLHI3Pxd+Zn1sLgYYYxcFExABERA/CRsNGRoQICV0ISwwJgkKJzZ2Bho8ISgILjQaKGV4cFtn', 'An4rNDpyCG5tAlN5WCY7Dy0qCzwiaht+MXd5JAoyOQojBh82JREEIiZ7BCoOKn40CCgnOjsHSUEDLh5jPRs3PBU2NT0wbh4IEgs5', 'ORJqFgV2NyIUNWkIIHhkKCs8GjUsMz1xamkDZEFcam0mOWovDGQUBWAlKztyKxsmHxE6ESRhDThlNxYsEBx6fRcVcigdNgBJGUMJ', 'MTo+HWQNFHkpZgkIEwc9emkNFWluR1ZGfQY6eDY8cxUwBBQDYiw0agAgZiU3LwQcJmwXGCsDEA4ECDoPOgIzDjJ+fAcGZz0LHTc5', 'KHEbJjZ0BBFwGC4wYDMxdRkkDio3BBY4NmosYwQ7OBt+BhErR1RnTWpiKTd5N24/LzUMF3B5DCMRNQsiLmA0OS09BywtJAsVeyQH', 'CnI5BRY6Dhw4AjgSJQ0jFQgnLXcVDzkIJRJ8bAhre3FNFxkEH2QUDDIdM2RjEQguHnYGDw8SeHYYaykmLmQLFSwtFQkSCTcqamZq', 'LTEKDRFkYBF0ADozCXgoKSwXCgUba1pqIhhhOAYOGD0sFmc0PzYfD2wlJXZ7NX01aWcLOiMPOxogLD4OAh4NcQdfYBw0AR4WNS4R', 'PjsYMTZqZCkoDWIrezYNYG0dFHoJNwcsZR8nJndsMwgiFm0+Nzg6RkZQXgkxIj8xDQ8jEwkNeScmdg0BNHAnEz4UBjwCJXk2ByBt', 'cSMxY3JvIScSHQY2aTwoPRwRESMFLiZmOWo1GEAfBW0LAmM8HAZseAYtNzQuZxkyLB0cdjchKSQsPmUqAHQxLRt4LDITcCAeW0hx', 'diYHPiw7LVNkZW07Yn5/HC8MJ3BoHWYTCDs7LhlwAw1zPAIbASh/Fih1Hx0bHmdzDzYnVWpmfC4EHhEbdS4Acwg/FnEHbiAxPQ9q', 'PmEMfXI1AxI/bSl9AiUVN2AndgE3EilhEQVnSAwzOTt/HyIIBCYEIGYmJnlxDSomcXNkfCs2aRB+HxctMj44LRZ/CW4RUUJABSkC', 'NRhnHzgxJyJ7I3Z5bjp8LS19Z2p8DGYANQExaR4vJAQqAAMbaQxoYww6Jh8KPGMRNWYJLRw6LhodJXxvaXUJcAVwLXweFBkzCygR', 'DzJ+VlwAOWAcbBMHaWoqFgRveA==', 'GXxoP3QAZg1sFgt6bRUyGy8vNg0lC2gRMmsPACcuBT8FYD82PigyEnIpeXoKATwFOhwTCTAjc3o7NHQeFRE8GC51LyYMJGsJNSZi', 'JioLB30oB2E4Nz43Jh58Ki9pH3EpM31CHUMQM31jGyMTZA4IAT4qIBtvMSspbnYyMzZtHSAiGw50AjAHIhEqJiptQ3xCRTwTFSZm', 'Pg8mLBcgCCAzLjUhex0INgQeIWFtLz1obVNIHXMRbQN/JCUHIS8UO2AtDnUgMBUZbg8HIGY5Y2EdNnAkN3EdfgUJJzMpV2VTRgds', 'ZyQNPSo9eTEEPRwqZ2QFeDA7NGceahgcLCgnYDciLA86PDAjDzp9CjAYEA5jLRMyf3cULH4Ram5AVGVSERMaICJxBiYRJW1mEj07', 'dyBwA1tXUgYdODMRFj0aJiYvIiMJIi0pKTsVC3kvZSYeNz48AzkMMzYYCQY9LiteWFhcOy0ibSA2OzYgJ34CNQVyHAslKnklDyYk', 'MicIFmUFJTwbOQJkGXQzAxETRwFIXhsiDhgKFwwLAy4FFCE2ERc8ZwYGIQ8XGQ5iBzYTCBk2DHclZxowYSV0R1V5PDMnODQTKAMy', 'awVxdFUWMgYbPzccPTAWHWEnJDUcAS0OOHE5CggYPmopMXVoZjwLAQV7dnYHcHNHUmsUBCUZKA8hPBRtPC01CXEVMDorBx5uIwoq', 'Gy0qNzUqPDoeE3AcBV92XA0NDX8HHSQFNnE3Pio1AShtKikuFRUPHCgHM2R8f3wgZAo6NiYpK3JqAHNYVhhtKywIByc4ChJnYSgU', 'MRUmHgYcJmghCDpnYgJfZj1geTo8cxIDLzcPCnMpOxYgcB11Nh8rC3RhagouDhQtPAthBgghCilLCGdSbmZjID1wBmFwJiU5FTYb', 'FXQ+ZzIWOAhoDy8Oe3h2Cg9pATkyKzcAGXknLyFqOy1EAUFWaDgiIgQwFyBzdDMIFToICg87KiQkJywfCiQEOGM1dhM9O3c0B3MW', 'JTM+BB4rGCw+EDIwOzQGKBQCNgwiE3t/CjsOPSIJMgp8YG1qNHtjYTYweBY3DQdwAQVxEmYcJzkJICEaaGojACgafzw5BW4BFmhm', 'fSUdfQVmASgaYnY9DyAGP2pxaWYHJiEmYTQPC3w3EyAkAwQJKQ4JGAoEeAgILmUlCz4MMWIjH3doODQ9BQVhWAQHeg42N3hmEHdy', 'U3J9dgcUdBATNBg+AS4UNCcnCBQaPS8DGiA9C2khNCEXMiEwfx03Zw13CBR1fGRtBhc7MDAxLxwuKWcmLCp3Yy45fyslICNqEiUa', 'LjMpDxo5Om4IMgMKPQAEJnkWGRI8NBwkDHtnej04OHsCCgd1R1BzESR6NTURFwcBMDM9cXs4PxsRLnkmfAQYKx4TJzMXdhMTNisc', 'IToENyojGCsEEWwAPCwnGTJ2YQkFAHMHPWMDFhEJLQMkLWIwND0Ya2omeyY1L3I7OWUKPwB0LSc5fnwcBxAJOgACZV4VHBYkBjsT', 'BQE2ASkCFiFgDAV/cH59ECUSF2hkCVFkEx06H3k7JWU9KTQcch5qbzY3egItOicmawIdeBAXDSBiKSRtJg4NOUNDBhsZDBwgNw4L', 'fC02JR1hGTJ8PRMFBhMKdDYhPTAnDT0NHhk1HhoScnIfAzYbNyYVCmp8fFFtPQYjFX0Oahd2AA0+NBkHcTopDRIBLQI6bjMiBhgr', 'OSZxegMtAC0CKH4QLxZsMX57MgYxE2lKeHYABSx0EQgMHCUEJRIiNSF0KWhuAHc0ZDxhMgYYeh0jOgQ9ZH4aLnMJawtlYX8wHRQw', 'KGgMKz0pWVVKZSUNFjVqAhMpMwodZwIGFnESaikFeggxCGY3ZgAycHVmGSEiEDMKHxNKBANHEGYPe2o0FSB2MA07dR47PRApOnUb', 'JXIUHTgiNnNpYGorJDQADwI9ABV8LjEULXEEF2odPRYaV0h5VQtkNg4VBiUJB3AyMBw4CzUvNz0uMicqYiYAYwFsJxRjOAwjHT0v', 'fys2Pw99HmM5CGY8MBEtfhkqYHYaO1VCWEw6YgcReTs6MnINPxduCzksAT09IDsMKxp0CD14MSJsE3EpFAUHNjMJcEJqVj0YZxZn', 'YiYIMGVuOiM0MjwvC2xgfTonPwh7AHUaLicBOSkmIT43SHRAejobdDw3FHBqaiAsHxZ6JWs3DRsICXp8CxlpMRcwHWxsCDl9bxMR', 'NmgcGScjDhQnPjQZfx0LJzEHCS0cASt0YmdHRykhCxsVKT0kai9iZRY+DTkSKnAzLSYBJQ89EX4aKDUDDQ48Mn99HjkHfgZxPCId', 'NSEuGAkxOTMGfH1rHB87fH8zOwU8HSIQHAx2KBlCeXwxP2c3PDVtIw0yMmZzYxEiLis+DykCJn0FYT0eYAkRbCcLKG0vcQFsSElq', 'LWJ9DXgOOy0uJzt4CBVqKRZ5YyJ7ez0kASsadQoHBHRqQiokJgYRLzAiP3IdIgIaDjFgNS4uFnMVZRo2IwE7ABF7ESQLPBkpDmtK', 'DhB2IyM5GTUCZDM0F2c4HBsFAhcdEUtAY3B0GydgJzcQeAx3Nj8TFQJpGzk9biI8cB1sPgMZJhMhOTx+LSkuIg0xSFNlZBc7IzAd', 'FDkfJzBsZz17JSMuKDwPBzkOZCkZeSpmFDpiGR8ZDXwyEREEe19ANDsFZGQVMgd2G2Y6MDV3azclGCYZBRclL2U/ORMWew0/Ljoa', 'GBF8ZQYlOQJ/FhEOIBoFfGcAejolI20hKwckC3NiGB0hDBcLJRwRKAoII2gTPwAbJnZiDB8fbCpuNQdoVANGJhQeIzR3LwZyMDoU', 'MiYqPBEXD2MBezYECzYNLQ5rcHkqJBMwLyRqNmMcFDIcZAJtJDN2GVpSXlIMZz9jGA8ueA9oBAMcfhAKbm0PLxYeABM5KisuMTIa', 'GypgFGI7LxNiFyVlISIOMxY3MHoADygdPyUEIHxtc3UQPAB6cAAnYB5DYEt7BQY/NgEjahsjDWYHdw4zKTsdZwtzPhEiNH9mdjgE', 'FgkfFQctAxUGHiAhZgFsO2IfHRV5bhcyC3l5XR0mOhpqKzI3PzBsHwAvKgkxNgoMBCF3NgkHNxlgITAwDiQUdHM0FylAaXwfLBQu', 'KWF5OQYIMmB6eyJpJnBpNAdkA1obDQYjPjhsBXExFiI8fy8sITcZIw0+MWQ+KGMiJ3UOJXggD20PFWkURlJ8ZiY0BiQHaiUqMwAA', 'Akh+EjMaIGJqHhEiLCUnISZqDyBpKjUXGRI7OgEEDg59AjoiISMYeDRpKgdaBGd0N31mGictYg0NHDZ8CgUPMjY/ajktPSA6FCR5', 'OQcnITM9fA05QR8GWCYCAiIobjg5AnI6HiEWBmloJTl3KAh8Imt/ISk1cyQnfSN6ESE0ADx4AldbMTE2EQdwNhp8aCQjHy0oKGBm', 'N3khBwoUMyMffRMNFm8mXH1RZWszKiFgeRsKIhosCDdjACIbbR03aA8zCBVkBgECEjsMEHwlbHsGbG0EX0pNOS0rNjMLPBwrdgwn', 'HAZgGCtkBCwmUQllBx4xNTo1Lg0YcgU/YyxjOx4RDA47KQogCxYZEz5tCSUZMxsdZy8zKGxbRVB/FD0lGSAnajF0Ljs1JxUgYxo1', 'B2c/GwJqPSIKKh0FPyknPQ1oeBcwBhVmDmZ9ODp2JjJmJmVpIS9uaHBYBWAUejoiJg8lNXFoHAdwCwkCLzx+IzFycCYtHxQ2MSEV', 'Kj53FWZ9XUR9LGQ7bAsqMBB9DmYldxYYAwswAG4KLTJiNyhhAGYnexZ+NgUxIXZuNgNhVmUvNngeKjU2agsZMycGLyQ0Dwoxbisu', 'Aw8rA3IzfwwiFDIXORkdMn8dGi0lOycobXFKSwBpMS0xJg0JOQkHegF3Fi8ACzQZKmwvNSQSERY8GTIWIiIfP2gzajQ8BQd2dSdh', 'bgV2dmRqJikpZysuLBF7Ey54EHIfERpiAV1+dDNjByAMLAImIgEhDWd0InZqAAQ7LgACLAMCOyU1MyEYOHk+PXE1ZwdYS31wEisZ', 'Jw07PhI9Hi9gCUIGPg0uNh4NZjUTc2VhbgUua2AREgINfiATPTFqfCAPJCQ4KyQqHgwba3gGAFIWFjRgMTFoB3YwN2orJwkUMyUC', 'FXdjSkMwI3UENxttYC4yE30dNg4TOj0pCTAfamMeNjUhDSoTF3soCi8bCxU6R0VzRhIDFSxrKzglLDMsZyx/IgxvaxAtDBEmBhA0', 'OwAXNScuLg8ucQYGEWsHBQcyZCx+fTsjago5AGhzXSgvdSA3JyUcI3AcEwsENjAMMiQCCz9uBwkiChlnDBkaAwEpOy11bx5WektD', 'LTMeNislYHAdCBIVaBB2JiktHDg5fShnNw4SIzkaFwIXbA1eWmJzCz0DYwsxEWYTcRthAQgVAzcPHzsbDgokLCgwIWwwdR4NdiYn', 'bGUIIwpmLzwTCntGSFIWBRsFGHYPCy56MAMGLjszPwYYNzcyHxUpADp+FmoxEHp3BzASah49Yh8BXTMgJmAKNjcLHAowOjNjNwlr', 'Nj8mPyI/LDsEERwrHmh4QQJbcDQ/GRs7LDZzaCAcfWMQEWg6DisoDQNlbHs3LAE/em08ORosCCEDKHMIS1kcDHwcJnAbOx0aP2Id', 'BWUPNnMjMXQNOQ89cRo1Yyo4Dg1zADAedx4lc2oJAAkAWhMvNiwFbmZqLnE/GCg6ND46Nz4FexEJHT4SFQEEBnVsLigUNztzaTxX', 'YwI+GSs3HBliOw8ACR5yKSUvNjJyChIhKyw9CXMRZjkrOyV1O2ldYXlNNQd9HiU7KyEsEQU/Hx5yCm1oPCkXfAk8Dhc3HCANcwNi', 'fittKlVGXR8qGwciJ3dsJX03J30qHBEuOzt9Li8lHSNtJQsqYjAbEz8AFBExCmg6dQIAQGobKxBnODY7ciUhAg17A2IoOH4wDWR1', 'KQsgFwcOLXQ6NjgIfzkHOnEqEAwfITsHCwgXcGN/URU6KWAXBDQSDno3AD07B3V2LR4UDh4iJTIKPClndDs3Yio5OnotMwgKUQRd', 'OjMZEA8aJhsyCGwFQmlzRTcmIyQKBBdqKDAZMAc6dgI2FwcQenoEa2s0PyYAIBQ1OwQMOQItDBcZWnlRCSEcHTgqGAMHehs4JjU7', 'Nw0YKygJNB8ldy18EwdlCBwjKCpiM3Y3aigHKzNqG0BQCQUDEiUuKwQoEwURATgWDzQsGCIxeQtkOjwCO2xzGiMNO2ExKn0pE0tW', 'LwcdITAmfS4ZORs+PjkOfg4KB2gzeD82FmB9KTcteHcABkYJHXouZDwZYhEoOzw0FxMrBQhvaWopJyd+dB4+aWUlYwMmLRMjBhkJ', 'PidiEjV/BQADcQA8ZjYBHnA2HD8aKDR8BDAXS2NQZCUjCSR9IjMpNTQsKAoUB2kdBjwFByYNfXBhCAQmdDUgDnoZdHsfCQVURkhA', 'dg0KeikbCFZya0AJM3sCHwInayZ0BTVyCgU/OmoQDXA6Cj5sBDF6IXQQYwAsBTISPD4tdWIHH2YjLjsldAk1fDEjZ3IAGw0AFjgq', 'BUhiHQ0WFWsqChACKi8TLBpzHSsHEhc0KjdjPD4bGGcNcgwlAQQwDAhuHXwAcQcZICg2BRQoF3MHOWM3KBc5FgYNbgsuIzNqGAYp', 'FGcoNDMxLgJtOnF6cC8uJT13egccOhkFIicuKWh7PyIjERFxECtnZ3sfGAd+HmUAOzkxBjAlCyoJMx8MDhZyJRFhaxwVLHsrCW0w', 'C2UMaDE7IRZuAQxrLUZjS240EGcYCBM+HjNxNBQNIi0KO2oNOzQFNDEOaBR3NQQpYR0hIxEScQoncF5XUwkzFGIZLxc5MjYHZm4P', 'OxMmHzp3JzgmOCpvAyAuagVUBkFmJBQ5F3k2HBYtIxcPOQkXYSgPNBUeIhsmGggOOm53LBk9Azs9PGgISFgDUjAdfyM3IjAgdzE5', 'Cg4VHUh8Rx4RPQMEDGk0DnBmKxYiGApqFTxwOjMdJTg2AXYgADYde3olJ3gfDQ56aAtkHQ86AhYmCzVwFBo7ASZ4Dyw7eyhoBXwr', 'N2UjOjgrFg8oKw0XaS8HLTgBBxE5OWMVAm1wEwEGZBomG24/DGpFaFAwLTg+Fm4wPXxsH2NuFCUoPmd4AzoONRlvYhUNNgloADl7', 'Mi93QmJ7KiYYYhsPdBBqNCUmfDtqHgEnAyRoAjcCMTtnBwQjDgUeABxwERExPVxYWAVsIx0+NTIPazZ0BQEgHRgxNgp8Jjk+NWQ6', 'GCgMNGN0ChRqdGMyGDwLIyUgBxUZOiYrAjkLNjZ/ehY2JB8vK0FZBmw+Ni4eHXIpfHQ3ED8NJgY/LxQdEQI9IWY6NhVkMiYUOmY4', 'KGw8bgwYX0ZFUGc4Zzo9AAwpagA7Aj90Gz0/MAt4aANzNnRkHGAwNjVmYil9OSxzaxBLfGVsax42Y3kANRgpbCRgdiE3LjttG3gu', 'JyAzJDdsZTAdLREyDXgALRkANQFpcAJ0Vjw0eiYbaisxag0eGm4+JQk1CSx1bD18NghjOGARLXsnLCwnGREpbBwBYlpaF2cnOD95', 'ES8EY0hjMBEvGzEvbwMvcmQVCSYnEmweBhszLnZ9CiULegR1DWwfOiwdBAwzZl54cQA5DCQ/ZxU9IT83JCYzCxMsYWwvbjI6aiZq', 'HHB/enoBEwkqOBhmSlpSGGQCYDw4azYJBB0dEQ91Py8dBzciJy9nFAo1ZGJxBjUPGys8eQI2CF5gSEwOInwYHAIlIyxsDAINFhEM', 'KzwvZTc2B3kEbhcaKzkBLCEwD2h9V2h6ayY8YR0UcBg2O2wcLjgmDXZtGgYPKTE0NT0FNycHNSB+FnlmPi1vGlh4XG00Oh0yYTQM', 'dQYXD35mJXIlYRUPZComHHQ8bgkLMWgTHyU9B2J4ZB1sZS8+NDgqaig6BVxIRxI6FWIHGRo+FSEvKiAHLC4LDX5udydqPDRhADYh', 'fAsANCwYB200JX8DJDMKIjAAHSkmKhpiOx8tLT13LBh2GwpYaREDHmoMaGEwARICF3oFHWlmOSokei8qOClkfQ0QCRkdGTlmGQNy', 'HgI0DQs7FTs6NjcLeCkSFX8ADxg/ahR/NR9mHSohcT0EBnwgFidHaF5wOmF6YSU3Cyd9eiB9Px1qDghtIQkkDXYYETd9PzAuNSV/', 'bGQPHSYGMxs/EDglMRsHKBJvKgd7fSkgMmAAdxopd2IiGnwWchEPBVgfV2UxGw82YA8PETEkZiAXAQIIKD1jeS5yCiMPaTYNDHMg', 'exgoaXIuKCkFUkcEBzEvYGVzGDwsEA1nHTxqPCkuACwbEQI5L2IeYBAoAG0iFx4tODcSCkNkSldwBDYiZgsMCyglNgczHSJvLHB+', 'MTlgPC4uLzYBOioxFzoZVBkfGhs5Zmt1FhELMmcfdDkIEXYFPSclPiImDSkLIgYyOTgmegMMLjQQNlVGSB8RYXQmFSR0Fwh0En0r', 'NjZzYmUBNRQtFy4ZEXF9AxUFaCgBNgIyNTwNHhkMPQltekFfejsUASceDmdqI3UNEwN+CSg+OR8saHJ8BxN7HA4SfQ1scQkGKyk0', 'MW4zaUFdBAY5HwElFQ0TGXwwbXk3L3ErMGsRIg1gMh0FNhwnJCAADB4FFjQvAQwRRGB8XQdhZx4gIxkRHScMZAocFSIrJXgKAmRx', 'ZDYBNCJ/D3Y7AQo4CR8TbAx0BEQFB2YHN2IsagEvKRwwH2cANhMXKTULcx0CEwIWeQcSGxksPz0qP30IGgRAdWUnPgADChEsOy8N', 'M2EKAjUHOxwrF3E/OGcFNTcoHUEGG20bPjoYExYYcw8dFAkALSN2awEucTM1IXQkZnoMNyQgOg1hGyoDFSdGRnFkDS8eDT9xKjUR', 'dAsMJgUiNSkkbR1YaF1sKWwoHyB1HRZyd2cAcX0CEgFsAHcIeQ4ENH88JzIXKz0dBi05DigyDEdIAEZrEQMiNDYFIWp6LTsjeChv', 'fAZtHXRSfh8pMC97MxIpYXMLJzZ3O3A7IBlwIzU6LWc4YzMIJh0vJD0KAwZ8MzA9dXFQXzZsKgQqEjZhEi1mBXd1Ah0uKyQndHoA', 'NRohNQIuPgk7cHBKS0w2DwIRZQwZC3AHGRQBHg9vOzMuJwh+cQIZJgJ9GRF0NiY3P2oqK2wZWml1czEsADcVNCUjBHQxJyE0dTdq', 'NjwoBAw4HDwlBQxzCgYxMWd7JyF2AnwNJx4Bag4JRncAUAcDKiU1cydgDDYUNjcOA2lrDjoCAQccGmoICAxnDzs7OQ4UKwh3AQdG', 'AjEOJHYkZBkCGXkJEhgidzslAj1oEgc2EysLHj4FNz0mNghoClVHDRUmfWUoIw5icgsgMHAUJB4yKz0wJ3x9BDwaMTs8DiY+AyEp', 'LgZlDxd9BDcoCn9wJWE9LjIfPWBeRE4aJwECMCc4YwpxJxQLBwkTDigOIDo9NgQlBmB8EnN0Ez4pLzUdC20dB3tlfg09FnthFQt8', 'YAgDN3FsNQwJehhwPxQDbywmMQ0SNmtsNSctexd2BQ82CQ4bChA+ax8EYiwgGAxhLhgJFhYAayN+K3UMHTAndXMVOhwGIx8xcAsd'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
