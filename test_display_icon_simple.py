#!/usr/bin/env python3
"""
Simple test to verify Display.png icon integration
"""

import os
from pathlib import Path

def test_display_icon_files():
    """Test that Display.png exists in all required locations"""
    
    print("Testing Display Icon Files")
    print("=" * 30)
    
    # Test locations
    locations = [
        "assets/Display.png",
        "YES/assets/Display.png", 
        "YES_OBFUSCATED/assets/Display.png"
    ]
    
    all_found = True
    
    for location in locations:
        if Path(location).exists():
            print(f"✅ Found: {location}")
        else:
            print(f"❌ Missing: {location}")
            all_found = False
    
    return all_found

def test_icon_mapping():
    """Test that Display.png is mapped in pos_app.py files"""
    
    print("\nTesting Icon Mapping")
    print("=" * 20)
    
    files_to_check = [
        "pos_app.py",
        "YES/pos_app.py"
    ]
    
    all_mapped = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            if "'Display.png': 'display_settings'" in content:
                print(f"✅ Icon mapping found in: {file_path}")
            else:
                print(f"❌ Icon mapping missing in: {file_path}")
                all_mapped = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_mapped = False
    
    return all_mapped

def test_button_logic():
    """Test that display button has icon-only logic"""
    
    print("\nTesting Button Logic")
    print("=" * 18)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_logic = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Check for special display_settings handling
            if "if icon_key == 'display_settings':" in content:
                print(f"✅ Special button logic found in: {file_path}")
            else:
                print(f"❌ Special button logic missing in: {file_path}")
                all_logic = False
            
            # Check for icon-only sizing
            if "width=40, height=40" in content:
                print(f"✅ Icon-only sizing found in: {file_path}")
            else:
                print(f"❌ Icon-only sizing missing in: {file_path}")
                all_logic = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_logic = False
    
    return all_logic

def test_obfuscated_assets():
    """Test that obfuscated version has the icon"""
    
    print("\nTesting Obfuscated Assets")
    print("=" * 25)
    
    obf_assets_dir = Path("YES_OBFUSCATED/assets")
    
    if not obf_assets_dir.exists():
        print("❌ YES_OBFUSCATED/assets directory not found")
        return False
    
    # List all files in obfuscated assets
    files = list(obf_assets_dir.glob("*.png"))
    file_names = [f.name for f in files]
    
    print(f"📁 Files in YES_OBFUSCATED/assets: {len(file_names)}")
    for name in sorted(file_names):
        if name == "Display.png":
            print(f"✅ {name}")
        else:
            print(f"   {name}")
    
    return "Display.png" in file_names

def main():
    """Run all simple tests"""
    
    print("🖼️ DISPLAY ICON INTEGRATION TEST")
    print("=" * 40)
    
    tests = [
        ("Icon Files", test_display_icon_files),
        ("Icon Mapping", test_icon_mapping), 
        ("Button Logic", test_button_logic),
        ("Obfuscated Assets", test_obfuscated_assets)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Display.png icon is properly integrated")
        print("✅ Display button will show icon only (no text)")
        print("✅ Icon is available in all versions")
        print("✅ Ready for use!")
    else:
        print("⚠️ Some tests failed")
        print("❌ Display icon integration incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 Display icon integration complete!")
        print("📋 The Display button now shows the icon without text")
        print("🔧 Users will see a clean icon-only button")
    else:
        print("\n❌ Display icon integration needs attention")
    
    exit(0 if success else 1)
