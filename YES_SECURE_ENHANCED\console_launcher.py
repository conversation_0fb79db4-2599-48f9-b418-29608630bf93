#!/usr/bin/env python3
"""
Console-based Secure Launcher
Fallback launcher that works without GUI
"""

import os
import sys
import subprocess

def print_header():
    """Print header"""
    print("=" * 50)
    print("🔒 SECURE POS SYSTEM LAUNCHER")
    print("=" * 50)

def check_security():
    """Check security with console output"""
    print("\n🔍 Performing security checks...")
    
    try:
        # Try to load security manager
        from security_manager import security_manager
        
        print("✅ Security manager loaded")
        
        # Get machine info
        machine_info = security_manager.get_machine_info()
        print(f"🖥️ Machine ID: {machine_info['machine_id'][:16]}...")
        print(f"🖥️ System: {machine_info['system']}")
        
        # Perform security check
        security_ok, message = security_manager.security_check()
        
        if security_ok:
            print("✅ Security checks passed")
            return True, security_manager
        else:
            print(f"⚠️ Security warning: {message}")
            if "UNAUTHORIZED_MACHINE" in message:
                return False, security_manager
            else:
                # Allow launch with warnings
                choice = input("Continue anyway? (y/n): ").lower()
                return choice == 'y', security_manager
                
    except ImportError:
        print("⚠️ Security manager not available - basic mode")
        choice = input("Continue in basic mode? (y/n): ").lower()
        return choice == 'y', None
    except Exception as e:
        print(f"❌ Security error: {e}")
        choice = input("Continue anyway? (y/n): ").lower()
        return choice == 'y', None

def authorize_machine(security_manager):
    """Authorize machine via console"""
    print("\n🔑 MACHINE AUTHORIZATION")
    print("This machine needs authorization to run the POS system.")
    
    auth_code = input("Enter authorization code: ").strip()
    
    if security_manager:
        try:
            if security_manager.authorize_machine(auth_code):
                print("✅ Machine authorized successfully!")
                return True
            else:
                print("❌ Invalid authorization code!")
                return False
        except Exception as e:
            print(f"❌ Authorization error: {e}")
            return False
    else:
        # Fallback authorization
        if auth_code == "SECURE_POS_2024_AUTH":
            print("✅ Machine authorized (basic mode)!")
            return True
        else:
            print("❌ Invalid authorization code!")
            return False

def launch_pos():
    """Launch the POS system"""
    print("\n🚀 Launching POS system...")
    
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found!")
        return False
    
    try:
        # Launch the system
        result = subprocess.run([sys.executable, "main.py"], check=False)
        print(f"\n✅ POS system exited with code: {result.returncode}")
        return True
        
    except Exception as e:
        print(f"❌ Launch error: {e}")
        return False

def main():
    """Main console launcher"""
    try:
        print_header()
        
        # Check if we're in the right directory
        if not os.path.exists("main.py"):
            print("❌ Error: main.py not found!")
            print("Please run this launcher from the POS system directory.")
            input("Press Enter to exit...")
            return
        
        while True:
            # Security check
            security_ok, security_manager = check_security()
            
            if not security_ok:
                # Try authorization
                if security_manager or input("Try authorization? (y/n): ").lower() == 'y':
                    if authorize_machine(security_manager):
                        continue  # Retry security check
                    else:
                        print("❌ Authorization failed!")
                        break
                else:
                    print("❌ Access denied!")
                    break
            
            # Launch system
            print("\n📋 MENU:")
            print("1. 🚀 Launch POS System")
            print("2. 🔑 Authorize Machine")
            print("3. ❌ Exit")
            
            choice = input("\nSelect option (1-3): ").strip()
            
            if choice == "1":
                if launch_pos():
                    print("\n🔄 POS system closed. You can launch again or exit.")
                else:
                    print("❌ Failed to launch POS system!")
                    
            elif choice == "2":
                authorize_machine(security_manager)
                
            elif choice == "3":
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice!")
            
            print("\n" + "-" * 30)
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        import traceback
        traceback.print_exc()
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
