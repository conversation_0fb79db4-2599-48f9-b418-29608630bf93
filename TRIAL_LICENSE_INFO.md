# POS System - 24-Hour Trial License

## 🕐 Trial License Details

**License Code:** `LC-78C8C6898C3A4`  
**Duration:** 24 hours from first activation  
**Type:** Hardcoded trial license  

## 🔧 How It Works

### First Activation
1. Client enters license code: `LC-78C8C6898C3A4`
2. System creates `trial_license.dat` file with start time
3. Full POS system access is granted
4. 24-hour countdown begins

### Subsequent Uses
1. System checks existing `trial_license.dat` file
2. Calculates elapsed time since first activation
3. Shows remaining hours in license message
4. Blocks access after 24 hours have passed

### After Expiration
1. License validation fails with "24-hour trial period has expired"
2. POS system requires a new valid license
3. Trial cannot be reset without manual intervention

## 📋 Implementation Details

### Files Created
- **`trial_license.dat`** - Stores trial activation data
  ```json
  {
    "license_key": "LC-78C8C6898C3A4",
    "start_time": "2025-05-30T17:47:48.557646",
    "computer_id": "A272B6AF8EEFE067"
  }
  ```

### Security Features
- ✅ **Computer ID binding** - Trial tied to specific computer
- ✅ **Tamper detection** - License mismatch protection
- ✅ **Time enforcement** - Strict 24-hour limit
- ✅ **No reset mechanism** - Cannot be easily restarted

## 🎯 Client Instructions

### For Clients
1. **Start the POS system**
2. **When prompted for license, enter:** `LC-78C8C6898C3A4`
3. **System will show:** "24-hour trial license activated"
4. **Full access granted** for 24 hours
5. **After 24 hours:** Contact administrator for full license

### License Messages
- **First use:** "24-hour trial license activated. Trial period started."
- **During trial:** "Trial license active. X.X hours remaining"
- **After expiration:** "24-hour trial period has expired"

## 🛡️ Protected Versions

The trial license works in **ALL** client versions:

✅ **YES** - Protected version  
✅ **YES_OBFUSCATED** - Secure obfuscated version  
✅ **YES_COMPILED** - Compiled version  
✅ **Main System** - Development version  

## ⚠️ Important Notes

### For Administrators
- **Trial is per-computer** - Each computer gets its own 24-hour trial
- **No database required** - Works offline with hardcoded validation
- **Cannot be extended** - Hard 24-hour limit enforced
- **File-based tracking** - Creates `trial_license.dat` in POS directory

### For Clients
- **One-time trial** - Cannot be restarted on same computer
- **Full functionality** - No feature limitations during trial
- **Time-limited** - Exactly 24 hours from first activation
- **Contact admin** - For full license after trial expires

## 🔧 Technical Implementation

### Code Location
- **File:** `license_client.py`
- **Method:** `validate_trial_license()`
- **Trigger:** License code `LC-78C8C6898C3A4`

### Validation Flow
1. Check if license equals `LC-78C8C6898C3A4`
2. Look for existing `trial_license.dat`
3. If found: Check elapsed time vs 24 hours
4. If not found: Create new trial file with current time
5. Return validation result with remaining time

### Reset Instructions (Admin Only)
To reset trial for testing:
```bash
# Delete the trial file
rm trial_license.dat
# Or on Windows
del trial_license.dat
```

---
**🕐 Trial License: LC-78C8C6898C3A4 - 24 Hours Limited Access**
