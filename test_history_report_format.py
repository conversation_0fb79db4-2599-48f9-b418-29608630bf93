#!/usr/bin/env python3
"""
Test the new history report format and logo positioning
"""

import sys
import os
from pathlib import Path

def test_history_report_format():
    """Test that history report matches the requested format"""
    
    print("Testing History Report Format")
    print("=" * 30)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for NO LOGO in history reports
            if "# Header - NO LOGO for history reports" in content:
                print(f"   ✅ No logo in history reports")
            else:
                print(f"   ❌ Logo still present in history reports")
                all_correct = False
            
            # Check for cashier field (filtered user, not printer)
            if "cashier_name = filter_info.get('user_filter', 'All Users')" in content:
                print(f"   ✅ Shows filtered cashier, not printer")
            else:
                print(f"   ❌ Cashier field incorrect")
                all_correct = False
            
            # Check for period formatting
            if "Period: Today" in content and "Period: {date_range}" in content:
                print(f"   ✅ Period formatting correct")
            else:
                print(f"   ❌ Period formatting incorrect")
                all_correct = False
            
            # Check for table format
            if "Product:" in content and "Price" in content and "Qty" in content and "Total" in content:
                print(f"   ✅ Table header format correct")
            else:
                print(f"   ❌ Table header format incorrect")
                all_correct = False
            
            # Check for underscores as separators
            if '"_" * 50' in content:
                print(f"   ✅ Underscore separators used")
            else:
                print(f"   ❌ Underscore separators missing")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_logo_positioning():
    """Test that logo is moved to the right in customer receipts"""
    
    print("\nTesting Logo Positioning")
    print("=" * 25)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_positioned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for logo positioning code
            if "logo_x_pos = x_pos + 100" in content:
                print(f"   ✅ Logo moved 100 pixels to the right")
            else:
                print(f"   ❌ Logo positioning not changed")
                all_positioned = False
            
            # Check for comment about moving logo
            if "moved to the right" in content:
                print(f"   ✅ Logo positioning comment found")
            else:
                print(f"   ❌ Logo positioning comment missing")
                all_positioned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_positioned = False
    
    return all_positioned

def test_table_format_structure():
    """Test the table format structure"""
    
    print("\nTesting Table Format Structure")
    print("=" * 31)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_structured = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for table row formatting
            if "f\"{display_name:<20} {price:<10.2f} {quantity:<8} {total:<10.2f}\"" in content:
                print(f"   ✅ Table row formatting correct")
            else:
                print(f"   ❌ Table row formatting incorrect")
                all_structured = False
            
            # Check for column widths
            if ":<20" in content and ":<10" in content and ":<8" in content:
                print(f"   ✅ Column widths defined")
            else:
                print(f"   ❌ Column widths not defined")
                all_structured = False
            
            # Check for product name truncation
            if "[:18]" in content:
                print(f"   ✅ Product names truncated to 18 chars")
            else:
                print(f"   ❌ Product name truncation missing")
                all_structured = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_structured = False
    
    return all_structured

def test_concise_format():
    """Test that format is concise and to the point"""
    
    print("\nTesting Concise Format")
    print("=" * 23)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_concise = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for concise header
            if "SALES HISTORY REPORT" in content and not "Generated by:" in content:
                print(f"   ✅ Concise header (no 'Generated by')")
            else:
                print(f"   ❌ Header not concise")
                all_concise = False
            
            # Check for simple total format
            if 'f"Total: {grand_total:.2f} MAD"' in content:
                print(f"   ✅ Simple total format")
            else:
                print(f"   ❌ Total format not simple")
                all_concise = False
            
            # Check that verbose elements are removed
            if "TRANSACTIONS:" not in content and "UNIQUE ITEMS:" not in content:
                print(f"   ✅ Verbose elements removed")
            else:
                print(f"   ❌ Still contains verbose elements")
                all_concise = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_concise = False
    
    return all_concise

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/receipt_generator.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all history report format tests"""
    
    print("📊 HISTORY REPORT FORMAT TEST SUITE")
    print("=" * 37)
    
    tests = [
        ("History Report Format", test_history_report_format),
        ("Logo Positioning", test_logo_positioning),
        ("Table Format Structure", test_table_format_structure),
        ("Concise Format", test_concise_format),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 37)
    print("📊 RESULTS")
    print("=" * 37)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ History report format matches specification")
        print("✅ Logo moved to the right in customer receipts")
        print("✅ Table format is clean and organized")
        print("✅ Format is concise and to the point")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ History report format may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📊 History report format successfully implemented!")
        print("👤 Shows filtered cashier (not printer)")
        print("📅 Smart period formatting (today vs date range)")
        print("📋 Clean table format with proper columns")
        print("🖼️ Logo moved to the right in customer receipts")
        print("📄 Concise format to save paper")
    else:
        print("\n❌ History report format needs attention")
    
    exit(0 if success else 1)
