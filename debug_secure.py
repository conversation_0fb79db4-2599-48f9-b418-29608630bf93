#!/usr/bin/env python3
"""
Debug the secure system to find the issue
"""

import os
import sys

def test_security_manager():
    """Test if security manager loads"""
    print("🔍 Testing security manager...")
    try:
        os.chdir("YES_SECURE_ENHANCED")
        sys.path.insert(0, ".")
        
        from security_manager import security_manager
        print("✅ Security manager loaded successfully")
        
        # Test machine fingerprint
        machine_id = security_manager.machine_id
        print(f"✅ Machine ID: {machine_id[:16]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Security manager error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_protected_file():
    """Test if protected files work"""
    print("\n🔍 Testing protected file execution...")
    try:
        # Test main.py
        if os.path.exists("main.py"):
            print("✅ main.py found")
            
            # Try to read it
            with open("main.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "ENCRYPTED_DATA" in content:
                print("✅ main.py is protected (encrypted)")
            else:
                print("⚠️ main.py is not protected")
                
            return True
        else:
            print("❌ main.py not found")
            return False
            
    except Exception as e:
        print(f"❌ Protected file error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_launch():
    """Test simple launcher"""
    print("\n🔍 Testing simple launcher...")
    try:
        import tkinter as tk
        
        root = tk.Tk()
        root.title("Test Launcher")
        root.geometry("300x200")
        
        def test_launch():
            print("🚀 Test launch clicked")
            root.quit()
        
        btn = tk.Button(root, text="Test Launch", command=test_launch)
        btn.pack(expand=True)
        
        print("✅ Simple launcher created")
        
        # Don't actually show it in test
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Simple launcher error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🔧 DEBUGGING SECURE SYSTEM")
    print("=" * 30)
    
    # Save current directory
    original_dir = os.getcwd()
    
    try:
        # Test 1: Security manager
        security_ok = test_security_manager()
        
        # Test 2: Protected files
        files_ok = test_protected_file()
        
        # Test 3: Simple launcher
        launcher_ok = test_simple_launch()
        
        print("\n📊 DEBUG RESULTS:")
        print(f"Security Manager: {'✅' if security_ok else '❌'}")
        print(f"Protected Files: {'✅' if files_ok else '❌'}")
        print(f"Simple Launcher: {'✅' if launcher_ok else '❌'}")
        
        if security_ok and files_ok and launcher_ok:
            print("\n✅ All components working - issue might be elsewhere")
        else:
            print("\n❌ Found issues - need to fix components")
            
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Restore directory
        os.chdir(original_dir)

if __name__ == "__main__":
    main()
