"""
Database operations for POS System
Handles all database connections and operations
"""

import sqlite3
import hashlib
import os
from datetime import datetime
from contextlib import contextmanager
import time

# Performance optimization: Simple in-memory cache for frequently accessed data
_cache = {
    'categories': {'data': None, 'timestamp': 0, 'ttl': 300},  # 5 minutes TTL
    'products': {'data': None, 'timestamp': 0, 'ttl': 300},    # 5 minutes TTL
    'users': {'data': None, 'timestamp': 0, 'ttl': 600},       # 10 minutes TTL
}

def _is_cache_valid(cache_key):
    """Check if cached data is still valid"""
    cache_entry = _cache.get(cache_key)
    if not cache_entry or cache_entry['data'] is None:
        return False
    return (time.time() - cache_entry['timestamp']) < cache_entry['ttl']

def _get_cached_data(cache_key):
    """Get data from cache if valid"""
    if _is_cache_valid(cache_key):
        return _cache[cache_key]['data']
    return None

def _set_cached_data(cache_key, data):
    """Store data in cache with timestamp"""
    _cache[cache_key]['data'] = data
    _cache[cache_key]['timestamp'] = time.time()

def _invalidate_cache(cache_key):
    """Invalidate specific cache entry"""
    if cache_key in _cache:
        _cache[cache_key]['data'] = None
        _cache[cache_key]['timestamp'] = 0

def get_db_connection():
    """Get database connection with row factory"""
    conn = sqlite3.connect('pos_system.db')
    conn.row_factory = sqlite3.Row  # Enable column access by name
    return conn

@contextmanager
def safe_db_connection():
    """Context manager for safe database connections with guaranteed cleanup"""
    conn = None
    try:
        conn = get_db_connection()
        yield conn
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except:
                pass  # Rollback might fail if connection is broken
        raise e
    finally:
        if conn:
            try:
                conn.close()
            except:
                pass  # Close might fail if connection is already closed

def init_database():
    """Initialize database with all required tables"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Users table
        c.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                is_admin INTEGER DEFAULT 0,
                button_color TEXT DEFAULT '#f8f9fa',
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Categories table
        c.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                image BLOB,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Products table
        c.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category_id INTEGER,
                price REAL NOT NULL,
                image BLOB,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # Sales table
        c.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                total REAL NOT NULL,
                date DATETIME DEFAULT CURRENT_TIMESTAMP,
                items TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Add items column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE sales ADD COLUMN items TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Sale items table
        c.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER,
                product_name TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                is_extra INTEGER DEFAULT 0,
                FOREIGN KEY (sale_id) REFERENCES sales (id)
            )
        ''')

        # Receipt settings table
        c.execute('''
            CREATE TABLE IF NOT EXISTS receipt_settings (
                id INTEGER PRIMARY KEY,
                business_name TEXT DEFAULT 'Your Business Name',
                business_address TEXT DEFAULT 'Your Business Address',
                business_phone TEXT DEFAULT 'Your Phone Number',
                header_text TEXT DEFAULT 'POS SYSTEM RECEIPT',
                footer_text TEXT DEFAULT 'Thank you for your business!',
                logo_image BLOB,
                paper_size TEXT DEFAULT '300x95',
                font_size INTEGER DEFAULT 19,
                line_spacing INTEGER DEFAULT 8,
                selected_printer TEXT DEFAULT 'Default Printer'
            )
        ''')

        # Add line_spacing column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN line_spacing INTEGER DEFAULT 8")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add selected_printer column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN selected_printer TEXT DEFAULT 'Default Printer'")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add logo size column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN logo_size INTEGER DEFAULT 100")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add logo file path column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN logo_file_path TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Custom paper sizes table
        c.execute('''
            CREATE TABLE IF NOT EXISTS custom_paper_sizes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                width INTEGER NOT NULL,
                height INTEGER NOT NULL
            )
        ''')

        # System settings table
        c.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY,
                language TEXT DEFAULT 'english',
                history_reset_hour INTEGER DEFAULT 6,
                history_reset_minute INTEGER DEFAULT 0,
                max_product_columns INTEGER DEFAULT 4,
                product_button_size INTEGER DEFAULT 130,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Add history reset time columns if they don't exist (for existing databases)
        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN history_reset_hour INTEGER DEFAULT 6")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN history_reset_minute INTEGER DEFAULT 0")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add display settings columns if they don't exist (for existing databases)
        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN max_product_columns INTEGER DEFAULT 4")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN product_button_size INTEGER DEFAULT 130")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Create default admin user if none exists (but don't update existing admin password)
        c.execute("SELECT COUNT(*) FROM users WHERE username='admin'")
        if c.fetchone()[0] == 0:
            hashed_password = hashlib.sha256('@H@W@LeComptoir@'.encode()).hexdigest()
            c.execute("""
                INSERT INTO users (username, password, role, is_admin, button_color)
                VALUES (?, ?, ?, ?, ?)
            """, ('admin', hashed_password, 'admin', 1, '#007bff'))
            print("Default admin user created with password: @H@W@LeComptoir@")
        # Note: If admin user already exists, we preserve their current password

        # No default categories or products - clean start

        # Create default receipt settings if none exist
        c.execute("SELECT COUNT(*) FROM receipt_settings WHERE id=1")
        if c.fetchone()[0] == 0:
            c.execute("""
                INSERT INTO receipt_settings (id) VALUES (1)
            """)

        # Create default system settings if none exist
        c.execute("SELECT COUNT(*) FROM system_settings WHERE id=1")
        if c.fetchone()[0] == 0:
            c.execute("""
                INSERT INTO system_settings (id, language) VALUES (1, 'english')
            """)

        # Storage/inventory management tables
        c.execute('''
            CREATE TABLE IF NOT EXISTS storage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                current_stock INTEGER NOT NULL DEFAULT 0,
                min_stock_level INTEGER NOT NULL DEFAULT 5,
                max_stock_level INTEGER NOT NULL DEFAULT 100,
                unit TEXT NOT NULL DEFAULT 'pieces',
                cost_per_unit REAL NOT NULL DEFAULT 0.0,
                supplier TEXT DEFAULT '',
                last_restocked TEXT DEFAULT '',
                notes TEXT DEFAULT '',
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        ''')

        # Stock movements table for tracking inventory changes
        c.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                reason TEXT NOT NULL,
                date TEXT NOT NULL,
                user TEXT NOT NULL,
                cost_per_unit REAL DEFAULT 0.0,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        ''')

        # Email settings table for email recipients
        c.execute('''
            CREATE TABLE IF NOT EXISTS email_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email_address TEXT UNIQUE NOT NULL,
                is_active INTEGER DEFAULT 1,
                added_date TEXT,
                added_by_user_id INTEGER,
                FOREIGN KEY (added_by_user_id) REFERENCES users (id)
            )
        ''')

        # SMTP configuration table for email functionality
        c.execute('''
            CREATE TABLE IF NOT EXISTS smtp_config (
                id INTEGER PRIMARY KEY,
                email_enabled INTEGER DEFAULT 0
            )
        ''')

        # Performance optimization: Add database indexes for frequently queried columns
        print("Creating performance indexes...")

        # Index for sales queries (most frequent operations)
        c.execute('CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_sales_user_id ON sales(user_id)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_sales_total ON sales(total)')

        # Index for sale_items queries (for sales history with items)
        c.execute('CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON sale_items(sale_id)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_sale_items_product_name ON sale_items(product_name)')

        # Index for products queries (product management and POS display)
        c.execute('CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_products_price ON products(price)')

        # Index for categories (category selection)
        c.execute('CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)')

        # Index for users (authentication and user management)
        c.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)')

        # Index for storage/inventory queries
        c.execute('CREATE INDEX IF NOT EXISTS idx_storage_product_id ON storage(product_id)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_storage_current_stock ON storage(current_stock)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_storage_min_stock ON storage(min_stock_level)')

        # Index for stock movements (inventory tracking)
        c.execute('CREATE INDEX IF NOT EXISTS idx_stock_movements_product_id ON stock_movements(product_id)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements(date)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON stock_movements(movement_type)')

        conn.commit()
        print("Database initialized successfully with performance indexes!")

    except Exception as e:
        print(f"Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def authenticate_user(username, password):
    """Authenticate user credentials with optimized query"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        # Optimized: Use index on username for faster lookup
        c.execute("""
            SELECT id, username, role, is_admin, button_color
            FROM users
            WHERE username = ? AND password = ?
            LIMIT 1
        """, (username, hashed_password))

        user = c.fetchone()
        if user:
            return dict(user)
        return None
    finally:
        conn.close()

def get_categories_cached():
    """Get categories with caching for better performance"""
    # Check cache first
    cached_data = _get_cached_data('categories')
    if cached_data is not None:
        return cached_data

    # Fetch from database if not cached
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT id, name, image FROM categories ORDER BY name")
        categories = [dict(row) for row in c.fetchall()]

        # Cache the result
        _set_cached_data('categories', categories)
        return categories
    finally:
        conn.close()

def get_products_cached():
    """Get products with caching for better performance"""
    # Check cache first
    cached_data = _get_cached_data('products')
    if cached_data is not None:
        return cached_data

    # Fetch from database if not cached
    conn = get_db_connection()
    try:
        c = conn.cursor()
        # Optimized query with proper indexing
        c.execute("""
            SELECT p.id, p.name, p.category_id, p.price, p.image,
                   c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.name
        """)
        products = [dict(row) for row in c.fetchall()]

        # Cache the result
        _set_cached_data('products', products)
        return products
    finally:
        conn.close()

def get_products_by_category_cached(category_id):
    """Get products by category with optimized query"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        # Optimized: Use index on category_id
        c.execute("""
            SELECT id, name, price, image
            FROM products
            WHERE category_id = ?
            ORDER BY name
        """, (category_id,))
        return [dict(row) for row in c.fetchall()]
    finally:
        conn.close()

def invalidate_products_cache():
    """Invalidate products cache when data changes"""
    _invalidate_cache('products')

def invalidate_categories_cache():
    """Invalidate categories cache when data changes"""
    _invalidate_cache('categories')

# Remote Management API Functions
def get_system_stats():
    """Get system statistics for remote monitoring"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Get basic counts
        c.execute("SELECT COUNT(*) as count FROM sales")
        total_sales = c.fetchone()['count']

        c.execute("SELECT COUNT(*) as count FROM products")
        total_products = c.fetchone()['count']

        c.execute("SELECT COUNT(*) as count FROM categories")
        total_categories = c.fetchone()['count']

        c.execute("SELECT COUNT(*) as count FROM users")
        total_users = c.fetchone()['count']

        # Get today's sales
        from datetime import datetime, date
        today = date.today().isoformat()
        c.execute("SELECT COUNT(*) as count, COALESCE(SUM(total), 0) as total FROM sales WHERE date LIKE ?", (f"{today}%",))
        today_stats = c.fetchone()

        # Get recent sales (last 7 days)
        c.execute("""
            SELECT DATE(date) as sale_date, COUNT(*) as count, SUM(total) as total
            FROM sales
            WHERE date >= date('now', '-7 days')
            GROUP BY DATE(date)
            ORDER BY sale_date DESC
        """)
        recent_sales = [dict(row) for row in c.fetchall()]

        return {
            'total_sales': total_sales,
            'total_products': total_products,
            'total_categories': total_categories,
            'total_users': total_users,
            'today_sales_count': today_stats['count'],
            'today_sales_total': today_stats['total'],
            'recent_sales': recent_sales,
            'last_updated': datetime.now().isoformat()
        }
    finally:
        conn.close()

def get_low_stock_products():
    """Get products with low stock for remote monitoring"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            SELECT p.id, p.name, s.current_stock, s.min_stock_level,
                   c.name as category_name
            FROM products p
            LEFT JOIN storage s ON p.id = s.product_id
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE s.current_stock <= s.min_stock_level
            ORDER BY (s.current_stock - s.min_stock_level) ASC
        """)
        return [dict(row) for row in c.fetchall()]
    finally:
        conn.close()

def get_sales_summary(days=30):
    """Get sales summary for remote reporting"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            SELECT DATE(date) as sale_date,
                   COUNT(*) as transaction_count,
                   SUM(total) as daily_total,
                   AVG(total) as avg_transaction
            FROM sales
            WHERE date >= date('now', '-{} days')
            GROUP BY DATE(date)
            ORDER BY sale_date DESC
        """.format(days))
        return [dict(row) for row in c.fetchall()]
    finally:
        conn.close()

def remote_add_product(name, category_id, price, image_data=None):
    """Add product via remote API"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            INSERT INTO products (name, category_id, price, image)
            VALUES (?, ?, ?, ?)
        """, (name, category_id, price, image_data))

        product_id = c.lastrowid
        conn.commit()

        # Invalidate cache
        invalidate_products_cache()

        return {'success': True, 'product_id': product_id}
    except Exception as e:
        return {'success': False, 'error': str(e)}
    finally:
        conn.close()

def remote_update_product(product_id, name=None, category_id=None, price=None, image_data=None):
    """Update product via remote API"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Build dynamic update query
        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if category_id is not None:
            updates.append("category_id = ?")
            params.append(category_id)
        if price is not None:
            updates.append("price = ?")
            params.append(price)
        if image_data is not None:
            updates.append("image = ?")
            params.append(image_data)

        if not updates:
            return {'success': False, 'error': 'No updates provided'}

        params.append(product_id)
        query = f"UPDATE products SET {', '.join(updates)} WHERE id = ?"

        c.execute(query, params)
        conn.commit()

        # Invalidate cache
        invalidate_products_cache()

        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}
    finally:
        conn.close()

def remote_delete_product(product_id):
    """Delete product via remote API"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("DELETE FROM products WHERE id = ?", (product_id,))
        conn.commit()

        # Invalidate cache
        invalidate_products_cache()

        return {'success': True}
    except Exception as e:
        return {'success': False, 'error': str(e)}
    finally:
        conn.close()

def get_available_users():
    """Get list of all users for login dropdown"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT username, button_color FROM users ORDER BY username")
        return [dict(row) for row in c.fetchall()]
    finally:
        conn.close()

def save_language_setting(language):
    """Save language preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE system_settings
            SET language = ?, updated_date = CURRENT_TIMESTAMP
            WHERE id = 1
        """, (language,))
        conn.commit()
    finally:
        conn.close()

def load_language_setting():
    """Load saved language preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT language FROM system_settings WHERE id = 1")
        result = c.fetchone()
        return result['language'] if result else 'english'
    finally:
        conn.close()

def save_history_reset_time(hour, minute):
    """Save history reset time preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE system_settings
            SET history_reset_hour = ?, history_reset_minute = ?, updated_date = CURRENT_TIMESTAMP
            WHERE id = 1
        """, (hour, minute))
        conn.commit()
        print(f"History reset time saved: {hour:02d}:{minute:02d}")
    except Exception as e:
        print(f"Error saving history reset time: {e}")
    finally:
        conn.close()

def load_history_reset_time():
    """Load saved history reset time preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT history_reset_hour, history_reset_minute FROM system_settings WHERE id = 1")
        result = c.fetchone()
        if result:
            return result['history_reset_hour'], result['history_reset_minute']
        else:
            return 6, 0  # Default: 6:00 AM
    except Exception as e:
        print(f"Error loading history reset time: {e}")
        return 6, 0  # Default: 6:00 AM
    finally:
        conn.close()

def get_display_settings():
    """Get display settings from database"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT max_product_columns, product_button_size FROM system_settings WHERE id = 1")
        result = c.fetchone()

        if result:
            return {
                'max_columns': result['max_product_columns'] or 4,
                'button_size': result['product_button_size'] or 130
            }
        else:
            # Return defaults if no settings found
            return {
                'max_columns': 4,
                'button_size': 130
            }
    except Exception as e:
        print(f"Error getting display settings: {e}")
        return {
            'max_columns': 4,
            'button_size': 130
        }
    finally:
        conn.close()

def save_display_settings(max_columns, button_size):
    """Save display settings to database"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Check if settings exist
        c.execute("SELECT id FROM system_settings WHERE id = 1")
        if c.fetchone():
            # Update existing
            c.execute("""
                UPDATE system_settings
                SET max_product_columns = ?, product_button_size = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = 1
            """, (max_columns, button_size))
        else:
            # Insert new
            c.execute("""
                INSERT INTO system_settings (id, max_product_columns, product_button_size)
                VALUES (1, ?, ?)
            """, (max_columns, button_size))

        conn.commit()
        print(f"Display settings saved: {max_columns} columns, {button_size}px buttons")
        return True
    except Exception as e:
        print(f"Error saving display settings: {e}")
        return False
    finally:
        conn.close()


