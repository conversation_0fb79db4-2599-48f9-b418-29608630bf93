#!/usr/bin/env python3
"""
Test the offline installation functionality
"""

import sys
import os
import platform
from pathlib import Path

def test_offline_packages_exist():
    """Test that offline packages exist in all directories"""

    print("Testing Offline Package Files")
    print("=" * 30)

    # Test locations
    locations = [
        "offline_packages",
        "YES/offline_packages",
        "YES_OBFUSCATED/offline_packages"
    ]

    all_found = True

    for location in locations:
        location_path = Path(location)
        if location_path.exists():
            wheel_files = list(location_path.glob("*.whl"))
            print(f"✅ Found: {location} ({len(wheel_files)} packages)")

            # List the packages
            for wheel in sorted(wheel_files):
                print(f"   📦 {wheel.name}")
        else:
            print(f"❌ Missing: {location}")
            all_found = False

    return all_found

def test_install_py_functions():
    """Test that install.py has the new offline functions"""

    print("\nTesting install.py Functions")
    print("=" * 28)

    files_to_check = [
        "install.py",
        "YES/install.py",
        "YES_OBFUSCATED/install.py"
    ]

    required_functions = [
        "check_internet_connection",
        "get_offline_packages_dir",
        "find_compatible_wheel"
    ]

    all_functions = True

    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            print(f"\n📋 Checking: {file_path}")

            for func_name in required_functions:
                if f"def {func_name}(" in content:
                    print(f"   ✅ {func_name}")
                else:
                    print(f"   ❌ {func_name}")
                    all_functions = False

            # Check for offline installation logic
            if "Found offline package:" in content:
                print(f"   ✅ Offline installation logic")
            else:
                print(f"   ❌ Offline installation logic")
                all_functions = False

        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_functions = False

    return all_functions

def test_wheel_compatibility():
    """Test that we have wheels for current Python version"""

    print(f"\nTesting Wheel Compatibility")
    print("=" * 27)

    # Get current Python info
    py_version = f"{sys.version_info.major}{sys.version_info.minor}"
    py_tag = f"cp{py_version}"

    if platform.system() == "Windows":
        if platform.architecture()[0] == "64bit":
            platform_tag = "win_amd64"
        else:
            platform_tag = "win32"
    else:
        platform_tag = "unknown"

    print(f"🐍 Current Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"🏷️  Python Tag: {py_tag}")
    print(f"💻 Platform Tag: {platform_tag}")

    # Check main offline packages
    offline_dir = Path("offline_packages")
    if not offline_dir.exists():
        print("❌ No offline packages directory found")
        return False

    # Check for Pillow
    pillow_wheels = list(offline_dir.glob(f"pillow*{py_tag}*{platform_tag}.whl"))
    if pillow_wheels:
        print(f"✅ Compatible Pillow wheel: {pillow_wheels[0].name}")
    else:
        # Check for any Pillow wheel
        any_pillow = list(offline_dir.glob("pillow*.whl"))
        if any_pillow:
            print(f"⚠️  Pillow wheel available but may not be compatible: {any_pillow[0].name}")
        else:
            print("❌ No Pillow wheel found")
            return False

    # Check for pywin32 (Windows only)
    if platform.system() == "Windows":
        pywin32_wheels = list(offline_dir.glob(f"pywin32*{py_tag}*{platform_tag}.whl"))
        if pywin32_wheels:
            print(f"✅ Compatible pywin32 wheel: {pywin32_wheels[0].name}")
        else:
            # Check for any pywin32 wheel
            any_pywin32 = list(offline_dir.glob("pywin32*.whl"))
            if any_pywin32:
                print(f"⚠️  pywin32 wheel available but may not be compatible: {any_pywin32[0].name}")
            else:
                print("❌ No pywin32 wheel found")
                return False
    else:
        print("ℹ️  pywin32 not needed on non-Windows systems")

    return True

def test_import_install_functions():
    """Test that we can import the new install.py functions"""

    print(f"\nTesting Function Imports")
    print("=" * 24)

    try:
        # Add current directory to path
        sys.path.insert(0, str(Path(".").absolute()))

        # Import install module
        import install

        # Test functions exist
        functions_to_test = [
            'check_internet_connection',
            'get_offline_packages_dir',
            'find_compatible_wheel'
        ]

        for func_name in functions_to_test:
            if hasattr(install, func_name):
                print(f"✅ {func_name} - importable")

                # Test the function
                if func_name == 'get_offline_packages_dir':
                    result = getattr(install, func_name)()
                    print(f"   📁 Returns: {result}")
                elif func_name == 'check_internet_connection':
                    result = getattr(install, func_name)()
                    print(f"   🌐 Internet: {'Available' if result else 'Not available'}")
                elif func_name == 'find_compatible_wheel':
                    offline_dir = install.get_offline_packages_dir()
                    result = getattr(install, func_name)('Pillow', offline_dir)
                    if result:
                        print(f"   📦 Found wheel: {result.name}")
                    else:
                        print(f"   📦 No compatible wheel found")
            else:
                print(f"❌ {func_name} - not found")
                return False

        return True

    except Exception as e:
        print(f"❌ Error importing install functions: {e}")
        return False

def main():
    """Run all offline installation tests"""

    print("📦 OFFLINE INSTALLATION TEST SUITE")
    print("=" * 40)

    tests = [
        ("Offline Package Files", test_offline_packages_exist),
        ("install.py Functions", test_install_py_functions),
        ("Wheel Compatibility", test_wheel_compatibility),
        ("Function Imports", test_import_install_functions)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 35)

        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")

    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")

    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Offline installation is ready")
        print("✅ Libraries pre-installed and available")
        print("✅ install.py supports offline mode")
        print("✅ Compatible packages for current system")
    else:
        print("⚠️ Some tests failed")
        print("❌ Offline installation may not work properly")

    return passed == total

if __name__ == "__main__":
    success = main()

    if success:
        print("\n📦 Offline installation system is ready!")
        print("🔧 Users can install without internet connection")
        print("💡 Run: python install.py")
    else:
        print("\n❌ Offline installation system needs attention")

    exit(0 if success else 1)
