#!/usr/bin/env python3
"""
POS Remote Management API Server
Free solution for remote POS management using Flask
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import sqlite3
import hashlib
import jwt
import datetime
from functools import wraps
import os
import threading
import time

# Import our database functions
from database import (
    get_db_connection, get_system_stats, get_low_stock_products,
    get_sales_summary, remote_add_product, remote_update_product,
    remote_delete_product, get_categories_cached, get_products_cached,
    authenticate_user
)

app = Flask(__name__)
CORS(app)  # Enable cross-origin requests

# Configuration
app.config['SECRET_KEY'] = 'pos-remote-management-secret-key-2024'
API_VERSION = 'v1'

# Remote authentication functions
def init_remote_auth_table():
    """Initialize remote authentication table"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            CREATE TABLE IF NOT EXISTS remote_auth (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_date TEXT NOT NULL,
                last_login TEXT,
                is_active INTEGER DEFAULT 1
            )
        """)

        # Check if default admin exists
        c.execute("SELECT COUNT(*) FROM remote_auth WHERE username = 'admin'")
        if c.fetchone()[0] == 0:
            # Create default admin with password "0000"
            import hashlib
            from datetime import datetime
            password_hash = hashlib.sha256("0000".encode()).hexdigest()
            c.execute("""
                INSERT INTO remote_auth (username, password_hash, created_date)
                VALUES (?, ?, ?)
            """, ("admin", password_hash, datetime.now().isoformat()))

        conn.commit()
    finally:
        conn.close()

def authenticate_remote_user(username, password):
    """Authenticate remote user with custom credentials"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        c.execute("""
            SELECT username, created_date FROM remote_auth
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, password_hash))

        user = c.fetchone()
        if user:
            # Update last login
            from datetime import datetime
            c.execute("""
                UPDATE remote_auth SET last_login = ? WHERE username = ?
            """, (datetime.now().isoformat(), username))
            conn.commit()

            return {
                'username': user['username'],
                'is_admin': True,  # All remote users are admins
                'last_login': datetime.now().isoformat()
            }
        return None
    finally:
        conn.close()

def change_remote_credentials(old_username, old_password, new_username, new_password):
    """Change remote access credentials"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Verify old credentials
        old_password_hash = hashlib.sha256(old_password.encode()).hexdigest()
        c.execute("""
            SELECT id FROM remote_auth
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (old_username, old_password_hash))

        if not c.fetchone():
            return False, "Invalid current credentials"

        # Check if new username already exists (if different)
        if new_username != old_username:
            c.execute("SELECT id FROM remote_auth WHERE username = ? AND is_active = 1", (new_username,))
            if c.fetchone():
                return False, "Username already exists"

        # Update credentials
        new_password_hash = hashlib.sha256(new_password.encode()).hexdigest()
        c.execute("""
            UPDATE remote_auth
            SET username = ?, password_hash = ?
            WHERE username = ? AND password_hash = ?
        """, (new_username, new_password_hash, old_username, old_password_hash))

        conn.commit()
        return True, "Credentials updated successfully"
    except Exception as e:
        return False, f"Error updating credentials: {e}"
    finally:
        conn.close()

# Initialize remote auth on startup
init_remote_auth_table()

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
            
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except:
            return jsonify({'error': 'Token is invalid'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated(current_user, *args, **kwargs):
        if not current_user.get('is_admin'):
            return jsonify({'error': 'Admin access required'}), 403
        return f(current_user, *args, **kwargs)
    return decorated

@app.route('/')
def dashboard():
    """Main dashboard page"""
    dashboard_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>POS Remote Management</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: white; }
            .card { background: rgba(45, 45, 45, 0.9); border: 1px solid #ff8c00; }
            .btn-primary { background: #ff8c00; border-color: #ff8c00; }
            .btn-primary:hover { background: #e67e00; border-color: #e67e00; }
            .navbar { background: rgba(26, 26, 26, 0.95) !important; }
            .stat-card { transition: transform 0.3s; }
            .stat-card:hover { transform: translateY(-5px); }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-dark">
            <div class="container">
                <span class="navbar-brand">🏪 POS Remote Management</span>
                <div id="connectionStatus" class="badge bg-success">Connected</div>
            </div>
        </nav>

        <div class="container mt-4">
            <div id="loginSection">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-lock"></i> Remote Access Login</h4>
                            </div>
                            <div class="card-body">
                                <form id="loginForm">
                                    <div class="mb-3">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-sign-in-alt"></i> Login
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="dashboardSection" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <h2><i class="fas fa-tachometer-alt"></i> System Overview</h2>
                        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-outline-danger btn-sm float-end" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>

                <div class="row" id="statsCards">
                    <!-- Stats cards will be populated here -->
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alerts</h5>
                            </div>
                            <div class="card-body" id="lowStockList">
                                Loading...
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> Recent Sales</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs"></i> Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="showProductManager()">
                                            <i class="fas fa-box"></i><br>Manage Products
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="showUserManager()">
                                            <i class="fas fa-users"></i><br>Manage Users
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="showReports()">
                                            <i class="fas fa-chart-bar"></i><br>View Reports
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="showSettings()">
                                            <i class="fas fa-cog"></i><br>Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div class="modal fade" id="settingsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content bg-dark">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-cog"></i> Remote Access Settings</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="credentialsForm">
                            <div class="mb-3">
                                <label class="form-label">Current Username</label>
                                <input type="text" class="form-control" id="oldUsername" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="oldPassword" required>
                            </div>
                            <hr>
                            <div class="mb-3">
                                <label class="form-label">New Username</label>
                                <input type="text" class="form-control" id="newUsername" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control" id="newPassword" required minlength="4">
                                <div class="form-text">Minimum 4 characters</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="changeCredentials()">
                            <i class="fas fa-save"></i> Update Credentials
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            let authToken = localStorage.getItem('pos_auth_token');
            let salesChart = null;

            // Check if already logged in
            if (authToken) {
                showDashboard();
            }

            // Login form handler
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                try {
                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, password })
                    });

                    const data = await response.json();
                    if (data.token) {
                        authToken = data.token;
                        localStorage.setItem('pos_auth_token', authToken);
                        showDashboard();
                    } else {
                        alert('Login failed: ' + (data.error || 'Invalid credentials'));
                    }
                } catch (error) {
                    alert('Connection error: ' + error.message);
                }
            });

            function showDashboard() {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                refreshData();
            }

            function logout() {
                localStorage.removeItem('pos_auth_token');
                authToken = null;
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('dashboardSection').style.display = 'none';
            }

            async function refreshData() {
                if (!authToken) return;

                try {
                    // Get system stats
                    const statsResponse = await fetch('/api/v1/stats', {
                        headers: { 'Authorization': 'Bearer ' + authToken }
                    });
                    const stats = await statsResponse.json();
                    updateStatsCards(stats);

                    // Get low stock
                    const stockResponse = await fetch('/api/v1/inventory/low-stock', {
                        headers: { 'Authorization': 'Bearer ' + authToken }
                    });
                    const lowStock = await stockResponse.json();
                    updateLowStock(lowStock);

                    // Get sales data for chart
                    const salesResponse = await fetch('/api/v1/sales/summary?days=7', {
                        headers: { 'Authorization': 'Bearer ' + authToken }
                    });
                    const salesData = await salesResponse.json();
                    updateSalesChart(salesData);

                } catch (error) {
                    console.error('Error refreshing data:', error);
                    document.getElementById('connectionStatus').className = 'badge bg-danger';
                    document.getElementById('connectionStatus').textContent = 'Connection Error';
                }
            }

            function updateStatsCards(stats) {
                const cardsHtml = `
                    <div class="col-md-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                                <h4>${stats.total_sales}</h4>
                                <p class="text-muted">Total Sales</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="fas fa-box fa-2x text-success mb-2"></i>
                                <h4>${stats.total_products}</h4>
                                <p class="text-muted">Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="fas fa-calendar-day fa-2x text-warning mb-2"></i>
                                <h4>${stats.today_sales_count}</h4>
                                <p class="text-muted">Today's Sales</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="fas fa-dollar-sign fa-2x text-info mb-2"></i>
                                <h4>${stats.today_sales_total?.toFixed(2) || '0.00'} MAD</h4>
                                <p class="text-muted">Today's Revenue</p>
                            </div>
                        </div>
                    </div>
                `;
                document.getElementById('statsCards').innerHTML = cardsHtml;
            }

            function updateLowStock(lowStock) {
                if (lowStock.length === 0) {
                    document.getElementById('lowStockList').innerHTML = 
                        '<p class="text-success"><i class="fas fa-check"></i> All products have adequate stock</p>';
                } else {
                    const listHtml = lowStock.map(item => 
                        `<div class="alert alert-warning mb-2">
                            <strong>${item.name}</strong><br>
                            <small>Stock: ${item.current_stock} / Min: ${item.min_stock_level}</small>
                        </div>`
                    ).join('');
                    document.getElementById('lowStockList').innerHTML = listHtml;
                }
            }

            function updateSalesChart(salesData) {
                const ctx = document.getElementById('salesChart').getContext('2d');
                
                if (salesChart) {
                    salesChart.destroy();
                }

                salesChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: salesData.map(d => d.sale_date),
                        datasets: [{
                            label: 'Daily Sales (MAD)',
                            data: salesData.map(d => d.daily_total),
                            borderColor: '#ff8c00',
                            backgroundColor: 'rgba(255, 140, 0, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { labels: { color: 'white' } }
                        },
                        scales: {
                            x: { ticks: { color: 'white' } },
                            y: { ticks: { color: 'white' } }
                        }
                    }
                });
            }

            // Placeholder functions for quick actions
            function showProductManager() { alert('Product Manager - Coming Soon!'); }
            function showUserManager() { alert('User Manager - Coming Soon!'); }
            function showReports() { alert('Reports - Coming Soon!'); }

            function showSettings() {
                const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
                modal.show();
            }

            async function changeCredentials() {
                const oldUsername = document.getElementById('oldUsername').value;
                const oldPassword = document.getElementById('oldPassword').value;
                const newUsername = document.getElementById('newUsername').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (newPassword !== confirmPassword) {
                    alert('New passwords do not match!');
                    return;
                }

                if (newPassword.length < 4) {
                    alert('New password must be at least 4 characters!');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/auth/change-credentials', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + authToken
                        },
                        body: JSON.stringify({
                            old_username: oldUsername,
                            old_password: oldPassword,
                            new_username: newUsername,
                            new_password: newPassword
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        alert('✅ Credentials updated successfully! Please login again with your new credentials.');
                        logout(); // Force re-login
                        bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
                    } else {
                        alert('❌ Error: ' + (data.error || 'Failed to update credentials'));
                    }
                } catch (error) {
                    alert('❌ Connection error: ' + error.message);
                }
            }

            // Auto-refresh every 30 seconds
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """
    return render_template_string(dashboard_html)

# API Routes
@app.route(f'/api/{API_VERSION}/auth/login', methods=['POST'])
def login():
    """Authenticate user and return JWT token"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'error': 'Username and password required'}), 400

    # Authenticate remote user with custom credentials
    user = authenticate_remote_user(username, password)
    if not user:
        return jsonify({'error': 'Invalid credentials'}), 401

    # Generate JWT token
    token = jwt.encode({
        'user': user,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }, app.config['SECRET_KEY'], algorithm='HS256')

    return jsonify({
        'token': token,
        'user': user,
        'expires_in': 86400  # 24 hours
    })

@app.route(f'/api/{API_VERSION}/stats')
@token_required
def get_stats(current_user):
    """Get system statistics"""
    try:
        stats = get_system_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route(f'/api/{API_VERSION}/inventory/low-stock')
@token_required
def get_low_stock(current_user):
    """Get low stock products"""
    try:
        low_stock = get_low_stock_products()
        return jsonify(low_stock)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route(f'/api/{API_VERSION}/sales/summary')
@token_required
def get_sales_summary_api(current_user):
    """Get sales summary"""
    days = request.args.get('days', 30, type=int)
    try:
        summary = get_sales_summary(days)
        return jsonify(summary)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route(f'/api/{API_VERSION}/auth/change-credentials', methods=['POST'])
@token_required
def change_credentials(current_user):
    """Change remote access credentials"""
    data = request.get_json()
    old_username = data.get('old_username')
    old_password = data.get('old_password')
    new_username = data.get('new_username')
    new_password = data.get('new_password')

    if not all([old_username, old_password, new_username, new_password]):
        return jsonify({'error': 'All fields are required'}), 400

    if len(new_password) < 4:
        return jsonify({'error': 'New password must be at least 4 characters'}), 400

    success, message = change_remote_credentials(old_username, old_password, new_username, new_password)

    if success:
        return jsonify({'message': message})
    else:
        return jsonify({'error': message}), 400

if __name__ == '__main__':
    print("🚀 Starting POS Remote Management API Server...")
    print("=" * 60)
    print("📱 Local Dashboard: http://localhost:5000")
    print("🌐 Network Access: http://[YOUR_IP]:5000")
    print("")
    print("🔗 FOR WORLDWIDE ACCESS:")
    print("   1. Download ngrok: https://ngrok.com/download")
    print("   2. Run: ngrok http 5000")
    print("   3. Use the https URL provided by ngrok")
    print("")
    print("🔐 DEFAULT LOGIN:")
    print("   Username: admin")
    print("   Password: 0000")
    print("   (Change these in Settings after login)")
    print("=" * 60)
    print("⚡ Server starting...")

    app.run(host='0.0.0.0', port=5000, debug=False)
