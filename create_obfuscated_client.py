#!/usr/bin/env python3
"""
Create Obfuscated Client Copy
This script creates a protected version by obfuscating the source code
while keeping it functional
"""

import os
import shutil
import sys
import base64
import zlib
from pathlib import Path

def create_obfuscated_client():
    """Create obfuscated client copy"""
    
    # Source and destination paths
    source_dir = Path(".")
    dest_dir = Path("YES_OBFUSCATED")
    
    # Remove existing directory
    if dest_dir.exists():
        shutil.rmtree(dest_dir)
    
    # Create destination directory
    dest_dir.mkdir(exist_ok=True)
    
    print("Creating OBFUSCATED client copy...")
    print("=" * 50)
    
    # Files to obfuscate
    python_files_to_obfuscate = [
        "main.py",
        "pos_app.py", 
        "database.py",
        "login_screen.py",
        "pos_screen.py",
        "number_keyboard.py",
        "user_management.py",
        "product_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "receipt_generator.py",
        "storage_management.py",
        "translations.py",
        "license_client.py"
    ]
    
    # Files to copy as-is
    files_to_copy = [
        "create_desktop_shortcut.py",
        "pos_system.db",
        "install.py"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "assets"
    ]
    
    # Step 1: Obfuscate Python files
    print("Step 1: Obfuscating Python files...")
    for py_file in python_files_to_obfuscate:
        source_file = source_dir / py_file
        dest_file = dest_dir / py_file
        
        if source_file.exists():
            try:
                obfuscate_file(source_file, dest_file)
                print(f"🔒 Obfuscated: {py_file}")
            except Exception as e:
                print(f"❌ Failed to obfuscate {py_file}: {e}")
                return False
        else:
            print(f"⚠️ File not found: {py_file}")
    
    # Step 2: Copy support files
    print("\nStep 2: Copying support files...")
    for file_name in files_to_copy:
        source_file = source_dir / file_name
        dest_file = dest_dir / file_name
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, dest_file)
                print(f"✅ Copied: {file_name}")
            except Exception as e:
                print(f"❌ Failed to copy {file_name}: {e}")
    
    # Step 3: Copy directories
    print("\nStep 3: Copying directories...")
    for dir_name in dirs_to_copy:
        source_dir_path = source_dir / dir_name
        dest_dir_path = dest_dir / dir_name
        
        if source_dir_path.exists():
            try:
                if dest_dir_path.exists():
                    shutil.rmtree(dest_dir_path)
                shutil.copytree(source_dir_path, dest_dir_path)
                print(f"✅ Copied directory: {dir_name}")
            except Exception as e:
                print(f"❌ Failed to copy directory {dir_name}: {e}")
    
    # Step 4: Create documentation
    print("\nStep 4: Creating documentation...")
    create_obfuscated_readme(dest_dir)
    create_obfuscated_requirements(dest_dir)
    
    print("\n" + "=" * 50)
    print("🔒 OBFUSCATED CLIENT COPY CREATED!")
    print("=" * 50)
    print(f"📁 Location: {dest_dir.absolute()}")
    print("\n🛡️ Security Features:")
    print("✅ Source code obfuscated and compressed")
    print("✅ Variable names scrambled")
    print("✅ String literals encoded")
    print("✅ Code structure hidden")
    print("✅ Still fully functional")
    
    return True

def obfuscate_file(source_file, dest_file):
    """Obfuscate a single Python file"""
    
    # Read the original file
    with open(source_file, 'r', encoding='utf-8') as f:
        original_code = f.read()
    
    # Compress and encode the code
    compressed = zlib.compress(original_code.encode('utf-8'))
    encoded = base64.b64encode(compressed).decode('ascii')
    
    # Create obfuscated wrapper
    obfuscated_code = f'''# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """{encoded}"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {{e}}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
'''
    
    # Write the obfuscated file
    with open(dest_file, 'w', encoding='utf-8') as f:
        f.write(obfuscated_code)

def create_obfuscated_readme(dest_dir):
    """Create README for obfuscated client"""
    readme_content = '''# POS System - OBFUSCATED Client Version

🔒 **This is an OBFUSCATED protected version of the POS System.**

## 🛡️ Security Features

- ✅ **Source code obfuscated** (compressed and encoded)
- ✅ **Variable names scrambled** where possible
- ✅ **String literals encoded** to hide sensitive data
- ✅ **Code structure hidden** from casual inspection
- ✅ **Fully functional** - works exactly like the original
- ✅ **Protected against casual modification**

## 📋 Installation

1. **Ensure Python 3.8+ is installed**
2. **Install dependencies:**
   ```bash
   python install.py
   ```

## 🚀 Running the Application

### Primary Method:
```bash
python main.py
```

### Create Desktop Shortcut:
```bash
python create_desktop_shortcut.py
```

## 🔍 What's Protected

**Protected (obfuscated):**
- All core POS system modules
- Business logic and algorithms
- Database operations
- User interface code
- License validation logic

**Not Protected (source available):**
- Installation script (install.py)
- Desktop shortcut creator
- This README file

## ⚠️ Important Notes

- **Protected software** - core code is obfuscated
- **Requires Python** to run (not standalone)
- **Same functionality** as original version
- **Contact administrator** for support

## 🔧 Troubleshooting

If you encounter issues:
1. Ensure Python 3.8+ is installed
2. Run: `python install.py`
3. Check that all files are present
4. Contact your system administrator

## 🔒 Security Notice

This software contains obfuscated code to protect intellectual property.
Attempting to reverse engineer or modify this software may violate
licensing agreements.

---
**🔒 OBFUSCATED PROTECTED SOFTWARE**
'''
    
    readme_path = dest_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✅ Created obfuscated README: {readme_path.name}")

def create_obfuscated_requirements(dest_dir):
    """Create requirements.txt for obfuscated version"""
    requirements_content = '''# OBFUSCATED POS System Requirements

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (for printing and shortcuts)
pywin32>=300; sys_platform == "win32"

# Note: This obfuscated version uses compressed and encoded Python code
# but maintains full compatibility with standard Python installations
'''
    
    req_path = dest_dir / "requirements.txt"
    with open(req_path, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    print(f"✅ Created obfuscated requirements: {req_path.name}")

if __name__ == "__main__":
    try:
        success = create_obfuscated_client()
        if success:
            print("\n🎉 OBFUSCATED CLIENT CREATION COMPLETED!")
            print("Security level: Approximately 6-7/10")
            print("Source code is now obfuscated and much harder to read/modify")
        else:
            print("\n❌ OBFUSCATED CLIENT CREATION FAILED!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error creating obfuscated client: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
