import pygame
import random
import math
import json
import os
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
TILE_SIZE = 32
MAP_WIDTH = 25
MAP_HEIGHT = 20
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
BROWN = (139, 69, 19)
DARK_GREEN = (0, 128, 0)
GOLD = (255, 215, 0)

class TileType(Enum):
    WALL = 0
    FLOOR = 1
    DOOR = 2
    STAIRS_DOWN = 3
    STAIRS_UP = 4

class ItemType(Enum):
    WEAPON = 0
    ARMOR = 1
    POTION = 2
    SCROLL = 3
    TREASURE = 4

@dataclass
class Item:
    name: str
    item_type: ItemType
    value: int
    description: str
    rarity: str = "common"
    
    def get_color(self):
        colors = {
            "common": WHITE,
            "uncommon": GREEN,
            "rare": BLUE,
            "epic": PURPLE,
            "legendary": GOLD
        }
        return colors.get(self.rarity, WHITE)

@dataclass
class Stats:
    hp: int
    max_hp: int
    attack: int
    defense: int
    speed: int
    level: int = 1
    exp: int = 0
    exp_to_next: int = 100

class Entity:
    def __init__(self, x, y, char, color, name, stats):
        self.x = x
        self.y = y
        self.char = char
        self.color = color
        self.name = name
        self.stats = stats
        self.inventory = []
        self.equipment = {
            "weapon": None,
            "armor": None
        }
        
    def move(self, dx, dy, game_map):
        new_x = self.x + dx
        new_y = self.y + dy
        
        if (0 <= new_x < MAP_WIDTH and 0 <= new_y < MAP_HEIGHT and 
            game_map[new_y][new_x] != TileType.WALL):
            self.x = new_x
            self.y = new_y
            return True
        return False
    
    def attack(self, target):
        weapon_bonus = 0
        if self.equipment["weapon"]:
            weapon_bonus = self.equipment["weapon"].value
            
        damage = max(1, self.stats.attack + weapon_bonus - target.get_defense())
        target.take_damage(damage)
        return damage
    
    def take_damage(self, damage):
        self.stats.hp = max(0, self.stats.hp - damage)
        
    def get_defense(self):
        armor_bonus = 0
        if self.equipment["armor"]:
            armor_bonus = self.equipment["armor"].value
        return self.stats.defense + armor_bonus
    
    def is_alive(self):
        return self.stats.hp > 0
    
    def heal(self, amount):
        self.stats.hp = min(self.stats.max_hp, self.stats.hp + amount)

class Player(Entity):
    def __init__(self, x, y):
        stats = Stats(hp=100, max_hp=100, attack=10, defense=5, speed=10)
        super().__init__(x, y, '@', YELLOW, "Hero", stats)
        self.gold = 0
        self.dungeon_level = 1
        
    def gain_exp(self, amount):
        self.stats.exp += amount
        while self.stats.exp >= self.stats.exp_to_next:
            self.level_up()
    
    def level_up(self):
        self.stats.exp -= self.stats.exp_to_next
        self.stats.level += 1
        self.stats.exp_to_next = int(self.stats.exp_to_next * 1.5)
        
        # Stat increases
        hp_gain = random.randint(8, 15)
        attack_gain = random.randint(1, 3)
        defense_gain = random.randint(1, 2)
        
        self.stats.max_hp += hp_gain
        self.stats.hp = self.stats.max_hp  # Full heal on level up
        self.stats.attack += attack_gain
        self.stats.defense += defense_gain
        
        return hp_gain, attack_gain, defense_gain

class Monster(Entity):
    def __init__(self, x, y, monster_type, dungeon_level):
        monsters = {
            "goblin": {"char": "g", "color": GREEN, "hp": 20, "attack": 5, "defense": 1, "exp": 15},
            "orc": {"char": "o", "color": DARK_GREEN, "hp": 35, "attack": 8, "defense": 3, "exp": 25},
            "skeleton": {"char": "s", "color": WHITE, "hp": 25, "attack": 6, "defense": 2, "exp": 20},
            "troll": {"char": "T", "color": BROWN, "hp": 60, "attack": 12, "defense": 5, "exp": 40},
            "dragon": {"char": "D", "color": RED, "hp": 100, "attack": 20, "defense": 8, "exp": 100}
        }
        
        monster_data = monsters[monster_type]
        
        # Scale with dungeon level
        level_multiplier = 1 + (dungeon_level - 1) * 0.3
        hp = int(monster_data["hp"] * level_multiplier)
        attack = int(monster_data["attack"] * level_multiplier)
        defense = int(monster_data["defense"] * level_multiplier)
        
        stats = Stats(hp=hp, max_hp=hp, attack=attack, defense=defense, speed=5)
        
        super().__init__(x, y, monster_data["char"], monster_data["color"], 
                        monster_type.capitalize(), stats)
        self.exp_value = int(monster_data["exp"] * level_multiplier)
        self.monster_type = monster_type
        
    def ai_move(self, player, game_map, monsters):
        # Simple AI: move towards player if in range
        dx = player.x - self.x
        dy = player.y - self.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance <= 6:  # Detection range
            # Move towards player
            move_x = 0 if dx == 0 else (1 if dx > 0 else -1)
            move_y = 0 if dy == 0 else (1 if dy > 0 else -1)
            
            # Try to move towards player
            if not self.move(move_x, move_y, game_map):
                # If blocked, try alternative moves
                if move_x != 0 and self.move(move_x, 0, game_map):
                    pass
                elif move_y != 0 and self.move(0, move_y, game_map):
                    pass
                else:
                    # Random move if stuck
                    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
                    random.shuffle(directions)
                    for dx, dy in directions:
                        if self.move(dx, dy, game_map):
                            break

class DungeonGenerator:
    @staticmethod
    def generate_dungeon():
        # Initialize with walls
        dungeon = [[TileType.WALL for _ in range(MAP_WIDTH)] for _ in range(MAP_HEIGHT)]
        
        # Create rooms
        rooms = []
        for _ in range(random.randint(6, 10)):
            room_width = random.randint(4, 8)
            room_height = random.randint(4, 6)
            room_x = random.randint(1, MAP_WIDTH - room_width - 1)
            room_y = random.randint(1, MAP_HEIGHT - room_height - 1)
            
            # Check if room overlaps with existing rooms
            overlap = False
            for existing_room in rooms:
                if (room_x < existing_room[0] + existing_room[2] + 1 and
                    room_x + room_width + 1 > existing_room[0] and
                    room_y < existing_room[1] + existing_room[3] + 1 and
                    room_y + room_height + 1 > existing_room[1]):
                    overlap = True
                    break
            
            if not overlap:
                # Create room
                for y in range(room_y, room_y + room_height):
                    for x in range(room_x, room_x + room_width):
                        dungeon[y][x] = TileType.FLOOR
                
                rooms.append((room_x, room_y, room_width, room_height))
        
        # Connect rooms with corridors
        for i in range(len(rooms) - 1):
            room1 = rooms[i]
            room2 = rooms[i + 1]
            
            # Get center points
            x1 = room1[0] + room1[2] // 2
            y1 = room1[1] + room1[3] // 2
            x2 = room2[0] + room2[2] // 2
            y2 = room2[1] + room2[3] // 2
            
            # Create L-shaped corridor
            # Horizontal first
            start_x, end_x = (x1, x2) if x1 < x2 else (x2, x1)
            for x in range(start_x, end_x + 1):
                dungeon[y1][x] = TileType.FLOOR
            
            # Then vertical
            start_y, end_y = (y1, y2) if y1 < y2 else (y2, y1)
            for y in range(start_y, end_y + 1):
                dungeon[y][x2] = TileType.FLOOR
        
        # Add stairs
        if rooms:
            # Stairs up in first room
            first_room = rooms[0]
            stairs_up_x = first_room[0] + first_room[2] // 2
            stairs_up_y = first_room[1] + first_room[3] // 2
            dungeon[stairs_up_y][stairs_up_x] = TileType.STAIRS_UP
            
            # Stairs down in last room
            last_room = rooms[-1]
            stairs_down_x = last_room[0] + last_room[2] // 2
            stairs_down_y = last_room[1] + last_room[3] // 2
            dungeon[stairs_down_y][stairs_down_x] = TileType.STAIRS_DOWN
        
        return dungeon, rooms

class ItemGenerator:
    @staticmethod
    def generate_item(dungeon_level):
        # Higher level = better items
        rarity_chances = {
            1: {"common": 0.7, "uncommon": 0.25, "rare": 0.05},
            5: {"common": 0.5, "uncommon": 0.3, "rare": 0.15, "epic": 0.05},
            10: {"common": 0.3, "uncommon": 0.3, "rare": 0.25, "epic": 0.1, "legendary": 0.05}
        }

        # Get appropriate rarity chances for level
        if dungeon_level <= 3:
            chances = rarity_chances[1]
        elif dungeon_level <= 8:
            chances = rarity_chances[5]
        else:
            chances = rarity_chances[10]

        # Roll for rarity
        roll = random.random()
        cumulative = 0
        rarity = "common"
        for r, chance in chances.items():
            cumulative += chance
            if roll <= cumulative:
                rarity = r
                break

        # Generate item based on rarity
        item_type = random.choice(list(ItemType))

        if item_type == ItemType.WEAPON:
            weapons = {
                "common": [("Rusty Sword", 3), ("Wooden Club", 2), ("Dagger", 4)],
                "uncommon": [("Iron Sword", 6), ("Battle Axe", 8), ("Mace", 7)],
                "rare": [("Steel Blade", 12), ("War Hammer", 15), ("Enchanted Dagger", 10)],
                "epic": [("Flame Sword", 20), ("Frost Axe", 22), ("Lightning Spear", 18)],
                "legendary": [("Excalibur", 35), ("Mjolnir", 40), ("Shadowbane", 30)]
            }
            name, value = random.choice(weapons[rarity])
            return Item(name, ItemType.WEAPON, value, f"A {rarity} weapon", rarity)

        elif item_type == ItemType.ARMOR:
            armors = {
                "common": [("Leather Armor", 2), ("Cloth Robe", 1), ("Padded Vest", 3)],
                "uncommon": [("Chain Mail", 5), ("Scale Armor", 6), ("Studded Leather", 4)],
                "rare": [("Plate Armor", 10), ("Mage Robes", 8), ("Dragon Scale", 12)],
                "epic": [("Enchanted Plate", 18), ("Arcane Vestments", 15), ("Demon Hide", 20)],
                "legendary": [("Divine Armor", 30), ("Ethereal Robes", 25), ("Titan's Mail", 35)]
            }
            name, value = random.choice(armors[rarity])
            return Item(name, ItemType.ARMOR, value, f"A {rarity} armor piece", rarity)

        elif item_type == ItemType.POTION:
            potions = [
                ("Health Potion", 25, "Restores 25 HP"),
                ("Greater Health Potion", 50, "Restores 50 HP"),
                ("Super Health Potion", 100, "Restores 100 HP")
            ]
            name, value, desc = random.choice(potions)
            return Item(name, ItemType.POTION, value, desc, rarity)

        elif item_type == ItemType.TREASURE:
            treasures = {
                "common": [("Copper Coins", 10), ("Silver Ring", 25)],
                "uncommon": [("Gold Coins", 50), ("Gem", 75)],
                "rare": [("Ruby", 150), ("Sapphire", 200)],
                "epic": [("Diamond", 500), ("Ancient Relic", 750)],
                "legendary": [("Crown Jewel", 1500), ("Dragon Hoard", 2000)]
            }
            name, value = random.choice(treasures[rarity])
            return Item(name, ItemType.TREASURE, value, f"Valuable {rarity} treasure", rarity)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Infinite Dungeon Crawler - Roguelike RPG")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.big_font = pygame.font.Font(None, 36)

        # Game state
        self.game_map = None
        self.rooms = []
        self.player = None
        self.monsters = []
        self.items = []
        self.messages = []
        self.game_over = False
        self.turn_count = 0

        # UI state
        self.show_inventory = False
        self.selected_item = 0

        self.new_game()

    def new_game(self):
        self.generate_level()

    def generate_level(self):
        self.game_map, self.rooms = DungeonGenerator.generate_dungeon()

        # Place player in first room
        if self.rooms:
            first_room = self.rooms[0]
            player_x = first_room[0] + 1
            player_y = first_room[1] + 1

            if self.player is None:
                self.player = Player(player_x, player_y)
            else:
                self.player.x = player_x
                self.player.y = player_y

        # Generate monsters
        self.monsters = []
        monster_types = ["goblin", "orc", "skeleton"]
        if self.player.dungeon_level >= 5:
            monster_types.append("troll")
        if self.player.dungeon_level >= 10:
            monster_types.append("dragon")

        monster_count = min(15, 5 + self.player.dungeon_level)

        for _ in range(monster_count):
            # Place in random room (not first room)
            room = random.choice(self.rooms[1:] if len(self.rooms) > 1 else self.rooms)
            x = random.randint(room[0], room[0] + room[2] - 1)
            y = random.randint(room[1], room[1] + room[3] - 1)

            # Make sure position is empty
            if self.game_map[y][x] == TileType.FLOOR and not self.get_entity_at(x, y):
                monster_type = random.choice(monster_types)
                self.monsters.append(Monster(x, y, monster_type, self.player.dungeon_level))

        # Generate items
        self.items = []
        item_count = random.randint(8, 15)

        for _ in range(item_count):
            room = random.choice(self.rooms)
            x = random.randint(room[0], room[0] + room[2] - 1)
            y = random.randint(room[1], room[1] + room[3] - 1)

            if self.game_map[y][x] == TileType.FLOOR and not self.get_entity_at(x, y) and not self.get_item_at(x, y):
                item = ItemGenerator.generate_item(self.player.dungeon_level)
                self.items.append((x, y, item))

        self.add_message(f"Welcome to dungeon level {self.player.dungeon_level}!")

    def get_entity_at(self, x, y):
        if self.player.x == x and self.player.y == y:
            return self.player
        for monster in self.monsters:
            if monster.x == x and monster.y == y:
                return monster
        return None

    def get_item_at(self, x, y):
        for item_x, item_y, item in self.items:
            if item_x == x and item_y == y:
                return item
        return None

    def add_message(self, message):
        self.messages.append(message)
        if len(self.messages) > 10:
            self.messages.pop(0)

    def handle_player_move(self, dx, dy):
        if self.game_over:
            return

        new_x = self.player.x + dx
        new_y = self.player.y + dy

        # Check for monsters at target position
        target_monster = None
        for monster in self.monsters:
            if monster.x == new_x and monster.y == new_y:
                target_monster = monster
                break

        if target_monster:
            # Attack monster
            damage = self.player.attack(target_monster)
            self.add_message(f"You attack {target_monster.name} for {damage} damage!")

            if not target_monster.is_alive():
                self.add_message(f"{target_monster.name} dies!")
                self.player.gain_exp(target_monster.exp_value)
                self.monsters.remove(target_monster)

                # Check for level up
                if hasattr(self.player, '_just_leveled'):
                    delattr(self.player, '_just_leveled')
                    hp_gain, att_gain, def_gain = self.player.level_up()
                    self.add_message(f"Level up! +{hp_gain} HP, +{att_gain} ATK, +{def_gain} DEF")
        else:
            # Try to move
            if self.player.move(dx, dy, self.game_map):
                # Check for items
                item = self.get_item_at(self.player.x, self.player.y)
                if item:
                    self.player.inventory.append(item)
                    self.items = [(x, y, i) for x, y, i in self.items if not (x == self.player.x and y == self.player.y)]
                    self.add_message(f"You pick up {item.name}")

                # Check for stairs
                tile = self.game_map[self.player.y][self.player.x]
                if tile == TileType.STAIRS_DOWN:
                    self.player.dungeon_level += 1
                    self.generate_level()
                elif tile == TileType.STAIRS_UP and self.player.dungeon_level > 1:
                    self.player.dungeon_level -= 1
                    self.generate_level()

        # Monster turns
        self.monster_turn()
        self.turn_count += 1

    def monster_turn(self):
        for monster in self.monsters[:]:
            if not monster.is_alive():
                continue

            # Check if adjacent to player
            dx = abs(monster.x - self.player.x)
            dy = abs(monster.y - self.player.y)

            if dx <= 1 and dy <= 1 and (dx + dy) > 0:
                # Attack player
                damage = monster.attack(self.player)
                self.add_message(f"{monster.name} attacks you for {damage} damage!")

                if not self.player.is_alive():
                    self.game_over = True
                    self.add_message("You have died! Press R to restart.")
            else:
                # Move towards player
                monster.ai_move(self.player, self.game_map, self.monsters)

    def use_item(self, item):
        if item.item_type == ItemType.POTION:
            self.player.heal(item.value)
            self.add_message(f"You drink {item.name} and restore {item.value} HP!")
            self.player.inventory.remove(item)
        elif item.item_type == ItemType.WEAPON:
            old_weapon = self.player.equipment["weapon"]
            self.player.equipment["weapon"] = item
            self.player.inventory.remove(item)
            if old_weapon:
                self.player.inventory.append(old_weapon)
            self.add_message(f"You equip {item.name}!")
        elif item.item_type == ItemType.ARMOR:
            old_armor = self.player.equipment["armor"]
            self.player.equipment["armor"] = item
            self.player.inventory.remove(item)
            if old_armor:
                self.player.inventory.append(old_armor)
            self.add_message(f"You equip {item.name}!")
        elif item.item_type == ItemType.TREASURE:
            self.player.gold += item.value
            self.player.inventory.remove(item)
            self.add_message(f"You sell {item.name} for {item.value} gold!")

    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if self.game_over and event.key == pygame.K_r:
                self.player = None
                self.new_game()
                self.game_over = False
                return

            if self.show_inventory:
                if event.key == pygame.K_ESCAPE or event.key == pygame.K_i:
                    self.show_inventory = False
                elif event.key == pygame.K_UP:
                    self.selected_item = max(0, self.selected_item - 1)
                elif event.key == pygame.K_DOWN:
                    self.selected_item = min(len(self.player.inventory) - 1, self.selected_item + 1)
                elif event.key == pygame.K_RETURN and self.player.inventory:
                    if self.selected_item < len(self.player.inventory):
                        self.use_item(self.player.inventory[self.selected_item])
                        self.selected_item = min(self.selected_item, len(self.player.inventory) - 1)
                return

            # Movement and actions
            if event.key == pygame.K_LEFT or event.key == pygame.K_a:
                self.handle_player_move(-1, 0)
            elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                self.handle_player_move(1, 0)
            elif event.key == pygame.K_UP or event.key == pygame.K_w:
                self.handle_player_move(0, -1)
            elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                self.handle_player_move(0, 1)
            elif event.key == pygame.K_i:
                self.show_inventory = True
                self.selected_item = 0

    def draw_tile(self, x, y, tile_type):
        screen_x = x * TILE_SIZE
        screen_y = y * TILE_SIZE

        if tile_type == TileType.WALL:
            pygame.draw.rect(self.screen, DARK_GRAY, (screen_x, screen_y, TILE_SIZE, TILE_SIZE))
        elif tile_type == TileType.FLOOR:
            pygame.draw.rect(self.screen, GRAY, (screen_x, screen_y, TILE_SIZE, TILE_SIZE))
        elif tile_type == TileType.STAIRS_DOWN:
            pygame.draw.rect(self.screen, GRAY, (screen_x, screen_y, TILE_SIZE, TILE_SIZE))
            text = self.font.render('>', True, WHITE)
            self.screen.blit(text, (screen_x + 8, screen_y + 8))
        elif tile_type == TileType.STAIRS_UP:
            pygame.draw.rect(self.screen, GRAY, (screen_x, screen_y, TILE_SIZE, TILE_SIZE))
            text = self.font.render('<', True, WHITE)
            self.screen.blit(text, (screen_x + 8, screen_y + 8))

    def draw_entity(self, entity):
        screen_x = entity.x * TILE_SIZE
        screen_y = entity.y * TILE_SIZE

        # Draw character
        text = self.font.render(entity.char, True, entity.color)
        self.screen.blit(text, (screen_x + 8, screen_y + 8))

        # Draw health bar for monsters
        if isinstance(entity, Monster):
            bar_width = TILE_SIZE - 4
            bar_height = 4
            bar_x = screen_x + 2
            bar_y = screen_y + TILE_SIZE - 6

            # Background
            pygame.draw.rect(self.screen, RED, (bar_x, bar_y, bar_width, bar_height))

            # Health
            health_ratio = entity.stats.hp / entity.stats.max_hp
            health_width = int(bar_width * health_ratio)
            pygame.draw.rect(self.screen, GREEN, (bar_x, bar_y, health_width, bar_height))

    def draw_item(self, x, y, item):
        screen_x = x * TILE_SIZE
        screen_y = y * TILE_SIZE

        # Draw item symbol
        symbols = {
            ItemType.WEAPON: '/',
            ItemType.ARMOR: '[',
            ItemType.POTION: '!',
            ItemType.SCROLL: '?',
            ItemType.TREASURE: '$'
        }

        symbol = symbols.get(item.item_type, '?')
        text = self.font.render(symbol, True, item.get_color())
        self.screen.blit(text, (screen_x + 8, screen_y + 8))

    def draw_ui(self):
        # UI panel
        ui_x = MAP_WIDTH * TILE_SIZE
        pygame.draw.rect(self.screen, BLACK, (ui_x, 0, SCREEN_WIDTH - ui_x, SCREEN_HEIGHT))

        y_offset = 10

        # Player stats
        stats_text = [
            f"Level: {self.player.stats.level}",
            f"HP: {self.player.stats.hp}/{self.player.stats.max_hp}",
            f"Attack: {self.player.stats.attack}",
            f"Defense: {self.player.get_defense()}",
            f"EXP: {self.player.stats.exp}/{self.player.stats.exp_to_next}",
            f"Gold: {self.player.gold}",
            f"Dungeon Level: {self.player.dungeon_level}",
            f"Turn: {self.turn_count}"
        ]

        for text in stats_text:
            surface = self.font.render(text, True, WHITE)
            self.screen.blit(surface, (ui_x + 10, y_offset))
            y_offset += 25

        y_offset += 20

        # Equipment
        equipment_text = self.font.render("Equipment:", True, YELLOW)
        self.screen.blit(equipment_text, (ui_x + 10, y_offset))
        y_offset += 25

        weapon = self.player.equipment["weapon"]
        weapon_text = f"Weapon: {weapon.name if weapon else 'None'}"
        surface = self.font.render(weapon_text, True, WHITE)
        self.screen.blit(surface, (ui_x + 10, y_offset))
        y_offset += 20

        armor = self.player.equipment["armor"]
        armor_text = f"Armor: {armor.name if armor else 'None'}"
        surface = self.font.render(armor_text, True, WHITE)
        self.screen.blit(surface, (ui_x + 10, y_offset))
        y_offset += 40

        # Messages
        messages_text = self.font.render("Messages:", True, YELLOW)
        self.screen.blit(messages_text, (ui_x + 10, y_offset))
        y_offset += 25

        for message in self.messages[-8:]:  # Show last 8 messages
            surface = self.font.render(message, True, WHITE)
            self.screen.blit(surface, (ui_x + 10, y_offset))
            y_offset += 20

        # Controls
        y_offset = SCREEN_HEIGHT - 150
        controls_text = [
            "Controls:",
            "WASD/Arrows: Move",
            "I: Inventory",
            "ESC: Quit"
        ]

        for text in controls_text:
            color = YELLOW if text == "Controls:" else WHITE
            surface = self.font.render(text, True, color)
            self.screen.blit(surface, (ui_x + 10, y_offset))
            y_offset += 20

    def draw_inventory(self):
        # Semi-transparent overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))

        # Inventory window
        inv_width = 600
        inv_height = 500
        inv_x = (SCREEN_WIDTH - inv_width) // 2
        inv_y = (SCREEN_HEIGHT - inv_height) // 2

        pygame.draw.rect(self.screen, DARK_GRAY, (inv_x, inv_y, inv_width, inv_height))
        pygame.draw.rect(self.screen, WHITE, (inv_x, inv_y, inv_width, inv_height), 2)

        # Title
        title = self.big_font.render("Inventory", True, WHITE)
        self.screen.blit(title, (inv_x + 20, inv_y + 20))

        # Instructions
        instructions = [
            "Use UP/DOWN to select, ENTER to use/equip",
            "ESC to close inventory"
        ]

        y_offset = inv_y + 60
        for instruction in instructions:
            text = self.font.render(instruction, True, YELLOW)
            self.screen.blit(text, (inv_x + 20, y_offset))
            y_offset += 25

        y_offset += 20

        # Items
        if not self.player.inventory:
            empty_text = self.font.render("Inventory is empty", True, WHITE)
            self.screen.blit(empty_text, (inv_x + 20, y_offset))
        else:
            for i, item in enumerate(self.player.inventory):
                color = YELLOW if i == self.selected_item else item.get_color()

                # Item name and description
                item_text = f"{item.name} - {item.description}"
                if item.item_type in [ItemType.WEAPON, ItemType.ARMOR]:
                    item_text += f" (+{item.value})"
                elif item.item_type == ItemType.POTION:
                    item_text += f" (Heals {item.value})"
                elif item.item_type == ItemType.TREASURE:
                    item_text += f" (Worth {item.value} gold)"

                text = self.font.render(item_text, True, color)
                self.screen.blit(text, (inv_x + 20, y_offset))
                y_offset += 25

    def draw(self):
        self.screen.fill(BLACK)

        # Draw map
        for y in range(MAP_HEIGHT):
            for x in range(MAP_WIDTH):
                self.draw_tile(x, y, self.game_map[y][x])

        # Draw items
        for x, y, item in self.items:
            self.draw_item(x, y, item)

        # Draw monsters
        for monster in self.monsters:
            self.draw_entity(monster)

        # Draw player
        self.draw_entity(self.player)

        # Draw UI
        self.draw_ui()

        # Draw inventory if open
        if self.show_inventory:
            self.draw_inventory()

        # Game over screen
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill(RED)
            self.screen.blit(overlay, (0, 0))

            game_over_text = self.big_font.render("GAME OVER", True, WHITE)
            restart_text = self.font.render("Press R to restart", True, WHITE)

            text_x = SCREEN_WIDTH // 2 - game_over_text.get_width() // 2
            text_y = SCREEN_HEIGHT // 2 - 50

            self.screen.blit(game_over_text, (text_x, text_y))
            self.screen.blit(restart_text, (text_x, text_y + 50))

        pygame.display.flip()

    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE and not self.show_inventory:
                        running = False
                    else:
                        self.handle_input(event)

            self.draw()
            self.clock.tick(FPS)

        pygame.quit()

if __name__ == "__main__":
    game = Game()
    game.run()
