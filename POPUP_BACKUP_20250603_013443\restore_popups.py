#!/usr/bin/env python3
'''
Restore popup windows from backup 20250603_013443
'''

import shutil
import os

def restore_popups():
    '''Restore all popup windows from backup'''
    
    backup_files = ['user_management.py', 'product_management.py', 'sales_history.py', 'receipt_settings.py', 'storage_management.py', 'number_keyboard.py', 'pos_screen.py']
    
    print("🔄 RESTORING POPUP WINDOWS FROM BACKUP")
    print("=" * 40)
    
    restored = 0
    for file_name in backup_files:
        backup_path = os.path.join('POPUP_BACKUP_20250603_013443', file_name)
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, file_name)
            print(f"✅ Restored: {file_name}")
            restored += 1
        else:
            print(f"❌ Backup not found: {file_name}")
    
    print(f"\n📊 Restored {restored} files")
    print("🔄 Popup windows restored to original design!")
    
    return restored > 0

if __name__ == "__main__":
    success = restore_popups()
    exit(0 if success else 1)
