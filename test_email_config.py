#!/usr/bin/env python3
"""
Test the updated email configuration
"""

from email_manager import <PERSON>ailMana<PERSON>

def test_config():
    print("=== EMAIL CONFIGURATION TEST ===")
    
    em = EmailManager()
    config = em.get_smtp_config()
    
    print(f"Email: {config['sender_email']}")
    print(f"Password: {config['sender_password']}")
    print(f"SMTP Server: {config['smtp_server']}")
    print(f"SMTP Port: {config['smtp_port']}")
    
    print("\n=== TESTING SMTP CONNECTION ===")
    success, message = em.test_smtp_connection()
    
    print(f"Connection Test: {'✅ SUCCESS' if success else '❌ FAILED'}")
    print(f"Message: {message}")
    
    if not success:
        print("\n📧 DEDICATED POS EMAIL ACCOUNT:")
        print("Email: <EMAIL>")
        print("This account is pre-configured for SMTP access.")
        print("If connection fails, the account may need activation.")
    else:
        print("\n🎉 EMAIL SYSTEM READY!")
        print("You can now use email features in the POS system.")

if __name__ == "__main__":
    test_config()
