# POS System - Admin Credentials

## 🔐 Default Admin Login

**Username:** `admin`  
**Password:** `@H@W@LeComptoir@`

## 📋 Updated Versions

The admin password has been changed in **ALL** versions of the POS system:

✅ **Main System** (`pos_system.db`)  
✅ **YES** - Protected version (`YES/pos_system.db`)  
✅ **YES_OBFUSCATED** - Secure version (`YES_OBFUSCATED/pos_system.db`)  
✅ **YES_COMPILED** - Compiled version (`YES_COMPILED/pos_system.db`)  
✅ **YES_SECURE** - Secure version (`YES_SECURE/pos_system.db`)  

## 🔄 Password Change Details

- **Old Password:** `0000` ❌ (No longer works)
- **New Password:** `@H@W@LeComptoir@` ✅ (Active)
- **Change Date:** Applied to all versions
- **Security:** SHA-256 hashed in database

## 🚀 How to Login

1. Start the POS system
2. Select "admin" from the user list
3. Enter password: `@H@W@LeComptoir@`
4. Click login or press Enter

## ⚠️ Important Notes

- **Case Sensitive:** The password is case-sensitive
- **Special Characters:** Contains @ symbols - type exactly as shown
- **All Versions:** Same password works in all protected versions
- **Security:** Password is hashed and stored securely

## 🔧 For Developers

If you need to change the password again, modify the following in `database.py`:

```python
# Line ~166 and ~173
hashed_password = hashlib.sha256('@H@W@LeComptoir@'.encode()).hexdigest()
```

Then run:
```bash
python update_admin_password.py
```

---
**🔒 Keep this information secure and share only with authorized personnel.**
