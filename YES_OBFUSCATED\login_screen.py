# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
