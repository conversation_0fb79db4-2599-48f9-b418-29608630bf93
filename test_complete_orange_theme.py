#!/usr/bin/env python3
"""
Test complete orange theme including storage management and text colors
"""

import sys
import os
from pathlib import Path

def test_storage_management_theme():
    """Test that storage management uses orange/black theme"""
    
    print("Testing Storage Management Theme")
    print("=" * 34)
    
    files_to_check = [
        "storage_management.py",
        "YES/storage_management.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark backgrounds
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Main background is dark")
            else:
                print(f"   ❌ Main background not dark")
                all_themed = False
            
            # Check for dark frames
            if "bg='#2d2d2d'" in content:
                print(f"   ✅ Uses dark frames")
            else:
                print(f"   ❌ Doesn't use dark frames")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
            
            # Check for white text on dark backgrounds
            if "fg='white'" in content:
                print(f"   ✅ White text on dark backgrounds")
            else:
                print(f"   ❌ White text missing")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_text_colors_system_wide():
    """Test that all text is white on dark backgrounds"""
    
    print("\nTesting Text Colors System-Wide")
    print("=" * 33)
    
    files_to_check = [
        "pos_screen.py",
        "product_management.py",
        "user_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "number_keyboard.py"
    ]
    
    all_correct = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for white text
            white_text_count = content.count("fg='white'")
            
            # Check for problematic black text on dark backgrounds
            problematic_patterns = [
                "bg='#2d2d2d'.*fg='black'",
                "bg='#1a1a1a'.*fg='black'",
                "bg='#404040'.*fg='black'"
            ]
            
            has_problems = False
            for pattern in problematic_patterns:
                import re
                if re.search(pattern, content):
                    has_problems = True
                    break
            
            if white_text_count > 0 and not has_problems:
                print(f"   ✅ Text colors correct ({white_text_count} white text instances)")
            else:
                print(f"   ❌ Text color issues found")
                all_correct = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_correct = False
    
    return all_correct

def test_no_old_colors_remaining():
    """Test that no old color scheme remains"""
    
    print("\nTesting No Old Colors Remaining")
    print("=" * 33)
    
    files_to_check = [
        "pos_screen.py",
        "product_management.py",
        "user_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "storage_management.py",
        "number_keyboard.py"
    ]
    
    all_clean = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for old colors that should be replaced
            old_patterns = [
                "configure(bg='#f0f0f0')",
                "bg='white'",
                "fg='#2c3e50'",
                "font=('Helvetica'"
            ]
            
            found_old = []
            for pattern in old_patterns:
                if pattern in content:
                    found_old.append(pattern)
            
            if not found_old:
                print(f"   ✅ No old colors remaining")
            else:
                print(f"   ❌ Old colors found: {', '.join(found_old)}")
                all_clean = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_clean = False
    
    return all_clean

def test_button_colors_still_preserved():
    """Test that button colors are still preserved"""
    
    print("\nTesting Button Colors Still Preserved")
    print("=" * 37)
    
    files_to_check = [
        "pos_screen.py",
        "product_management.py",
        "storage_management.py"
    ]
    
    all_preserved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for preserved button colors
            button_colors = ['#28a745', '#dc3545', '#6c757d', '#007bff', '#ffc107']
            found_colors = sum(1 for color in button_colors if color in content)
            
            if found_colors >= 2:
                print(f"   ✅ Button colors preserved ({found_colors} colors found)")
            else:
                print(f"   ❌ Button colors not preserved ({found_colors} colors found)")
                all_preserved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_preserved = False
    
    return all_preserved

def test_all_versions_updated():
    """Test that all versions have been updated"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main Storage", "storage_management.py"),
        ("Protected Storage", "YES/storage_management.py"),
        ("Obfuscated Storage", "YES_OBFUSCATED/storage_management.py"),
        ("Main POS", "pos_screen.py"),
        ("Protected POS", "YES/pos_screen.py"),
        ("Obfuscated POS", "YES_OBFUSCATED/pos_screen.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name}")
        else:
            print(f"❌ Missing: {version_name}")
            all_updated = False
    
    return all_updated

def main():
    """Run all complete orange theme tests"""
    
    print("🧡 COMPLETE ORANGE THEME TEST SUITE")
    print("=" * 37)
    
    tests = [
        ("Storage Management Theme", test_storage_management_theme),
        ("Text Colors System-Wide", test_text_colors_system_wide),
        ("No Old Colors Remaining", test_no_old_colors_remaining),
        ("Button Colors Still Preserved", test_button_colors_still_preserved),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 37)
    print("📊 RESULTS")
    print("=" * 37)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Complete orange/black theme applied")
        print("✅ Storage management themed")
        print("✅ White text on dark backgrounds")
        print("✅ No old colors remaining")
        print("✅ Button colors preserved")
        print("✅ All versions updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Theme application may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🧡 Complete orange theme successfully applied!")
        print("📦 Storage management fully themed")
        print("⚪ All text is white on dark backgrounds")
        print("🖤 No old color scheme remaining")
        print("🔘 Button colors preserved as requested")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Complete orange theme needs attention")
    
    exit(0 if success else 1)
