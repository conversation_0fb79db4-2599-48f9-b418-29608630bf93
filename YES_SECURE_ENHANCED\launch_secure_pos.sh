#!/bin/bash

echo "========================================"
echo "    SECURE POS SYSTEM LAUNCHER"
echo "========================================"
echo

# Change to script directory
cd "$(dirname "$0")"

echo "Current directory: $(pwd)"
echo

# Check if main.py exists
if [ ! -f "main.py" ]; then
    echo "ERROR: main.py not found!"
    echo "Please ensure you're running this from the POS system directory."
    read -p "Press Enter to exit..."
    exit 1
fi

echo "Checking Python installation..."
python3 --version || python --version
if [ $? -ne 0 ]; then
    echo "ERROR: Python not found!"
    echo "Please install Python 3.7+ and try again."
    read -p "Press Enter to exit..."
    exit 1
fi

echo
echo "Starting secure launcher..."
echo

# Try GUI launcher first
echo "Attempting GUI launcher..."
python3 simple_secure_launcher.py 2>/dev/null || python simple_secure_launcher.py 2>/dev/null
if [ $? -ne 0 ]; then
    echo
    echo "GUI launcher failed, trying console launcher..."
    echo
    python3 console_launcher.py 2>/dev/null || python console_launcher.py 2>/dev/null
    if [ $? -ne 0 ]; then
        echo
        echo "Console launcher failed, trying direct launch..."
        echo
        echo "WARNING: Launching without security checks!"
        echo
        python3 main.py 2>/dev/null || python main.py 2>/dev/null
    fi
fi

echo
echo "POS system closed."
read -p "Press Enter to exit..."
