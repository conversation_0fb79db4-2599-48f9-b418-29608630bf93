#!/usr/bin/env python3
"""
Test full translation and working keyboard functionality
"""

import sys
import os
from pathlib import Path

def test_full_translation_support():
    """Test that ALL text in login screen translates"""
    
    print("Testing Full Translation Support")
    print("=" * 34)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_translating = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for translatable brand title
            if "self.app.get_text('login_title')" in content:
                print(f"   ✅ Brand title uses translation")
            else:
                print(f"   ❌ Brand title not translatable")
                all_translating = False
            
            # Check for translatable tagline
            if "Solution de Point de Vente Moderne" in content and "Modern Point of Sale Solution" in content:
                print(f"   ✅ Tagline translates (French/English)")
            else:
                print(f"   ❌ Tagline not translatable")
                all_translating = False
            
            # Check for translatable features
            if "Rapide et Fiable" in content and "Fast & Reliable" in content:
                print(f"   ✅ Feature list translates")
            else:
                print(f"   ❌ Feature list not translatable")
                all_translating = False
            
            # Check for translatable welcome text
            if "Bon Retour" in content and "Welcome Back" in content:
                print(f"   ✅ Welcome text translates")
            else:
                print(f"   ❌ Welcome text not translatable")
                all_translating = False
            
            # Check for translatable subtitle
            if "Connectez-vous à votre compte" in content and "Sign in to your account" in content:
                print(f"   ✅ Subtitle translates")
            else:
                print(f"   ❌ Subtitle not translatable")
                all_translating = False
            
            # Check for refresh interface method
            if "def refresh_interface(self):" in content:
                print(f"   ✅ Interface refresh method exists")
            else:
                print(f"   ❌ Interface refresh method missing")
                all_translating = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_translating = False
    
    return all_translating

def test_keyboard_detection_fix():
    """Test that keyboard can detect password entry correctly"""
    
    print("\nTesting Keyboard Detection Fix")
    print("=" * 32)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_detection = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for password entry with bullet character
            if "show='●'" in content:
                print(f"   ✅ Password entry uses bullet character")
            else:
                print(f"   ❌ Password entry bullet character missing")
                all_detection = False
            
            # Check for fixed detection method
            if "widget.cget('show') in ['*', '●']" in content:
                print(f"   ✅ Detection method handles both * and ●")
            else:
                print(f"   ❌ Detection method not fixed")
                all_detection = False
            
            # Check for force show parameter
            if "self.show_password_keyboard(force_show=True)" in content:
                print(f"   ✅ Keyboard forced to show on user click")
            else:
                print(f"   ❌ Keyboard not forced to show")
                all_detection = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_detection = False
    
    return all_detection

def test_keyboard_orange_theme():
    """Test that keyboard uses complete orange theme"""
    
    print("\nTesting Keyboard Orange Theme")
    print("=" * 31)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_orange_theme = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark window background
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Dark window background")
            else:
                print(f"   ❌ Dark window background missing")
                all_orange_theme = False
            
            # Check for orange title
            if "fg='#ff8c00'" in content and "title_label" in content:
                print(f"   ✅ Orange title color")
            else:
                print(f"   ❌ Orange title color missing")
                all_orange_theme = False
            
            # Check for dark number buttons
            if "bg='#404040'" in content and "Number button" in content:
                print(f"   ✅ Dark number button background")
            else:
                print(f"   ❌ Dark number button background missing")
                all_orange_theme = False
            
            # Check for orange active states
            if "activebackground='#ff8c00'" in content:
                print(f"   ✅ Orange active states")
            else:
                print(f"   ❌ Orange active states missing")
                all_orange_theme = False
            
            # Check for orange enter button
            if "bg='#ff8c00'" in content and "ENTER" in content:
                print(f"   ✅ Orange enter button")
            else:
                print(f"   ❌ Orange enter button missing")
                all_orange_theme = False
            
            # Check for modern styling
            if "relief='flat'" in content and "cursor='hand2'" in content:
                print(f"   ✅ Modern flat styling")
            else:
                print(f"   ❌ Modern flat styling missing")
                all_orange_theme = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_orange_theme = False
    
    return all_orange_theme

def test_property_text_translation():
    """Test that property text translates correctly"""
    
    print("\nTesting Property Text Translation")
    print("=" * 35)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_property = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for English property text
            if "Intellectual property of Hossam Lotfi and Walid Abdou" in content:
                print(f"   ✅ English property text correct")
            else:
                print(f"   ❌ English property text incorrect")
                all_property = False
            
            # Check for French property text
            if "Propriété intellectuelle de Hossam Lotfi et Walid Abdou" in content:
                print(f"   ✅ French property text correct")
            else:
                print(f"   ❌ French property text incorrect")
                all_property = False
            
            # Check for property text method
            if "def get_property_text(self):" in content:
                print(f"   ✅ Property text method exists")
            else:
                print(f"   ❌ Property text method missing")
                all_property = False
            
            # Check for property text update
            if "self.property_label.config(text=self.get_property_text())" in content:
                print(f"   ✅ Property text updates on language change")
            else:
                print(f"   ❌ Property text doesn't update")
                all_property = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_property = False
    
    return all_property

def test_keyboard_functionality():
    """Test that keyboard has all necessary functionality"""
    
    print("\nTesting Keyboard Functionality")
    print("=" * 32)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_functionality = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for number input functionality
            if "def add_input(self, value):" in content:
                print(f"   ✅ Number input functionality")
            else:
                print(f"   ❌ Number input functionality missing")
                all_functionality = False
            
            # Check for clear functionality
            if "def clear_input(self):" in content:
                print(f"   ✅ Clear input functionality")
            else:
                print(f"   ❌ Clear input functionality missing")
                all_functionality = False
            
            # Check for backspace functionality
            if "def backspace(self):" in content:
                print(f"   ✅ Backspace functionality")
            else:
                print(f"   ❌ Backspace functionality missing")
                all_functionality = False
            
            # Check for enter callback
            if "def enter_pressed(self):" in content and "on_enter_callback" in content:
                print(f"   ✅ Enter callback functionality")
            else:
                print(f"   ❌ Enter callback functionality missing")
                all_functionality = False
            
            # Check for keyboard closing
            if "def close(self):" in content:
                print(f"   ✅ Keyboard close functionality")
            else:
                print(f"   ❌ Keyboard close functionality missing")
                all_functionality = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_functionality = False
    
    return all_functionality

def test_all_versions_updated():
    """Test that all versions have been updated"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main Login", "login_screen.py"),
        ("Main Keyboard", "number_keyboard.py"),
        ("Protected Login", "YES/login_screen.py"),
        ("Protected Keyboard", "YES/number_keyboard.py"),
        ("Obfuscated Login", "YES_OBFUSCATED/login_screen.py"),
        ("Obfuscated Keyboard", "YES_OBFUSCATED/number_keyboard.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name}")
        else:
            print(f"❌ Missing: {version_name}")
            all_updated = False
    
    return all_updated

def main():
    """Run all translation and keyboard functionality tests"""
    
    print("🌐⌨️ TRANSLATION & KEYBOARD FUNCTIONALITY TEST SUITE")
    print("=" * 52)
    
    tests = [
        ("Full Translation Support", test_full_translation_support),
        ("Keyboard Detection Fix", test_keyboard_detection_fix),
        ("Keyboard Orange Theme", test_keyboard_orange_theme),
        ("Property Text Translation", test_property_text_translation),
        ("Keyboard Functionality", test_keyboard_functionality),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 52)
    print("📊 RESULTS")
    print("=" * 52)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Complete translation support for all text")
        print("✅ Keyboard detection fixed (● and * characters)")
        print("✅ Orange/black theme applied to keyboard")
        print("✅ Property text translates between languages")
        print("✅ Full keyboard functionality working")
        print("✅ All versions (main, protected, obfuscated) updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Translation or keyboard functionality incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🌐⌨️ Translation and keyboard functionality complete!")
        print("🔤 ALL text translates between English and French")
        print("⌨️ Number keyboard works properly with orange theme")
        print("📍 Language picker at bottom left with property text")
        print("🎨 Consistent orange/black/dark gray theme throughout")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Translation or keyboard functionality needs attention")
    
    exit(0 if success else 1)
