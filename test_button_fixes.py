#!/usr/bin/env python3
"""
Test the button fixes and empty text areas
"""

import sys
import os
from pathlib import Path

def test_empty_text_areas():
    """Test that text areas start empty by default"""
    
    print("Testing Empty Text Areas")
    print("=" * 25)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_empty = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check that example text is removed
            if "example_text = " in content:
                print(f"   ❌ Example text still present")
                all_empty = False
            else:
                print(f"   ✅ Example text removed")
            
            # Check for "Start with empty text area" comment
            if "Start with empty text area" in content:
                print(f"   ✅ Empty text area comment found")
            else:
                print(f"   ❌ Empty text area comment missing")
                all_empty = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_empty = False
    
    return all_empty

def test_button_layout_fixes():
    """Test that button layout has been improved"""
    
    print("\nTesting Button Layout Fixes")
    print("=" * 28)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for improved button frame packing
            if "side=tk.BOTTOM" in content:
                print(f"   ✅ Button frame uses BOTTOM packing")
            else:
                print(f"   ❌ Button frame BOTTOM packing missing")
                all_fixed = False
            
            # Check for increased padding
            if "pady=(20, 0)" in content:
                print(f"   ✅ Increased button padding found")
            else:
                print(f"   ❌ Increased button padding missing")
                all_fixed = False
            
            # Check for "ensure it's visible" comment
            if "ensure it's visible" in content:
                print(f"   ✅ Button visibility comment found")
            else:
                print(f"   ❌ Button visibility comment missing")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_bulk_dialog_structure():
    """Test that bulk dialogs have proper structure"""
    
    print("\nTesting Bulk Dialog Structure")
    print("=" * 30)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_structured = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bulk product dialog
            if "show_bulk_product_dialog" in content:
                print(f"   ✅ Bulk product dialog method found")
            else:
                print(f"   ❌ Bulk product dialog method missing")
                all_structured = False
            
            # Check for bulk category dialog
            if "show_bulk_category_dialog" in content:
                print(f"   ✅ Bulk category dialog method found")
            else:
                print(f"   ❌ Bulk category dialog method missing")
                all_structured = False
            
            # Check for proper button structure
            if "📥 Add All Products" in content:
                print(f"   ✅ Product confirm button found")
            else:
                print(f"   ❌ Product confirm button missing")
                all_structured = False
            
            if "📥 Add All Categories" in content:
                print(f"   ✅ Category confirm button found")
            else:
                print(f"   ❌ Category confirm button missing")
                all_structured = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_structured = False
    
    return all_structured

def test_dialog_sizes():
    """Test that dialog sizes are appropriate"""
    
    print("\nTesting Dialog Sizes")
    print("=" * 21)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_sized = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bulk product dialog size
            if "700x600" in content:
                print(f"   ✅ Bulk product dialog size (700x600)")
            else:
                print(f"   ❌ Bulk product dialog size missing")
                all_sized = False
            
            # Check for bulk category dialog size
            if "500x400" in content:
                print(f"   ✅ Bulk category dialog size (500x400)")
            else:
                print(f"   ❌ Bulk category dialog size missing")
                all_sized = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_sized = False
    
    return all_sized

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/product_management.py"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all button fix tests"""
    
    print("🔘 BUTTON FIXES TEST SUITE")
    print("=" * 30)
    
    tests = [
        ("Empty Text Areas", test_empty_text_areas),
        ("Button Layout Fixes", test_button_layout_fixes),
        ("Bulk Dialog Structure", test_bulk_dialog_structure),
        ("Dialog Sizes", test_dialog_sizes),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 35)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Text areas start empty by default")
        print("✅ Confirm buttons are visible")
        print("✅ Button layout improved")
        print("✅ Dialog sizes appropriate")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Button fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔘 Button fixes successfully implemented!")
        print("📝 Text areas start empty (no example content)")
        print("🔘 Confirm buttons are now visible")
        print("📐 Dialog layouts improved")
    else:
        print("\n❌ Button fixes need attention")
    
    exit(0 if success else 1)
