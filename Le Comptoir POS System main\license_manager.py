"""
License Management System
"""

import json
import datetime
from pathlib import Path

class LicenseManager:
    def __init__(self, license_file="license.json"):
        self.license_file = Path(license_file)
        self.license_data = None
        self.load_license()
        
    def load_license(self):
        """Load license from file"""
        try:
            if self.license_file.exists():
                with open(self.license_file, 'r') as f:
                    self.license_data = json.load(f)
            else:
                self.license_data = None
        except Exception:
            self.license_data = None
            
    def is_valid(self):
        """Check if license is valid"""
        if not self.license_data:
            return False
            
        try:
            expiry_str = self.license_data.get('expiry_date')
            if not expiry_str:
                return False
                
            expiry_date = datetime.datetime.strptime(expiry_str, '%Y-%m-%d')
            return datetime.datetime.now() < expiry_date
        except Exception:
            return False
            
    def get_feature(self, feature_name):
        """Check if a feature is enabled"""
        if not self.license_data:
            return False
            
        features = self.license_data.get('features', [])
        return feature_name in features
        
    def get_max_terminals(self):
        """Get maximum number of terminals allowed"""
        if not self.license_data:
            return 1
            
        return self.license_data.get('max_terminals', 1)
