#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "pos_screen.py"
PROTECTION_DATE = "2025-06-03T03:26:12.872907"
ENCRYPTED_DATA = """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*************************************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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
