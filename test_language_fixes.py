#!/usr/bin/env python3
"""
Test the language switching fixes and UI improvements
"""

import sys
import os
from pathlib import Path

def test_interface_stability():
    """Test that interface doesn't disappear when changing language"""
    
    print("Testing Interface Stability")
    print("=" * 29)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_stable = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for robust refresh method
            if "try:" in content and "refresh_interface" in content and "except Exception as e:" in content:
                print(f"   ✅ Error handling in refresh method")
            else:
                print(f"   ❌ Error handling missing in refresh method")
                all_stable = False
            
            # Check for state preservation
            if "current_username = self.username_var.get()" in content and "current_password = self.password_var.get()" in content:
                print(f"   ✅ State preservation implemented")
            else:
                print(f"   ❌ State preservation missing")
                all_stable = False
            
            # Check for state restoration
            if "self.username_var.set(current_username)" in content and "self.password_var.set(current_password)" in content:
                print(f"   ✅ State restoration implemented")
            else:
                print(f"   ❌ State restoration missing")
                all_stable = False
            
            # Check for fallback mechanism
            if "if not hasattr(self, 'frame') or not self.frame:" in content:
                print(f"   ✅ Fallback mechanism exists")
            else:
                print(f"   ❌ Fallback mechanism missing")
                all_stable = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_stable = False
    
    return all_stable

def test_responsive_font_size():
    """Test that French title uses smaller font to fit"""
    
    print("\nTesting Responsive Font Size")
    print("=" * 30)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_responsive = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for responsive font size logic
            if "font_size = 24 if self.app.current_language == 'french' else 28" in content:
                print(f"   ✅ Responsive font size for French")
            else:
                print(f"   ❌ Responsive font size missing")
                all_responsive = False
            
            # Check for font size usage
            if "font=('Segoe UI', font_size, 'bold')" in content:
                print(f"   ✅ Font size variable used correctly")
            else:
                print(f"   ❌ Font size variable not used")
                all_responsive = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_responsive = False
    
    return all_responsive

def test_black_dropdown_text():
    """Test that language dropdown has black text for readability"""
    
    print("\nTesting Black Dropdown Text")
    print("=" * 29)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_black_text = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for light background
            if "fieldbackground='#e0e0e0'" in content:
                print(f"   ✅ Light gray background for dropdown")
            else:
                print(f"   ❌ Light background missing")
                all_black_text = False
            
            # Check for black text
            if "foreground='black'" in content and "Orange.TCombobox" in content:
                print(f"   ✅ Black text for dropdown")
            else:
                print(f"   ❌ Black text not configured")
                all_black_text = False
            
            # Check for border styling
            if "borderwidth=1" in content and "relief='solid'" in content:
                print(f"   ✅ Proper border styling")
            else:
                print(f"   ❌ Border styling missing")
                all_black_text = False
            
            # Check for dropdown list styling
            if "style.map('Orange.TCombobox'" in content:
                print(f"   ✅ Dropdown list styling configured")
            else:
                print(f"   ❌ Dropdown list styling missing")
                all_black_text = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_black_text = False
    
    return all_black_text

def test_error_handling():
    """Test that language change has proper error handling"""
    
    print("\nTesting Error Handling")
    print("=" * 24)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_error_handling = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for try-catch in change_language
            if "try:" in content and "self.refresh_interface()" in content and "except Exception as e:" in content:
                print(f"   ✅ Error handling in language change")
            else:
                print(f"   ❌ Error handling missing in language change")
                all_error_handling = False
            
            # Check for fallback mechanism
            if "if hasattr(self, 'language_combo'):" in content and "self.language_combo.set(selected)" in content:
                print(f"   ✅ Fallback mechanism for language combo")
            else:
                print(f"   ❌ Fallback mechanism missing")
                all_error_handling = False
            
            # Check for hasattr checks
            if "if hasattr(self, 'username_var')" in content and "if hasattr(self, 'password_var')" in content:
                print(f"   ✅ Attribute existence checks")
            else:
                print(f"   ❌ Attribute existence checks missing")
                all_error_handling = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_error_handling = False
    
    return all_error_handling

def test_translation_preservation():
    """Test that translations are preserved during refresh"""
    
    print("\nTesting Translation Preservation")
    print("=" * 33)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_preserved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for language variable preservation
            if "current_language = self.language_var.get()" in content:
                print(f"   ✅ Language variable preserved")
            else:
                print(f"   ❌ Language variable not preserved")
                all_preserved = False
            
            # Check for language variable restoration
            if "self.language_var.set(current_language)" in content:
                print(f"   ✅ Language variable restored")
            else:
                print(f"   ❌ Language variable not restored")
                all_preserved = False
            
            # Check for property text update
            if "self.property_label.config(text=self.get_property_text())" in content:
                print(f"   ✅ Property text updates immediately")
            else:
                print(f"   ❌ Property text doesn't update")
                all_preserved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_preserved = False
    
    return all_preserved

def test_all_versions_updated():
    """Test that all versions have been updated"""
    
    print("\nTesting All Versions Updated")
    print("=" * 30)
    
    versions = [
        ("Main", "login_screen.py"),
        ("Protected", "YES/login_screen.py"),
        ("Obfuscated", "YES_OBFUSCATED/login_screen.py")
    ]
    
    all_updated = True
    
    for version_name, file_path in versions:
        if Path(file_path).exists():
            print(f"✅ Found: {version_name} - {file_path}")
        else:
            print(f"❌ Missing: {version_name} - {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all language switching fix tests"""
    
    print("🔧 LANGUAGE SWITCHING FIXES TEST SUITE")
    print("=" * 40)
    
    tests = [
        ("Interface Stability", test_interface_stability),
        ("Responsive Font Size", test_responsive_font_size),
        ("Black Dropdown Text", test_black_dropdown_text),
        ("Error Handling", test_error_handling),
        ("Translation Preservation", test_translation_preservation),
        ("All Versions Updated", test_all_versions_updated)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Interface remains stable during language changes")
        print("✅ French title uses smaller font to fit properly")
        print("✅ Language dropdown has black text for readability")
        print("✅ Robust error handling prevents crashes")
        print("✅ Translation state preserved during refresh")
        print("✅ All versions (main, protected, obfuscated) updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Language switching fixes may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All language switching issues fixed!")
        print("🔄 Interface no longer disappears when changing language")
        print("📏 French title sized appropriately to fit area")
        print("🖤 Language dropdown text is black for better readability")
        print("🛡️ Robust error handling prevents interface crashes")
        print("💾 User state preserved during language changes")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Some language switching issues need attention")
    
    exit(0 if success else 1)
