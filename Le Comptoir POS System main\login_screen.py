# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['fwYeb2YFXXocZyJtCHc2BAA1ZgcWPgwPESkQKTp7EGcsAWcjMj8lID82Ki8yFRQzXnQBdTNgIScDMW89NBsQOTYgbhkoGSUJe399', 'KjEKCHwWPDwlHSc1LjY3BxgXHxk3dQIQGQglM3ZuMmJYV1EKMQcdZREuB31oAgc2OCswPCh6DTckNTApOn0ODRUaMg4IATs4DBRv', 'cToHGyNzIy5qaHgbKQF7LhopDx53ciMzNSMNY2onHCp3Ih0UahdmHTY9BRh/KyJwHRdpKF9ReQRtP38BZBYQIAlyYisOZ3ApYD0p', 'HD0MfWwCAQoJbDgOPwZXX0sDHTg4PCcbPXgVO2JmJxV0DgAHeQUWLh8YOgUjPnswKGdxGQRvfj8eb0hZYFkbDT0iBzguMBAgej5w', 'DDc+YSV9BhIpfzg9NDIMNRUKc1VUABZjfhdkC2khKwhiBzIGJWMOCSduC38rI2c2OgNlJjo5YhwJLRMgEiZAe2MDHmw1BCorbyFz', 'KRIRIBELaFRbaDolIDUnbwl3EQdgdA5ybR0rHjYtMSMxdHtiHAwXLgYtPTQ9EjcvbFxRcHwPYRZjFHI2Y3ETejggYyxoNhcDGzQJ', 'emQJFBYgOAs0DWoqMwdofltwDyQ3Fg8OGSE2PAhzDTcAIR0EchQFEwMHZRUEMS42bAQjGhMbKWFqQWJcQxZlCBsqN20FPDkgAHcr', 'KWQFHXI+JwAaFW45PA0OBz8yBHUZYGc4Kw4DfBVtJxcnAyctMCs9fT4jCSMcdQlQWzIFGxolMysdMBUwCGp0EzItJiUICi4magUh', 'OnwgExogLwUWYwhsGQUaPBJ9bDc2djk9OhYTJzdofRFrOHYHdWQVFzsSay43FyMXLx0jeAlvPQcQDnEbcWFwHBFkEGotbA8fZTgG', 'YBA3JR94fhcIfC4lZnZEYiojHDEjMCprMgVmCwBncg8eBxFqOyAdOwxoamQCPQ0EJCUDZyYWci4dUmt1GDIPbAcZaQd8Ch8WKg4H', 'Eyl5FiAPGwknKShmBRhhdgIVbDwBZCtsIh0qC0RGeXhwAiguKBk1Zic7ExAjeSMdFWh5Izk9cgUFERE1E24VAj0gFjgMKT8mBgJ3', 'f3ZhNXxxOzhoR0UFFxM5MSFqOisQcHo6DTs3PzMKMjMBGyplHXsoFX9uMzooAiZqJTYpMQVYBGJnG3RsCioNNA4qA2ErYzQvagZ/', 'V0UaBS4SYy4dNQ57ejAMdXh1MT16cgkHDCJnZyM2bBFyGgYDfRUvNC4pGVxxDTs2dWEWEhgeNTEwIH0Fbh1sGyoZbH4MKixpFTot', 'I3kVJw8gAhYPQnxDAms9KDA0NmZmETR6JRE1JzQ6JX8XEhtxADgafQk1LDIZKHp5Lh9uMCZKVAdeBRk2Hx4oZ2EwLgQmHDw7NWop', 'HQAyBBEhaHV0QH8QAHg5AgwrIT1sHDstKRE2bzoddXd/KzkpOx0hNTAyOBkuPhoSPzctHVpgUxA8KGUhCWcHETk3GS0NMAAWMSkT', 'IXwhbWUJPjUVNzIMNSd6CGJtKh8mAzNoGy4kBG4iDTMIVFgdQisYfTIgMmwAKXo6OA50E3E9CHoRJwUPBRM2AhYsPSkTPXgJCCkR', 'dXchfQdnOzZ5YzMPbGI3ICYFLRoGYAgdfC42H3s7NyYLKAcwMRQCDAIvNjpzNBFqMzQmPidtdjMjJHocMQlqby1KZFsHdAB0LmME', 'L2x7BBpmFiIMbAQpbRk6dm0uEmEFBkhgRGs/GmAweC83IgcRNy4qBXUAGntwDBs3J257AiQGM3IiGCoWMmAUaWcEUlAHLxItDCgo', 'aGglCgp5MWd0ZWV/OjIHIgh9Ng45D28SZgR7RxBtNWQjJwwFdnsWZD8vcz0dHB90Fxk8IAYHYHk2Dg5mMwwgaSAgARd3c0dOZxgd', 'dB8VLwVnCAZqLGpnIxEAKHE+ECF5ITUWFj8OPSQTL3wbOBl7HWFuPjw5PQMYawYPIAZ8fRAWP3Q7Igs4FzgYZAInbSEtOgR7PT05', 'MmwobiMNIGETBiB2fCcxbGknBDMkaA58Dn0tCRA6bgtXCwEXHT5gEy8tOnIFYTQ8OzloOGg6ansBfGoLPzYYH3UuJH8GeQ4YN2kK', 'YjQYZyh7CnAqMxIVKgYsIGwwGQkFB3EwaUp6Vl0lHD86IiIFJyB2HAcPHikKKjUwEjF+DRk7KGEWOQIKMXlkKBEnISsQV2oHbDIi', 'CgR2cDoIHR5/YggMBxIycQc8KWQvLS1KaV0fMAB7LWAwOzk0Gw0+LAEvExcxKxU1DDE8b2BjJAYoFiQFOiYHJH0OBQNnBHUPHXkB', 'LRsBPBtidAQbKy0iBxUWOTABJR4/Lyd0AiAOEisxGz0fPAM0BgAqNiMKfic8EiMQPgNbfgwtIS4dJDY1YCQuHH0AejkSMzo9cGgf', 'KhVFdFRXMWJ9ewAVHgtyBhojPyI0NBQLfCdyKAwRKwcUfns9Dz0sIGFwMXRgGANjSHs5fnU8PHkIMhE1OAE2HBczHS06NwB8dSc8', 'ZTB9OgQrIBR8LTc8CSIXFzc/FzM5ZTsOfGx5ARYSYWhTdi07HjYLAioHanBlGBUoLGpgFid5CzMNeT4ZBXg3KTVlfSkAaCQDEjZY', 'Omo5E2MCG24/NC8YMTcNAGArZWJ2OnMRbHwhdxQtJA04endncGoXODkCIhRhJilgOggKCS4pKGMlCQ4JeTAoNSEMBCEGGnw/Jxsq', 'CAEuBysAHnx2KTV+bgAxUAVFcj0HNWYKOSoYMxsPFTM7JTwfEAMnCyV3F3BkOnctd2ghJjgEcA92MwgBRmUGPjQ1Pj4ybgoiOWcQ', 'AiUnaSIJA307bSgpAQ94Mmo0Z0RUWAkvLmAkNWwnKQtmOwt0dg8YB3EXenkTBTUiKGQzAgY5eSYdaR0KbDIdY0dbbjh+HhU5FngS', 'FiZiJhMmIzx7AhY/fCQAEgYxcXsRKjMlEjUqMDwAAXA8HRg7FHI7A0BqWHQlejxnBwcEDisdNzQ4IDgXZhg4By4HIGsGYBwbBiUm', 'axsNJwo7Iyl9PX88ZCwfcwEoFn1qeSY6a0sIY0I4MHoAPBAneCcVehgKIwINPzEeKw59dxttBQA/PgMMOxgYFCYlKWktQUFqZzos', 'NDdgLBEGIQV6Bz0gGSgpNyIPNi0BfRo7PGQydBA/MxcXcDJ0HBMLSRlFCjQdYjx4OGUIFgIRHDZ4HSs+K3YHZA1iOxYhJBp2Czk/', 'azRnDDsVLCccZwI2eBgKMiAuLQwmcg8VG1VDCno0DTh/Hil0NSYNJRcVf3UcLxImcywCFXk6YXl+Egc2MSgiOBsRHxYndQNGXRIN', 'fD0nMxFnARESIwkGBR87PSAkNH4yfTUCZRx/fSoxBxwfFi8GF2heG1dBPgV8En0OE3wuEzEhH3UsL2pnInEUcmojLGQ8FwMNd2J8', 'WmNqQwdjIWZkFmk9MxIjMDUFFmgoKS80MxocOxZ/Az4tICpnKykYaxl9OxB3CQQAbiM+OjY5ORwiAmU1NhUXHj8FMCo1MiBnCSJl', 'MRcwAR8zZX0jdAUNHG0SJQ1gaiBtBBwdHy97Yy92OzQkamAUBEcFfjk8OSFldmY3LXRlIyIjCXUyOHs5dHwdYRUaKx1lLwEkcAs7', 'AhwWZyM2MhJlcRsbbWAVEDREdFNSBwYeEx4UPjx9BhwWISRzPnI8CzgOYCoGPmUEe2AHemwNKwgOKCQXbWVGC2xwEQ8wOXATCjxx', 'MxFkNzcCAz0dODE8Lj4TchggG2xiAithLS1sO3s6MwdzKi9bH3MDMAZjFmcoKyQULwckBB05PREVEBh7MS4APjsIKBtqDycqNzcd', 'LiMAdDN3FzVcaHhNEwIBGR9zETYpNgMZNnlwFxMbcBYCcn0+aB04fGQreh0TJi8rLC8bZ0pxfVgPEgAgCyBrJnwNMwduenJxPjE4', 'LjNHSRlhEH4NDigYZiUEGzw7Ihh5ExNnAjAlfn0ZHgpkfxsrJDAuPDQxEi5pCHpId14UGgcjMzIlOCwheiIfBjYMdhcuKw0tCz0e', 'IhwXDHxXX2ZvbAAAAAcpMTwLITs/fgRxNy9jMRk+AjMHZxghMi9oex0ZIStyBjQpGXZkXSsnGycwDWlnISEvY3QgDTdgKHATERs8', 'Lys0I3cVP3QRcXIjCAFsJxY+bXU2LToaBDYpEzoPQnpGYwU0eTo0JzkCBAliJiQmESgtZg4jKX0WNRwHOzkWEg5tIygXPiQnaDZH', 'PSkTJSI3a3Z4WHBsfhRgI3gMfBByOR0hHScgHDEPdmwlAGdvYR0lOyQlGwItN2gkFBQefEdaTRw3GD8aFmc7FHECZw8pdBsTESMk', 'ZyNuHRlzJTc5dykTHi50OGo5IiMKCTEbAAdyNwQROxs+AiAuHkBRVGJ0ZS05YDctIjc1GWcqNTcWCTILETJ9CgopNn0YGSgoez09', 'PA4uIGIDBjQLJQoTDwRcV2xqAwJ7FCsUGTAZOyoBLzUXKmYnFHICIHkMZ2YnLDAIJQYeJGkkcm8PeF4LUShsf3tiNGsqA3dlACga', 'O3Y3Jxk5KXAcBhtoeQdLADBtO2M9LC9iN3otCnx+GSIgCRgRCHg9GjQKYXwOJw0VOzk/ZycCHAx4RkFXajQCACQYOmp8CgVgNXUJ', 'FDRrCQsuFT45DQMzHTU0agBheAQnMwEZYHoXKjE9EwVSX3wPGT9ifQ47ER8yEiUCOyQ1CCg8LSoDARBuHDE1GHUTN2Y4IBAJE24x', 'MHwMYWwXFSxhfXo9DD4BZhIRbmpVWUtAJzw+LhoqOTUgBS8lKnpwLDxtBA4pJzIbbRp9Jxt0CSEgG3k3JTArcHtJY15qOy1hHSBm', 'GwZxAX5hcB5zDHIeKSIpBG4mDgo+ZDV9AmcGATQoCjUoLX9+U3YlBzY9Pw06JxMQMWp0eyB1PmgxOy4FC2spIhAJbBMzIAV3Bxcx', 'NCw5JTUdPBkyOnkiHydjNBkdcmw5BSsePh91GBFgXlhxLGQ6OQV0LRspGWckICIWLw5vZwJ0KHxmPmchOT4ADQwTCSA7fQoPOFFY', 'EHAcIitnZjYpBC0ZZj8de3APIQtsY1ZicRpgfz0UJzofMwszK3wAAgoQZ30VcCwDZR4xZ3YsJzFsE34pEC0fCDxLdAtdJWw7HDQ2', 'MgYZN3YncycWdGIGQ0IFET97YXZ0PjcTJms1FQhsMyYPJTAocGQ3Nj0VOA8tEGYNAhpgfSkvfx9ITigvA3slFRY6HAYzJRx1DT4f', 'Ow4PYD8vKi5vNyoxAiYsK30oaBoBBSsuIxF3DBcCKSEJWV9weR16eTY7OGk7ahMiOAoWCRwQNRgWdHkoNzoAZx86cwIiLD0ANTMR', 'ehYfBRNsAj0tNwo/OjUJCSUCIwUfKCMyBgAkPj96YwMVOSgZdjIIQVwZVW5sJwM2dRc0JAw4K3clLC00aX94FD8qIGwIKwR/ACYO', 'B3cUcDEINmR9AW19YC0eHCZpOVNoWg1wEwo+BRctMhU7GmYkLzsrK2p5Cwx+MhU5Njc5MRIvDChgHBEzLWFmVGl+UAViKTw0AD5h', 'BgJaBnwZBC0BNzcYGXIUOScVdTNxPmggcxAtcyQGGysHBjNxGRF3DyYvATo9BUBDQCUsY205CTgBJidtGHAUdhkJOw4JDgAmNDwA', 'LDwEahUKdmRtADUGMBUHDjl8DRw4fW0YRWQEbSkkKGILFxhjcRoSKy4DJBkOJiZ3NQUdYW4dAn0cP3skcBg7KxMfYDlUfkZ1PjQN', 'FHMcZTF1BhhwFAweNyY8NDo8BhYoHmo4Ah0KM3A1Bg9/dgALWUd9Gwc/CyMQcygZMzcAAH0ZMCsXNC0TKiE1NTUIIAw1MxplOBYk', 'PT4/cjQQBlliUhdhDjILFGoBARcvaxVjDWIecBsZCgQzGgdgFisyDwcSfDk8aXItLy9eCHAAPTAhPgE1CgYKdRYFPH81EitnC3kP', 'bBs0HgcqAWILCHQAHSNscS0mHi42OmQXCT0hezl8IXcWCCxvamZFBwETYCIAECwQEg8mBwpxJDsgMxUZLyoSBxEyGBR3BSA0ZhAN', 'cX8nGHQWGgI8GzcyJSR9fDA4AWkdAgh6ajxpZgo+DAg3ZzMXADxyEgFwdR9jTigjKBFhDmcDcHINPzB5Aiksawc7EQ8iOWceBg0D', 'KjUWDWA/fn8MJQpuO3xCdF8eZh5nJCgZYi83MWcsOjYjKSoqEQ0CfCo1KmEFATUJNxB2Hg4EJm4HcFV0RggyKj4ddAcBbiAZNgIc', 'Dzp8ER15DXslDRA5AzgTPzAxbj4DA2YoDDZmOwAGDSENC2VyQFkGIX4iAQk9AiB6FBhudHU+EWYLcnpkLgMlBjcdAigVbAoIATgo', 'MC0XcRE2Dy8wDxo8BTBuO2U6AzozE3QLPHZVSF0KDBwHAAsQa3AROysMGxE8KQc4dwZ+LQY9CSB7Yi43PREiJWktcGozcVNZG2kB', 'bBktPykASlRhdGAAJRYiOScmNjwBd3RuCz8PADQkc3E3CxwRBTASbHsuADwqJnQuOQVpBF8QBwAhHnQ6OXNzB2UkJQw0NzQiMBsb', 'VEdtZQshajUIZQF0YCc2CRtiNy0jcioxfDUxASQ/HRUNHi8gHA9zdB4OWGVhUSV6HxYFdRNrdnIzFiMvM24oJjIbJgBwBSU1PH46', 'DmoZBW45E2gBeRYjNgdwDH0MYBQPNEcEQ2ZrETwhACpmFXARJAp2CxQyNBdjIGhgbhAzaBwqLS9obDkoFm0gMm5wQQFUdykfFmVj', 'eTIQAhBmdHEhOz8rdBJoNWN3FCU1AgseOAAFPDkEGHsBCwoLABoDY0YFMydjIWR2FAYwbBskEXg1LjRrASQ0Mz0mZz47eCNqBmJm', 'A1xtNjN1Ix4Zajd8bAV5AGNxaj9mD2o2ch8/ZxkTfDUmbGN6AmEbHw88OFYIXEAOeiIQHRAUIAwiHBojZ2oNKTEeKzoDCSo5ZxMN', 'PCIbewNrawAqAwwyCz1wOAw8LSwcB34fWX0JI30yYSwrJgk5F2IkZ3MxIHB/dxMaKBkSPgorHA8XEns7CQkRLQM7UFF2QQYdO2J9', 'MjAqGn81PAU6MRATSnFEXS5kdQM0DicwMxQAJyAmaj4+MQpzMQ0hJisefQMsMHsWKwNhGQZ9Aw8GB35WKCZjFT0kO2QTMAI3dh0O', 'cRchGBA0Fws8cicjMDIpbmIcajApESUnYXACOTgQaiEdcTU+Bn9xFxIHcntXDQUjPjQQE2UHbGEQDWcsPyo0ewYzMRUlK2I+H2M3', 'YApKAUFwMA0KbDR4MgoXDxA9MhguNnY8GXETfS8+HSg/J20GDmYPFh8mIjxyEmVoWXkPGjkzPBU+NHYsGjEJKjc3FTMtES8bADBu', 'KzUmExY8BTkBdipRVQZeZhc5BmE4ZwVxOh8baj02FW0rDxgtLyoUKxkQfwIpDBkqOTlrHTAROmN9dgEUIh5iOy8OfHQlDGYXZxkX', 'FTMYAFhUYhI3AQ0YNCYKFmgPAi4VBzA8FR8XMRgJeT0EYHlmBBccBhkaJz83GAd6X2FaLmEqbBYyZjIvEjJveA==', 'A3c/IxEgIBd3ZiAvCTtwVkJMCDZ9HjEDajscIGU0MQIzDQsLLggOZA0HEx0cKxlzbBwufAERciJya0BIBB8Veh82ZyoqPyx0NGYJ', 'AnhwbSEAF2YVOWQIDAUxM3QxNSFuOScBLwAFFAorJ38rdWAxIwMnDHEsZnhzf0IpNjgQF3goJhRxAx19BCsTFhInB2wyMxwtIAoJ', 'Y3MvPWYycA4dfAV0En0ELnVzNzMNLXQvfBcWWXpBWBEUOmA8d3QBcwYjYhcmBTZsbQV4FXsyBTY6JyM3FglkBX4iNgV1FnAERnBw', 'cGwRBnBsBQApAjRqHCoLaHwGGCkkeSo4CgFjeBcXFCYCFBB9BHN8PSADGwZ2EHwhB2Y4Myd5Cxo8HWo1JygAKTcoejMmAjJ5Njxr', 'Fz4YMRk1P3VhBAM2HXwUCSxYWkZhBiYKMwISGAonCAIQNw0YdW0+OCZxejAWDggkPjYkdjAkBmUXcyx2JVNjWRtmBipgIXgbG2oM', 'fSwgDSwFNgR0AhUaDHhman4FDHskHjNmChUaZmI3fAgWDD45AycxFTtqMxhgHDN2DiAiOxshMBsXeHcHdmZtIm0GNykdNzYwJnMA', 'awAwCTJ1MBk8Nj0mfDdhBh1zcx8bPwU8CysRGShyIxsHBmoSah0CNhY9NGQKFRUeAG4IOTMKfSYAchM5d2QZeiYEAWw4NWwnEQwb', 'PjgHGWQpETAxYT0NHSQ9MyNlaH0JbwkLAVMfL2IpIQJ0NWYPdBIZP39wDStvLnB1eCMELRcmLW0cBWc/GWE+KG5rEEF0GWQTIz0C', 'eABDHRBnfxAtKCVuESEjBHsAKGsVewkJLXVmLilqLB0kdQB7Ki0dPCMACgBWRkI8ZDotaiYLFgALP2cvewwZaBsweAEKNwYVOCEf', 'HnkPZjocdGw+Ln0FOSxrPgoUD3Y9HGkUImIEGS0EZCZqOA46Emh9V0YXEn4eGnkYMCxzFj4iezkALhM4eDssan0oJDNkBH0zJgQL', 'KQsgLTkfLTk6NiBtaSUqYic7ECM5OioTATEXcAZ7AAtlNiEQAxkBBDkvCDR0Dwp2bmMuAQwoOhJjHSxsEyYwHh8WCjohOxR8YXpR', 'OSo0JgEDaCY1FT4FOCcANwsbcA0qDngOYTF7ckRMCAYqLX0RLGsUJQQxC2MGDSMmewx0Ox86KDYwdz8BbBILeSAwCn1uFGhUcFIa', 'InkVdh5gJjNpYAJ1fTt+HWMKCmtjISgNIik0EW5yNhIEAXo2GT48YiB7LzMuegUhdDgkaGhwUUNlFz4CARQSOSMzMgN5FDR2LxY4', 'fVtBXz1mKm04eSUYbmhnAggrInUTEHx4dikAYQc+PiIgNBtiHHgED3g3IwtjRn1wE3p8eygnJj0LLmcncDQPNWhnJnYCDxIQJyId', 'dhkhCzkGKTgBP2cXGwI9MCsCPR54MxsuOghhcnYEGBo9fzk5aAl9LzJ9FDwtDT8wHSkyLHMeJmhiOH8rdRV5I34dfz03OWpaam0N', 'cD51ITQsF2ISeiIKACl2GwALByQnMzEVFT4BK2UJOSU9Nx8PcjwJJXFHXVY5MiISH3Q9AxALegAhCzMQPCd7BhkfdDs8aQsbbScR', 'LiodAAx7CWwbKzw5ZH0ZOn0lLCYjKzt+LW4OYltGUQg+dWEaIDIlcQsfFDN9JBcjBywVOgURKC0xInYNKyZjHAc4bg4gaBdcQmVB', 'LzgdHnkNAyUgERYFJz00AmoEezJsDDg7Fy05MjpnUQdnQWctISwcJQtrFRpmJwYVdRseDmMXJD8CCyd7GwQVFRZkDS0tK3sLLjxQ', 'GWpiDWcBGRYjdhUVZx98ZhBxZWIrFzpyExATQlFEQTAsAicGOBgmABMaZHR1IG0LZiUZcH8NFwU2PnsZCXAuLCYAaw8zNw56ZVcE', 'MWF+IiM7MRg1HyUgaioLWR9FXGkXYzY/EzBnNHczIQAtFABpGj5waHgHBXA6YTcYN3oSCCE8PXIEci8GekdCFw16AWMAEQt2czEa', 'fAQhb3hTXFI2GBUiIiopZXJ7HT8gHzsgMQgbDg4EHWUbaSoLYBwBLQgfKQkqEjQ3VGFUUDY/OTcAFRl4LBUHA3J9KWo8M3EEOn52', 'biozGGptfwZ6BypnEB8oZBBzIWB7JnspLDIfCnNVXVI0fgsaFDYyHS4hYgANLhkxFz5nDjcqEGMzOh4kADYPMiJ8GhhzF28KBFcD', 'EhFUBUpZDg1nBQoVLTADNTA3LTgEbQALenA3chFiMyMaYAUKexYFdwMmAXA2Fn1FalYnOzoaayVoYyYbEAIdJjQYLzEuczE+JjUc', 'NQkxNjdnNxc9dCo7LTByOnxuajs1KgcQfTl7KhUocCR3P21+XgoBGwM0IhA3DTcNOWQiFQ4uNzs6KTEAGysfLBl9AwIhGT0/Hjkp', 'MTkKKy90NG1/BXgCHgEbBAMiCBUDKyYBP2N1IBgFMi07eCl5HSp5ATV0dxp5KRwtJz1oOlACX14XYiBtNAQxIyB3EXktCBY4NxkE', 'ECoIbX94HwkiASwbSgFYbA5kOCF9dwhqMXJnIyYnKz0taQ95NX1zHDspASZ7IXIhCCl5OjIrHzdVd1ZNbAwiISB1KRQ0CxoDbiEZ', 'Bw4ILTYmHWdmCzpyABBmJDgpOB8OLAtpdQYVOAInB3gsNjARemo0HTNxNgcEeAYtFGV0ABs+MiwtPC0WFxI8DyoPWQl7bAoXfQJn', 'NWA7CyI2eTwBOydqACcEXlcMDzANDCUKKjAre2YwMwALKio8L3MXIQw8O2Y3LSYSF2AuFmUmESQRO1dcAHNpAz9nByZoYSd1L2EM', 'CwkBNhA/L20jdhQ8CXEYDlNRGUA1LwY/axQoPQ8xGQQHfxY/P2d7LjYGIhwqGD0/fxR1ORkhfWw9bm5qB0dITR4sImUjEQ0SATAE', 'MxNsfXsaERN2LS58RVljDhdje2Y3NTx9FRwcdSh0bBMPCxgqJRcxMzlmIx18Ly05Nh8nAjNpb0pZB1JmDxxkBDsMJRcbFiASKRgV', 'LRorPxk7PwhqKVx6Q3k7LAkHBgkeai0XZBUUfGoxLCkxBRUzCiZsezgfPilxDGZ6IyotF2wbalsAZBc5OxA5am0LHwEtJCwrKGpp', 'BB0QBR5wOw93GRAaaw49dRMBLywBbDgHNy4FYURXBWMIHjtwayckF34VPyUnCxQtYygxAxEVGGllIGQ8MQN+fXsTP3cAGQtlcAVo', 'KWMuMBV4FGFuCzAlagB3bjgxZxkRKQoxMQonNyBudzoRfj0PeiZqPHReAWxnbCUYYHQsNnAtZgYTJiMNEHApOyVzKQclYGUZBg56', 'MwArKDoUDyB6NTcTeQ0rHFxzUEVtYR8uKCRwF3EkHhl9Li0fCR4CIDohAWEGaH0lDDEvfxs7HRslLWE0c1EAAhohH2J9bgUpEhJt', 'MCJgMDRvNR0xIxsmKTZtHilwOC4cBzFwEysiIDRwBRt5J3AHCwkbekhBYhcdCBkndw0FJDAiCxwKNyNoMwUVJXlxJSkCJRwuKntg', 'AhwLcQg7awdzOQBuOmgzPngQKSQeJykfFjgGM2oBQH5ObzQ6JiBuOHwfcWIlcgsQCytuPy42GgEmJSU0HQRwF2UvFh9wLCRrLUhU', 'C3AdATsPOQ00PTtkQx0ACAc+MGI4KR0UNg1hLBZ1OGgtZycqLjIDEwk2Nh5uBDEtfX4MPi5yalhmSGI5F2MEJCgXYAsJERgcPTdp', 'OC0gai1maGgEYQgkLzwwBQo8dQsNECQnGzEWERh3DgQrYg9/E3YNDgU2LHkiNnhqEhJoUXhANR1/FwECJTQEehoZHXUtNBAecXV7', 'dzotMCcIcC07FDEKaCIRYTg/KD4bBCE7CycpGi4tMwpzaAYFEGd6MDglMBE/KXoodxwFCWtwGxcqCCQjcHsAPgwWaBAffQ0XMyIv', 'YyFgA28lDXYzSwJ0RmgkHjoKeDseNgk/ZG4rEQ01OnENGnJuGxgqFDkOfXIwfgYmNwcyajVBAx18PQAOYT8qDmpuCTokIRQYPS0J', 'Oz8BHCYQcSsQJxImDhxgKTs0OQBqFT4zLAgUBAMINxl+Rm05MyMWAiYYNwloYQV9eQo5AAk/EnsbfWM1aDsaJxYuMiMhKjQGMC85', 'AgAbJAhuAXdoAzRwLg99CBg2NgEGMQ4uOyIYACgkdCEHZwcEBz09Bi4/DRhkNwstKiANdDBuFQwTE3NxMWY5FRcnLRs1JDUsGnl9', 'IjIWC3w6F2ANfyU3Lid7diV/bmpuBwQ8Fgl7EmItJTgxIzgcd1MDTDEmJxU2AA00ACITGCAIbh46ayRyJBhyAxEXPn42PQ5gPGA0', 'YWsEOTInMWswORAfPyYaB3EDKSchFyAlalFfdw8bYxYdFRAhdzo/YTEpJGgcKCUYEn9yeS0zZjwsCTQwMBwjDz4PGGt+agZVJSQA', 'Wj4hfSIEcmclIBdtKgspDy4QMhh4emRyIBRiBhU9NQQeDmQ9JSkoFC9qRUNHEQUFYnkjLCl9cQJ9JyFuKxRvLSBoLB19BxQcKGID', 'ZTU9EAE1JDg/KxRuIQ8JeHc7EWNmHBsuGCEoAAgsKSgPE2ttXRt5TD4FKCQhcG4cKxJ+NiQ9JA8LPiszdzkIKBcFYwQQAiEEKHx9', 'Yz0uZBMVZ3YuOz51fRQybActKjoEdAhqBDUsLANoJDM8A29kMGwTSHJeBAkxPTAlGXQQEQQvJA0CND0YBRwPFhsxP3AUZHsCNXYi', 'DTMCNzQNFwAJayB5FgV0Zj1lNGQYcHczMSUsZxFqIxMAV3YEOWcfBAUmZmIMGwwHJCMlbRUXJzILBSYIJRVhLAxyFjNxI2ETeA5h', 'MD8tAT0VeX8ydnYxf2AeaGQ3cjtLAQBjKRo/GWQzZngWOSAYMDQCdWk8fzUNMSlhLGIFPTsNOjoiJ3ZmHTcqZgRSC38RMwg2YhkL', 'cwQKHWc8em0BGiAvGXwSJBwscEpUGRsNZAYEZRMKagcsMTd3Khsibi0cFQ0SNAgmYCQjI3INYwsaAjcoIRc9SGFfVT4MNmAhEzMb', 'HSlzLzlaXWt5Dz51MSoyDzAtcz1nIDgCLjIlJXYpPjIGJX8mBDchMBZmIjgxIy9rLAN3Bn1nEyYRBSIIZiQWYSoDIA8pYSkhIzoq', 'ZD4wGmYVF2ofJzszZhMvEG8YFBAlGToUHSoqYWQGdHo2LH0YOj89Nx0FfAQNO2YDMDQvETYSLRg3LykWaAsFcDk5BgELaSAwN2YM', 'YX5qTm1mGBllLmhjcTNjeQgnEBgJbyIqBSwCCisePX4mADdtCyw7bgcyLxZ4QFl3cDobBmAUMhksC343cBoyLhg5GxJ2ITQKa2c1', 'E2AsJAcSPCJmMC0ncRY2bH8CLTlDfEBHOB96BDYLKiYrJT8eJC5qGDhrGCQ3En1jdCVgfD4ALz0tOSsMcxQOPgRJWUcmFDYlZBMl', 'ATUpDGohHSIXJAQBdhsHfiwvfScMD2BIBkI5EnohODQTCiAMIwt1CA0ODScNcw0icjQsGyo7DSF1ECF2KBgDAWFnf3JcBBl6ImMB', 'AT90HiIIMidxZXAfdg0uPD07XmV9RiccLhw8dS0/ISx6Yxd9CT8UMxoVLB8rOmw/CwcxMSUsAwEdBQ5zPhR0GwsfKRs0BDQrbSUU', 'DxIPUGNKRS8yAgQzDjljDXMHFCoqJhsqZzAkbGQsBAURE3o1FwB/eno5JQRzNjUHRWN2LS8vMgYjPAsNBX44Cy0UPGoGAzERDxUw', 'KB90SkdnUGoSdGwCAy01IyE3PW4FdS9uOiMXcCYdEG0JGTs2LyslCnwjLw0NNyViYgMMPC88OhgzBgluAgVmfHwJbRp0JQpzPD8a', 'AGFgG3wZMxp+HwgPCChwcR9ncCkxOH8iLxxqIjUAag0bFCI7LgRxeiQLKmgXI3w2CXQQGQYicCUtFjJZRUh+OWIIIDgkCGQ2NGc/', 'cjMZAWRqRx1jLS1nOSUQHAcvPQwuAxU9PioyECpqPyk1AXgZJ3AMCwMgJ3kfFQlhcVoFMB0KYj0YPj0yEmYCIWMRPGE8OQY1BXF5', 'FyB9CywTF2k8VntgQS0wdTYaNgsbFDQ7ES8oIxUXOSo7Ang8GCknHnt7KzADLBU7awgJO2hzfUt9KTgVMDcgMWdxADFlNwVybCM1', 'M3l/GncLZi88JDEaN3IIYgZQQjceJhs1dQk8LyIROx08MTYJB2N0OSAcKionHwIedy4APiQZLDEwLTxDQWMfEGNjJwQbMRwVcxJ5', 'UUdiZWthPWAdNG41fSQzIyo+C2MrZyA0AQMvJiw5H34zbnoQKiZ5dCcwPmpGRUJXHS0/PmQsCCYVFCRmfBlwIisHPA8Tfg0nK2c1', 'E3gEIhtuYyoiOD0mNTI4Kh12KmJiPXYJARc4BX5/WXk2bX4yBxAmEREvYjYpGxBtL2kvJRETcWMdFGQVEwELLmIXPTguNWg7aGpL', 'cQ8nNBkGdx4Ycwd+KAIONzsPZzIkORJ0ZzYkPwgyPABsPTc5LSIOLWZEWXdDFWwPZx4tGjtqJiRkASolCj89egA5eWo6bAUEezAX', 'NztwHHlgDnMvYwsHPRERBzo4AVgCYSYaKxxgMDogIiAyZz95BzcSa3gzOh8rPzY5MRYxKy44Bjx4ZiQdFWhqYwtbFw0mZDY3KxgX', 'HQxwIS8TFgR3eExoASh7HjkYNW4VZzZxOzk0Pi0ENSc8CiUtKic3bA5oAj14OhAsCCg5BEpEWgtgNh0fJg8yCTsWCnc6d2NqazAL', 'ah8cMXVpOBkEaBoOKGs9BHkgBnshPwRlCAUpNj1bRGpCPWYJYCQrDDB9LmQgbjwUHDdqGiYCZAE2bCAFOBZwdRpiYB0beAwdGmtq', 'IBltIQEUKAxpHipxAj4HJC8bAgUQDQcALAN5PDMfD2kDfUFwDhYvYBQIOWdyezgmNTQtIiM1enA2eH0hZwoiexsXMyIAAnYaITUJ', 'OhsUczh0BRIfYTU5CxsWJR82FyckYRkDcwtVPToJbBxuFhVuc2wdE3oWEggmKnE5IBRhGjcAPQ48OzoZCzgSYAloDVR1BQU7bGMb', 'AGkmBCZ7DQdlDTsEECgNLBYHZVhDDB0KZyEvDWUUJjoIFnsRPTUnLnN0eQo2JxEadjgvcBMmNhscDHJqNVFEBl4bEwgSNiJufHVs', 'djULMRQUGxtxLAR5MjpqHWw9MhAzOxo3eSFpLUNxRAAPPAktNREFYiElBTsIfAkzNhZncAkDBhwWKhQFbQQiY3h+fC0bdHYtc2Ba', 'dDwvAhMqZQgdERQWOh56JikrPC5KSkBHazs/DAJwNB99dBk+dAhqOWEUPjcGDSIaDycqIAQsExMkGH8oMX07K2cBSHgeGSJiOREq', 'OgAgaHByHWwJZR0yNW4KIAQ3Oj4tKg8sGxcFEiR4djcoCGA2JCloMggkey0/DBsFXFVgVilhehBrFxQqAhQBMz8ibigUBRJqEH0f', 'bmI9OTQYCikVFy0xIS4uYhoTHQsOOjETajIYZA03dCYDLCkzfjNrLVgDeHgUOB4CaxRseBYEZxc/DwsxdgwQCDUzISJqGD8tLX0P', 'AhoJdxwnXn9CTDkPeT44GSkKMgkjAwg5ES9qLS8WGRJuahUUAH0nDSY+H3YdEXMxbBoZZFR8PTonfzooEB0SexMUICJxaCEKEQxo', 'e3txCn0xODUvKQsDZEYKACJnChc+NAM7JyAzewZrAysrbnYpBygoIDE4BDFwEwd4BiZ/IxAdfxt9WnQcHB9rMxQKE2wPFQskGTce', 'MxwEHHhsKi4JJlFdfAdnYyAtHiQFZDwEMjctCnUKbBF8DwIGIh1vewc1eylxNXoMICklCz0bBkZaRRQEYz0feBUjMQYZZT86FRcB', 'LwAfDGNke3txNz4ZHgI8Gx0oakIFC1AaMzs2MRsXYncaGWJ2NAcfbxYNbhN8Nh9uHGEOGncCOHt8e2wZBmA7RggKRDYCewQTajMC', 'HXAAA34MEikxD2AvdCgxMRgFAUZ7DyIuDQp3MB4gLR8bPXUlDj4cDyV2EnZqExEoGQMLLARiDioFfCMcKgRTdlU1PjZgYCNsO3cw', 'Gj06ACdpPgBeC3BmbBwxJ3Q8EW4sPicUHiQLYRU5NAcoADMaPmd4PHcVMBAsOTkEcAgeS1tEexcHfz4lAj5lIxk0ZykZK24LJSMv', 'dnsYCiQyITF5ZBMWOnBgCxsyPzQOSwcEehlgAToENhU2P3p6BiErFTIBNzo5dDouAAdgPCoWfHRtfgM9bXJ0GgdWfgMHbiYADQJu', 'H2QRLXlqNjEqFRF5F2FvACg4AnMJP3E4FmgFd2EGeUpYRiYEfz4TanQdKyoBZTQ6MhUjNX8FNXIyPzc+ZgFjMyUlDR8YZwNuChcB', 'ahsqPzYGCgEgJAJnABwtbA43LHYuOBZkFyg/ChICNxo7IjhtKTY2cAVqZEMwBC4sYDloNgkHAh0tHgYsDAYaNzFzdQZmADcfAiMC', 'KxNhbgNqFwEhNwUmBgQiEiQhIiwtKwEDIDlnamdwbCMoMBUvEAIoKhwfKXU0Aj4IATUPewobZykkCzwrNmEnHiA2Om5gantVGQdq', 'ATk3JTUCbiFiZAEuMzBsaBkFMAxqIyoUKH0mLy8wGho5GXImOGhCY1FOMxwaY2c2NzYteiIXfCECbHIsAw4TfX0kMjVqOWxzMiYs', 'Ky0dEBIPCCQ/A20cCDIpBx5qND4jZzs+DiczBQ44BTkRawZZXUdbFmIDM2ovGSUCcDgnMx87Dw0LLhQ5JyAxPWQkAR1yGi4uPn0y', 'dEFbDCMqbGF0CmB3Fx8BJxotPCoMGiA0LnEDbTMlKWwWM2EdPSw5IRUDFXxWf3BmPHkjNHc1IzF1Az0udXU4a3QuEnEzC2Y3YxYf', 'a1MFVlAHBzQAfTE7AzEAejoCIwo+AQh7DTd9FQAcCWF5FQA5JRsBemkJM2s8AVNjDC1nNiMUcylmAXIcfQM6LiI+FgIGBSUEZjJg', 'KRggNQQ2NDAjBD0HCjkCISoLBX4VYTh9aQJDNCc4EWNuNRw9J2w/AQIgKSoXP2o6GCAeLCMkPxcvCiAhJRwQMx9vLll2RGc3Zzgd'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
