#!/usr/bin/env python3
"""
POS Remote Management Startup Script
Completely FREE solution for remote POS management
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['flask', 'flask_cors', 'jwt']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements_remote.txt'])
        print("✅ Packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages. Please install manually:")
        print("pip install flask flask-cors PyJWT")
        return False

def start_ngrok():
    """Start ngrok tunnel for remote access"""
    print("\n🌐 FOR WORLDWIDE ACCESS:")
    print("1. Download ngrok from: https://ngrok.com/download")
    print("2. Extract and add to PATH")
    print("3. Run: ngrok http 5000")
    print("4. Use the https URL provided by ngrok")
    print("5. Share the URL with authorized users")
    print("   (They can login with the credentials above)")

def main():
    """Main startup function"""
    print("🏪 POS Remote Management System")
    print("=" * 50)
    print("🆓 100% FREE Remote Management Solution")
    print("🔒 Secure • 📱 Mobile-Friendly • ⚡ Real-time")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('pos_system.db'):
        print("❌ Error: pos_system.db not found!")
        print("Please run this script from your POS system directory.")
        input("Press Enter to exit...")
        return
    
    # Check requirements
    missing = check_requirements()
    if missing:
        print(f"📦 Missing packages: {', '.join(missing)}")
        if input("Install now? (y/n): ").lower() == 'y':
            if not install_requirements():
                return
        else:
            print("Please install required packages first.")
            return
    
    print("\n🚀 Starting Remote Management Server...")
    
    # Start the Flask server
    try:
        # Import and start the server
        from remote_api import app
        
        print("\n✅ Server starting successfully!")
        print("📱 Local Dashboard: http://localhost:5000")
        print("🔑 DEFAULT LOGIN:")
        print("   Username: admin")
        print("   Password: 0000")
        print("   (Change these in Settings after login)")
        print("⚡ Real-time monitoring enabled")
        
        # Start ngrok instructions
        start_ngrok()
        
        print("\n🎯 Features Available:")
        print("  • Real-time sales monitoring")
        print("  • Low stock alerts")
        print("  • Sales analytics")
        print("  • Remote product management")
        print("  • User management")
        print("  • System statistics")
        
        print("\n🔥 Server is running! Press Ctrl+C to stop.")
        
        # Open browser automatically
        try:
            webbrowser.open('http://localhost:5000')
        except:
            pass
        
        # Start the Flask app
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n\n👋 Remote Management Server stopped.")
        print("Thank you for using POS Remote Management!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("Please check your POS system installation.")
        input("Press Enter to exit...")

if __name__ == '__main__':
    main()
