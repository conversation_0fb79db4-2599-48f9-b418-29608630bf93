#!/usr/bin/env python3
"""
Create Daily Use Desktop Shortcut for YES_SECURE
Simple shortcut for client daily use
"""

import os
import sys
import platform

def create_windows_shortcut():
    """Create Windows desktop shortcut"""
    try:
        # Try to create a proper .lnk shortcut
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            current_dir = os.path.abspath(".")
            python_exe = sys.executable
            
            shortcut_path = os.path.join(desktop, "POS System.lnk")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = python_exe
            shortcut.Arguments = "main.py"
            shortcut.WorkingDirectory = current_dir
            shortcut.IconLocation = os.path.join(current_dir, "assets", "logo.ico")
            shortcut.Description = "POS System - Daily Use"
            shortcut.save()
            
            print(f"✅ Desktop shortcut created: {shortcut_path}")
            return True
            
        except ImportError:
            # Fallback to batch file
            return create_batch_shortcut()
            
    except Exception as e:
        print(f"❌ Failed to create shortcut: {e}")
        return False

def create_batch_shortcut():
    """Create batch file shortcut (Windows fallback)"""
    try:
        # Get desktop path
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        if not os.path.exists(desktop):
            # Try alternative desktop location
            desktop = os.path.join(os.environ.get("USERPROFILE", ""), "Desktop")
        
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        batch_content = f'''@echo off
title POS System
cd /d "{current_dir}"
echo Starting POS System...
echo.
"{python_exe}" main.py
if errorlevel 1 (
    echo.
    echo Error occurred. Press any key to close.
    pause >nul
)
'''
        
        batch_path = os.path.join(desktop, "POS System.bat")
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ Desktop shortcut created: {batch_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create batch shortcut: {e}")
        return False

def create_linux_shortcut():
    """Create Linux desktop shortcut"""
    try:
        desktop = os.path.expanduser("~/Desktop")
        if not os.path.exists(desktop):
            desktop = os.path.expanduser("~")
        
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=POS System
Comment=Point of Sale System
Exec={python_exe} {os.path.join(current_dir, "main.py")}
Icon={os.path.join(current_dir, "assets", "logo.png")}
Path={current_dir}
Terminal=false
StartupNotify=true
Categories=Office;Finance;
"""
        
        shortcut_path = os.path.join(desktop, "POS System.desktop")
        with open(shortcut_path, 'w') as f:
            f.write(desktop_content)
        
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create Linux shortcut: {e}")
        return False

def create_macos_shortcut():
    """Create macOS shortcut"""
    try:
        desktop = os.path.expanduser("~/Desktop")
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        script_content = f"""#!/bin/bash
cd "{current_dir}"
"{python_exe}" main.py
"""
        
        shortcut_path = os.path.join(desktop, "POS System.command")
        with open(shortcut_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create macOS shortcut: {e}")
        return False

def create_usage_guide():
    """Create usage guide"""
    guide_content = """# POS SYSTEM - DAILY USE GUIDE

## For Daily Use:
1. Double-click "POS System" shortcut on your desktop
2. Enter license code when prompted: LC-78C8C6898C3A4
3. Use the system normally

## System Features:
- Complete point of sale functionality
- User management with different access levels
- Product and category management
- Sales history and reporting
- Receipt printing and settings
- Storage/inventory management

## Default Login:
- Username: admin
- Password: @H@W@LeComptoir@

## Troubleshooting:
- If shortcut doesn't work, navigate to POS folder and run main.py
- If license expires, contact system administrator
- For technical support, check the system documentation

## Security Level: 5/10 - Basic Protection
- File organization and structure
- License validation system
- Clean deployment without development files

---
For technical support or license renewal, contact your system administrator.
"""
    
    try:
        with open("DAILY_USE_GUIDE.txt", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        print("✅ Usage guide created: DAILY_USE_GUIDE.txt")
        return True
    except Exception as e:
        print(f"❌ Failed to create guide: {e}")
        return False

def main():
    """Main function"""
    print("🔗 CREATING POS SYSTEM DESKTOP SHORTCUT")
    print("=" * 40)
    
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found!")
        print("Please run this script from the POS system directory.")
        input("Press Enter to exit...")
        return False
    
    system = platform.system()
    success = False
    
    print(f"🖥️ Detected system: {system}")
    
    if system == "Windows":
        print("\n🔗 Creating Windows shortcut...")
        success = create_windows_shortcut()
        
    elif system == "Linux":
        print("\n🔗 Creating Linux shortcut...")
        success = create_linux_shortcut()
        
    elif system == "Darwin":  # macOS
        print("\n🔗 Creating macOS shortcut...")
        success = create_macos_shortcut()
        
    else:
        print(f"❌ Unsupported system: {system}")
    
    # Create usage guide
    guide_created = create_usage_guide()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DESKTOP SHORTCUT CREATED SUCCESSFULLY!")
        print("✅ Shortcut available on desktop")
        print("✅ Usage guide created")
        print("\n📋 For daily use:")
        print("1. Double-click 'POS System' on desktop")
        print("2. Enter license code: LC-78C8C6898C3A4")
        print("3. Login with admin/@H@W@LeComptoir@")
        print("\n🔒 Security Level: 5/10 - Basic protection")
    else:
        print("⚠️ SHORTCUT CREATION FAILED")
        print("📋 Manual startup:")
        print("1. Navigate to POS system folder")
        print("2. Run: python main.py")
        
    if guide_created:
        print("📖 See DAILY_USE_GUIDE.txt for detailed instructions")
    
    input("\nPress Enter to continue...")
    return success

if __name__ == "__main__":
    main()
