#!/usr/bin/env python3
"""
Create Compiled Client Copy
This script creates a protected version using Python's compileall module
and removes source files, keeping only bytecode
"""

import os
import shutil
import compileall
import sys
from pathlib import Path

def create_compiled_client():
    """Create compiled client copy with bytecode only"""
    
    # Source and destination paths
    source_dir = Path(".")
    dest_dir = Path("YES_COMPILED")
    
    # Remove existing directory
    if dest_dir.exists():
        shutil.rmtree(dest_dir)
    
    # Create destination directory
    dest_dir.mkdir(exist_ok=True)
    
    print("Creating COMPILED client copy...")
    print("=" * 50)
    
    # Files to compile and protect
    python_files_to_compile = [
        "main.py",
        "pos_app.py", 
        "database.py",
        "login_screen.py",
        "pos_screen.py",
        "number_keyboard.py",
        "user_management.py",
        "product_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "receipt_generator.py",
        "storage_management.py",
        "translations.py",
        "license_client.py"
    ]
    
    # Files to copy as-is (keep as source for functionality)
    files_to_copy = [
        "create_desktop_shortcut.py",
        "pos_system.db",
        "install.py"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "assets"
    ]
    
    # Step 1: Copy all Python files first
    print("Step 1: Copying Python files...")
    for py_file in python_files_to_compile:
        source_file = source_dir / py_file
        dest_file = dest_dir / py_file
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, dest_file)
                print(f"✅ Copied: {py_file}")
            except Exception as e:
                print(f"❌ Failed to copy {py_file}: {e}")
                return False
        else:
            print(f"⚠️ File not found: {py_file}")
    
    # Step 2: Compile all Python files to bytecode
    print("\nStep 2: Compiling to bytecode...")
    try:
        # Compile all .py files in the destination directory
        compileall.compile_dir(
            dest_dir, 
            maxlevels=1, 
            optimize=2,  # Highest optimization level
            quiet=0,
            force=True
        )
        print("✅ All Python files compiled to bytecode")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Step 3: Remove source .py files (keep only .pyc)
    print("\nStep 3: Removing source files (keeping bytecode only)...")
    for py_file in python_files_to_compile:
        source_py = dest_dir / py_file
        if source_py.exists():
            try:
                source_py.unlink()  # Delete the .py file
                print(f"🔒 Removed source: {py_file}")
            except Exception as e:
                print(f"⚠️ Could not remove {py_file}: {e}")
    
    # Step 4: Copy support files
    print("\nStep 4: Copying support files...")
    for file_name in files_to_copy:
        source_file = source_dir / file_name
        dest_file = dest_dir / file_name
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, dest_file)
                print(f"✅ Copied: {file_name}")
            except Exception as e:
                print(f"❌ Failed to copy {file_name}: {e}")
    
    # Step 5: Copy directories
    print("\nStep 5: Copying directories...")
    for dir_name in dirs_to_copy:
        source_dir_path = source_dir / dir_name
        dest_dir_path = dest_dir / dir_name
        
        if source_dir_path.exists():
            try:
                if dest_dir_path.exists():
                    shutil.rmtree(dest_dir_path)
                shutil.copytree(source_dir_path, dest_dir_path)
                print(f"✅ Copied directory: {dir_name}")
            except Exception as e:
                print(f"❌ Failed to copy directory {dir_name}: {e}")
    
    # Step 6: Create launcher that works with __pycache__
    print("\nStep 6: Creating bytecode launcher...")
    create_bytecode_launcher(dest_dir)
    
    # Step 7: Create documentation
    print("\nStep 7: Creating documentation...")
    create_compiled_readme(dest_dir)
    create_compiled_requirements(dest_dir)
    
    # Step 8: Verify bytecode files exist
    print("\nStep 8: Verifying compilation...")
    pycache_dir = dest_dir / "__pycache__"
    if pycache_dir.exists():
        pyc_files = list(pycache_dir.glob("*.pyc"))
        print(f"✅ Found {len(pyc_files)} compiled bytecode files")
        for pyc_file in pyc_files[:5]:  # Show first 5
            print(f"   📁 {pyc_file.name}")
        if len(pyc_files) > 5:
            print(f"   ... and {len(pyc_files) - 5} more")
    else:
        print("⚠️ No __pycache__ directory found")
    
    print("\n" + "=" * 50)
    print("🔒 COMPILED CLIENT COPY CREATED!")
    print("=" * 50)
    print(f"📁 Location: {dest_dir.absolute()}")
    print("\n🛡️ Security Features:")
    print("✅ Source code removed (bytecode only)")
    print("✅ Optimized compilation (-O2)")
    print("✅ Standard Python bytecode format")
    print("✅ Compatible with Python import system")
    print("✅ Much harder to read/modify")
    
    return True

def create_bytecode_launcher(dest_dir):
    """Create launcher that works with bytecode files"""
    
    launcher_content = '''#!/usr/bin/env python3
"""
COMPILED POS SYSTEM LAUNCHER
Launches the POS system from compiled bytecode
PROTECTED SOFTWARE - DO NOT MODIFY
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the compiled POS System"""
    try:
        print("🔒 Loading Compiled POS System...")
        print("=" * 50)
        print("    COMPILED POS SYSTEM STARTING")
        print("=" * 50)
        
        # Get current directory and add to path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # Import and run the main module (will load from __pycache__)
        import main
        main.main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Please ensure all required dependencies are installed.")
        print("Run: python install.py")
    except Exception as e:
        print(f"❌ Error starting Compiled POS System: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\\n🔒 Compiled POS System shutdown complete.")

if __name__ == "__main__":
    main()
'''
    
    launcher_path = dest_dir / "start_compiled_pos.py"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    print(f"✅ Created bytecode launcher: {launcher_path.name}")

def create_compiled_readme(dest_dir):
    """Create README for compiled client"""
    readme_content = '''# POS System - COMPILED Client Version

🔒 **This is a COMPILED protected version of the POS System.**

## 🛡️ Security Features

- ✅ **Source code removed** (bytecode only in __pycache__)
- ✅ **Optimized compilation** (-O2 for performance)
- ✅ **Standard Python bytecode** format
- ✅ **No readable .py files** for core modules
- ✅ **Protected against casual modification**

## 📋 Installation

1. **Ensure Python 3.8+ is installed**
2. **Install dependencies:**
   ```bash
   python install.py
   ```

## 🚀 Running the Application

### Primary Method:
```bash
python start_compiled_pos.py
```

### Create Desktop Shortcut:
```bash
python create_desktop_shortcut.py
```

## 🔍 What's Protected

**Protected (bytecode only):**
- All core POS system modules
- Business logic and algorithms
- Database operations
- User interface code

**Not Protected (source available):**
- Installation script (install.py)
- Desktop shortcut creator
- This README file

## ⚠️ Important Notes

- **Protected software** - core functionality is compiled
- **Requires Python** to run (bytecode is not standalone)
- **Same Python version** recommended for best compatibility
- **Contact administrator** for support

## 🔧 Troubleshooting

If you encounter issues:
1. Ensure Python 3.8+ is installed
2. Run: `python install.py`
3. Check that __pycache__ directory exists
4. Contact your system administrator

---
**🔒 COMPILED PROTECTED SOFTWARE**
'''
    
    readme_path = dest_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✅ Created compiled README: {readme_path.name}")

def create_compiled_requirements(dest_dir):
    """Create requirements.txt for compiled version"""
    requirements_content = '''# COMPILED POS System Requirements

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (for printing and shortcuts)
pywin32>=300; sys_platform == "win32"

# Note: This compiled version uses standard Python bytecode
# and should work with any compatible Python 3.8+ installation
'''
    
    req_path = dest_dir / "requirements.txt"
    with open(req_path, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    print(f"✅ Created compiled requirements: {req_path.name}")

if __name__ == "__main__":
    try:
        success = create_compiled_client()
        if success:
            print("\n🎉 COMPILED CLIENT CREATION COMPLETED!")
            print("Security level: Approximately 6-7/10")
            print("Source code is now protected with bytecode compilation")
        else:
            print("\n❌ COMPILED CLIENT CREATION FAILED!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error creating compiled client: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
