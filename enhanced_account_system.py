#!/usr/bin/env python3
"""
Enhanced LeComptoir Account System with Master Admin and POS Linking
Creates 5000 accounts + master admin account for managing all POS systems
"""

import sqlite3
import hashlib
import random
import string
import datetime
import csv
import json
from pathlib import Path

class EnhancedAccountSystem:
    """Enhanced account system with master admin and POS linking"""
    
    def __init__(self):
        self.accounts = []
        self.used_usernames = set()
        self.used_passwords = set()
    
    def generate_username(self):
        """Generate unique username"""
        while True:
            username = f"pos_{random.randint(1000, 9999)}"
            if username not in self.used_usernames:
                self.used_usernames.add(username)
                return username
    
    def generate_password(self):
        """Generate secure unique password"""
        while True:
            password = (
                ''.join(random.choices(string.ascii_uppercase, k=2)) +
                ''.join(random.choices(string.ascii_lowercase, k=2)) +
                ''.join(random.choices(string.digits, k=2)) +
                ''.join(random.choices('!@#$%', k=2))
            )
            password_list = list(password)
            random.shuffle(password_list)
            password = ''.join(password_list)
            
            if password not in self.used_passwords:
                self.used_passwords.add(password)
                return password
    
    def create_enhanced_database(self):
        """Create enhanced database with all required tables"""
        conn = sqlite3.connect('remote_management_accounts.db')
        conn.row_factory = sqlite3.Row
        
        try:
            c = conn.cursor()
            
            # Remote accounts table (5000 POS accounts)
            c.execute("""
                CREATE TABLE IF NOT EXISTS remote_accounts (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    password_plain TEXT NOT NULL,
                    account_status TEXT DEFAULT 'available',
                    client_name TEXT,
                    client_location TEXT,
                    license_code TEXT,
                    assigned_date TEXT,
                    first_login TEXT,
                    last_login TEXT,
                    created_date TEXT NOT NULL,
                    linked_pos_id TEXT,
                    computer_fingerprint TEXT
                )
            """)
            
            # Master admin accounts table
            c.execute("""
                CREATE TABLE IF NOT EXISTS master_admins (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    email TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_date TEXT NOT NULL,
                    last_login TEXT,
                    permissions TEXT DEFAULT 'all'
                )
            """)
            
            # POS systems table (linked systems)
            c.execute("""
                CREATE TABLE IF NOT EXISTS pos_systems (
                    id INTEGER PRIMARY KEY,
                    pos_id TEXT UNIQUE NOT NULL,
                    account_username TEXT NOT NULL,
                    computer_fingerprint TEXT NOT NULL,
                    client_name TEXT,
                    client_location TEXT,
                    installation_date TEXT NOT NULL,
                    last_activity TEXT,
                    is_active INTEGER DEFAULT 1,
                    current_user TEXT,
                    total_sales INTEGER DEFAULT 0,
                    total_revenue REAL DEFAULT 0.0,
                    FOREIGN KEY (account_username) REFERENCES remote_accounts (username)
                )
            """)
            
            # Client connections table
            c.execute("""
                CREATE TABLE IF NOT EXISTS client_connections (
                    id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    pos_id TEXT,
                    client_ip TEXT,
                    connection_date TEXT,
                    last_activity TEXT,
                    is_active INTEGER DEFAULT 1,
                    session_data TEXT,
                    FOREIGN KEY (username) REFERENCES remote_accounts (username)
                )
            """)
            
            # Activity log table
            c.execute("""
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    pos_id TEXT,
                    action TEXT NOT NULL,
                    details TEXT,
                    timestamp TEXT NOT NULL,
                    ip_address TEXT
                )
            """)
            
            # Create master admin account for Hossam Lotfi
            c.execute("SELECT COUNT(*) FROM master_admins WHERE username = 'Hoss@mLotfi01042000'")
            if c.fetchone()[0] == 0:
                master_password_hash = hashlib.sha256("@123H456W789LeComptoir@".encode()).hexdigest()
                c.execute("""
                    INSERT INTO master_admins 
                    (username, password_hash, full_name, email, created_date, permissions)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, ("Hoss@mLotfi01042000", master_password_hash, "Hossam Lotfi", 
                      "<EMAIL>", datetime.datetime.now().isoformat(), "all"))
                print("✅ Master admin account created: Hoss@mLotfi01042000")
            
            conn.commit()
            return conn
            
        except Exception as e:
            print(f"Error creating database: {e}")
            conn.close()
            return None
    
    def generate_5000_accounts(self):
        """Generate 5000 POS accounts"""
        print("🔧 Generating 5,000 LeComptoir POS accounts...")
        
        conn = self.create_enhanced_database()
        if not conn:
            return False
        
        try:
            c = conn.cursor()
            
            # Check existing accounts
            c.execute("SELECT COUNT(*) FROM remote_accounts")
            existing_count = c.fetchone()[0]
            
            if existing_count > 0:
                print(f"⚠️  Found {existing_count} existing accounts.")
                choice = input("Delete and recreate all accounts? (y/n): ").lower().strip()
                if choice == 'y':
                    c.execute("DELETE FROM remote_accounts")
                    c.execute("DELETE FROM pos_systems")
                    c.execute("DELETE FROM client_connections")
                    c.execute("DELETE FROM activity_log")
                    conn.commit()
                    print("🗑️  Existing accounts deleted.")
                else:
                    print("❌ Operation cancelled.")
                    return False
            
            # Generate accounts in batches
            batch_size = 100
            created_count = 0
            
            for batch in range(0, 5000, batch_size):
                batch_accounts = []
                batch_end = min(batch + batch_size, 5000)
                
                print(f"📝 Creating accounts {batch + 1} to {batch_end}...")
                
                for i in range(batch, batch_end):
                    username = self.generate_username()
                    password = self.generate_password()
                    password_hash = hashlib.sha256(password.encode()).hexdigest()
                    created_date = datetime.datetime.now().isoformat()
                    
                    account = {
                        'username': username,
                        'password': password,
                        'password_hash': password_hash,
                        'created_date': created_date
                    }
                    
                    batch_accounts.append(account)
                    self.accounts.append(account)
                
                # Insert batch
                c.executemany("""
                    INSERT INTO remote_accounts 
                    (username, password_hash, password_plain, created_date)
                    VALUES (?, ?, ?, ?)
                """, [(acc['username'], acc['password_hash'], acc['password'], acc['created_date']) 
                      for acc in batch_accounts])
                
                conn.commit()
                created_count += len(batch_accounts)
                
                progress = (created_count / 5000) * 100
                print(f"✅ Progress: {created_count}/5000 ({progress:.1f}%)")
            
            print(f"🎉 Successfully created {created_count} POS accounts!")
            print(f"🔐 Master admin account: Hoss@mLotfi01042000")
            return True
            
        except Exception as e:
            print(f"❌ Error generating accounts: {e}")
            return False
        finally:
            conn.close()
    
    def export_accounts_csv(self):
        """Export accounts to CSV"""
        print("📄 Exporting accounts to CSV...")
        
        conn = sqlite3.connect('remote_management_accounts.db')
        conn.row_factory = sqlite3.Row
        
        try:
            c = conn.cursor()
            c.execute("""
                SELECT username, password_plain, account_status, created_date 
                FROM remote_accounts 
                ORDER BY username
            """)
            accounts = c.fetchall()
            
            with open("REMOTE_ACCOUNTS_LIST.csv", 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                writer.writerow([
                    'Account Number', 'Username', 'Password', 'Status', 
                    'Created Date', 'Instructions'
                ])
                
                for i, account in enumerate(accounts, 1):
                    writer.writerow([
                        f"#{i:04d}",
                        account['username'],
                        account['password_plain'],
                        account['account_status'],
                        account['created_date'],
                        "Enter these credentials during POS first-time setup"
                    ])
            
            print(f"✅ Exported {len(accounts)} accounts to REMOTE_ACCOUNTS_LIST.csv")
            return True
            
        except Exception as e:
            print(f"❌ Error exporting: {e}")
            return False
        finally:
            conn.close()
    
    def link_pos_system(self, username, password, computer_fingerprint, client_name=""):
        """Link a POS system to an account"""
        conn = sqlite3.connect('remote_management_accounts.db')
        conn.row_factory = sqlite3.Row
        
        try:
            c = conn.cursor()
            
            # Verify account credentials
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            c.execute("""
                SELECT username, account_status FROM remote_accounts 
                WHERE username = ? AND password_hash = ?
            """, (username, password_hash))
            
            account = c.fetchone()
            if not account:
                return False, "Invalid credentials"
            
            if account['account_status'] == 'linked':
                return False, "Account already linked to another POS system"
            
            # Generate unique POS ID
            pos_id = f"POS_{username}_{random.randint(1000, 9999)}"
            
            # Update account status
            c.execute("""
                UPDATE remote_accounts 
                SET account_status = 'linked', 
                    linked_pos_id = ?, 
                    computer_fingerprint = ?,
                    client_name = ?,
                    first_login = ?
                WHERE username = ?
            """, (pos_id, computer_fingerprint, client_name, 
                  datetime.datetime.now().isoformat(), username))
            
            # Create POS system record
            c.execute("""
                INSERT INTO pos_systems 
                (pos_id, account_username, computer_fingerprint, client_name, installation_date)
                VALUES (?, ?, ?, ?, ?)
            """, (pos_id, username, computer_fingerprint, client_name,
                  datetime.datetime.now().isoformat()))
            
            # Log activity
            c.execute("""
                INSERT INTO activity_log 
                (username, pos_id, action, details, timestamp)
                VALUES (?, ?, ?, ?, ?)
            """, (username, pos_id, "POS_LINKED", 
                  f"POS system linked to {client_name}", 
                  datetime.datetime.now().isoformat()))
            
            conn.commit()
            return True, pos_id
            
        except Exception as e:
            return False, f"Error linking POS: {e}"
        finally:
            conn.close()

def main():
    """Main execution"""
    print("🏪 LECOMPTOIR ENHANCED ACCOUNT SYSTEM")
    print("=" * 60)
    print("🎯 Creating 5,000 POS accounts + Master Admin")
    print("🔐 Master Admin: Hoss@mLotfi01042000")
    print("=" * 60)
    
    system = EnhancedAccountSystem()
    
    if system.generate_5000_accounts():
        system.export_accounts_csv()
        
        print("\n🎉 ENHANCED SYSTEM READY!")
        print("=" * 40)
        print("✅ 5,000 POS accounts created")
        print("✅ Master admin account created")
        print("✅ POS linking system ready")
        print("✅ CSV export completed")
        print("\n🔐 MASTER ADMIN ACCESS:")
        print("   Username: Hoss@mLotfi01042000")
        print("   Password: @123H456W789LeComptoir@")
        print("   Permissions: Full system management")
        print("\n🚀 System ready for deployment!")
    else:
        print("❌ System creation failed!")

if __name__ == '__main__':
    main()
