#!/usr/bin/env python3
"""
Test all fixes: database-based product aggregation and UI improvements
"""

import sys
import os
from pathlib import Path

def test_database_product_aggregation():
    """Test that product aggregation uses database directly"""
    
    print("Testing Database Product Aggregation")
    print("=" * 37)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for database import
            if "from database import get_db_connection" in content:
                print(f"   ✅ Imports database connection")
            else:
                print(f"   ❌ Missing database import")
                all_fixed = False
            
            # Check for sale_items table query
            if "FROM sale_items" in content:
                print(f"   ✅ Queries sale_items table")
            else:
                print(f"   ❌ Doesn't query sale_items table")
                all_fixed = False
            
            # Check for proper SQL query
            if "WHERE sale_id IN" in content:
                print(f"   ✅ Uses proper SQL WHERE clause")
            else:
                print(f"   ❌ Missing proper SQL WHERE clause")
                all_fixed = False
            
            # Check for product aggregation
            if "product_name = item['product_name']" in content:
                print(f"   ✅ Extracts product names from database")
            else:
                print(f"   ❌ Doesn't extract product names properly")
                all_fixed = False
            
            # Check for quantity aggregation
            if "total_quantity" in content and "quantity_in_sale" in content:
                print(f"   ✅ Aggregates quantities correctly")
            else:
                print(f"   ❌ Quantity aggregation incorrect")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_add_categories_window():
    """Test that add categories window is bigger"""
    
    print("\nTesting Add Categories Window")
    print("=" * 30)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_bigger = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bigger window size
            if "choice_dialog.geometry(\"500x300\")" in content:
                print(f"   ✅ Window size increased to 500x300")
            else:
                print(f"   ❌ Window size not increased")
                all_bigger = False
            
            # Check for bigger padding
            if "padx=40, pady=40" in content:
                print(f"   ✅ Padding increased to 40px")
            else:
                print(f"   ❌ Padding not increased")
                all_bigger = False
            
            # Check for bigger font
            if "font=('Helvetica', 16, 'bold')" in content:
                print(f"   ✅ Title font increased to 16pt")
            else:
                print(f"   ❌ Title font not increased")
                all_bigger = False
            
            # Check for bigger buttons
            if "font=('Helvetica', 14)" in content and "width=25" in content:
                print(f"   ✅ Buttons made bigger (14pt font, width 25)")
            else:
                print(f"   ❌ Buttons not made bigger")
                all_bigger = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_bigger = False
    
    return all_bigger

def test_slider_width():
    """Test that sliders are wider"""
    
    print("\nTesting Slider Width")
    print("=" * 21)
    
    files_to_check = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_wider = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for wider slider
            if "length=400" in content:
                print(f"   ✅ Slider width increased to 400px")
            else:
                print(f"   ❌ Slider width not increased")
                all_wider = False
            
            # Check for comment
            if "# Slider - WIDER" in content:
                print(f"   ✅ Slider width change documented")
            else:
                print(f"   ❌ Slider width change not documented")
                all_wider = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_wider = False
    
    return all_wider

def test_product_aggregation_logic():
    """Test the product aggregation logic step by step"""
    
    print("\nTesting Product Aggregation Logic")
    print("=" * 34)
    
    print("📋 Expected Logic:")
    print("   1. Get sale IDs from sales_data")
    print("   2. Query sale_items table with those IDs")
    print("   3. For each item, extract: product_name, price, quantity")
    print("   4. Aggregate by product_name:")
    print("      - Sum quantities across all sales")
    print("      - Keep unit price consistent")
    print("      - Calculate total = unit_price × total_quantity")
    print("   5. Display in table format")
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_logical = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for sale ID extraction
            if "sale_ids = [str(sale.get('id', 0)) for sale in sales_data" in content:
                print(f"   ✅ Extracts sale IDs correctly")
            else:
                print(f"   ❌ Sale ID extraction incorrect")
                all_logical = False
            
            # Check for database query structure
            if "SELECT product_name, quantity, price" in content:
                print(f"   ✅ Queries correct columns")
            else:
                print(f"   ❌ Query columns incorrect")
                all_logical = False
            
            # Check for aggregation logic
            if "if product_name in unique_products:" in content:
                print(f"   ✅ Has aggregation logic")
            else:
                print(f"   ❌ Missing aggregation logic")
                all_logical = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_logical = False
    
    return all_logical

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/receipt_generator.py",
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/receipt_settings.py"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all final tests"""
    
    print("🔧 FINAL FIXES TEST SUITE")
    print("=" * 27)
    
    tests = [
        ("Database Product Aggregation", test_database_product_aggregation),
        ("Add Categories Window", test_add_categories_window),
        ("Slider Width", test_slider_width),
        ("Product Aggregation Logic", test_product_aggregation_logic),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 27)
    print("📊 RESULTS")
    print("=" * 27)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Product aggregation uses database directly")
        print("✅ Add categories window is bigger and clearer")
        print("✅ Sliders are wider for better usability")
        print("✅ Product logic is correct and robust")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Final fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All final fixes successfully implemented!")
        print("🗃️ Product aggregation now uses sale_items table")
        print("📊 Shows real products with correct quantities")
        print("🖼️ Add categories window is bigger and clearer")
        print("🎚️ Sliders are wider for better control")
        print("🔒 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Final fixes need attention")
    
    exit(0 if success else 1)
