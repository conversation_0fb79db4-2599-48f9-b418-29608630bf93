"""
On-screen number keyboard for POS System
Provides touch-friendly number input with optional decimal support
"""

import tkinter as tk
from tkinter import ttk
import os
from PIL import Image, ImageTk

class NumberKeyboard:
    """Modern on-screen number keyboard"""

    active_keyboard = None  # Class variable to track active keyboard

    @classmethod
    def close_active_keyboard(cls):
        """Close any currently active keyboard"""
        if cls.active_keyboard and cls.active_keyboard.keyboard_window:
            try:
                cls.active_keyboard.keyboard_window.destroy()
                cls.active_keyboard.is_showing = False
                cls.active_keyboard = None
            except:
                pass

    def __init__(self, parent, target_widget, title="Number Keyboard", on_enter_callback=None, pos_system=None, numbers_only=False):
        self.parent = parent
        self.target_widget = target_widget
        self.keyboard_window = None
        self.title = title
        self.on_enter_callback = on_enter_callback  # Custom callback for Enter button
        self.pos_system = pos_system  # Reference to POS system for translations
        self.is_showing = False  # Prevent multiple instances
        self.numbers_only = numbers_only  # If True, only show numbers (no decimal point)
        self.enter_icon = None  # Will store the enter icon
        self.manually_closed = False  # Track if keyboard was manually closed

    def show(self):
        """Display the number keyboard"""
        if self.is_showing:
            return

        # Close any existing keyboard first
        NumberKeyboard.close_active_keyboard()

        self.is_showing = True
        NumberKeyboard.active_keyboard = self

        # Create modern keyboard window with orange/black theme
        self.keyboard_window = tk.Toplevel(self.parent)
        self.keyboard_window.title(self.title)
        self.keyboard_window.geometry("450x420")  # Bigger size
        self.keyboard_window.resizable(False, False)
        self.keyboard_window.configure(bg='#1a1a1a')

        # Make it modal and always on top
        self.keyboard_window.transient(self.parent)
        self.keyboard_window.grab_set()
        self.keyboard_window.attributes('-topmost', True)

        # Center the keyboard on screen
        self.keyboard_window.update_idletasks()
        x = (self.keyboard_window.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.keyboard_window.winfo_screenheight() // 2) - (420 // 2)
        self.keyboard_window.geometry(f"450x420+{x}+{y}")

        # Load enter icon if available
        self.load_enter_icon()

        # Handle window close
        self.keyboard_window.protocol("WM_DELETE_WINDOW", self.close)

        # Create keyboard layout
        self.create_keyboard()

        # Focus on the target widget
        self.target_widget.focus_set()

    def load_enter_icon(self):
        """Load enter icon from assets folder"""
        try:
            icon_path = os.path.join("assets", "enter.png")
            if os.path.exists(icon_path):
                image = Image.open(icon_path)
                # Resize to fit button
                image = image.resize((30, 20), Image.Resampling.LANCZOS)
                self.enter_icon = ImageTk.PhotoImage(image)
        except Exception as e:
            print(f"Could not load enter icon: {e}")
            self.enter_icon = None

    def create_keyboard(self):
        """Create the keyboard layout with modern orange/black theme"""
        # Orange header
        header_frame = tk.Frame(self.keyboard_window, bg='#ff8c00', height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text=f"🔢 {self.title}",
                               font=('Segoe UI', 14, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Main content frame
        main_frame = tk.Frame(self.keyboard_window, bg='#1a1a1a', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Keyboard grid with dark theme
        keyboard_frame = tk.Frame(main_frame, bg='#1a1a1a')
        keyboard_frame.pack(expand=True)

        # Button style for orange/black theme
        button_style = {
            'font': ('Segoe UI', 16, 'bold'),
            'width': 4,
            'height': 2,
            'relief': 'flat',
            'bd': 0,
            'cursor': 'hand2'
        }

        # Number buttons layout (3x3 grid + 0 row with enter)
        numbers = [
            ['7', '8', '9'],
            ['4', '5', '6'],
            ['1', '2', '3'],
            ['0', 'C', 'ENTER', '.']
        ]

        # Create number buttons
        for row_idx, row in enumerate(numbers):
            for col_idx, num in enumerate(row):
                if num == '.' and self.numbers_only:
                    # Skip decimal point for numbers-only mode
                    continue
                elif num == 'C':
                    # Clear button - red for clear action
                    btn = tk.Button(keyboard_frame, text=num, bg='#cc0000', fg='white',
                                  activebackground='#990000', activeforeground='white',
                                  command=self.clear_input, **button_style)
                elif num == 'ENTER':
                    # Enter button with orange theme
                    if self.enter_icon:
                        btn = tk.Button(keyboard_frame, image=self.enter_icon, bg='#ff8c00', fg='white',
                                      activebackground='#e67e00', activeforeground='white',
                                      command=self.enter_pressed, width=60, height=40)
                    else:
                        btn = tk.Button(keyboard_frame, text='↵', bg='#ff8c00', fg='white',
                                      activebackground='#e67e00', activeforeground='white',
                                      command=self.enter_pressed, **button_style)
                elif num == '.':
                    # Decimal point - gray
                    btn = tk.Button(keyboard_frame, text=num, bg='#606060', fg='white',
                                  activebackground='#808080', activeforeground='white',
                                  command=lambda: self.add_input('.'), **button_style)
                else:
                    # Number button - dark gray with orange active
                    btn = tk.Button(keyboard_frame, text=num, bg='#404040', fg='white',
                                  activebackground='#ff8c00', activeforeground='white',
                                  command=lambda n=num: self.add_input(n), **button_style)

                btn.grid(row=row_idx, column=col_idx, padx=5, pady=5, sticky='nsew')

        # Configure grid weights
        for i in range(4):
            keyboard_frame.grid_rowconfigure(i, weight=1)
        for i in range(4):  # Now 4 columns instead of 3
            keyboard_frame.grid_columnconfigure(i, weight=1)

        # Bottom buttons frame with dark theme
        bottom_frame = tk.Frame(main_frame, bg='#1a1a1a')
        bottom_frame.pack(fill=tk.X, pady=(15, 0))

        # Backspace button with orange theme
        backspace_btn = tk.Button(bottom_frame, text='⌫', bg='#ff8c00', fg='white',
                                font=('Segoe UI', 14, 'bold'), width=10, height=1,
                                relief='flat', bd=0, cursor='hand2',
                                activebackground='#e67e00', activeforeground='white',
                                command=self.backspace)
        backspace_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Close button with red theme
        close_btn = tk.Button(bottom_frame, text='✕ Close', bg='#cc0000', fg='white',
                            font=('Segoe UI', 12, 'bold'), width=12, height=1,
                            relief='flat', bd=0, cursor='hand2',
                            activebackground='#990000', activeforeground='white',
                            command=self.manual_close)
        close_btn.pack(side=tk.RIGHT)

    def add_input(self, value):
        """Add input to target widget"""
        try:
            current_pos = self.target_widget.index(tk.INSERT)
            self.target_widget.insert(current_pos, value)
        except:
            # Fallback for widgets that don't support insert at cursor
            current_value = self.target_widget.get()
            self.target_widget.delete(0, tk.END)
            self.target_widget.insert(0, current_value + value)

    def clear_input(self):
        """Clear all input"""
        self.target_widget.delete(0, tk.END)

    def backspace(self):
        """Remove last character"""
        try:
            current_pos = self.target_widget.index(tk.INSERT)
            if current_pos > 0:
                self.target_widget.delete(current_pos - 1, current_pos)
        except:
            # Fallback for widgets that don't support cursor position
            current_value = self.target_widget.get()
            if current_value:
                self.target_widget.delete(0, tk.END)
                self.target_widget.insert(0, current_value[:-1])

    def enter_pressed(self):
        """Handle enter button press"""
        print(f"Enter pressed! Callback: {self.on_enter_callback}")  # Debug
        if self.on_enter_callback:
            # Call custom callback if provided
            self.on_enter_callback()
            # Always close keyboard after callback
            self.close()
        else:
            # Default behavior - just close keyboard
            self.close()

    def manual_close(self):
        """Handle manual close button click"""
        self.manually_closed = True
        self.close()
        print("Keyboard manually closed")  # Debug

    def close(self):
        """Close the keyboard"""
        if self.keyboard_window:
            try:
                self.keyboard_window.destroy()
            except:
                pass
        self.is_showing = False
        if NumberKeyboard.active_keyboard == self:
            NumberKeyboard.active_keyboard = None
        print("Keyboard closed")  # Debug

def open_onscreen_keyboard():
    """Global function to open basic on-screen keyboard (for compatibility)"""
    # This is a placeholder for compatibility with old code
    # In practice, NumberKeyboard should be used directly
    pass
