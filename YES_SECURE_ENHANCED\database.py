#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "database.py"
PROTECTION_DATE = "2025-06-03T03:26:12.833901"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
