#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "login_screen.py"
PROTECTION_DATE = "2025-06-03T03:26:12.851204"
ENCRYPTED_DATA = """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********************************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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
