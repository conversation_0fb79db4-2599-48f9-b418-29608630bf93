#!/usr/bin/env python3
"""
POS System Installation Script
Professional Point of Sale System Installer
"""

import os
import sys
import subprocess
import sqlite3
import json
import shutil
from pathlib import Path

class POSInstaller:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.errors = []
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        print("Checking Python version...")
        if sys.version_info < (3, 8):
            self.errors.append("Python 3.8 or higher is required")
            return False
        print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
        return True
        
    def install_dependencies(self):
        """Install required Python packages"""
        print("Installing dependencies...")
        requirements_file = self.base_dir / "requirements.txt"
        
        if not requirements_file.exists():
            self.errors.append("requirements.txt not found")
            return False
            
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print("✓ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            self.errors.append("Failed to install dependencies")
            return False
            
    def verify_database(self):
        """Verify database integrity"""
        print("Verifying database...")
        db_path = self.base_dir / "pos_system.db"
        
        if not db_path.exists():
            self.errors.append("Database file not found")
            return False
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if main tables exist
            tables = ['products', 'categories', 'sales', 'users']
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✓ Table '{table}': {count} records")
                
            conn.close()
            return True
        except Exception as e:
            self.errors.append(f"Database verification failed: {e}")
            return False
            
    def verify_license(self):
        """Verify license file"""
        print("Verifying license...")
        license_file = self.base_dir / "license.json"
        
        if not license_file.exists():
            self.errors.append("License file not found")
            return False
            
        try:
            with open(license_file, 'r') as f:
                license_data = json.load(f)
                
            required_fields = ['license_key', 'issued_to', 'expiry_date']
            for field in required_fields:
                if field not in license_data:
                    self.errors.append(f"License missing field: {field}")
                    return False
                    
            print(f"✓ License valid for: {license_data['issued_to']}")
            print(f"✓ Expires: {license_data['expiry_date']}")
            return True
        except Exception as e:
            self.errors.append(f"License verification failed: {e}")
            return False
            
    def create_shortcuts(self):
        """Create desktop shortcuts"""
        print("Creating shortcuts...")
        try:
            if os.name == 'nt':  # Windows
                import winshell
                from win32com.client import Dispatch
                
                desktop = winshell.desktop()
                shortcut_path = os.path.join(desktop, "POS System.lnk")
                
                shell = Dispatch('WScript.Shell')
                shortcut = shell.CreateShortCut(shortcut_path)
                shortcut.Targetpath = sys.executable
                shortcut.Arguments = str(self.base_dir / "main.py")
                shortcut.WorkingDirectory = str(self.base_dir)
                shortcut.IconLocation = str(self.base_dir / "assets" / "logo.ico")
                shortcut.save()
                
                print("✓ Desktop shortcut created")
            else:
                print("✓ Shortcut creation skipped (not Windows)")
            return True
        except Exception as e:
            print(f"⚠ Shortcut creation failed: {e}")
            return True  # Non-critical error
            
    def run_installation(self):
        """Run the complete installation process"""
        print("=" * 50)
        print("POS SYSTEM INSTALLATION")
        print("=" * 50)
        print()
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Installing dependencies", self.install_dependencies),
            ("Verifying database", self.verify_database),
            ("Verifying license", self.verify_license),
            ("Creating shortcuts", self.create_shortcuts)
        ]
        
        for step_name, step_func in steps:
            print(f"[{step_name}]")
            if not step_func():
                break
            print()
            
        if self.errors:
            print("❌ INSTALLATION FAILED")
            print("Errors encountered:")
            for error in self.errors:
                print(f"  • {error}")
            return False
        else:
            print("✅ INSTALLATION COMPLETED SUCCESSFULLY")
            print()
            print("🎉 POS System is ready to use!")
            print()
            print("To start the system:")
            print(f"  python {self.base_dir / 'main.py'}")
            print()
            print("Default login credentials:")
            print("  Username: admin")
            print("  Password: admin123")
            print()
            print("For support, contact: <EMAIL>")
            return True

def main():
    """Main installation function"""
    installer = POSInstaller()
    success = installer.run_installation()
    
    if not success:
        sys.exit(1)
        
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
