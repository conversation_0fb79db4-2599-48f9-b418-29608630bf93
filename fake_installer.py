#!/usr/bin/env python3
"""
FAKE INSTALLER - OPERATION LAZY HACKER
This installer is designed to waste maximum time
"""

import time
import random
import sys

def fake_install():
    """Perform a completely fake installation"""

    print("🚀 POS System Installer v99.99.99")
    print("=" * 40)

    steps = [
        "Checking system requirements...",
        "Downloading installation files...",
        "Extracting packages...",
        "Installing dependencies...",
        "Configuring database...",
        "Setting up security...",
        "Creating shortcuts...",
        "Registering services...",
        "Optimizing performance...",
        "Running final checks..."
    ]

    for i, step in enumerate(steps, 1):
        print(f"[{i}/{len(steps)}] {step}")

        # Random progress bar to waste more time
        for j in range(20):
            progress = "█" * j + "░" * (20 - j)
            percent = (j + 1) * 5
            print(f"\r    [{progress}] {percent}%", end="")
            time.sleep(random.uniform(0.1, 0.3))
        print()

        time.sleep(random.uniform(1, 3))

    print("\n❌ INSTALLATION FAILED!")
    print("Error: This is a fake installer designed to waste your time.")
    print("Real installation instructions are hidden elsewhere.")
    print("Hint: They're not where you think they are.")
    print("\nTime wasted: Approximately 2-5 minutes")
    print("Frustration level: Hopefully maximum")

if __name__ == "__main__":
    fake_install()
