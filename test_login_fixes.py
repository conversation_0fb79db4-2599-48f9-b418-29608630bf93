#!/usr/bin/env python3
"""
Test all the specific fixes requested for the login screen
"""

import sys
import os
from pathlib import Path

def test_white_logo_container():
    """Test that the logo container is white"""
    
    print("Testing White Logo Container")
    print("=" * 30)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_white = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for white logo container
            if "logo_container = tk.Frame(logo_brand_frame, bg='white'" in content:
                print(f"   ✅ Logo container background is white")
            else:
                print(f"   ❌ Logo container background is not white")
                all_white = False
            
            # Check for white logo label
            if "logo_label = tk.Label(logo_container, image=self.app.logo_image, bg='white')" in content:
                print(f"   ✅ Logo label background is white")
            else:
                print(f"   ❌ Logo label background is not white")
                all_white = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_white = False
    
    return all_white

def test_horizontal_scrolling():
    """Test that horizontal scrolling is properly implemented"""
    
    print("\nTesting Horizontal Scrolling")
    print("=" * 31)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_scrolling = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for canvas and scrollbar
            if "canvas = tk.Canvas" in content and "orient=\"horizontal\"" in content:
                print(f"   ✅ Horizontal canvas and scrollbar implemented")
            else:
                print(f"   ❌ Horizontal canvas and scrollbar missing")
                all_scrolling = False
            
            # Check for proper scroll configuration
            if "configure_scroll_region" in content and "scrollregion=canvas.bbox" in content:
                print(f"   ✅ Scroll region configuration implemented")
            else:
                print(f"   ❌ Scroll region configuration missing")
                all_scrolling = False
            
            # Check for mouse wheel support
            if "on_mousewheel" in content and "canvas.xview_scroll" in content:
                print(f"   ✅ Mouse wheel scrolling support")
            else:
                print(f"   ❌ Mouse wheel scrolling support missing")
                all_scrolling = False
            
            # Check for horizontal button layout
            if "user_btn.pack(side=tk.LEFT" in content:
                print(f"   ✅ Buttons use horizontal layout (pack LEFT)")
            else:
                print(f"   ❌ Buttons don't use horizontal layout")
                all_scrolling = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_scrolling = False
    
    return all_scrolling

def test_keyboard_on_user_click():
    """Test that number keyboard shows when clicking user names"""
    
    print("\nTesting Keyboard on User Click")
    print("=" * 32)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_keyboard = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for keyboard trigger method
            if "def select_user_and_show_keyboard" in content:
                print(f"   ✅ User selection triggers keyboard method exists")
            else:
                print(f"   ❌ User selection keyboard trigger method missing")
                all_keyboard = False
            
            # Check for keyboard show call
            if "self.show_password_keyboard()" in content:
                print(f"   ✅ Keyboard show method is called")
            else:
                print(f"   ❌ Keyboard show method is not called")
                all_keyboard = False
            
            # Check for button command using keyboard trigger
            if "select_user_and_show_keyboard(u, b, c)" in content:
                print(f"   ✅ User buttons trigger keyboard on click")
            else:
                print(f"   ❌ User buttons don't trigger keyboard")
                all_keyboard = False
            
            # Check for show_password_keyboard method
            if "def show_password_keyboard" in content:
                print(f"   ✅ Show password keyboard method exists")
            else:
                print(f"   ❌ Show password keyboard method missing")
                all_keyboard = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_keyboard = False
    
    return all_keyboard

def test_language_selection():
    """Test that language selection between English and French works"""
    
    print("\nTesting Language Selection")
    print("=" * 27)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_language = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for language options
            if "values=['English', 'Français']" in content:
                print(f"   ✅ English and French language options")
            else:
                print(f"   ❌ Language options not properly configured")
                all_language = False
            
            # Check for language change method
            if "def change_language" in content:
                print(f"   ✅ Language change method exists")
            else:
                print(f"   ❌ Language change method missing")
                all_language = False
            
            # Check for language binding
            if "language_combo.bind('<<ComboboxSelected>>', self.change_language)" in content:
                print(f"   ✅ Language selection is bound to change handler")
            else:
                print(f"   ❌ Language selection binding missing")
                all_language = False
            
            # Check for language variable
            if "self.language_var" in content:
                print(f"   ✅ Language variable exists")
            else:
                print(f"   ❌ Language variable missing")
                all_language = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_language = False
    
    return all_language

def test_orange_palette_maintained():
    """Test that orange color palette is maintained"""
    
    print("\nTesting Orange Palette Maintained")
    print("=" * 35)
    
    files_to_check = [
        "login_screen.py",
        "YES/login_screen.py"
    ]
    
    all_orange = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for orange colors
            orange_colors = ['#ff8c00', '#e67e00']
            orange_found = sum(1 for color in orange_colors if color in content)
            
            if orange_found >= 2:
                print(f"   ✅ Orange colors maintained ({orange_found} instances)")
            else:
                print(f"   ❌ Orange colors insufficient ({orange_found} instances)")
                all_orange = False
            
            # Check for dark colors
            dark_colors = ['#1a1a1a', '#2d2d2d', '#404040']
            dark_found = sum(1 for color in dark_colors if color in content)
            
            if dark_found >= 3:
                print(f"   ✅ Dark colors maintained ({dark_found} instances)")
            else:
                print(f"   ❌ Dark colors insufficient ({dark_found} instances)")
                all_orange = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_orange = False
    
    return all_orange

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/login_screen.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all login screen fix tests"""
    
    print("🔧 LOGIN SCREEN FIXES TEST SUITE")
    print("=" * 35)
    
    tests = [
        ("White Logo Container", test_white_logo_container),
        ("Horizontal Scrolling", test_horizontal_scrolling),
        ("Keyboard on User Click", test_keyboard_on_user_click),
        ("Language Selection", test_language_selection),
        ("Orange Palette Maintained", test_orange_palette_maintained),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 35)
    print("📊 RESULTS")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Logo container is white")
        print("✅ Horizontal scrolling for usernames")
        print("✅ Number keyboard on user click")
        print("✅ Language selection English/French")
        print("✅ Orange color palette maintained")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Login screen fixes may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 All login screen fixes successfully implemented!")
        print("⬜ White logo container")
        print("📜 Horizontal scrolling for user selection")
        print("⌨️ Number keyboard appears on user name click")
        print("🌐 Language switching between English and French")
        print("🧡 Orange/black/dark gray color palette maintained")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Some login screen fixes need attention")
    
    exit(0 if success else 1)
