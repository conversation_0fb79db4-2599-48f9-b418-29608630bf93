#!/usr/bin/env python3
"""
Start LeComptoir Remote Management Server
Simple startup script with error handling
"""

import os
import sys
import time

def start_server():
    """Start the LeComptoir server with error handling"""
    
    print("🏪 LECOMPTOIR REMOTE MANAGEMENT SERVER")
    print("=" * 60)
    
    # Check if database exists
    if not os.path.exists('remote_management_accounts.db'):
        print("❌ Account database not found!")
        print("💡 Run 'python create_5000_accounts.py' first")
        return False
    
    print("✅ Account database found")
    
    # Check dependencies
    try:
        import flask
        import flask_cors
        import jwt
        import sqlite3
        print("✅ All dependencies available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run 'pip install flask flask-cors PyJWT'")
        return False
    
    # Import and start server
    try:
        print("🔧 Loading server modules...")
        from central_remote_server import app, account_manager
        
        print("📊 Checking account system...")
        stats = account_manager.get_account_stats()
        print(f"   Total accounts: {stats['total_accounts']}")
        print(f"   Available: {stats['available']}")
        print(f"   Active: {stats['active']}")
        
        print("\n🌐 STARTING LECOMPTOIR SERVER...")
        print("=" * 60)
        print("📱 Dashboard: http://localhost:5000")
        print("🔐 Test Login: pos_1000 / V2%7y@Yv")
        print("🌍 For worldwide access: Use 'ngrok http 5000'")
        print("=" * 60)
        print("⚡ Server starting... (Press Ctrl+C to stop)")
        print()
        
        # Start Flask app
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        return True
        
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("💡 Check the error details above")
        return False

if __name__ == '__main__':
    try:
        start_server()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print("💡 Please report this error")
