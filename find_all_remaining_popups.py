#!/usr/bin/env python3
"""
Find and list ALL remaining popup windows that need redesigning
"""

import os
import re

def find_all_popups():
    """Find all popup windows in all files"""
    
    print("🔍 FINDING ALL POPUP WINDOWS IN CODEBASE")
    print("=" * 42)
    
    files_to_check = [
        'user_management.py',
        'product_management.py', 
        'sales_history.py',
        'receipt_settings.py',
        'storage_management.py',
        'pos_screen.py',
        'number_keyboard.py',
        'login_screen.py',
        'pos_app.py'
    ]
    
    all_popups = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all Toplevel windows
            toplevel_matches = re.findall(r'(\w+)\s*=\s*tk\.Toplevel\(.*?\)', content)
            
            # Find all geometry calls
            geometry_matches = re.findall(r'geometry\("(\d+x\d+)"\)', content)
            
            # Find dialog creation patterns
            dialog_patterns = [
                r'def\s+show_(\w+)_dialog',
                r'def\s+(\w+)_dialog',
                r'messagebox\.\w+',
                r'filedialog\.\w+',
                r'colorchooser\.\w+'
            ]
            
            dialog_functions = []
            for pattern in dialog_patterns:
                matches = re.findall(pattern, content)
                dialog_functions.extend(matches)
            
            if toplevel_matches or geometry_matches or dialog_functions:
                all_popups[file_path] = {
                    'toplevel_vars': toplevel_matches,
                    'sizes': geometry_matches,
                    'dialog_functions': dialog_functions
                }
                
                print(f"\n📋 {file_path}:")
                if toplevel_matches:
                    print(f"   🪟 Toplevel windows: {toplevel_matches}")
                if geometry_matches:
                    print(f"   📏 Sizes found: {geometry_matches}")
                if dialog_functions:
                    print(f"   🔧 Dialog functions: {dialog_functions}")
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
    
    return all_popups

def analyze_popup_status():
    """Analyze which popups have been redesigned and which haven't"""
    
    print("\n🎨 ANALYZING POPUP REDESIGN STATUS")
    print("=" * 35)
    
    # Check for modern design indicators
    modern_indicators = [
        "bg='#ff8c00'",  # Orange header
        "configure(bg='#1a1a1a')",  # Dark background
        "relief='solid', bd=1",  # Section styling
        "relief='flat', bd=0",  # Modern buttons
        "fg='#ff8c00'",  # Orange text
    ]
    
    files_to_check = [
        'user_management.py',
        'product_management.py', 
        'sales_history.py',
        'receipt_settings.py',
        'storage_management.py',
        'pos_screen.py',
        'number_keyboard.py'
    ]
    
    redesign_status = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            modern_count = 0
            for indicator in modern_indicators:
                modern_count += content.count(indicator)
            
            # Determine status
            if modern_count >= 5:
                status = "✅ FULLY REDESIGNED"
            elif modern_count >= 2:
                status = "🔄 PARTIALLY REDESIGNED"
            else:
                status = "❌ NOT REDESIGNED"
            
            redesign_status[file_path] = {
                'status': status,
                'modern_count': modern_count
            }
            
            print(f"📋 {file_path}: {status} ({modern_count} modern elements)")
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
    
    return redesign_status

def identify_missing_popups():
    """Identify specific popups that still need redesigning"""
    
    print("\n🚨 IDENTIFYING MISSING POPUP REDESIGNS")
    print("=" * 40)
    
    missing_popups = []
    
    # Check specific known popups
    known_popups = [
        {
            'file': 'sales_history.py',
            'popup': 'Transaction Details Dialog',
            'function': 'show_transaction_inspection_dialog',
            'expected_size': '750x650'
        },
        {
            'file': 'sales_history.py', 
            'popup': 'Date Picker Dialog',
            'function': 'show_date_picker',
            'expected_size': '450x550'
        },
        {
            'file': 'receipt_settings.py',
            'popup': 'Receipt Settings Window',
            'function': 'ReceiptSettings',
            'expected_size': '850x650'
        },
        {
            'file': 'storage_management.py',
            'popup': 'Storage Management Window', 
            'function': 'StorageManagement',
            'expected_size': '950x650'
        },
        {
            'file': 'pos_screen.py',
            'popup': 'Various Dialogs',
            'function': 'multiple',
            'expected_size': 'various'
        }
    ]
    
    for popup_info in known_popups:
        file_path = popup_info['file']
        
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if it has modern design
            has_orange_header = "bg='#ff8c00'" in content
            has_dark_bg = "configure(bg='#1a1a1a')" in content
            has_modern_sections = "relief='solid', bd=1" in content
            
            if not (has_orange_header and has_dark_bg):
                missing_popups.append(popup_info)
                print(f"❌ {popup_info['popup']} in {file_path}")
                print(f"   Function: {popup_info['function']}")
                print(f"   Expected size: {popup_info['expected_size']}")
                print(f"   Missing: Orange header={not has_orange_header}, Dark BG={not has_dark_bg}")
            else:
                print(f"✅ {popup_info['popup']} in {file_path} - Already redesigned")
                
        except Exception as e:
            print(f"❌ Error checking {file_path}: {e}")
    
    return missing_popups

def main():
    """Find and analyze all popup windows"""
    
    print("🎨 COMPLETE POPUP ANALYSIS")
    print("=" * 27)
    
    # Find all popups
    all_popups = find_all_popups()
    
    # Analyze redesign status
    redesign_status = analyze_popup_status()
    
    # Identify missing redesigns
    missing_popups = identify_missing_popups()
    
    print("\n" + "=" * 50)
    print("📊 COMPLETE POPUP ANALYSIS RESULTS")
    print("=" * 50)
    
    total_files = len([f for f in redesign_status.keys()])
    redesigned_files = len([f for f, s in redesign_status.items() if "FULLY REDESIGNED" in s['status']])
    partial_files = len([f for f, s in redesign_status.items() if "PARTIALLY REDESIGNED" in s['status']])
    missing_files = len([f for f, s in redesign_status.items() if "NOT REDESIGNED" in s['status']])
    
    print(f"📋 Total files analyzed: {total_files}")
    print(f"✅ Fully redesigned: {redesigned_files}")
    print(f"🔄 Partially redesigned: {partial_files}")
    print(f"❌ Not redesigned: {missing_files}")
    print(f"🚨 Missing popup redesigns: {len(missing_popups)}")
    
    if missing_popups:
        print("\n🚨 PRIORITY REDESIGNS NEEDED:")
        for popup in missing_popups:
            print(f"   • {popup['popup']} in {popup['file']}")
    
    print("\n🎯 NEXT STEPS:")
    if missing_files > 0:
        print("1. Redesign remaining popup windows with orange/black theme")
        print("2. Add orange headers to all dialogs")
        print("3. Ensure white text on dark backgrounds")
        print("4. Apply modern flat button styling")
        print("5. Test all popup windows for proper sizing")
    else:
        print("✅ All popup windows have been redesigned!")
    
    return len(missing_popups) == 0

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 All popup windows analyzed and redesigned!")
        print("🧡 Modern orange/black aesthetic throughout")
        print("📱 Professional appearance maintained")
    else:
        print("\n⚠️ Some popup windows still need redesigning")
    
    exit(0 if success else 1)
