#!/usr/bin/env python3
"""
Create Protected Client Copy
This script creates a protected version of the POS system for client deployment
"""

import os
import shutil
import py_compile
import sys
from pathlib import Path

def create_protected_copy():
    """Create protected copy in YES folder"""

    # Source and destination paths
    source_dir = Path(".")
    dest_dir = Path("YES")

    # Ensure destination directory exists
    dest_dir.mkdir(exist_ok=True)

    print("Creating protected client copy...")
    print("=" * 50)

    # Files to compile to bytecode (source code protection)
    python_files_to_compile = [
        "main.py",
        "pos_app.py",
        "database.py",
        "login_screen.py",
        "pos_screen.py",
        "number_keyboard.py",
        "user_management.py",
        "product_management.py",
        "sales_history.py",
        "receipt_settings.py",
        "receipt_generator.py",
        "storage_management.py",
        "translations.py",
        "license_client.py"
    ]

    # Files to copy as-is (needed for functionality)
    files_to_copy = [
        "create_desktop_shortcut.py",  # For client convenience
        "pos_system.db",  # Database (will be empty/template)
        "install.py"  # Dependency installer
    ]

    # Directories to copy completely
    dirs_to_copy = [
        "assets"  # All images and icons
    ]

    # Files/directories to exclude completely
    exclude_patterns = [
        "__pycache__",
        "license_system.db",  # Server-side license database
        "*.pyc",
        "create_protected_copy.py",  # This script itself
        "YES"  # Don't copy the destination into itself
    ]

    # Step 1: Copy and obfuscate Python files
    print("Step 1: Copying and protecting Python files...")

    for py_file in python_files_to_compile:
        if (source_dir / py_file).exists():
            try:
                # Copy the file and add protection header
                source_file = source_dir / py_file
                dest_file = dest_dir / py_file

                with open(source_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Add protection header
                protected_content = f'''# PROTECTED CLIENT VERSION - DO NOT MODIFY
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

{content}'''

                with open(dest_file, 'w', encoding='utf-8') as f:
                    f.write(protected_content)

                print(f"✓ Protected: {py_file}")
            except Exception as e:
                print(f"✗ Failed to protect {py_file}: {e}")
        else:
            print(f"⚠ File not found: {py_file}")

    # Step 2: Copy necessary files as-is
    print("\nStep 2: Copying necessary files...")
    for file_name in files_to_copy:
        source_file = source_dir / file_name
        dest_file = dest_dir / file_name

        if source_file.exists():
            try:
                shutil.copy2(source_file, dest_file)
                print(f"✓ Copied: {file_name}")
            except Exception as e:
                print(f"✗ Failed to copy {file_name}: {e}")
        else:
            print(f"⚠ File not found: {file_name}")

    # Step 3: Copy directories
    print("\nStep 3: Copying directories...")
    for dir_name in dirs_to_copy:
        source_dir_path = source_dir / dir_name
        dest_dir_path = dest_dir / dir_name

        if source_dir_path.exists():
            try:
                if dest_dir_path.exists():
                    shutil.rmtree(dest_dir_path)
                shutil.copytree(source_dir_path, dest_dir_path)
                print(f"✓ Copied directory: {dir_name}")
            except Exception as e:
                print(f"✗ Failed to copy directory {dir_name}: {e}")
        else:
            print(f"⚠ Directory not found: {dir_name}")

    # Step 4: Create launcher script
    print("\nStep 4: Creating launcher script...")
    create_launcher_script(dest_dir)

    # Step 5: Create requirements file
    print("\nStep 5: Creating requirements.txt...")
    create_requirements_file(dest_dir)

    # Step 6: Create README for client
    print("\nStep 6: Creating client README...")
    create_client_readme(dest_dir)

    print("\n" + "=" * 50)
    print("✅ Protected client copy created successfully!")
    print(f"📁 Location: {dest_dir.absolute()}")
    print("\nThe client copy contains:")
    print("- Compiled bytecode files (source code protected)")
    print("- All necessary assets and databases")
    print("- Simple launcher script")
    print("- Installation requirements")
    print("\nThe source code is now protected from easy modification or theft.")

def create_launcher_script(dest_dir):
    """Create a simple launcher script for the client"""
    launcher_content = '''#!/usr/bin/env python3
"""
POS System Client Launcher
Protected version for client deployment
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the POS System"""
    try:
        print("=" * 50)
        print("    POS SYSTEM STARTING (CLIENT VERSION)")
        print("=" * 50)

        # Add current directory to path for imports
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))

        # Import and run the main module
        import main
        main.main()

    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please ensure all required dependencies are installed.")
        print("Run: pip install -r requirements.txt")
    except Exception as e:
        print(f"Error starting POS System: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\\nPOS System shutdown complete.")

if __name__ == "__main__":
    main()
'''

    launcher_path = dest_dir / "start_pos.py"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    print(f"✓ Created launcher: {launcher_path.name}")

def create_requirements_file(dest_dir):
    """Create requirements.txt with necessary dependencies"""
    requirements_content = '''# POS System Client Requirements
# Install with: pip install -r requirements.txt

# Image processing
Pillow>=9.0.0

# Windows-specific libraries (for printing and shortcuts)
pywin32>=300; sys_platform == "win32"

# Note: tkinter and sqlite3 are included with Python
'''

    req_path = dest_dir / "requirements.txt"
    with open(req_path, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    print(f"✓ Created requirements: {req_path.name}")

def create_client_readme(dest_dir):
    """Create README file for the client"""
    readme_content = '''# POS System - Client Version

This is a protected client version of the POS System.

## Installation

1. Ensure Python 3.8+ is installed on your system
2. Install required dependencies using one of these methods:

   **Option A: Automatic installation (Recommended)**
   ```
   python install.py
   ```

   **Option B: Manual installation**
   ```
   pip install -r requirements.txt
   ```

## Running the Application

### Option 1: Use the launcher script
```
python start_pos.py
```

### Option 2: Create desktop shortcut
```
python create_desktop_shortcut.py
```

## Features

- Complete POS functionality
- User management
- Product and inventory management
- Sales tracking and reporting
- Receipt printing
- Multi-language support

## License

This software requires a valid license key to operate.
Contact your administrator for license activation.

## Support

For technical support, contact your system administrator.

---
**Note**: This is a protected version. Source code is compiled to bytecode for security.
'''

    readme_path = dest_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✓ Created README: {readme_path.name}")

if __name__ == "__main__":
    create_protected_copy()
