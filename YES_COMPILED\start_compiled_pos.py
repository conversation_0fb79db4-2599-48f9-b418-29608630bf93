#!/usr/bin/env python3
"""
COMPILED POS SYSTEM LAUNCHER
Launches the POS system from compiled bytecode
PROTECTED SOFTWARE - DO NOT MODIFY
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the compiled POS System"""
    try:
        print("🔒 Loading Compiled POS System...")
        print("=" * 50)
        print("    COMPILED POS SYSTEM STARTING")
        print("=" * 50)
        
        # Get current directory and add to path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # Import and run the main module (will load from __pycache__)
        import main
        main.main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Please ensure all required dependencies are installed.")
        print("Run: python install.py")
    except Exception as e:
        print(f"❌ Error starting Compiled POS System: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🔒 Compiled POS System shutdown complete.")

if __name__ == "__main__":
    main()
