#!/usr/bin/env python3
"""
Test the new display settings functionality
"""

import sys
from pathlib import Path

def test_display_settings():
    """Test the display settings functionality"""
    
    print("Testing Display Settings Functionality")
    print("=" * 40)
    
    # Test main directory
    print("📋 Testing main directory...")
    sys.path.insert(0, str(Path(".").absolute()))
    
    try:
        from database import get_display_settings, save_display_settings, init_database
        
        # Initialize database to ensure tables exist
        init_database()
        print("✅ Database initialized")
        
        # Test getting default settings
        settings = get_display_settings()
        print(f"✅ Default settings retrieved: {settings}")
        
        # Verify defaults
        if settings['max_columns'] == 4 and settings['button_size'] == 130:
            print("✅ Default values are correct")
        else:
            print(f"⚠️ Unexpected default values: {settings}")
        
        # Test saving new settings
        test_columns = 5
        test_size = 140
        
        if save_display_settings(test_columns, test_size):
            print(f"✅ Settings saved: {test_columns} columns, {test_size}px")
        else:
            print("❌ Failed to save settings")
            return False
        
        # Test retrieving saved settings
        new_settings = get_display_settings()
        if new_settings['max_columns'] == test_columns and new_settings['button_size'] == test_size:
            print("✅ Settings retrieved correctly after save")
        else:
            print(f"❌ Settings not saved correctly: {new_settings}")
            return False
        
        # Reset to defaults
        save_display_settings(4, 130)
        print("✅ Reset to default settings")
        
    except Exception as e:
        print(f"❌ Error testing main directory: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test YES directory
    print("\n📋 Testing YES directory...")
    sys.path.insert(0, str(Path("YES").absolute()))
    
    try:
        # Clear module cache
        modules_to_clear = ['database', 'pos_screen']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        from database import get_display_settings as get_yes, save_display_settings as save_yes
        
        # Test YES version
        yes_settings = get_yes()
        print(f"✅ YES settings retrieved: {yes_settings}")
        
        # Test saving in YES version
        if save_yes(3, 120):
            print("✅ YES settings saved successfully")
        else:
            print("❌ Failed to save YES settings")
            return False
        
        # Reset YES to defaults
        save_yes(4, 130)
        
    except Exception as e:
        print(f"❌ Error testing YES directory: {e}")
        return False
    
    # Test YES_OBFUSCATED directory
    print("\n📋 Testing YES_OBFUSCATED directory...")
    sys.path.insert(0, str(Path("YES_OBFUSCATED").absolute()))
    
    try:
        # Clear module cache
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        import database as db_obf
        
        # Test obfuscated version
        obf_settings = db_obf.get_display_settings()
        print(f"✅ YES_OBFUSCATED settings retrieved: {obf_settings}")
        
        # Test saving in obfuscated version
        if db_obf.save_display_settings(6, 150):
            print("✅ YES_OBFUSCATED settings saved successfully")
        else:
            print("❌ Failed to save YES_OBFUSCATED settings")
            return False
        
        # Reset obfuscated to defaults
        db_obf.save_display_settings(4, 130)
        
    except Exception as e:
        print(f"❌ Error testing YES_OBFUSCATED directory: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 DISPLAY SETTINGS TEST PASSED!")
    print("=" * 40)
    print("✅ Display settings work in all versions:")
    print("   - Main directory")
    print("   - YES (protected)")
    print("   - YES_OBFUSCATED (secure)")
    print("✅ Settings can be saved and retrieved")
    print("✅ Default values are correct")
    
    return True

def test_pos_screen_integration():
    """Test that pos_screen can use the display settings"""
    
    print("\n🖼️ Testing POS Screen Integration:")
    print("-" * 30)
    
    try:
        # Test main version
        sys.path.insert(0, str(Path(".").absolute()))
        
        # Clear module cache
        if 'pos_screen' in sys.modules:
            del sys.modules['pos_screen']
        
        import pos_screen
        
        # Check if the load_products method exists and mentions display settings
        if hasattr(pos_screen.POSScreen, 'load_products'):
            print("✅ load_products method found in main version")
        else:
            print("❌ load_products method not found")
            return False
        
        # Check if show_display_settings method exists
        if hasattr(pos_screen.POSScreen, 'show_display_settings'):
            print("✅ show_display_settings method found in main version")
        else:
            print("❌ show_display_settings method not found")
            return False
        
        # Test YES version
        sys.path.insert(0, str(Path("YES").absolute()))
        
        if 'pos_screen' in sys.modules:
            del sys.modules['pos_screen']
        
        import pos_screen as pos_screen_yes
        
        if hasattr(pos_screen_yes.POSScreen, 'show_display_settings'):
            print("✅ show_display_settings method found in YES version")
        else:
            print("❌ show_display_settings method not found in YES version")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing POS screen integration: {e}")
        return False

def main():
    """Run all display settings tests"""
    
    print("🔧 DISPLAY SETTINGS TEST SUITE")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Display settings functionality
    if test_display_settings():
        tests_passed += 1
        print("✅ Test 1 PASSED: Display settings functionality")
    else:
        print("❌ Test 1 FAILED: Display settings functionality")
    
    # Test 2: POS screen integration
    if test_pos_screen_integration():
        tests_passed += 1
        print("✅ Test 2 PASSED: POS screen integration")
    else:
        print("❌ Test 2 FAILED: POS screen integration")
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Display settings are working correctly")
        print("✅ Users can now customize product display")
        print("✅ Bigger buttons by default (130px)")
        print("✅ Configurable columns (2-8)")
        print("✅ Settings persist in database")
    else:
        print("⚠️ Some tests failed")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎛️ Display settings are ready for use!")
        print("📋 Users can access via Admin menu → Display")
    else:
        print("\n❌ Display settings test failed!")
    sys.exit(0 if success else 1)
