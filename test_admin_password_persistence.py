#!/usr/bin/env python3
"""
Test that admin password changes persist after database reinitialization
"""

import sys
import os
import hashlib
from pathlib import Path

def test_password_persistence():
    """Test that admin password changes are preserved"""
    
    print("Testing Admin Password Persistence")
    print("=" * 40)
    
    # Test main directory
    print("📋 Testing main directory...")
    sys.path.insert(0, str(Path(".").absolute()))
    
    try:
        from database import init_database, authenticate_user, get_db_connection
        
        # Step 1: Initialize database (should create admin with default password)
        print("🔄 Step 1: Initializing database...")
        init_database()
        
        # Verify default password works
        user = authenticate_user("admin", "@H@W@LeComptoir@")
        if user:
            print("✅ Default admin password works: @H@W@LeComptoir@")
        else:
            print("❌ Default admin password failed")
            return False
        
        # Step 2: Change admin password manually
        print("🔄 Step 2: Changing admin password...")
        new_password = "MyNewSecurePassword123"
        hashed_new_password = hashlib.sha256(new_password.encode()).hexdigest()
        
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("UPDATE users SET password = ? WHERE username = 'admin'", (hashed_new_password,))
            conn.commit()
            print(f"✅ Admin password changed to: {new_password}")
        finally:
            conn.close()
        
        # Step 3: Verify new password works
        print("🔄 Step 3: Verifying new password...")
        user_new = authenticate_user("admin", new_password)
        if user_new:
            print(f"✅ New admin password works: {new_password}")
        else:
            print(f"❌ New admin password failed: {new_password}")
            return False
        
        # Step 4: Verify old password no longer works
        print("🔄 Step 4: Verifying old password is rejected...")
        user_old = authenticate_user("admin", "@H@W@LeComptoir@")
        if user_old:
            print("❌ Old password still works (this is wrong!)")
            return False
        else:
            print("✅ Old password correctly rejected")
        
        # Step 5: Re-initialize database (this was causing the bug)
        print("🔄 Step 5: Re-initializing database (simulating POS restart)...")
        init_database()
        
        # Step 6: Verify new password still works after reinitialization
        print("🔄 Step 6: Verifying password persists after restart...")
        user_after_restart = authenticate_user("admin", new_password)
        if user_after_restart:
            print(f"✅ Password persisted after restart: {new_password}")
        else:
            print(f"❌ Password was reset after restart (BUG!)")
            return False
        
        # Step 7: Verify old password still doesn't work
        print("🔄 Step 7: Verifying old password still rejected...")
        user_old_after = authenticate_user("admin", "@H@W@LeComptoir@")
        if user_old_after:
            print("❌ Old password works again after restart (BUG!)")
            return False
        else:
            print("✅ Old password still correctly rejected")
        
        # Step 8: Reset to default for cleanup
        print("🔄 Step 8: Resetting to default password for cleanup...")
        default_hash = hashlib.sha256("@H@W@LeComptoir@".encode()).hexdigest()
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("UPDATE users SET password = ? WHERE username = 'admin'", (default_hash,))
            conn.commit()
            print("✅ Reset to default password")
        finally:
            conn.close()
        
    except Exception as e:
        print(f"❌ Error testing main directory: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test YES directory
    print("\n📋 Testing YES directory...")
    sys.path.insert(0, str(Path("YES").absolute()))
    
    try:
        # Clear module cache
        modules_to_clear = ['database']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        from database import init_database as init_yes, authenticate_user as auth_yes, get_db_connection as conn_yes
        
        # Change to YES directory for database operations
        original_dir = os.getcwd()
        os.chdir("YES")
        
        try:
            # Initialize and test password persistence in YES version
            init_yes()
            
            # Change password
            test_password = "YESTestPassword456"
            hashed_test = hashlib.sha256(test_password.encode()).hexdigest()
            
            conn = conn_yes()
            try:
                c = conn.cursor()
                c.execute("UPDATE users SET password = ? WHERE username = 'admin'", (hashed_test,))
                conn.commit()
            finally:
                conn.close()
            
            # Re-initialize (test persistence)
            init_yes()
            
            # Verify password persisted
            user_yes = auth_yes("admin", test_password)
            if user_yes:
                print("✅ YES version: Password persisted after restart")
            else:
                print("❌ YES version: Password was reset (BUG!)")
                return False
            
            # Reset to default
            default_hash = hashlib.sha256("@H@W@LeComptoir@".encode()).hexdigest()
            conn = conn_yes()
            try:
                c = conn.cursor()
                c.execute("UPDATE users SET password = ? WHERE username = 'admin'", (default_hash,))
                conn.commit()
            finally:
                conn.close()
            
        finally:
            os.chdir(original_dir)
        
    except Exception as e:
        print(f"❌ Error testing YES directory: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 PASSWORD PERSISTENCE TEST PASSED!")
    print("=" * 40)
    print("✅ Admin password changes are preserved")
    print("✅ Database reinitialization doesn't reset password")
    print("✅ Fix works in all versions")
    print("✅ Users can safely change admin password")
    
    return True

def main():
    """Run password persistence test"""
    
    print("🔧 ADMIN PASSWORD PERSISTENCE TEST")
    print("=" * 50)
    
    success = test_password_persistence()
    
    if success:
        print("\n🎉 TEST PASSED!")
        print("✅ Admin password persistence is working correctly")
        print("✅ Users can change admin password and it will persist")
        print("✅ No more password reset on POS restart")
    else:
        print("\n❌ TEST FAILED!")
        print("⚠️ Admin password persistence issue still exists")
    
    return success

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🔑 Admin password changes now persist correctly!")
    else:
        print("\n❌ Password persistence test failed!")
    sys.exit(0 if success else 1)
