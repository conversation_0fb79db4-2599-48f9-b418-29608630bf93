#!/usr/bin/env python3
"""
Infinite Dungeon Crawler - Roguelike RPG
========================================

An epic turn-based roguelike RPG with infinite replayability!

FEATURES:
🏰 Procedurally generated dungeons - every playthrough is unique
⚔️ Turn-based tactical combat with strategic depth
📈 Character progression with leveling, stats, and equipment
🎒 Rich inventory system with weapons, armor, potions, and treasures
👹 Diverse monsters that scale with dungeon depth
💎 Rarity-based loot system (Common to Legendary)
🏆 Infinite progression - see how deep you can go!

GAMEPLAY FEATURES:
- Randomly generated dungeon layouts with rooms and corridors
- Multiple monster types with unique stats and behaviors
- Equipment system with weapons and armor that affect combat
- Potion system for healing and recovery
- Experience and leveling system with stat growth
- Treasure hunting with valuable items to collect
- Progressive difficulty - deeper levels = stronger enemies and better loot

CONTROLS:
- WASD or Arrow Keys: Move/Attack
- I: Open/Close Inventory
- UP/DOWN: Navigate inventory
- ENTER: Use/Equip selected item
- ESC: Close inventory or quit game
- R: Restart when dead

COMBAT SYSTEM:
- Turn-based: You move, then all monsters move
- Attack by moving into an enemy
- Damage = Your Attack + Weapon Bonus - Enemy Defense
- Equipment directly affects your combat effectiveness
- Gain EXP from defeating monsters to level up

PROGRESSION:
- Level up to increase HP, Attack, and Defense
- Find better weapons and armor as you go deeper
- Collect treasure to track your success
- Each dungeon level has stronger monsters and better loot

REQUIREMENTS:
- Python 3.6+
- Pygame library

If you don't have Pygame installed, this script will offer to install it.
"""

import sys
import subprocess
import os

def check_pygame():
    """Check if pygame is installed, offer to install if not."""
    try:
        import pygame
        return True
    except ImportError:
        print("🎮 Pygame is not installed!")
        print("To install pygame, run: pip install pygame")
        print()
        
        response = input("Would you like me to try installing it now? (y/n): ").lower()
        if response == 'y':
            try:
                print("Installing pygame...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
                print("✅ Pygame installed successfully!")
                return True
            except subprocess.CalledProcessError:
                print("❌ Failed to install pygame. Please install it manually.")
                return False
        return False

def main():
    print("=" * 60)
    print("    🏰 INFINITE DUNGEON CRAWLER - ROGUELIKE RPG 🏰")
    print("=" * 60)
    print()
    print("Welcome to the infinite dungeon! How deep can you survive?")
    print()
    
    if not check_pygame():
        print("Cannot start game without pygame. Exiting...")
        input("Press Enter to exit...")
        return
    
    print("🎮 Starting Infinite Dungeon Crawler...")
    print("💡 Tip: Check the in-game UI for controls and stats!")
    print("🎯 Goal: Survive as long as possible and collect treasure!")
    print()
    
    # Import and run the game
    try:
        from dungeon_crawler import Game
        game = Game()
        game.run()
    except Exception as e:
        print(f"❌ Error starting game: {e}")
        print("Make sure dungeon_crawler.py is in the same directory.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
