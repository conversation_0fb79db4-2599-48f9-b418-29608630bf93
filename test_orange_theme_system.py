#!/usr/bin/env python3
"""
Test that orange/black theme has been applied to entire POS system
"""

import sys
import os
from pathlib import Path

def test_pos_screen_theme():
    """Test that POS screen uses orange/black theme"""
    
    print("Testing POS Screen Theme")
    print("=" * 26)
    
    files_to_check = [
        "pos_screen.py",
        "YES/pos_screen.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark backgrounds
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Main background is dark (#1a1a1a)")
            else:
                print(f"   ❌ Main background not dark")
                all_themed = False
            
            # Check for dark frames
            if "bg='#2d2d2d'" in content:
                print(f"   ✅ Frames use dark theme (#2d2d2d)")
            else:
                print(f"   ❌ Frames don't use dark theme")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
            
            # Check for orange total color
            if "fg='#ff8c00'" in content:
                print(f"   ✅ Orange accent color used")
            else:
                print(f"   ❌ Orange accent color missing")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_product_management_theme():
    """Test that product management uses orange/black theme"""
    
    print("\nTesting Product Management Theme")
    print("=" * 34)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark backgrounds
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Main background is dark")
            else:
                print(f"   ❌ Main background not dark")
                all_themed = False
            
            # Check for dark frames
            if "bg='#2d2d2d'" in content:
                print(f"   ✅ Uses dark frames")
            else:
                print(f"   ❌ Doesn't use dark frames")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_user_management_theme():
    """Test that user management uses orange/black theme"""
    
    print("\nTesting User Management Theme")
    print("=" * 31)
    
    files_to_check = [
        "user_management.py",
        "YES/user_management.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark theme
            if "bg='#1a1a1a'" in content or "bg='#2d2d2d'" in content:
                print(f"   ✅ Uses dark theme")
            else:
                print(f"   ❌ Doesn't use dark theme")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_sales_history_theme():
    """Test that sales history uses orange/black theme"""
    
    print("\nTesting Sales History Theme")
    print("=" * 29)
    
    files_to_check = [
        "sales_history.py",
        "YES/sales_history.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark theme
            if "bg='#1a1a1a'" in content or "bg='#2d2d2d'" in content:
                print(f"   ✅ Uses dark theme")
            else:
                print(f"   ❌ Doesn't use dark theme")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_receipt_settings_theme():
    """Test that receipt settings uses orange/black theme"""
    
    print("\nTesting Receipt Settings Theme")
    print("=" * 32)
    
    files_to_check = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark theme
            if "bg='#1a1a1a'" in content or "bg='#2d2d2d'" in content:
                print(f"   ✅ Uses dark theme")
            else:
                print(f"   ❌ Doesn't use dark theme")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_number_keyboard_theme():
    """Test that number keyboard uses orange/black theme"""
    
    print("\nTesting Number Keyboard Theme")
    print("=" * 31)
    
    files_to_check = [
        "number_keyboard.py",
        "YES/number_keyboard.py"
    ]
    
    all_themed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for dark background
            if "configure(bg='#1a1a1a')" in content:
                print(f"   ✅ Dark background")
            else:
                print(f"   ❌ Dark background missing")
                all_themed = False
            
            # Check for orange elements
            if "fg='#ff8c00'" in content:
                print(f"   ✅ Orange elements")
            else:
                print(f"   ❌ Orange elements missing")
                all_themed = False
            
            # Check for Segoe UI font
            if "font=('Segoe UI'" in content:
                print(f"   ✅ Uses Segoe UI font")
            else:
                print(f"   ❌ Doesn't use Segoe UI font")
                all_themed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_themed = False
    
    return all_themed

def test_button_colors_preserved():
    """Test that button colors are preserved"""
    
    print("\nTesting Button Colors Preserved")
    print("=" * 33)
    
    files_to_check = [
        "pos_screen.py",
        "product_management.py",
        "YES/pos_screen.py",
        "YES/product_management.py"
    ]
    
    all_preserved = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for preserved button colors
            button_colors = ['#28a745', '#dc3545', '#6c757d', '#007bff']
            found_colors = sum(1 for color in button_colors if color in content)
            
            if found_colors >= 2:
                print(f"   ✅ Button colors preserved ({found_colors} colors found)")
            else:
                print(f"   ❌ Button colors not preserved ({found_colors} colors found)")
                all_preserved = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_preserved = False
    
    return all_preserved

def test_obfuscated_version():
    """Test that obfuscated version exists and is updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 28)
    
    obfuscated_files = [
        "YES_OBFUSCATED/pos_screen.py",
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/number_keyboard.py"
    ]
    
    all_obfuscated = True
    
    for file_path in obfuscated_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            all_obfuscated = False
    
    return all_obfuscated

def main():
    """Run all orange theme tests"""
    
    print("🧡 ORANGE THEME SYSTEM-WIDE TEST SUITE")
    print("=" * 40)
    
    tests = [
        ("POS Screen Theme", test_pos_screen_theme),
        ("Product Management Theme", test_product_management_theme),
        ("User Management Theme", test_user_management_theme),
        ("Sales History Theme", test_sales_history_theme),
        ("Receipt Settings Theme", test_receipt_settings_theme),
        ("Number Keyboard Theme", test_number_keyboard_theme),
        ("Button Colors Preserved", test_button_colors_preserved),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Orange/black/dark gray theme applied system-wide")
        print("✅ Segoe UI font used throughout")
        print("✅ Button colors preserved as requested")
        print("✅ All popup windows themed")
        print("✅ Protected and obfuscated versions updated")
    else:
        print("⚠️ Some tests failed")
        print("❌ Theme application may be incomplete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🧡 Orange theme successfully applied to entire POS system!")
        print("🖤 Dark backgrounds with orange accents throughout")
        print("🔤 Modern Segoe UI font in all windows")
        print("🔘 Button colors preserved as requested")
        print("📱 Consistent theme across main app and popups")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ Orange theme application needs attention")
    
    exit(0 if success else 1)
