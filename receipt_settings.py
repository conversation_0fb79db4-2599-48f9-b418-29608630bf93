"""
Receipt Settings Screen
Provides receipt configuration and printing settings
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk

from database import get_db_connection

class ReceiptSettings:
    """Receipt settings and configuration interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.settings = {}
        self.logo_image = None

    def show(self):
        """Display the receipt settings screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Initialize settings with defaults
        self.settings = {
            'business_name': 'Your Business Name',
            'business_address': 'Your Business Address',
            'business_phone': 'Your Phone Number',
            'header_text': '',
            'footer_text': 'Thank you for your business!',
            'paper_size': '300x95',
            'font_size': 19,
            'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing
            'selected_printer': 'Default Printer',
            'logo_image': None
        }

        # Create interface
        self.create_interface()

    def hide(self):
        """Hide the receipt settings screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _bind_mousewheel_to_widget(self, widget):
        """Recursively bind mouse wheel to widget and all its children"""
        widget.bind("<MouseWheel>", self._on_mousewheel)
        for child in widget.winfo_children():
            self._bind_mousewheel_to_widget(child)

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the receipt settings interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('receipt_settings'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content with scrolling - wider to use full page width
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=20)

        # Create scrollable canvas
        self.canvas = tk.Canvas(content_frame, bg='#1a1a1a', highlightthickness=0)
        scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#1a1a1a')

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", self._on_mousewheel)

        # Create settings form in scrollable frame
        self.create_settings_form(self.scrollable_frame)

        # Bind mouse wheel to all widgets for smooth scrolling
        self._bind_mousewheel_to_widget(self.scrollable_frame)

    def create_settings_form(self, parent):
        """Create balanced touch-friendly receipt settings form"""
        # Main container with proper spacing - wider to use full page width
        main_container = tk.Frame(parent, bg='#2d2d2d')
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=15)

        # Header with title on left and buttons on right
        header_frame = tk.Frame(main_container, bg='#ff8c00', height=50)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        header_frame.pack_propagate(False)

        # Title on the left
        header_label = tk.Label(header_frame, text="Receipt Settings",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(side=tk.LEFT, padx=(20, 0), expand=True, anchor='w')

        # Action buttons on the right
        button_frame = tk.Frame(header_frame, bg='#ff8c00')
        button_frame.pack(side=tk.RIGHT, padx=(0, 20))

        # Reset button
        reset_btn = tk.Button(button_frame, text="🔄 Reset",
                             font=('Segoe UI', 10, 'bold'), bg='#ffc107', fg='white',
                             padx=15, pady=6, command=self.reset_settings,
                             relief='solid', bd=1)
        reset_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Save button
        save_btn = tk.Button(button_frame, text="💾 Save",
                            font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                            padx=15, pady=6, command=self.save_settings,
                            relief='solid', bd=1)
        save_btn.pack(side=tk.LEFT)

        # Content in organized sections
        content_frame = tk.Frame(main_container, bg='#2d2d2d')
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Create sections with balanced sizing
        self.create_business_section(content_frame)
        self.create_formatting_section(content_frame)
        self.create_logo_section(content_frame)
        self.create_printer_section(content_frame)

        # Load settings after all form fields are created
        self.load_settings()

    def create_business_section(self, parent):
        """Create business information section"""
        section_frame = tk.LabelFrame(parent, text="Business Information",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 12))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=15, pady=12)

        # Three-column layout for better balance
        left_col = tk.Frame(container, bg='#2d2d2d')
        left_col.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        center_col = tk.Frame(container, bg='#2d2d2d')
        center_col.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 8))

        right_col = tk.Frame(container, bg='#2d2d2d')
        right_col.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # Business fields distributed across three columns
        self.create_field(left_col, "Business Name", 'business_name')
        self.create_field(left_col, "Business Address", 'business_address')
        self.create_field(center_col, "Phone Number", 'business_phone')
        self.create_field(center_col, "Header Text", 'header_text')
        self.create_field(right_col, "Footer Text", 'footer_text')

    def create_formatting_section(self, parent):
        """Create formatting section"""
        section_frame = tk.LabelFrame(parent, text="Formatting Options",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 12))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=15, pady=12)

        # Three-column layout for formatting controls
        left_format = tk.Frame(container, bg='#2d2d2d')
        left_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        center_format = tk.Frame(container, bg='#2d2d2d')
        center_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 8))

        right_format = tk.Frame(container, bg='#2d2d2d')
        right_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # Formatting controls
        self.create_paper_size_control(left_format)
        self.create_font_control(center_format)
        self.create_spacing_control(right_format)

    def create_logo_section(self, parent):
        """Create logo section"""
        section_frame = tk.LabelFrame(parent, text="Logo Settings",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 12))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=15, pady=12)

        # Logo selection
        logo_frame = tk.Frame(container, bg='#2d2d2d')
        logo_frame.pack(fill=tk.X, pady=(0, 10))

        select_btn = tk.Button(logo_frame, text="📁 Select Logo",
                              font=('Segoe UI', 11, 'bold'), bg='#007bff', fg='white',
                              padx=20, pady=8, command=self.select_logo,
                              relief='solid', bd=1)
        select_btn.pack(side=tk.LEFT)

        self.logo_preview_label = tk.Label(logo_frame, text="No logo selected",
                                          font=('Segoe UI', 10), bg='#2d2d2d', fg='#6c757d')
        self.logo_preview_label.pack(side=tk.LEFT, padx=(15, 0))

        # Logo size control
        self.create_logo_size_control(container)

    def create_printer_section(self, parent):
        """Create printer section"""
        section_frame = tk.LabelFrame(parent, text="Printer Settings",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 12))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=15, pady=12)

        # Printer selection
        printer_frame = tk.Frame(container, bg='#2d2d2d')
        printer_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(printer_frame, text="Select Printer:", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 5))

        self.selected_printer_var = tk.StringVar(value=self.settings.get('selected_printer', 'Default Printer'))
        self.printer_combo = ttk.Combobox(printer_frame, textvariable=self.selected_printer_var,
                                         values=['Default Printer'], state='readonly',
                                         font=('Segoe UI', 10), width=50)
        self.printer_combo.pack(fill=tk.X)

        # Printer buttons
        button_frame = tk.Frame(container, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, pady=(10, 0))

        detect_btn = tk.Button(button_frame, text="🔍 Detect",
                              font=('Segoe UI', 10, 'bold'), bg='#17a2b8', fg='white',
                              padx=15, pady=6, command=self.detect_printers,
                              relief='solid', bd=1)
        detect_btn.pack(side=tk.LEFT, padx=(0, 8))

        connect_btn = tk.Button(button_frame, text="🔗 Connect",
                               font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                               padx=15, pady=6, command=self.connect_to_printer,
                               relief='solid', bd=1)
        connect_btn.pack(side=tk.LEFT, padx=(0, 8))

        test_btn = tk.Button(button_frame, text="🧪 Test",
                            font=('Segoe UI', 10, 'bold'), bg='#ffc107', fg='white',
                            padx=15, pady=6, command=self.test_print,
                            relief='solid', bd=1)
        test_btn.pack(side=tk.LEFT)

        # Status
        self.printer_status_label = tk.Label(container, text="Ready to detect printers",
                                           font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        self.printer_status_label.pack(pady=(8, 0))

    def create_field(self, parent, label_text, field_name):
        """Create a balanced form field"""
        field_frame = tk.Frame(parent, bg='#2d2d2d')
        field_frame.pack(fill=tk.X, pady=(0, 10))

        # Label
        label = tk.Label(field_frame, text=label_text, font=('Segoe UI', 10, 'bold'),
                        bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 4))

        # Entry
        entry_var = tk.StringVar(value=self.settings.get(field_name, ''))
        entry = tk.Entry(field_frame, textvariable=entry_var, font=('Segoe UI', 10),
                        bg='#404040', fg='white', insertbackground='white',
                        relief='solid', bd=1)
        entry.pack(fill=tk.X, ipady=4)

        # Store reference
        setattr(self, f'{field_name}_var', entry_var)

    def create_paper_size_control(self, parent):
        """Create paper size control"""
        tk.Label(parent, text="Paper Size", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 5))

        self.paper_size_var = tk.StringVar(value=self.settings.get('paper_size', '300x95'))
        available_sizes = self.get_available_paper_sizes()

        size_combo = ttk.Combobox(parent, textvariable=self.paper_size_var,
                                 values=available_sizes, state='readonly',
                                 font=('Segoe UI', 9), width=15)
        size_combo.pack(fill=tk.X)
        size_combo.bind('<<ComboboxSelected>>', lambda e: self.handle_size_selection())

    def create_font_control(self, parent):
        """Create font size slider control like logo size"""
        # Header with value
        header_frame = tk.Frame(parent, bg='#2d2d2d')
        header_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(header_frame, text="Font Size:", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 19))
        self.font_size_value_label = tk.Label(header_frame, text="19pt",
                                             font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.font_size_value_label.pack(side=tk.RIGHT)

        # Slider like logo size - wider to use more space
        self.font_size_slider = tk.Scale(parent, from_=8, to=72,
                                        orient=tk.HORIZONTAL, variable=self.font_size_var,
                                        font=('Segoe UI', 8), bg='#404040', fg='white',
                                        highlightthickness=0, length=300,
                                        command=self.update_font_size_label)
        self.font_size_slider.pack(fill=tk.X)

    def create_spacing_control(self, parent):
        """Create line spacing slider control like logo size"""
        # Header with value
        header_frame = tk.Frame(parent, bg='#2d2d2d')
        header_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(header_frame, text="Line Spacing:", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        self.line_spacing_var = tk.IntVar(value=self.settings.get('line_spacing', 0))
        self.line_spacing_value_label = tk.Label(header_frame, text="0px",
                                                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.line_spacing_value_label.pack(side=tk.RIGHT)

        # Slider like logo size - wider to use more space
        self.line_spacing_slider = tk.Scale(parent, from_=0, to=20,
                                           orient=tk.HORIZONTAL, variable=self.line_spacing_var,
                                           font=('Segoe UI', 8), bg='#404040', fg='white',
                                           highlightthickness=0, length=300,
                                           command=self.update_line_spacing_label)
        self.line_spacing_slider.pack(fill=tk.X)

        # Help text
        help_label = tk.Label(parent, text="(0-20 pixels)",
                            font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        help_label.pack(pady=(3, 0))

    def create_logo_size_control(self, parent):
        """Create logo size control"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(10, 0))

        # Header with value
        header_frame = tk.Frame(size_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(header_frame, text="Logo Size:", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        self.logo_size_var = tk.IntVar(value=100)
        self.size_value_label = tk.Label(header_frame, text="100%",
                                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.size_value_label.pack(side=tk.RIGHT)

        # Slider - wider to use more space
        self.logo_size_slider = tk.Scale(size_frame, from_=25, to=200,
                                        orient=tk.HORIZONTAL, variable=self.logo_size_var,
                                        font=('Segoe UI', 8), bg='#404040', fg='white',
                                        highlightthickness=0, length=400,
                                        command=self.update_size_label)
        self.logo_size_slider.pack(fill=tk.X)



    def create_touch_header(self, parent):
        """Create large touch-friendly header"""
        header_frame = tk.Frame(parent, bg='#ff8c00', height=80)
        header_frame.pack(fill=tk.X, padx=20, pady=(20, 30))
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text="Receipt Settings",
                               font=('Segoe UI', 24, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

    def create_touch_business_section(self, parent):
        """Create touch-friendly business information section"""
        section_frame = tk.LabelFrame(parent, text="Business Information",
                                     font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=2)
        section_frame.pack(fill=tk.X, padx=20, pady=(0, 25))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=25, pady=25)

        # Large touch-friendly fields
        self.create_touch_field(container, "Business Name", 'business_name')
        self.create_touch_field(container, "Business Address", 'business_address')
        self.create_touch_field(container, "Phone Number", 'business_phone')
        self.create_touch_field(container, "Header Text", 'header_text')
        self.create_touch_field(container, "Footer Text", 'footer_text')

    def create_touch_formatting_section(self, parent):
        """Create touch-friendly formatting section"""
        section_frame = tk.LabelFrame(parent, text="Formatting Options",
                                     font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=2)
        section_frame.pack(fill=tk.X, padx=20, pady=(0, 25))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=25, pady=25)

        # Paper size with large buttons
        self.create_touch_paper_size(container)

        # Font size with big +/- buttons
        self.create_touch_font_size(container)

        # Line spacing with big +/- buttons
        self.create_touch_line_spacing(container)

    def create_touch_field(self, parent, label_text, field_name):
        """Create large touch-friendly form field"""
        field_frame = tk.Frame(parent, bg='#2d2d2d')
        field_frame.pack(fill=tk.X, pady=(0, 20))

        # Large label
        label = tk.Label(field_frame, text=label_text, font=('Segoe UI', 14, 'bold'),
                        bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 8))

        # Large entry field
        entry_var = tk.StringVar(value=self.settings.get(field_name, ''))
        entry = tk.Entry(field_frame, textvariable=entry_var, font=('Segoe UI', 14),
                        bg='#404040', fg='white', insertbackground='white',
                        relief='solid', bd=2, height=2)
        entry.pack(fill=tk.X, ipady=8)

        # Store reference
        setattr(self, f'{field_name}_var', entry_var)

    def create_touch_paper_size(self, parent):
        """Create touch-friendly paper size selector"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(size_frame, text="Paper Size", font=('Segoe UI', 14, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 10))

        # Large buttons for common sizes
        button_frame = tk.Frame(size_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X)

        self.paper_size_var = tk.StringVar(value=self.settings.get('paper_size', '300x95'))

        sizes = [
            ("Receipt (80mm)", "80x297"),
            ("Receipt (58mm)", "58x297"),
            ("Default", "300x95"),
            ("A4", "2480x3508")
        ]

        for i, (name, size) in enumerate(sizes):
            btn = tk.Button(button_frame, text=name, font=('Segoe UI', 12, 'bold'),
                           bg='#007bff', fg='white', padx=15, pady=12,
                           command=lambda s=size: self.set_paper_size(s),
                           relief='solid', bd=2)
            btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

    def create_touch_font_size(self, parent):
        """Create touch-friendly font size control"""
        font_frame = tk.Frame(parent, bg='#2d2d2d')
        font_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(font_frame, text="Font Size", font=('Segoe UI', 14, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 10))

        control_frame = tk.Frame(font_frame, bg='#2d2d2d')
        control_frame.pack(fill=tk.X)

        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 19))

        # Large - button
        minus_btn = tk.Button(control_frame, text="−", font=('Segoe UI', 20, 'bold'),
                             bg='#dc3545', fg='white', width=3, height=2,
                             command=lambda: self.adjust_font_size(-1),
                             relief='solid', bd=2)
        minus_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Current value display
        self.font_size_label = tk.Label(control_frame, text="19pt",
                                       font=('Segoe UI', 16, 'bold'),
                                       bg='#404040', fg='white', width=8, height=2,
                                       relief='solid', bd=2)
        self.font_size_label.pack(side=tk.LEFT, padx=(0, 10))

        # Large + button
        plus_btn = tk.Button(control_frame, text="+", font=('Segoe UI', 20, 'bold'),
                            bg='#28a745', fg='white', width=3, height=2,
                            command=lambda: self.adjust_font_size(1),
                            relief='solid', bd=2)
        plus_btn.pack(side=tk.LEFT)

    def create_touch_line_spacing(self, parent):
        """Create touch-friendly line spacing control"""
        spacing_frame = tk.Frame(parent, bg='#2d2d2d')
        spacing_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(spacing_frame, text="Line Spacing", font=('Segoe UI', 14, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 10))

        control_frame = tk.Frame(spacing_frame, bg='#2d2d2d')
        control_frame.pack(fill=tk.X)

        self.line_spacing_var = tk.IntVar(value=self.settings.get('line_spacing', 0))

        # Large - button
        minus_btn = tk.Button(control_frame, text="−", font=('Segoe UI', 20, 'bold'),
                             bg='#dc3545', fg='white', width=3, height=2,
                             command=lambda: self.adjust_line_spacing(-1),
                             relief='solid', bd=2)
        minus_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Current value display
        self.line_spacing_label = tk.Label(control_frame, text="0px",
                                          font=('Segoe UI', 16, 'bold'),
                                          bg='#404040', fg='white', width=8, height=2,
                                          relief='solid', bd=2)
        self.line_spacing_label.pack(side=tk.LEFT, padx=(0, 10))

        # Large + button
        plus_btn = tk.Button(control_frame, text="+", font=('Segoe UI', 20, 'bold'),
                            bg='#28a745', fg='white', width=3, height=2,
                            command=lambda: self.adjust_line_spacing(1),
                            relief='solid', bd=2)
        plus_btn.pack(side=tk.LEFT)

    def create_touch_logo_section(self, parent):
        """Create touch-friendly logo section"""
        section_frame = tk.LabelFrame(parent, text="Logo Settings",
                                     font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=2)
        section_frame.pack(fill=tk.X, padx=20, pady=(0, 25))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=25, pady=25)

        # Large logo selection button
        logo_frame = tk.Frame(container, bg='#2d2d2d')
        logo_frame.pack(fill=tk.X, pady=(0, 20))

        select_logo_btn = tk.Button(logo_frame, text="📁 Select Logo Image",
                                   font=('Segoe UI', 16, 'bold'), bg='#007bff', fg='white',
                                   padx=30, pady=15, command=self.select_logo,
                                   relief='solid', bd=2)
        select_logo_btn.pack(side=tk.LEFT)

        self.logo_preview_label = tk.Label(logo_frame, text="No logo selected",
                                          font=('Segoe UI', 12), bg='#2d2d2d', fg='#6c757d')
        self.logo_preview_label.pack(side=tk.LEFT, padx=(20, 0))

        # Logo size with large slider
        self.create_touch_logo_size(container)

    def create_touch_printer_section(self, parent):
        """Create touch-friendly printer section"""
        section_frame = tk.LabelFrame(parent, text="Printer Settings",
                                     font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=2)
        section_frame.pack(fill=tk.X, padx=20, pady=(0, 25))

        container = tk.Frame(section_frame, bg='#2d2d2d')
        container.pack(fill=tk.X, padx=25, pady=25)

        # Printer selection
        printer_frame = tk.Frame(container, bg='#2d2d2d')
        printer_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(printer_frame, text="Select Printer", font=('Segoe UI', 14, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 10))

        self.selected_printer_var = tk.StringVar(value=self.settings.get('selected_printer', 'Default Printer'))
        self.printer_combo = ttk.Combobox(printer_frame, textvariable=self.selected_printer_var,
                                         values=['Default Printer'], state='readonly',
                                         font=('Segoe UI', 12), height=15)
        self.printer_combo.pack(fill=tk.X, ipady=8)

        # Large printer action buttons
        button_frame = tk.Frame(container, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, pady=(20, 0))

        detect_btn = tk.Button(button_frame, text="🔍 Detect Printers",
                              font=('Segoe UI', 14, 'bold'), bg='#17a2b8', fg='white',
                              padx=20, pady=12, command=self.detect_printers,
                              relief='solid', bd=2)
        detect_btn.pack(side=tk.LEFT, padx=(0, 15), fill=tk.X, expand=True)

        connect_btn = tk.Button(button_frame, text="🔗 Connect",
                               font=('Segoe UI', 14, 'bold'), bg='#28a745', fg='white',
                               padx=20, pady=12, command=self.connect_to_printer,
                               relief='solid', bd=2)
        connect_btn.pack(side=tk.LEFT, padx=(0, 15), fill=tk.X, expand=True)

        test_btn = tk.Button(button_frame, text="🧪 Test Print",
                            font=('Segoe UI', 14, 'bold'), bg='#ffc107', fg='white',
                            padx=20, pady=12, command=self.test_print,
                            relief='solid', bd=2)
        test_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Status display
        self.printer_status_label = tk.Label(container, text="Ready to detect printers",
                                           font=('Segoe UI', 12), bg='#2d2d2d', fg='#6c757d')
        self.printer_status_label.pack(pady=(15, 0))

    def create_touch_logo_size(self, parent):
        """Create touch-friendly logo size control"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(20, 0))

        tk.Label(size_frame, text="Logo Size", font=('Segoe UI', 14, 'bold'),
                bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 10))

        control_frame = tk.Frame(size_frame, bg='#2d2d2d')
        control_frame.pack(fill=tk.X)

        self.logo_size_var = tk.IntVar(value=100)

        # Large - button
        minus_btn = tk.Button(control_frame, text="−", font=('Segoe UI', 20, 'bold'),
                             bg='#dc3545', fg='white', width=3, height=2,
                             command=lambda: self.adjust_logo_size(-10),
                             relief='solid', bd=2)
        minus_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Current value display
        self.logo_size_label = tk.Label(control_frame, text="100%",
                                       font=('Segoe UI', 16, 'bold'),
                                       bg='#404040', fg='white', width=8, height=2,
                                       relief='solid', bd=2)
        self.logo_size_label.pack(side=tk.LEFT, padx=(0, 10))

        # Large + button
        plus_btn = tk.Button(control_frame, text="+", font=('Segoe UI', 20, 'bold'),
                            bg='#28a745', fg='white', width=3, height=2,
                            command=lambda: self.adjust_logo_size(10),
                            relief='solid', bd=2)
        plus_btn.pack(side=tk.LEFT)

    def create_touch_action_buttons(self, parent):
        """Create large touch-friendly action buttons"""
        # Spacer
        spacer = tk.Frame(parent, bg='#2d2d2d', height=30)
        spacer.pack(fill=tk.X)

        # Button container
        button_container = tk.Frame(parent, bg='#2d2d2d')
        button_container.pack(fill=tk.X, padx=20, pady=(0, 30))

        # Large action buttons
        reset_btn = tk.Button(button_container, text="🔄 Reset All Settings",
                             font=('Segoe UI', 16, 'bold'), bg='#ffc107', fg='white',
                             padx=40, pady=20, command=self.reset_settings,
                             relief='solid', bd=3)
        reset_btn.pack(side=tk.LEFT, padx=(0, 20), fill=tk.X, expand=True)

        save_btn = tk.Button(button_container, text="💾 Save Settings",
                            font=('Segoe UI', 16, 'bold'), bg='#28a745', fg='white',
                            padx=40, pady=20, command=self.save_settings,
                            relief='solid', bd=3)
        save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Touch control helper methods
    def set_paper_size(self, size):
        """Set paper size from touch button"""
        self.paper_size_var.set(size)



    def adjust_logo_size(self, delta):
        """Adjust logo size with touch buttons"""
        current = self.logo_size_var.get()
        new_size = max(25, min(200, current + delta))
        self.logo_size_var.set(new_size)
        self.logo_size_label.config(text=f"{new_size}%")

    def create_business_info_section(self, parent):
        """Create business information section"""
        section_frame = tk.LabelFrame(parent, text="🏢 Business Information",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 15))

        # Two-column layout for business fields
        business_container = tk.Frame(section_frame, bg='#2d2d2d')
        business_container.pack(fill=tk.X, padx=20, pady=15)

        left_business = tk.Frame(business_container, bg='#2d2d2d')
        left_business.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_business = tk.Frame(business_container, bg='#2d2d2d')
        right_business.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # Business fields
        self.create_modern_field(left_business, "Business Name", 'business_name')
        self.create_modern_field(left_business, "Business Address", 'business_address')
        self.create_modern_field(right_business, "Phone Number", 'business_phone')
        self.create_modern_field(right_business, "Header Text", 'header_text')

        # Footer text spans full width
        self.create_modern_field(business_container, "Footer Text", 'footer_text', full_width=True)

    def create_formatting_section(self, parent):
        """Create formatting options section"""
        section_frame = tk.LabelFrame(parent, text="🎨 Formatting Options",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 15))

        format_container = tk.Frame(section_frame, bg='#2d2d2d')
        format_container.pack(fill=tk.X, padx=20, pady=15)

        # Three-column layout for formatting
        left_format = tk.Frame(format_container, bg='#2d2d2d')
        left_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        center_format = tk.Frame(format_container, bg='#2d2d2d')
        center_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 10))

        right_format = tk.Frame(format_container, bg='#2d2d2d')
        right_format.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # Formatting controls
        self.create_paper_size_control(left_format)
        self.create_font_size_control(center_format)
        self.create_line_spacing_control(right_format)

    def create_modern_field(self, parent, label_text, field_name, full_width=False):
        """Create a modern styled form field"""
        if full_width:
            field_frame = tk.Frame(parent, bg='#2d2d2d')
            field_frame.pack(fill=tk.X, pady=(15, 0))
        else:
            field_frame = tk.Frame(parent, bg='#2d2d2d')
            field_frame.pack(fill=tk.X, pady=(0, 15))

        # Label with modern styling
        label = tk.Label(field_frame, text=label_text, font=('Segoe UI', 10, 'bold'),
                        bg='#2d2d2d', fg='#ff8c00')
        label.pack(anchor='w', pady=(0, 5))

        # Entry with modern styling
        entry_var = tk.StringVar(value=self.settings.get(field_name, ''))
        entry = tk.Entry(field_frame, textvariable=entry_var, font=('Segoe UI', 10),
                        bg='#404040', fg='white', insertbackground='white',
                        relief='solid', bd=1)
        entry.pack(fill=tk.X)

        # Store reference
        setattr(self, f'{field_name}_var', entry_var)

    def create_logo_section(self, parent):
        """Create logo management section"""
        section_frame = tk.LabelFrame(parent, text="🖼️ Logo Settings",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 15))

        logo_container = tk.Frame(section_frame, bg='#2d2d2d')
        logo_container.pack(fill=tk.X, padx=20, pady=15)

        # Logo selection
        logo_select_frame = tk.Frame(logo_container, bg='#2d2d2d')
        logo_select_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(logo_select_frame, text="Logo Image", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        logo_button_frame = tk.Frame(logo_select_frame, bg='#2d2d2d')
        logo_button_frame.pack(fill=tk.X)

        select_logo_btn = tk.Button(logo_button_frame, text="📁 Select Logo",
                                   font=('Segoe UI', 10, 'bold'), bg='#007bff', fg='white',
                                   padx=15, pady=5, command=self.select_logo)
        select_logo_btn.pack(side=tk.LEFT)

        self.logo_preview_label = tk.Label(logo_button_frame, text="No logo selected",
                                          font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        self.logo_preview_label.pack(side=tk.LEFT, padx=(15, 0))

        # Logo size control
        self.create_modern_logo_size_control(logo_container)

    def create_printer_section(self, parent):
        """Create printer settings section"""
        section_frame = tk.LabelFrame(parent, text="🖨️ Printer Settings",
                                     font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white',
                                     relief='solid', bd=1)
        section_frame.pack(fill=tk.X, pady=(0, 15))

        printer_container = tk.Frame(section_frame, bg='#2d2d2d')
        printer_container.pack(fill=tk.X, padx=20, pady=15)

        # Printer selection and controls
        printer_controls_frame = tk.Frame(printer_container, bg='#2d2d2d')
        printer_controls_frame.pack(fill=tk.X)

        # Printer selection
        printer_select_frame = tk.Frame(printer_controls_frame, bg='#2d2d2d')
        printer_select_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

        tk.Label(printer_select_frame, text="Select Printer", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        self.selected_printer_var = tk.StringVar(value=self.settings.get('selected_printer', 'Default Printer'))
        self.printer_combo = ttk.Combobox(printer_select_frame, textvariable=self.selected_printer_var,
                                         values=['Default Printer'], state='readonly',
                                         font=('Segoe UI', 9), width=25)
        self.printer_combo.pack(fill=tk.X)

        # Printer buttons
        printer_buttons_frame = tk.Frame(printer_controls_frame, bg='#2d2d2d')
        printer_buttons_frame.pack(side=tk.RIGHT)

        detect_btn = tk.Button(printer_buttons_frame, text="🔍 Detect",
                              font=('Segoe UI', 9, 'bold'), bg='#17a2b8', fg='white',
                              padx=10, pady=5, command=self.detect_printers)
        detect_btn.pack(side=tk.LEFT, padx=(0, 5))

        connect_btn = tk.Button(printer_buttons_frame, text="🔗 Connect",
                               font=('Segoe UI', 9, 'bold'), bg='#28a745', fg='white',
                               padx=10, pady=5, command=self.connect_to_printer)
        connect_btn.pack(side=tk.LEFT, padx=(0, 5))

        test_btn = tk.Button(printer_buttons_frame, text="🧪 Test",
                            font=('Segoe UI', 9, 'bold'), bg='#ffc107', fg='white',
                            padx=10, pady=5, command=self.test_print)
        test_btn.pack(side=tk.LEFT)

        # Printer status
        self.printer_status_label = tk.Label(printer_container, text="Ready to detect printers",
                                           font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        self.printer_status_label.pack(pady=(10, 0))

    def create_paper_size_control(self, parent):
        """Create modern paper size control"""
        tk.Label(parent, text="Paper Size", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        self.paper_size_var = tk.StringVar(value=self.settings.get('paper_size', '300x95'))
        available_sizes = self.get_available_paper_sizes()

        size_combo = ttk.Combobox(parent, textvariable=self.paper_size_var,
                                 values=available_sizes, state='readonly',
                                 font=('Segoe UI', 9))
        size_combo.pack(fill=tk.X)
        size_combo.bind('<<ComboboxSelected>>', lambda e: self.handle_size_selection())

    def create_font_size_control(self, parent):
        """Create modern font size control"""
        tk.Label(parent, text="Font Size", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 19))

        font_spinbox = tk.Spinbox(parent, from_=8, to=72, textvariable=self.font_size_var,
                                 font=('Segoe UI', 9), bg='#404040', fg='white',
                                 insertbackground='white', relief='solid', bd=1)
        font_spinbox.pack(fill=tk.X)

    def create_line_spacing_control(self, parent):
        """Create modern line spacing control"""
        tk.Label(parent, text="Line Spacing", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        self.line_spacing_var = tk.IntVar(value=self.settings.get('line_spacing', 0))

        spacing_spinbox = tk.Spinbox(parent, from_=0, to=20, textvariable=self.line_spacing_var,
                                   font=('Segoe UI', 9), bg='#404040', fg='white',
                                   insertbackground='white', relief='solid', bd=1)
        spacing_spinbox.pack(fill=tk.X)

        # Help text
        help_label = tk.Label(parent, text="Pixels between lines (0-20)",
                            font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        help_label.pack(pady=(5, 0))

    def create_modern_logo_size_control(self, parent):
        """Create modern logo size control"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(15, 0))

        tk.Label(size_frame, text="Logo Size", font=('Segoe UI', 10, 'bold'),
                bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', pady=(0, 5))

        # Size slider with value display
        slider_container = tk.Frame(size_frame, bg='#2d2d2d')
        slider_container.pack(fill=tk.X)

        self.logo_size_var = tk.IntVar(value=100)
        self.size_value_label = tk.Label(slider_container, text="100%",
                                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.size_value_label.pack(side=tk.RIGHT)

        self.logo_size_slider = tk.Scale(slider_container, from_=25, to=200,
                                        orient=tk.HORIZONTAL, variable=self.logo_size_var,
                                        font=('Segoe UI', 9), bg='#404040', fg='white',
                                        highlightthickness=0, length=300,
                                        command=self.update_size_label)
        self.logo_size_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

    def create_modern_action_buttons(self, parent):
        """Create modern action buttons positioned at bottom right"""
        # Spacer to push buttons to bottom
        spacer = tk.Frame(parent, bg='#1a1a1a', height=20)
        spacer.pack(fill=tk.X)

        # Button container
        button_container = tk.Frame(parent, bg='#1a1a1a')
        button_container.pack(fill=tk.X, pady=(20, 0))

        # Right-aligned button frame
        button_frame = tk.Frame(button_container, bg='#1a1a1a')
        button_frame.pack(side=tk.RIGHT)

        # Reset button
        reset_btn = tk.Button(button_frame, text="🔄 Reset Settings",
                             font=('Segoe UI', 11, 'bold'), bg='#ffc107', fg='white',
                             padx=20, pady=10, command=self.reset_settings,
                             relief='solid', bd=1)
        reset_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Save button
        save_btn = tk.Button(button_frame, text="💾 Save Settings",
                            font=('Segoe UI', 11, 'bold'), bg='#28a745', fg='white',
                            padx=25, pady=10, command=self.save_settings,
                            relief='solid', bd=1)
        save_btn.pack(side=tk.LEFT)

    def create_form_field(self, parent, label_text, field_name):
        """Create a form field"""
        field_frame = tk.Frame(parent, bg='#2d2d2d')
        field_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(field_frame, text=label_text, font=('Segoe UI', 10, 'bold'),
                        bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Entry
        entry_var = tk.StringVar(value=self.settings.get(field_name, ''))
        entry = tk.Entry(field_frame, textvariable=entry_var, font=('Segoe UI', 10), width=50)
        entry.pack(fill=tk.X)

        # Store reference
        setattr(self, f'{field_name}_var', entry_var)

    def create_logo_field(self, parent):
        """Create logo selection field"""
        logo_frame = tk.Frame(parent, bg='#2d2d2d')
        logo_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(logo_frame, text=self.app.get_text('logo'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Logo selection frame
        logo_select_frame = tk.Frame(logo_frame, bg='#2d2d2d')
        logo_select_frame.pack(fill=tk.X)

        # Select button
        select_btn = tk.Button(logo_select_frame, text=self.app.get_text('select_logo'),
                              font=('Segoe UI', 9), bg='#007bff', fg='white',
                              padx=15, pady=5, command=self.select_logo)
        select_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Logo preview
        self.logo_preview_label = tk.Label(logo_select_frame, text=self.app.get_text('no_logo_selected'),
                                          font=('Segoe UI', 9), bg='#2d2d2d', fg='#6c757d')
        self.logo_preview_label.pack(side=tk.LEFT)

        # Load existing logo if any
        if self.settings.get('logo_image'):
            self.logo_preview_label.config(text="Logo loaded")

    def create_paper_size_field(self, parent):
        """Create paper size selection field with printer detection"""
        size_frame = tk.Frame(parent, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(size_frame, text=self.app.get_text('paper_size'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Printer detection button
        detect_frame = tk.Frame(size_frame, bg='#2d2d2d')
        detect_frame.pack(fill=tk.X, pady=(0, 5))

        detect_btn = tk.Button(detect_frame, text="🖨️ Detect Printers",
                              font=('Segoe UI', 9, 'bold'), bg='#17a2b8', fg='white',
                              padx=10, pady=3, command=self.detect_printers)
        detect_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.printer_status_label = tk.Label(detect_frame, text="Click to detect connected printers",
                                           font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        self.printer_status_label.pack(side=tk.LEFT)

        # Printer selection
        printer_select_frame = tk.Frame(size_frame, bg='#2d2d2d')
        printer_select_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Label(printer_select_frame, text="Select Printer:",
                font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white').pack(anchor='w', pady=(0, 2))

        self.selected_printer_var = tk.StringVar(value=self.settings.get('selected_printer', 'Default Printer'))
        self.printer_combo = ttk.Combobox(printer_select_frame, textvariable=self.selected_printer_var,
                                         values=['Default Printer'], state='readonly',
                                         font=('Segoe UI', 9), width=30)
        self.printer_combo.pack(side=tk.LEFT, padx=(0, 10))

        connect_btn = tk.Button(printer_select_frame, text="Connect",
                               font=('Segoe UI', 9, 'bold'), bg='#28a745', fg='white',
                               padx=10, pady=2, command=self.connect_to_printer)
        connect_btn.pack(side=tk.LEFT)

        # Test print button on new line under connect button
        test_frame = tk.Frame(size_frame, bg='#2d2d2d')
        test_frame.pack(fill=tk.X, pady=(5, 0))

        test_print_btn = tk.Button(test_frame, text="Test Print",
                                  font=('Segoe UI', 9, 'bold'), bg='#ffc107', fg='white',
                                  padx=10, pady=2, command=self.test_print)
        test_print_btn.pack(anchor='w')

        # Size selection
        self.paper_size_var = tk.StringVar(value=self.settings.get('paper_size', '300x95'))

        # Get available sizes including detected printer sizes
        available_sizes = self.get_available_paper_sizes()

        size_combo = ttk.Combobox(size_frame, textvariable=self.paper_size_var,
                                 values=available_sizes, state='readonly',
                                 font=('Segoe UI', 10), width=25)
        size_combo.pack(anchor='w', pady=(5, 0))
        size_combo.bind('<<ComboboxSelected>>', lambda e: self.handle_size_selection())

        # Custom size fields (initially hidden)
        self.custom_size_frame = tk.Frame(size_frame, bg='#2d2d2d')

        custom_label = tk.Label(self.custom_size_frame, text="Custom Size:",
                               font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white')
        custom_label.pack(anchor='w', pady=(5, 2))

        custom_input_frame = tk.Frame(self.custom_size_frame, bg='#2d2d2d')
        custom_input_frame.pack(fill=tk.X)

        tk.Label(custom_input_frame, text="Width:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 5))

        self.custom_width_var = tk.StringVar(value="300")
        width_spinbox = tk.Spinbox(custom_input_frame, from_=100, to=5000, width=8,
                                  textvariable=self.custom_width_var, font=('Segoe UI', 9))
        width_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        tk.Label(custom_input_frame, text="Height:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT, padx=(0, 5))

        self.custom_height_var = tk.StringVar(value="95")
        height_spinbox = tk.Spinbox(custom_input_frame, from_=50, to=5000, width=8,
                                   textvariable=self.custom_height_var, font=('Segoe UI', 9))
        height_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        apply_custom_btn = tk.Button(custom_input_frame, text="Apply",
                                    font=('Segoe UI', 9, 'bold'), bg='#28a745', fg='white',
                                    padx=10, pady=2, command=self.apply_custom_size)
        apply_custom_btn.pack(side=tk.LEFT)

    def create_font_size_field(self, parent):
        """Create font size field"""
        font_frame = tk.Frame(parent, bg='#2d2d2d')
        font_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(font_frame, text=self.app.get_text('font_size'),
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Font size spinbox
        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 19))

        font_spinbox = tk.Spinbox(font_frame, from_=8, to=72, textvariable=self.font_size_var,
                                 font=('Segoe UI', 10), width=10)
        font_spinbox.pack(anchor='w')

    def create_line_spacing_field(self, parent):
        """Create line spacing field"""
        spacing_frame = tk.Frame(parent, bg='#2d2d2d')
        spacing_frame.pack(fill=tk.X, pady=(0, 15))

        # Label
        label = tk.Label(spacing_frame, text="Line Spacing",
                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        label.pack(anchor='w', pady=(0, 5))

        # Line spacing spinbox
        self.line_spacing_var = tk.IntVar(value=self.settings.get('line_spacing', 0))  # Changed default from 8 to 0

        spacing_spinbox = tk.Spinbox(spacing_frame, from_=0, to=20, textvariable=self.line_spacing_var,
                                   font=('Segoe UI', 10), width=10)
        spacing_spinbox.pack(anchor='w')

        # Help text
        help_label = tk.Label(spacing_frame, text="Pixels between lines (0-20)",
                            font=('Segoe UI', 8), bg='#2d2d2d', fg='#6c757d')
        help_label.pack(anchor='w', pady=(2, 0))





    def load_settings(self):
        """Load receipt settings from database and populate form fields"""
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM receipt_settings WHERE id = 1")
            result = c.fetchone()

            if result:
                # Convert sqlite3.Row to dictionary properly
                self.settings = {}
                for key in result.keys():
                    self.settings[key] = result[key]

                # Populate form fields with loaded values
                if hasattr(self, 'business_name_var'):
                    self.business_name_var.set(self.settings.get('business_name', ''))
                if hasattr(self, 'business_address_var'):
                    self.business_address_var.set(self.settings.get('business_address', ''))
                if hasattr(self, 'business_phone_var'):
                    self.business_phone_var.set(self.settings.get('business_phone', ''))
                if hasattr(self, 'header_text_var'):
                    self.header_text_var.set(self.settings.get('header_text', ''))
                if hasattr(self, 'footer_text_var'):
                    self.footer_text_var.set(self.settings.get('footer_text', ''))
                if hasattr(self, 'paper_size_var'):
                    self.paper_size_var.set(self.settings.get('paper_size', '300x95'))
                if hasattr(self, 'font_size_var'):
                    self.font_size_var.set(self.settings.get('font_size', 19))
                if hasattr(self, 'line_spacing_var'):
                    self.line_spacing_var.set(self.settings.get('line_spacing', 0))  # Changed default from 8 to 0
                if hasattr(self, 'selected_printer_var'):
                    self.selected_printer_var.set(self.settings.get('selected_printer', 'Default Printer'))

                # Load logo file path first (preferred method)
                logo_file_path = self.settings.get('logo_file_path')
                if logo_file_path and hasattr(self, 'logo_preview_label'):
                    import os
                    if os.path.exists(logo_file_path):
                        # File exists, use it
                        self.logo_file_path = logo_file_path
                        filename = os.path.basename(logo_file_path)
                        self.logo_preview_label.config(text=f"Logo: {filename} (from file)")
                        print(f"Loaded logo file path: {logo_file_path}")
                    else:
                        # File doesn't exist, clear the path
                        print(f"Logo file not found: {logo_file_path}")
                        logo_file_path = None

                # Load logo base64 data (fallback or backup)
                if self.settings.get('logo_image'):
                    logo_data = self.settings['logo_image']

                    # Handle both old raw bytes and new base64 format
                    if isinstance(logo_data, bytes):
                        # Old format: convert raw bytes to base64 and save back to database
                        import base64
                        self.logo_image = base64.b64encode(logo_data).decode('utf-8')
                        print(f"Converted old logo format (bytes length: {len(logo_data)}) to base64 (length: {len(self.logo_image)})")

                        # Save the converted format back to database immediately
                        try:
                            conn = get_db_connection()
                            c = conn.cursor()
                            c.execute("UPDATE receipt_settings SET logo_image = ? WHERE id = 1", (self.logo_image,))
                            conn.commit()
                            conn.close()
                            print("Updated database with base64 logo format")
                        except Exception as e:
                            print(f"Error updating logo format in database: {e}")

                    elif isinstance(logo_data, str):
                        # New format: already base64 encoded
                        self.logo_image = logo_data
                        print(f"Loaded logo in base64 format (length: {len(logo_data)})")
                    else:
                        print(f"Unknown logo format: {type(logo_data)}")
                        self.logo_image = None

                    # Only show "loaded from database" if no file path was found
                    if hasattr(self, 'logo_preview_label') and self.logo_image and not logo_file_path:
                        self.logo_preview_label.config(text=f"Logo loaded from database (base64 length: {len(self.logo_image)})")
                        print(f"Logo loaded successfully, base64 length: {len(self.logo_image)}")

                # Load logo size settings
                if hasattr(self, 'logo_size_var'):
                    self.logo_size_var.set(self.settings.get('logo_size', 100))  # Default 100% size

            else:
                # Default settings
                self.settings = {
                    'business_name': 'Your Business Name',
                    'business_address': 'Your Business Address',
                    'business_phone': 'Your Phone Number',
                    'header_text': '',
                    'footer_text': 'Thank you for your business!',
                    'paper_size': '300x95',
                    'font_size': 19,
                    'line_spacing': 0,  # Changed from 8 to 0 for tighter spacing
                    'selected_printer': 'Default Printer',
                    'logo_image': None
                }
        finally:
            conn.close()

    def select_logo(self):
        """Select logo image"""
        file_path = filedialog.askopenfilename(
            title=self.app.get_text('select_logo'),
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        if file_path:
            try:
                # Load and process image
                image = Image.open(file_path)

                # Convert to grayscale first
                if image.mode != 'L':
                    image = image.convert('L')

                # Convert to black and white for receipt printing
                threshold = 128
                image = image.point(lambda x: 0 if x < threshold else 255, '1')

                # Calculate aspect ratio and resize appropriately
                original_width, original_height = image.size
                aspect_ratio = original_width / original_height

                # Target size for receipt logo (not too big)
                max_width, max_height = 200, 80

                if aspect_ratio > 1:  # Wider than tall
                    new_width = min(max_width, original_width)
                    new_height = int(new_width / aspect_ratio)
                else:  # Taller than wide
                    new_height = min(max_height, original_height)
                    new_width = int(new_height * aspect_ratio)

                # Resize with high quality
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Store the file path for direct loading
                self.logo_file_path = file_path

                # Convert to bytes for storage and encode as base64 (backup compatibility)
                import io
                import base64
                img_bytes = io.BytesIO()
                image.save(img_bytes, format='PNG')
                # Store as base64 encoded string for compatibility with receipt generator
                self.logo_image = base64.b64encode(img_bytes.getvalue()).decode('utf-8')
                print(f"Logo file path: {file_path}")
                print(f"Logo encoded to base64, length: {len(self.logo_image)}")

                # Update preview label
                filename = file_path.split('/')[-1].split('\\')[-1]  # Handle both / and \ separators
                self.logo_preview_label.config(text=f"Logo: {filename} ({new_width}x{new_height})")



            except Exception as e:
                messagebox.showerror(self.app.get_text('error'), f"Error loading logo: {e}")

    def create_logo_size_fields(self, parent):
        """Create logo size adjustment fields with slider"""
        # Logo size frame
        logo_size_frame = tk.LabelFrame(parent, text="Logo Size Settings",
                                       font=('Segoe UI', 10, 'bold'), bg='#2d2d2d')
        logo_size_frame.pack(fill=tk.X, pady=5)

        # Create inner frame for better layout
        inner_frame = tk.Frame(logo_size_frame, bg='#2d2d2d')
        inner_frame.pack(fill=tk.X, padx=10, pady=10)

        # Logo size label and value display
        size_header_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        size_header_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(size_header_frame, text="Logo Size:",
                font=('Segoe UI', 10, 'bold'), bg='#2d2d2d').pack(side=tk.LEFT)

        self.logo_size_var = tk.IntVar(value=100)
        self.size_value_label = tk.Label(size_header_frame, text="100%",
                                        font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#007bff')
        self.size_value_label.pack(side=tk.RIGHT)

        # Logo size slider
        slider_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        slider_frame.pack(fill=tk.X, pady=5)

        # Size labels
        tk.Label(slider_frame, text="Small\n(50%)", font=('Segoe UI', 8),
                bg='#2d2d2d', fg='#6c757d').pack(side=tk.LEFT)

        # Slider - WIDER
        self.logo_size_slider = tk.Scale(slider_frame, from_=25, to=200,
                                        orient=tk.HORIZONTAL, variable=self.logo_size_var,
                                        font=('Segoe UI', 9), bg='#2d2d2d', fg='#333',
                                        highlightthickness=0, length=400,  # Increased from 300
                                        command=self.update_size_label)
        self.logo_size_slider.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=10)

        tk.Label(slider_frame, text="Large\n(200%)", font=('Segoe UI', 8),
                bg='#2d2d2d', fg='#6c757d').pack(side=tk.RIGHT)

        # Preset buttons
        preset_frame = tk.Frame(inner_frame, bg='#2d2d2d')
        preset_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Label(preset_frame, text="Quick presets:", font=('Segoe UI', 9),
                bg='#2d2d2d').pack(side=tk.LEFT)

        presets = [("Small", 75), ("Default", 100), ("Large", 150)]
        for name, value in presets:
            btn = tk.Button(preset_frame, text=name,
                           command=lambda v=value: self.set_logo_size(v),
                           font=('Segoe UI', 8), bg='#404040', relief='ridge',
                           padx=8, pady=2)
            btn.pack(side=tk.LEFT, padx=2)

        # Info label
        info_label = tk.Label(inner_frame,
                             text="💡 Tip: Drag slider or use presets to adjust logo size. 100% = default size",
                             font=('Segoe UI', 9, 'italic'), bg='#2d2d2d', fg='#6c757d')
        info_label.pack(pady=(5, 0))

    def update_size_label(self, value):
        """Update the size percentage label"""
        self.size_value_label.config(text=f"{value}%")

    def update_font_size_label(self, value):
        """Update the font size label"""
        self.font_size_value_label.config(text=f"{value}pt")

    def update_line_spacing_label(self, value):
        """Update the line spacing label"""
        self.line_spacing_value_label.config(text=f"{value}px")

    def set_logo_size(self, value):
        """Set logo size to specific value"""
        self.logo_size_var.set(value)
        self.update_size_label(value)








    def save_settings(self):
        """Save receipt settings"""
        try:
            # Collect form data
            settings_data = {
                'business_name': self.business_name_var.get(),
                'business_address': self.business_address_var.get(),
                'business_phone': self.business_phone_var.get(),
                'header_text': self.header_text_var.get(),
                'footer_text': self.footer_text_var.get(),
                'paper_size': self.paper_size_var.get(),
                'font_size': self.font_size_var.get(),
                'line_spacing': self.line_spacing_var.get(),
                'selected_printer': getattr(self, 'selected_printer_var', tk.StringVar()).get(),
                'logo_image': getattr(self, 'logo_image', None),
                'logo_file_path': getattr(self, 'logo_file_path', None),
                'logo_size': getattr(self, 'logo_size_var', tk.IntVar(value=100)).get()
            }

            conn = get_db_connection()
            try:
                c = conn.cursor()

                # Update or insert settings
                c.execute("SELECT id FROM receipt_settings WHERE id = 1")
                if c.fetchone():
                    # Update existing
                    c.execute("""
                        UPDATE receipt_settings SET
                        business_name=?, business_address=?, business_phone=?,
                        header_text=?, footer_text=?, logo_image=?, logo_file_path=?,
                        paper_size=?, font_size=?, line_spacing=?, selected_printer=?,
                        logo_size=?
                        WHERE id=1
                    """, (settings_data['business_name'], settings_data['business_address'],
                         settings_data['business_phone'], settings_data['header_text'],
                         settings_data['footer_text'], settings_data['logo_image'], settings_data['logo_file_path'],
                         settings_data['paper_size'], settings_data['font_size'],
                         settings_data['line_spacing'], settings_data['selected_printer'],
                         settings_data['logo_size']))
                else:
                    # Insert new
                    c.execute("""
                        INSERT INTO receipt_settings (id, business_name, business_address, business_phone,
                        header_text, footer_text, logo_image, logo_file_path, paper_size, font_size, line_spacing, selected_printer,
                        logo_size)
                        VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (settings_data['business_name'], settings_data['business_address'],
                         settings_data['business_phone'], settings_data['header_text'],
                         settings_data['footer_text'], settings_data['logo_image'], settings_data['logo_file_path'],
                         settings_data['paper_size'], settings_data['font_size'],
                         settings_data['line_spacing'], settings_data['selected_printer'],
                         settings_data['logo_size']))

                conn.commit()
                messagebox.showinfo(self.app.get_text('success'),
                                  self.app.get_text('settings_saved'))

            finally:
                conn.close()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_save_settings')}: {e}")

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno(self.app.get_text('confirm'),
                              self.app.get_text('confirm_reset')):
            # Reset form fields
            self.business_name_var.set(self.app.get_text('default_business_name'))
            self.business_address_var.set(self.app.get_text('default_business_address'))
            self.business_phone_var.set(self.app.get_text('default_business_phone'))
            self.header_text_var.set(self.app.get_text('default_header_text'))
            self.footer_text_var.set(self.app.get_text('default_footer_text'))

            self.paper_size_var.set('300x95')
            self.font_size_var.set(19)

            self.logo_image = None
            self.logo_preview_label.config(text=self.app.get_text('no_logo_selected'))



    def detect_printers(self):
        """Detect connected printers and their capabilities"""
        try:
            import win32print
            self.printer_status_label.config(text="Detecting printers...", fg='#ffc107')
            self.root.update()

            # Get list of printers
            printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)

            detected_sizes = []
            printer_names = ['Default Printer']
            printer_info = []

            for printer in printers:
                printer_name = printer[2]
                printer_names.append(printer_name)

                try:
                    # Get printer capabilities
                    handle = win32print.OpenPrinter(printer_name)

                    # Detect printer type and suggest sizes
                    if 'thermal' in printer_name.lower() or 'receipt' in printer_name.lower():
                        detected_sizes.extend(['80x297 (Thermal)', '58x297 (Thermal)', '80x200 (Thermal)'])
                        printer_type = "Thermal"
                    elif 'label' in printer_name.lower():
                        detected_sizes.extend(['100x150 (Label)', '100x100 (Label)'])
                        printer_type = "Label"
                    else:
                        detected_sizes.extend(['210x297 (A4)', '216x279 (Letter)'])
                        printer_type = "Standard"

                    printer_info.append(f"✓ {printer_name} ({printer_type})")
                    win32print.ClosePrinter(handle)

                except Exception as e:
                    printer_info.append(f"✗ {printer_name} (Error: {str(e)[:20]}...)")

            if len(printer_names) > 1:  # More than just "Default Printer"
                # Update printer combo
                self.printer_combo['values'] = printer_names
                self.detected_printer_sizes = list(set(detected_sizes))

                status_text = f"Found {len(printers)} printer(s)"
                self.printer_status_label.config(text=status_text, fg='#28a745')

                # Show printer info dialog
                info_text = "Detected Printers:\n" + "\n".join(printer_info[:5])
                if len(printer_info) > 5:
                    info_text += f"\n... and {len(printer_info) - 5} more"

                messagebox.showinfo("Printer Detection", info_text)

                # Refresh size combo
                self.refresh_paper_sizes()
            else:
                self.printer_status_label.config(text="No printers detected", fg='#dc3545')

        except ImportError:
            self.printer_status_label.config(text="Printer detection not available (Windows only)", fg='#ffc107')
        except Exception as e:
            self.printer_status_label.config(text=f"Detection failed: {str(e)[:30]}...", fg='#dc3545')

    def connect_to_printer(self):
        """Connect to selected printer"""
        selected_printer = self.selected_printer_var.get()

        if selected_printer == "Default Printer":
            self.printer_status_label.config(text="Using system default printer", fg='#28a745')
            self.connected_printer = None
            return

        try:
            import win32print
            # Test connection to printer
            handle = win32print.OpenPrinter(selected_printer)
            win32print.ClosePrinter(handle)

            self.connected_printer = selected_printer
            self.printer_status_label.config(text=f"Connected to {selected_printer}", fg='#28a745')

            # Auto-detect paper size for this printer
            self.auto_detect_paper_size(selected_printer)

        except Exception as e:
            self.printer_status_label.config(text=f"Connection failed: {str(e)[:30]}...", fg='#dc3545')
            self.connected_printer = None

    def auto_detect_paper_size(self, printer_name):
        """Auto-detect optimal paper size for connected printer"""
        try:
            # Suggest paper size based on printer name/type
            if 'thermal' in printer_name.lower() or 'receipt' in printer_name.lower():
                if '80' in printer_name.lower():
                    self.paper_size_var.set('80x297 (80mm Thermal)')
                elif '58' in printer_name.lower():
                    self.paper_size_var.set('58x297 (58mm Thermal)')
                else:
                    self.paper_size_var.set('300x95 (Default Thermal)')
            elif 'label' in printer_name.lower():
                self.paper_size_var.set('100x150 (Label)')
            else:
                self.paper_size_var.set('2480x3508 (A4)')



        except Exception:
            pass  # Fail silently, keep current size

    def test_print(self):
        """Print a test receipt to verify printer connection"""
        try:
            from datetime import datetime

            # Create test receipt content
            test_content = self.generate_test_receipt()

            # Print using connected printer or default
            printer_name = getattr(self, 'connected_printer', None)

            if self.print_receipt_content(test_content, printer_name):
                messagebox.showinfo("Test Print", "Test receipt sent to printer successfully!")
            else:
                messagebox.showerror("Test Print", "Failed to print test receipt")

        except Exception as e:
            messagebox.showerror("Test Print", f"Print test failed: {e}")

    def generate_test_receipt(self):
        """Generate test receipt content"""
        from datetime import datetime

        business_name = getattr(self, 'business_name_var', tk.StringVar()).get().strip()
        business_address = getattr(self, 'business_address_var', tk.StringVar()).get().strip()
        business_phone = getattr(self, 'business_phone_var', tk.StringVar()).get().strip()

        current_time = datetime.now()

        # Build content dynamically - only include non-empty fields
        content_lines = []

        # Add business info only if not empty
        if business_name:
            content_lines.append(business_name.upper())
        if business_address:
            content_lines.append(business_address)
        if business_phone:
            content_lines.append(f"Tel: {business_phone}")

        # Add spacing if we have business info
        if business_name or business_address or business_phone:
            content_lines.append("")

        # Add test receipt content
        content_lines.extend([
            f"Date: {current_time.strftime('%d/%m/%Y')}",
            f"Time: {current_time.strftime('%H:%M:%S')}",
            "="*40,
            "",
            "TEST RECEIPT",
            "This is a test print to verify",
            "printer connection and formatting.",
            "",
            "="*40,
            "Receipt #: TEST001",
            "="*40,
            "",
            "Thank you for testing!",
            ""
        ])

        return "\n".join(content_lines)

    def print_receipt_content(self, content, printer_name=None):
        """Print receipt content to specified printer with proper line spacing"""
        try:
            import win32print
            import win32ui
            import win32con

            # Use specified printer or default
            if printer_name:
                printer = printer_name
            else:
                printer = win32print.GetDefaultPrinter()

            # Create device context
            hdc = win32ui.CreateDC()
            hdc.CreatePrinterDC(printer)

            # Get printer capabilities for better scaling
            try:
                # Get printer resolution
                dpi_y = hdc.GetDeviceCaps(win32con.LOGPIXELSY)
                # Scale font size based on DPI (default 96 DPI)
                font_scale = max(dpi_y / 96.0, 1.0)
            except:
                font_scale = 1.0
                dpi_y = 96

            # Start document
            hdc.StartDoc("POS Test Receipt")
            hdc.StartPage()

            # Get current settings
            font_size = int(getattr(self, 'font_size_var', tk.IntVar(value=19)).get() * font_scale)
            line_spacing = getattr(self, 'line_spacing_var', tk.IntVar(value=3)).get()

            # Calculate proper line height with spacing
            line_spacing_printer = int(line_spacing * font_scale * (dpi_y / 96.0))
            line_height = font_size + line_spacing_printer + 10  # Extra padding for clarity

            # Create font for better text rendering
            try:
                font = win32ui.CreateFont({
                    'name': 'Courier New',
                    'height': font_size,
                    'weight': win32con.FW_BOLD
                })
                hdc.SelectObject(font)
            except:
                pass  # Use default font if creation fails

            # Print content line by line with proper spacing
            y_pos = 100  # Start position
            x_pos = 20   # Reduced left margin for less empty space

            lines = content.strip().split('\n')
            for line in lines:
                if line.strip():  # Only print non-empty lines
                    try:
                        hdc.TextOut(x_pos, y_pos, line)
                    except:
                        # Fallback for encoding issues
                        try:
                            hdc.TextOut(x_pos, y_pos, line.encode('utf-8', 'ignore').decode('utf-8'))
                        except:
                            hdc.TextOut(x_pos, y_pos, str(line))

                    y_pos += line_height
                else:
                    # Empty line - add half spacing
                    y_pos += line_height // 2

            # End document
            hdc.EndPage()
            hdc.EndDoc()
            hdc.DeleteDC()

            return True

        except Exception as e:
            print(f"Print error: {e}")
            return False

    def get_available_paper_sizes(self):
        """Get list of available paper sizes"""
        default_sizes = [
            '300x95 (Default Thermal)',
            '2480x3508 (A4)',
            '80x297 (80mm Thermal)',
            '58x297 (58mm Thermal)',
            '216x279 (Letter)',
            'Custom'
        ]

        # Add detected printer sizes if any
        detected = getattr(self, 'detected_printer_sizes', [])
        all_sizes = default_sizes + [f"{size} (Detected)" for size in detected]

        return all_sizes

    def refresh_paper_sizes(self):
        """Refresh the paper size combobox with detected sizes"""
        try:
            # Find the combobox and update its values
            for widget in self.root.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Combobox):
                            child['values'] = self.get_available_paper_sizes()
                            break
        except:
            pass

    def handle_size_selection(self):
        """Handle paper size selection"""
        selected = self.paper_size_var.get()

        if 'Custom' in selected:
            # Show custom size fields
            self.custom_size_frame.pack(fill=tk.X, pady=(10, 0))
        else:
            # Hide custom size fields
            self.custom_size_frame.pack_forget()



    def apply_custom_size(self):
        """Apply custom paper size"""
        try:
            width = int(self.custom_width_var.get())
            height = int(self.custom_height_var.get())

            if width < 100 or height < 50:
                messagebox.showerror("Error", "Minimum size: 100x50 pixels")
                return

            custom_size = f"{width}x{height}"
            self.paper_size_var.set(custom_size)
            self.custom_size_frame.pack_forget()


            messagebox.showinfo("Success", f"Custom size applied: {custom_size}")

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for width and height")
