# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support

import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['LTgrGRp+MisLZUp8bjwFdX8qdWg/ARJgOx15chk1PDkoEng9BgcpAnltLnZiEzYBb2AVbiplXl0MC2IeEBYsFhoKC2IfJgcvCQBt', 'Gi0+MnA6ISEXNDJ+JzMBEioANnoIHBIKZR0wH3IADzotCn50ag1/VkYCLzQoLmJyMBYOLSUlKhoNF2koJBVxeSc+LzkLdh8kcz8q', 'KhEVJj5qLn0nNylqJAh1GHwDMDZrIC50JwcidjYdACcfC3RCC3FqJhswNQw2fG4PJgQjLSRjA2s7cnsKPyELEgpgEiAyYgt4Fw8i', 'MSoqfzR6IQ4mLBQmJjwqB2JeBmsgehMzJXApMi06Nn0+OAxgKSYHbHoTZ2tlYwsMcnMsBgwgLSYhYBxFAltAPTA2MgQ7CBhqKC99', 'XgRLTi4jHH8jIxohHXobPnI1c20VFAIPKTgoYzkoFDkOIHcgZhZ4aGQMYSVkVH1SPBEqIgg2GCUGGyM+Myo2DHYRKXANESkfPWYi', 'GzV7ETc8OXs+ICcyKRh4ORYQfTURFwZ3QFwSPABtKyRnPTIlYit1KxICKhwfBjklAGURHyE7bQsTMicFGmc/dBUyfwMKVxk5OiUz', 'JSh6IhwXAC4SDwkgDWwoCh88ZDE2MDEXOT1kHjEHDzctW38HUi4QOichCzI+MiY2YTcVJw8MKXExNSZ9JDAaGBgcHQUxImQnLGQg', 'KQsgLQceLXQkLiNsfzkHOnEqPg58PW4nCxgXcFF+VwscBDcRAg0jBxM0YQJ4CmJ2aH4xKgAOEygDCioycRs5Ynh2FSU1IQlDZ317', 'bAErOi9xDRAUEBh9HzI2dGQneSgqIXcOFEFKU3snYD4AMzc5PxIEZSUxdHMMMCoqchl/N2JsYjB6Jj0oGTs7DwUmd2xvUV9fVzts', 'YxYTFnsZL2ktHCxtBAZadTskBA07NgcdJyp6MX0cAwArMSwHGiZ1JGZiAQV/LwUecBw7My81LTxic1AfEiUINxYXMWMJJh5mFgsM', 'YyErFXUZaCtxGTE4KBwYHCceMw1yIjtgGCZ7NGFmU2d+QmoUPCU2ID5jIDsmN3FnJ24tBTA7diAGMSkoOHoWJnFsejYoBTwyPylQ', 'Gxp6HhhgMmYHFTURaycYCjUAZy0ADCYrGxoIan4ONTkNfHYtFx0CMS12RGtFFRgtHygDDzcSdjx9cgsbNj0MLg82EigoGmUaPTgo', 'VQBMLAdnEmoucAUuICUVIQ4WGXY7cSsaP2ohBSU3Ni1wd2diCDsQLwASZndfeVhqNg4CYAcwPzR6LTQ8NSMjcgxnOHESNCJuNRoI', 'JH0/bWUWCRsNKzAreDkZIAQvGXdpUR8tEgYwaxMuFgNxA2EfGXUALTwgMxURJ2AaFmM2A25oFjMCLHQ+Ax81e2VTfisSHnslK2s0', 'FSkkCG9kRnFsOBohP30QDxxyDxlmL34TFCsnJSUTJhAwCTYiAns/cmd6GicoIz0tEwMDfFILJz8zFCg9OD0FICc9Ii0uDTAedwE+', 'IAMhOR17FnYSJDQDDR8HPCRwGnRIRVNfbBYbGSU3JxEOIGAdLAQqMDQVOHAPcjwzJxMLKGE0FjV4DilwEXYLNwcHZRs8bCYTPDVq', 'BwodcjM0YCh3HgQ6DC0nKjwJcjY8VAgLfQYYOWZjM3RqB3MlIXwuFyw0CgtuKywnHxNiIi4sPycyfycWa3g3YRsFe2dmcD4DMgMk', 'ZBdgLHYTZGI9ezwbHRYbfH1XBzUAAWdlcjo5IzZiGCAFDiMDaTgwJn8xNC4jKx4yNSE7HiwPNTEWDQp/H1tSajoAJRwYcAkTFSRq', 'ajV5F2wPNyIZGjclBA08al9FVWM9EXtmHjE2AxwSGDsIY3ccYB0QdXoJMnkrJjYhFyAuEScOdi0dPy8mWAhUdzAcDjYLNj02Biti', 'Kn1uYhMvfzYWBBQgEmIzYDYSExZxOBYcBT0vL0YIWkQMYjxtZiM+GjZxLx0CFiVxNisjGXF/Fj4uKREsEBRzYTAbCT4OJmEUXWRx', 'dSplDjF6Y2oGDxcdGBJ0CXN1ADMEEx5hCxQxPCB5CQl0bAZdSEVMNWMALDcHMxcxKWw2cw4jbDQpYywKIHY2Czk9Fy13KyIgNTYP', 'FQcuKQ4rGnZDSlAPZCo+PCIvJWotYSUVPQ9sYRslCgklDBRsHBArBT1sAjAbAw1kFmoSZwZBY25iYzVjNx4XLjsgKHE5OBI1HDgP', 'cjUAVVMAGGI1PT4kNQcfcTs5Pzg0Em8JJzIlCQ9mbQkXPiQzaBA7DjtwBjcaBkACfXsRHw4yNGoPPj8UEGAkenYjA2d9ES8THCIG', 'cmFpYUlgcDMGKDprNilqNXAaZjEVLC40OxpzNy8GNmt/B3g2CC5hIHZ8LB8zMQ9EGwJALQMpZDAPFTwNNiYwbmM2a2kxOTE6DTd5', 'FSp2LiQpJxRzKXZ0VwBmThZgLzokFRF8fS4FGjcGCm8RMAckIHouFG89GzlkMAd/eWAAbHkSamZIeEpGZh09NzBxJjgJExkAIB8E', 'Qm0eHnlmJjl0KSETYxgVdCgCbxsDKRQxaiAGYTcIECYgE354Nw8ACg87SwNqeT0mLSw4LwdmNyV+P3J4CWI/GAR1BRsjZhpnNn9n', 'cDc3ICkQHHUkam89N34WdiYrGDM9B34lNXBkfX0UbwQdOC5GXnFANWYcHjA3HDcIGgIxMAF2Yxt0HxsuHHQxLAhiP2U2BxMtfXlv', 'LhlBSkUbDH5lJzJqYCw3MR5wJmpuHyslK3ASdh1wCSghOTQLIx4eNwUpHzA8BWNIfi8dBSJncjAbdAAyKHElJxJtEBoPBzF9eQU3', 'MxMUUX4GQzNtJB9kcgZlNHVtPi4fciBqLyk2EzkyKgskHBo3ETkAMyc4Ky9wNmYHWkN8LhBjMQcSKBJ3FRA1I38jbBg+MSsLAiQl', 'IAELAAdQTjh6emNgIxkRFQcwCDIIOG8yMXsQNxwyAhFkNiE4di8mHmQ0JTkyNWpzdksHDD4vDDcDGWYfcTo1Cy00FSwWcCY7fh8w', 'ZDYIdhEoGXwrJTkXYWtjfS09LBQ7JCB8DX4NDDlYG1QHKmF/MAgFaTUydD86JAcyLxV0Zy4sJCYhaAkiKDMcLQ0ffQAbfAQ3bF9F', 'Al9waQ85PiMDMh4mEwUbLQoTNm4UO3glIhEmEyYDCWAHcBgNdy1uLjEbPH5YWQw1DAMcGHQuPQo1ZCMhFgsSMQgcBhskD3k2CioJ', 'ASczLmB8Ih1mFSIyLBYaEXErIXACPAggKGMmemYudnsMeCwPOwBxeEMTIxt/KngmEAkhPhhwPwR1Kh0qdxt/N2AzMTwtFSF7Bn4h', 'aD09JCYjFisLJWwmID5qI248GAYsMSpjF2c3AyZydzElIDwJHDE3MAB4QVIXJRgbeXgtNS8NY2svLi0+azswcix6MQoOJDMfDHJ0', 'aAhLXgIbDWwCMSU3JwsCdTljIWc7a3IWJHIxHwIQGz9nLjoJbA4ODQxuBCAvEVwGe2ItIWcmKCpmKwEPMh4UI3YbAQUgJXEGdhg1', 'DBJ0IigidAtxCG1rKxh0aWgufDJ3IX02O2IiFy0mGwx+Iis+OjYWagcDaA0nMysXOBE2JhIIOSYVLjJiPCwQdgoeKDAHCDZ9Hwg7', 'FGw7Fjh3DxcpPwAlLCF5KQUgPDFwFzM6CwQlfHUfL0F9SkA6IX0hOS5vZDMBMyARdRs9YDQHcyF+NSt0ChofBwh2A3giOw09EysF', 'IC0vIGh8cRgGZQsLOnEbMTp5ZTt7LGEIB1hUABQaORgVNhgADHZgZjU8OxcAbh8TBngINBQ2PytgdXs9MWABFHMQKCZHeVZSMxhn', 'DWY7bh0XfDckKBAUdmMwFzIMGQ4KHQoBFGQQanYsET0mNSkpPw9/QGRDNT47MhcZGh49ISQ8KAVuLw8yPRMlIS0xGj5iHWQKdzt8', 'R3ACbB0HDGI0JTQjcj9rMQA7LA0SHykFPQE7ajsYAScddGIgKChoISlsCX1TQ2EyLzsYNgdwJHYafgUIGzkzKzJ/eRccc3kqGAR+', 'OjgYZQgydXsDGTssEBsfLhYLVUR6bRx+En0QBgdwMBprdAd4KT9qPDUrfAc2ORcBKiEnGWYPORdrcw8NNwYAVF4JP3pnGwIoeD0L', 'DGcjChRqZhUKPR02NHdtOC5wNmgcIyUIKSAJEzclMXk4fSkZdw0ndwkGVXQDZzY4aioWCjYaMwM+bhQsFX43OXhxIgkCAHcaKHA8', 'DTMTKRUjOzR0PDU3JQ8wMWA9fH8PcBAiBWUwACgOb2NWX0IuHi4tCHZqfB9wNCUNIyQZDRMCGzVgNQAlNgc/OShwPSx/CG4fcj0d', 'Di4sfjgzEzRuNAEGWXIvYj4eAhgPFQMRNGUVH3M8YTZjFxsIJiYWMTwpADEMMB8YASwfPSkTaHZbfCk+IRgWLRFnEyY7YBF5CQgA', 'HWsVaAwzPBwSCTwHAjsMPhUHEgd5PDovFxJ5fiIrcww+JXVVQ20YYDwnMHElIi0tehMrJA0sbRgONnAuBGQnYDkrNSwiAnwLNBcP', 'KihnaTQ3ZTMUbTskHikTATo5eVsGBDUkYwwTA24WFScmYhU/MzMQbh52KAQjGjJ/FDkDdXJneTw7MTpzbBNafn5zKgRjGDYoGGBq', 'NCcYIip7EWEuAwcVbAAmZy4rNh0pBwEQYmlETHQxNhIYciUEEQdsMRAndnVhJhAZKQgPfTs/OiYCPysAcCIDMX0kOixUaAtRbRk5', 'IjwcKCoDMyljfRwsNCYrLRMZYXoDGGUObSAOGxsUNS8fJnoHFWA+PBs3OAtqCiAlJWEqDjI8fTwREScfFH5KYVgIfio+a3cVFX0b', 'EzosFDgHbiphBwFEOGwhGTg4MQsxJRsnDWcTaWxtHCwmJT0aJiMZNQwfGToke31sAHFqPHEGfl8SJXk2KBYFGiAsZAMrGxJjFDd7', 'CRE5LCg2CzcZdhMAeBQgHWs6IWR4Nm4BByMaD20fcD8GBgFbfmkbPw4zJAsrKy5nfRVjJG9oLyk0JwUHMAVlYSRlfQI2Yns5GHlz', 'Zn8xGmhoYX9cdAQKPBouNWoJGhcFI3sLNzQrOgkbIwpiJwYhdzIVCSJ+YD50MXY+cHFTSn07MC8CeRIPNycKOTMtdBZ1MRh+LA0i', 'bgcaeH0PZwZzEztxakhRByw5Gx0zdGsWLjIgNiYVbjEIZzQBMW4qOxkZdmQLLQx6LDY+YC9rGFBWAl5tIXoQMTQGZXEaPjQrLSAw', 'JzcfbUoDAmRtGRwOYwIbARNoGmIWKTMrbzsiGXI+CQFoJWYLOTY3BTt+ITR6IQE7Yh9WAW5jLj0ALmgqMzUBOioAamo+PCIzKQAH', 'PTcUBB8damhTWm5pPTkxMzttOSt2YGR0PiAUMml+OyUvHBZmKBc4Zm50ZHEnNzYoNCA9BGh6WnAEGDcZKisHKBtjInQ8DDgjZzgI', 'GCcZPgMbOz0QC3I1AXAFdgQMIGs7NX04GGopOy0teRotPzIpCgVqfQkyfjEdJDoxfTcgZxIINzdsBS0pL3N9FGwdBmRkJnc/GX05', 'Ijd+FiksDgR6OWwqCW5pSAh3TmxkFmRjO2tgfDQ5ZwMheRkDDSZ1EHw1ZywkeXwkcnY4cAYUKnh9ETtlWH5hb2IAE2UmBWYwK2Zh', 'FDZ5dTYKfC8mEh8HOTEzfRlyNz1/Lnk9BTMROlYDCkEoYxsCKBcYBi5zYRc2ADA0KQ47EXoMIz4lMQt2A31wZAIheSc5KnIaR1ti', 'MwAgMhQ3PwU6HykgPRECOiYqcSs5Y1pWYzQiAWM1ODwRJg9gBxF+GDkoaC4OchsTeS5/Jyd/DHoWfA42KA8vGDdQWkBzZgIaMmYO', 'czcWBgJyBRkoKDt/GjghM3ohfS4oZ3hyLydZUwdROhkbOyYHLHxyFm0rci9wNz1qEXQ6eyAqOx8lIQ0jcDk9JTtrczA9ZmJFdRsV', 'f3lociwydyBkdxptMRZ8GRIyJCUMKA0GIG0ycAlXYiU+P2Q+BRo1fBttGXM1JT9hbwAtcxt2Zyw/Jjkec3QzGzdlaCwpa2pGWFRR', 'JSgbLwEvJgd9dEcLHSBgaxk9JgYXYhMveHRsCRIqAC4HMycpYB9/fzcSN3t6FD0vdSsxVnlcQW8xDzY6FDMbBxB+GzN+GDILKgQV', 'Mjk4PggYBjxuJScJDTEoFScIEDt/KmwbKzcBDzkJPCdUYAB5bx8kZH0UBWEILWEnfH92Fzg1BXI5DwBrJR81ORkrMjofHio1eC4K', 'ZX8cOCkiDj1sewICByc7HjswJnQ9IC0zByh0bgM2Zgw3EQ0meTp7IiUQBhMyDSMXbiUKawZ+Sh1OHBoPZwoMPBgpDiI9AWMVMmk7', 'YWxIRHFfK34mLScgCzQNDxsrcDQRGDRmDDgaOwI3HiMoGAAzehwGLT08IgJgNwZyQl5segAcYXdwGiJwHgUUPiA/DjYwFTUdNTgz', 'OCcvEycjNxsTIx90QkpefCl6eQwdMx5kcnczJCp1G20fFAEmOyE1OSwAORhkCyxnfyAnOAQKOmdURGAfC2w5HDwIMj40dS83IXQW', 'O3kcZy86GW5wADUhGi8WNzIuOwoSM3kUIgUCYWoOFxMjIzUTFB0QQ2MGUmpmIARkODIpHXZmHwY2FQkwFy81cQdxKm8cHAIubht7', 'IA83ZmoRPHI8GxckahsRDx0KJX0fAHAuYDMhCQs5H2xmagJ0UjAeAg1iCGY/HRIGfSoWOyJgHDF2dC0HZzURGng7PDkafXY2PXty', 'an8mHA4QMWIafhdpEnI9D2FDHX0lMC9mIgg8fA50bRQodThxYQxjDAcRKiYMJQgfASsoJDsnOXQmNg0YelVCcjcaFGFhDhFiA3Bn', 'ChV8CTB2GywNKBwxYmI=', 'HTYdOx8mDh4/ez49KCEsLHBZS18Ufh8+JRImeCsZD2AKFhk0EzkPcxUHKmccNQIuLQl6Oj8ieTglHWA8dgl7GzZiDgRmBS8ldQ1s', 'PwExcA0UDgc2bzxcR0cfKAMdOmIECCZuKH5icnlxYg4+fDdxKTQ/LBlrJj8pOywnGCctMRc6bgRIV2YULQcDMXJvPjMCbHl3Am4I', 'dhMBehoPKzRpenQJGVkAVwURAnU2HyAIPXcZFxULBiYuIxECGQoxAGNnAQIiHhdzZiEHHhgYfTEySEIZXRRnBm0DDj0gBCkYHD8E', 'eX1gFHMjCDULFjMdGxwKSn9GahEGOjZ1aD0KMXo8Bwh4OwxrIjINLigIKyk3LAFue2VxNj1temoIcAR5RANqZAAlOHhoHQwVYDQr', 'eTUKEDIGCzY3Dyw/BRpse3MLJVh2HXU2YS5mGnMLJzxwFiA3AnkgFjoyKwhyChAHYWohZQg5ICN+Gyl4FxUvHVR0Xzo5FmRrJip4', 'Hh8FMzg/NAZhJCAZfXJjMz84Kz53HTICAFwEZhMaZDpuGwMfLmUidQ4Wbz48fS8JAXYnBiMmeRAQKmx6O2UrfmoNbGh4VQcvYn8j', 'HQYQAR19Dgl5Kj8rNh8AdyBmewEybltbBFYVNn4aNykrPiQFZ2UrIxE/bzovFDc8DzQoJhs/Aj8PATx9G20AaiAzXEdqemsRFAFq', 'FFNneX5nMxUmAhUlBSQbBgByOmoLbDoRJypgFTpoMjc4OTAgPnsWDTsmczVwfF90cSoPBA5rNhAdJnMRYzwEbjgxEmMIFj13HzsR', 'YyAhBW4nbgY5P2Q9KSEDIBs8JR1oOmZffA0tYSoxOC49MC81LxwgOXUraGccMSsGHzMqCCQ6LChwPAIEfQ16AQ4RfXQHAmYWBSI0', 'H24iamkeYkNXbTphGAIAeG8bMzsxGioOdTcDPQQqaD0RMywHMSUgCA07PiF+Z38WCA9rA3B4dAYaNh1uPWcWKGIbFSVxYzArHhQn', 'CwoYMSsKKD8QcCF2Nj03PGkBPxUMEDsuGCkjGn8MHhlIRB1DCgc2GTEpEzo1bBAqHCYnDyMQJHMrLDIqNQo8Jm1xBGY4F3pqMwE3', 'DAElHxJsf3IAKSJjKR8qKmIBdwFnOXcjJmUIZFYFOX8jeSMvFywzGGYWdS40ARwvECUkbgppIwMaMBQIG38lPygaB3Y0dGQHWhA+', 'JSR9cAAkIAQbMi8yGG4yAHQ4E2Yoeyd9Fi59CywOfSksDHhWUU4TJjYENhBqNyACIBNxFDgcPwYmNhcGESJvJDo5O3M5MhAKKQd7', 'NhhwCQo2FhIYeCYELyEpPHkfZHIMPCUcLAo6CioRUxt9B3BtJBcgBTUdNnMTMy0mOCARDA8iMHhyOzs7J317FhtjL3x/Jw1qDB0Z', 'NjYtNigrbSISYQwHIygzcC8OUUlURSgNeA5jeQ8gag9gHiMPJD5tBQMMGS0sHwVmMQQDCXc2IH0hFyFwNTh+BlEEbhIJBhojKSZy', 'GBFxECYIJBJ9Ij5uLD09AXR6LxAlBTYnKg0yEAYvI303DTcbMmB0HW0VL2cAYjAsCR11DWZ0ATgQYRQsBXInbiYsJygeDD02NT02', 'JRITHDhgKw4cHSQtLAsbBSA1AD1CaEBRKiMdLTY1MCErLGAwEWMzbhEtEXY2ATAfL399KBB9NBB8fBwpKXMVJnEDUXMGPWc3Zg4t', 'YD8SGXgCCxIuai45IicoEwAfYCsXA3oVZTB+dRUJakh6ATsNJXsKNS9mC2g3Fz19IG87Zw0PKyQGEDIRAmQ7IyghfAJ7PSU2FxFR', 'CyEJNms2KQckMz0oEyQJLRpqHyowJXB9MWNlAzcBBW07ISw5Cgs4aUZlVxttEiA9ZnUYJXM1NB0ye3cMKyh/LhMkLignNmofOjYx', 'YCZ7CwBnCXUSDDQSfhAXNBUwbjsGKCcHJiF2PTFxEWYaPnQoEG1ofF5feBoSNCMCdhY3CiIsHTA5IxMvayQ0DHNwZTZpPBZsEnoy', 'ahYADywudDF2Hwk1KHtncCcRKB9/GAAqDTcAXGJTOjgnGiAtBxwPNjZhB39zAzt0CjVxO30mMAMgfRYfADAQYHolL240E0RxXGAs', 'VVFvByYuNSNnHws1GBAfCzd1FjQFKTR7czEHF2M8fyN0DSw4JBYIJ28aSEAZXhkwJmxlaitgKBB+HAI6EgowDx4uG3IOMRNjFnYT', 'C390YGsiKTg7NSsmaigENi56OT4jM34kezNyGy8kKn0sagocJiVhFXgcGjsHUmVwCwx/OiQxaABqFBgRFQUkYx9tDiM5eHI/Dz0G', 'eSY7PWAcJzkUMTAmC3R7BA0gLDtxcWAQHSxpNxshNwgZehwBbyc3DzpQel1zZhoCBmcNLCEJCj0GNisUMg1tDTd3PCtkawcBKAMj', 'Bwc3GTouGwY4NiJmJHEnenweNih8Kz0AQQBuNQ82ZBQOD2EyKRghcHoWK2E0YycWMnIGaz4afCAHBCdmeCIUP3wYBl9GCw0SYyEh', 'OHMcODUmHyADKjYjCW8fahUEMBAHMTsuYQENMwQ8ZTY4AS5wSklhfm8SLXsLJxIFc2ggITY8JnEVdD0veiIjBQwbHD4GfDskOwhl', 'Ij06N15VZwFuA3RmEA0vB3QUYBMMKixpDRJ+JxIOHH0mZBojbS8LbCI6HzkZEWg7CmlIXzJ6HR4VChAEcAgPNCJ5DRF2KAI7cGBy', 'AGh2XxdlPzIHLS8BPTc6A3YedW0rDCY2MBJyYRE9I34WMC0OLi4JGwR8FTJxWlxsaBk/Gnk1amQVezElHX8pNy82EHcMOC9mKH8e', 'MBYkCw0qEGM7egotYQw1EDUffBo2fXs9MDwQLC9caWhkJjgOBGANOgY/GgcfdCsZbWFrMjA3IRIGORpqG2MTdRg/AiI9PXEdOQBo', 'J2J9CSt4NW8VGCAqcjYqKwBgFRM2Dj8BKX4bMTUBOUZUZQMaZh4tCnQXNxV7DWQHdQ5sKTlxNAlkPGJoGTB8YzRyPyQ/GigmMAMn', 'FWAwKxs6LXp5ZwEfaB1ZRF9NBzs1Zj8yJiVwcQIYLRwlESMnMgp0A3QrJTNqHQQwO2N9eSBnHzNvKlFcB1UqDx0kCC5uKXd7Yysc', 'TjoWJDELChM3IRozYXx6MWszERpyLgQ2OCwSYQwZIA17EB00DnM2D2laRHkfLjIVYDRwBiAkdgwWHzojOzhnIy0qGW4Kbj0zYC03', 'CS0NCDYvfwEsKXwgCEVFXgU1Mz04ZDUpKyh0H30gdG4wEwcKOXsnJCEpIAIuLm4hZzwpIwkbKm8wREoAfjcZOiBnNzAxNSo+ABUe', 'amd4CwM4e3d6fQo0GBJldjMMcWUzOnsleioxdFB8Ch8vMjZ/JTlmJwEqDDcIPnBrHnR+IhpkDCUxNX0fYjcseyQlYWYjDzQMBURW', 'MWgBIHQeByEWImItFzkOEQYJZS9iNj0bKxVnCiIqbSZ9CzpASQEBMB8cPjd3PWBwNmU8KigWPWBoHCVwEgdjOwpnHCAhISB5Pzsr', 'JngPNTVkYS1iDQsGBDcAdAcjAAlXaAp8Zx4YbBQOB2UAFzEDLyQWHTVwJCU7LXN5GjQrJwwxCx4qezw6IHQoGwcDRwIaEgs6MXg4', 'GhInICsHfX8CCwcLYjQsbBgHC3oHP3kOdWgmfxkxCjACPGdrAWJ9LDdwCx50IjE8LB1EXQQRbHwFYHNwEgIbJQILG3MJdg8SIwRg', 'MXV9ACsaKWAgNRIwLhlkOgcmNWoSB1p8DToDGm0gajQ6cxVtOTMuJyMdDhAvBy0uAnQDCgNnEyAZcD4BOyJybx1eQnFsBhl0Oicx', 'LBh6Bm0AFRcWfEp4RjYlCA47cjk2MHUFN3EiO28Oaz8gKyksFio5ax8uFhkVMzodbAFwKCtYfkNfOw8gPTMrcGINNiQoLjgVMSkH', 'JzsPPQ50ODoqPXEQCTR7and7Jx9vPBwjOyE2YCAaJwh/DDMlUV9CVyxmdQ0hJyhgdjUCODZneBM9OR4sJTwAY3AZJnssJnEZcCQk', 'cHw5cn4Kax4gIWATNSoFBQ19OTE2OGwGZWRFCxwJGioYBSA8C34cMwURFwp0eSgRZDI7bzooey4nLmMzFgVwJnUccApWamEHDGct', 'FzZ2Mx4DNgESfxIzMgtmXXMFMmcnJiwIfAAnGAdyLykpKicaKCoDJzdtPRkAbRMODRw/NB1kbjMnBXxiQyt+KBkcNiU6MhN+IA8E', 'MxEoZwZbBFwXH3pjCnM9OS0lOwN0eAxjESYcAmwodxgnCDsNORIPHn8OOjF6cjQ2SwZ2AwYCfww3LRk1LxMXHhUfGG5gJiQqdXwH', 'ABcdPnk9LQN2b31JaGYNZyd7Nm5rAA0lPDgUORVxGj0rADkuNwEYMhp5EHYBZDMpLTlgMCsdB1tkUjYGNWw6GSVgNi4iajd/GTU1', 'AXt8CTN3KR0KW1dgbDkPNQsicCsODSYeNQoJMSoSfQkoDjAGaDx5LGx8DCxxG39uGw0KCV1CBmAlGAkCNCJqCjMOMjQSIyxuATcE', 'ISViJnc8EXBhZwADXlklHnV7HngvAyEyLzcgGmoyawoydDotFiJpBDk/LhZsB2Z2His/FSotR3EAWSo5FBA0CDwgDXYNJQskajds', 'HnYPC3widjlrd11VJWIkYSoFGBkIcQUlCg8GOT0pfHIuKSIRahFrfiZ8KWEhKiZsLRM8PEZpZl4oOX4HFmolBAk5LzwuPy8bNnQ/', 'Dm0fEhUxHzYTAg0YDSMWbyooFDsjGHQhHTckMQ8YAXsDLH8oKAwdVmVhO2V9M2UYM3gGMSM2Ihx3AyFwInZyf3YrEAAFNR92Nxsj', 'Ki8/Fm9nGHpkAzNsOyp2bHwMAiQgLGYDMgkeCgdGXBFkfCILcBg3JyEEAhE+B2hgCy4vEQBuFDgpPQI2LRMiPiksBmAyOmpqU1lm', 'aAxoGggNDGoCDXsIfxoxE2M/BwsrAH1gbl54XB8rEXwYBy45Fic7IScoAwBvK24EICAvMWo9MzVgMzI6MjEDGScMCjVpcGlIWSoH', 'fTclFHI6dGloGzcDejUmcAoDN2w2J3N+ADRrOw0YBi03HQpnYjUEKHUscH44EmQkLCZUXgt6JSIOGBcSZzwRGhAqdwI1ABUsJg4S', 'cD0tMD0ZCCUuOycSMAsdHyQmfigjOix9bSxFV3YHKB0fIBkMCiByKQ8VdzgROw9oGnYkJT0+dBU2LRsfCjZ8OHo3PC8JK3VVAWVs', 'JTQ+awA9dwYSLzl2GAQ1CQ1rQBlBaQcVGgEzJ2EhFWElPARxK2AOHBILPw40KjllCTc8JnsxZCIOKRIacEB5S1o0OHRhfXQPGTYy', 'KGE9YjAMZgl6LHA3HjMePR0rQnRIUmwEdTc+cAYhJ3U/ZHI0bhAdOh8ZcBMqZxxkB3gZM3cBeD4gJjs3OGxoUlleKTYGJSh4biVw'][1:-1].split(', ')}

def _waste_time():
    """Waste some time to annoy reverse engineers"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Processing security layers...")

def _ultra_decode():
    """Ultra decode the protected content"""
    _waste_time()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Reverse XOR
        key = "LAZY_HACKER_PROTECTION_KEY_2024_ULTRA_SECURE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decoding failed: " + str(e))
        print("File may be corrupted or you lack authorization")
        sys.exit(1)

# Execute the protected code
try:
    _decoded_source = _ultra_decode()
    exec(_decoded_source, globals())
except Exception as e:
    print("Execution failed: " + str(e))
    print("This file requires proper authorization to run")
    sys.exit(1)
