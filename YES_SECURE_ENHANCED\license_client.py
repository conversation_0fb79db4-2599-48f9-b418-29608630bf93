#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "license_client.py"
PROTECTION_DATE = "2025-06-03T03:26:13.003491"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
