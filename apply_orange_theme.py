#!/usr/bin/env python3
"""
Apply orange/black/dark gray theme to all POS system files
Keep button colors, change backgrounds and text, use Segoe UI font
"""

import os
import re

def update_file_theme(file_path):
    """Update a single file with the new theme"""
    if not os.path.exists(file_path):
        print(f"⚠️ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Color replacements (backgrounds and text, NOT buttons)
        color_replacements = {
            # Background colors
            "bg='#f0f0f0'": "bg='#1a1a1a'",
            "bg='#2c3e50'": "bg='#2d2d2d'",
            "bg='white'": "bg='#2d2d2d'",
            "bg='#f8f9fa'": "bg='#404040'",
            "configure(bg='#f0f0f0')": "configure(bg='#1a1a1a')",
            "configure(bg='white')": "configure(bg='#2d2d2d')",
            
            # Text colors (not button text)
            "fg='#2c3e50'": "fg='white'",
            "fg='black'": "fg='white'",
            
            # Canvas colors
            "Canvas(.*?, bg='white'": "Canvas(\\1, bg='#2d2d2d'",
            "Canvas(.*?, bg='#f0f0f0'": "Canvas(\\1, bg='#1a1a1a'",
            
            # Frame colors
            "Frame(.*?, bg='white'": "Frame(\\1, bg='#2d2d2d'",
            "Frame(.*?, bg='#f0f0f0'": "Frame(\\1, bg='#1a1a1a'",
            "Frame(.*?, bg='#2c3e50'": "Frame(\\1, bg='#2d2d2d'",
            
            # Label colors (not button labels)
            "Label(.*?, .*?bg='white'": "Label(\\1, \\2bg='#2d2d2d'",
            "Label(.*?, .*?bg='#f0f0f0'": "Label(\\1, \\2bg='#1a1a1a'",
            "Label(.*?, .*?bg='#2c3e50'": "Label(\\1, \\2bg='#2d2d2d'",
        }
        
        # Font replacements
        font_replacements = {
            "font=('Helvetica'": "font=('Segoe UI'",
            "font=\\('Helvetica'": "font=('Segoe UI'",
        }
        
        # Apply color replacements
        for old_color, new_color in color_replacements.items():
            if '(' in old_color and ')' in old_color:
                # Use regex for complex patterns
                content = re.sub(old_color, new_color, content)
            else:
                # Simple string replacement
                content = content.replace(old_color, new_color)
        
        # Apply font replacements
        for old_font, new_font in font_replacements.items():
            if '\\(' in old_font:
                # Use regex
                content = re.sub(old_font, new_font, content)
            else:
                # Simple replacement
                content = content.replace(old_font, new_font)
        
        # Special cases for specific files
        if 'pos_screen.py' in file_path:
            # Update total label color to orange
            content = content.replace("fg='#2c3e50'", "fg='#ff8c00'")
            # Update cart item selection to orange
            content = content.replace("bg='#007bff'", "bg='#ff8c00'")
        
        # Write back the updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Updated: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Apply orange theme to all POS system files"""
    
    print("🎨 APPLYING ORANGE/BLACK THEME TO POS SYSTEM")
    print("=" * 45)
    print("📝 Keeping button colors, updating backgrounds and text")
    print("🔤 Changing font to Segoe UI throughout")
    print()
    
    # List of files to update
    files_to_update = [
        # Main POS files
        'pos_screen.py',
        'product_management.py',
        'user_management.py',
        'sales_history.py',
        'receipt_settings.py',
        'display_settings.py',
        'checkout.py',
        
        # Popup/dialog files
        'number_keyboard.py',
        'bulk_add_dialog.py',
        
        # YES version files
        'YES/pos_screen.py',
        'YES/product_management.py',
        'YES/user_management.py',
        'YES/sales_history.py',
        'YES/receipt_settings.py',
        'YES/display_settings.py',
        'YES/checkout.py',
        'YES/number_keyboard.py',
        'YES/bulk_add_dialog.py',
    ]
    
    updated_count = 0
    total_count = len(files_to_update)
    
    for file_path in files_to_update:
        if update_file_theme(file_path):
            updated_count += 1
    
    print()
    print("=" * 45)
    print("📊 RESULTS")
    print("=" * 45)
    print(f"Files Updated: {updated_count}/{total_count}")
    
    if updated_count == total_count:
        print("🎉 ALL FILES UPDATED SUCCESSFULLY!")
        print("✅ Orange/black/dark gray theme applied")
        print("✅ Segoe UI font applied throughout")
        print("✅ Button colors preserved")
        print("✅ Backgrounds and text updated")
    else:
        print("⚠️ Some files could not be updated")
        print("❌ Theme application may be incomplete")
    
    return updated_count == total_count

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 Orange theme successfully applied to entire POS system!")
        print("🖤 Dark backgrounds with orange accents")
        print("🔤 Modern Segoe UI font throughout")
        print("🔘 Button colors preserved as requested")
        print("📱 Consistent theme across all windows and popups")
    else:
        print("\n❌ Theme application needs attention")
    
    exit(0 if success else 1)
