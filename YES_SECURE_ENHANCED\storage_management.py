#!/usr/bin/env python3
"""
Protected POS System File
This file is encrypted and protected against unauthorized access.
"""

import base64
import zlib
import sys
import os
from datetime import datetime

# Protection metadata
PROTECTED_FILE = "storage_management.py"
PROTECTION_DATE = "2025-06-03T03:26:12.985077"
ENCRYPTED_DATA = """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"""

def _decrypt_and_execute():
    """Decrypt and execute the protected code"""
    try:
        # Import security manager for validation
        try:
            from security_manager import security_manager
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            if not security_ok:
                print(f"🚫 Security check failed: {message}")
                print("❌ Access denied to protected file")
                sys.exit(1)
                
        except ImportError:
            print("⚠️ Security manager not found - running in limited mode")
        
        # Decryption key
        key = "POS_SECURE_2024_PROTECTION_KEY"
        
        # Decrypt content
        try:
            decoded = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
            decompressed = zlib.decompress(decoded)
            
            key_bytes = key.encode('utf-8')
            decrypted = bytearray()
            
            for i, byte in enumerate(decompressed):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            original_code = bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            print(f"❌ Decryption failed: {e}")
            print("🚫 File may be corrupted or tampered with")
            sys.exit(1)
        
        # Execute original code
        try:
            # Create a new module namespace
            module_globals = {
                '__name__': __name__,
                '__file__': __file__,
                '__package__': __package__,
            }
            
            # Execute the decrypted code
            exec(original_code, module_globals)
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Protection system error: {e}")
        sys.exit(1)

# Auto-execute when imported or run
if __name__ == "__main__":
    _decrypt_and_execute()
else:
    # When imported, execute immediately
    _decrypt_and_execute()
