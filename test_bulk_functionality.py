#!/usr/bin/env python3
"""
Test the bulk functionality and import removal
"""

import sys
import os
from pathlib import Path

def test_import_removal():
    """Test that import functionality has been removed"""
    
    print("Testing Import Functionality Removal")
    print("=" * 37)
    
    files_to_check = [
        ("database.py", ["parse_menu_file", "import_menu_from_file"]),
        ("YES/database.py", ["parse_menu_file", "import_menu_from_file"]),
        ("product_management.py", ["show_menu_import", "Import Menu"]),
        ("YES/product_management.py", ["show_menu_import", "Import Menu"])
    ]
    
    all_removed = True
    
    for file_path, functions_to_check in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            for func_name in functions_to_check:
                if func_name in content:
                    print(f"   ❌ {func_name} still present")
                    all_removed = False
                else:
                    print(f"   ✅ {func_name} removed")
                    
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_removed = False
    
    return all_removed

def test_bulk_functionality():
    """Test that bulk functionality has been added"""
    
    print("\nTesting Bulk Functionality Addition")
    print("=" * 36)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    all_added = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bulk product functionality
            if "Add Multiple Products" in content:
                print(f"   ✅ Bulk product dialog found")
            else:
                print(f"   ❌ Bulk product dialog missing")
                all_added = False
            
            if "show_bulk_product_dialog" in content:
                print(f"   ✅ Bulk product method found")
            else:
                print(f"   ❌ Bulk product method missing")
                all_added = False
            
            # Check for bulk category functionality
            if "Add Multiple Categories" in content:
                print(f"   ✅ Bulk category dialog found")
            else:
                print(f"   ❌ Bulk category dialog missing")
                all_added = False
            
            if "show_bulk_category_dialog" in content:
                print(f"   ✅ Bulk category method found")
            else:
                print(f"   ❌ Bulk category method missing")
                all_added = False
            
            # Check for choice dialogs
            if "Choose how to add products:" in content:
                print(f"   ✅ Product choice dialog found")
            else:
                print(f"   ❌ Product choice dialog missing")
                all_added = False
            
            if "Choose how to add categories:" in content:
                print(f"   ✅ Category choice dialog found")
            else:
                print(f"   ❌ Category choice dialog missing")
                all_added = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_added = False
    
    return all_added

def test_menu_txt_cleanup():
    """Test that Menu.txt files are cleaned up"""
    
    print("\nTesting Menu.txt File Cleanup")
    print("=" * 30)
    
    menu_files = [
        "Menu.txt",
        "YES/Menu.txt",
        "YES_OBFUSCATED/Menu.txt"
    ]
    
    for file_path in menu_files:
        if Path(file_path).exists():
            print(f"📄 Found: {file_path} (can be removed if not needed)")
        else:
            print(f"✅ Clean: {file_path} (not present)")
    
    return True

def test_dialog_features():
    """Test specific dialog features"""
    
    print("\nTesting Dialog Features")
    print("=" * 24)
    
    files_to_check = [
        "product_management.py",
        "YES/product_management.py"
    ]
    
    features_found = 0
    total_features = 8
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking features in: {file_path}")
            
            # Check for specific features
            features = [
                ("Choice dialogs", "Choose how to add"),
                ("Bulk instructions", "Bulk Entry Instructions"),
                ("Text widgets", "tk.Text"),
                ("Scrollbars", "tk.Scrollbar"),
                ("Error handling", "Found errors:"),
                ("Progress reporting", "Added: {added_count}"),
                ("Clear functionality", "def clear_text"),
                ("Example content", "example_text")
            ]
            
            for feature_name, search_text in features:
                if search_text in content:
                    print(f"   ✅ {feature_name}")
                    features_found += 1
                else:
                    print(f"   ❌ {feature_name}")
            
            break  # Only check one file for detailed features
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return False
    
    return features_found >= (total_features - 1)  # Allow 1 missing

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/product_management.py",
        "YES_OBFUSCATED/database.py",
        "YES_OBFUSCATED/pos_screen.py"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all bulk functionality tests"""
    
    print("📋 BULK FUNCTIONALITY TEST SUITE")
    print("=" * 38)
    
    tests = [
        ("Import Removal", test_import_removal),
        ("Bulk Functionality", test_bulk_functionality),
        ("Menu.txt Cleanup", test_menu_txt_cleanup),
        ("Dialog Features", test_dialog_features),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 38)
    print("📊 RESULTS")
    print("=" * 38)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Import functionality removed")
        print("✅ Bulk functionality added")
        print("✅ Choice dialogs implemented")
        print("✅ Error handling and progress reporting")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Bulk functionality may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 Bulk functionality successfully implemented!")
        print("📝 Add Single Product/Category - Traditional one-by-one entry")
        print("📋 Add Multiple Products/Categories - Bulk text entry")
        print("🗑️ Import from Menu.txt - Completely removed")
    else:
        print("\n❌ Bulk functionality needs attention")
    
    exit(0 if success else 1)
