#!/usr/bin/env python3
"""
Test all receipt fixes: layout, logo positioning, total alignment, and history format
"""

import sys
import os
from pathlib import Path

def test_receipt_settings_layout():
    """Test that receipt settings layout has been improved"""
    
    print("Testing Receipt Settings Layout")
    print("=" * 32)
    
    files_to_check = [
        "receipt_settings.py",
        "YES/receipt_settings.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for bigger settings area
            if "width=600" in content and "fill=tk.BOTH, expand=True" in content:
                print(f"   ✅ Settings area enlarged (600px, expandable)")
            else:
                print(f"   ❌ Settings area not enlarged properly")
                all_fixed = False
            
            # Check for smaller preview area
            if "width=400" in content and "fill=tk.Y" in content:
                print(f"   ✅ Preview area made smaller (400px, fixed)")
            else:
                print(f"   ❌ Preview area not made smaller")
                all_fixed = False
            
            # Check for smaller preview container
            if "width=350, height=500" in content:
                print(f"   ✅ Preview container reduced (350x500)")
            else:
                print(f"   ❌ Preview container not reduced")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_customer_receipt_fixes():
    """Test customer receipt improvements"""
    
    print("\nTesting Customer Receipt Fixes")
    print("=" * 31)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_fixed = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for logo at very top
            if "# Logo at the very top - print as actual image" in content:
                print(f"   ✅ Logo positioned at very top")
            else:
                print(f"   ❌ Logo not positioned at very top")
                all_fixed = False
            
            # Check for left-aligned total
            if 'f"TOTAL: {total:.2f} MAD",  # Left aligned instead of right' in content:
                print(f"   ✅ Total left-aligned to prevent cutoff")
            else:
                print(f"   ❌ Total not left-aligned")
                all_fixed = False
            
            # Check for shorter line widths
            if '"=" * 40' in content and '"-" * 40' in content:
                print(f"   ✅ Line widths reduced to 40 characters")
            else:
                print(f"   ❌ Line widths not reduced")
                all_fixed = False
            
            # Check for shorter product names
            if "[:22]" in content:
                print(f"   ✅ Product names truncated to 22 chars")
            else:
                print(f"   ❌ Product names not truncated properly")
                all_fixed = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_fixed = False
    
    return all_fixed

def test_history_receipt_redesign():
    """Test history receipt redesign"""
    
    print("\nTesting History Receipt Redesign")
    print("=" * 33)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_redesigned = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for logo in history receipt
            if "# Logo at the very top" in content and "generate_history_receipt" in content:
                print(f"   ✅ History receipt includes logo")
            else:
                print(f"   ❌ History receipt missing logo")
                all_redesigned = False
            
            # Check for "Generated by" field
            if 'f"Generated by: {generated_by}"' in content:
                print(f"   ✅ Shows who generated the report")
            else:
                print(f"   ❌ Missing 'Generated by' field")
                all_redesigned = False
            
            # Check for unique items summary
            if "ITEMS SOLD SUMMARY" in content:
                print(f"   ✅ Shows unique items summary")
            else:
                print(f"   ❌ Missing unique items summary")
                all_redesigned = False
            
            # Check for quantity and totals per item
            if 'f"  Qty: {quantity} x {price:.2f} MAD"' in content:
                print(f"   ✅ Shows quantity and price per item")
            else:
                print(f"   ❌ Missing quantity/price per item")
                all_redesigned = False
            
            # Check for grand total
            if 'f"GRAND TOTAL: {total_amount:.2f} MAD"' in content:
                print(f"   ✅ Shows grand total at bottom")
            else:
                print(f"   ❌ Missing grand total")
                all_redesigned = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_redesigned = False
    
    return all_redesigned

def test_print_formatting():
    """Test print formatting for 15pt font"""
    
    print("\nTesting Print Formatting")
    print("=" * 25)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_formatted = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for 15pt font consideration comment
            if "15 font size consideration" in content:
                print(f"   ✅ 15pt font size considered")
            else:
                print(f"   ❌ 15pt font size not considered")
                all_formatted = False
            
            # Check for name truncation to fit
            if "[:25]" in content:
                print(f"   ✅ Names truncated to fit (25 chars)")
            else:
                print(f"   ❌ Names not truncated properly")
                all_formatted = False
            
            # Check for organized layout
            if "=" * 40 in content and "-" * 40 in content:
                print(f"   ✅ Organized layout with separators")
            else:
                print(f"   ❌ Layout not properly organized")
                all_formatted = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_formatted = False
    
    return all_formatted

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_files = [
        "YES_OBFUSCATED/receipt_settings.py",
        "YES_OBFUSCATED/receipt_generator.py"
    ]
    
    all_updated = True
    
    for file_path in obf_files:
        if Path(file_path).exists():
            print(f"✅ Found: {file_path}")
            
            # Check if it's actually obfuscated
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Obfuscated files should have scrambled names
                if len(content) > 100 and ('import' in content):
                    print(f"   ✅ File appears to be obfuscated")
                else:
                    print(f"   ⚠️ File may not be properly obfuscated")
            except:
                print(f"   ✅ File is obfuscated (cannot read normally)")
        else:
            print(f"❌ Missing: {file_path}")
            all_updated = False
    
    return all_updated

def main():
    """Run all receipt fix tests"""
    
    print("🧾 RECEIPT FIXES TEST SUITE")
    print("=" * 30)
    
    tests = [
        ("Receipt Settings Layout", test_receipt_settings_layout),
        ("Customer Receipt Fixes", test_customer_receipt_fixes),
        ("History Receipt Redesign", test_history_receipt_redesign),
        ("Print Formatting", test_print_formatting),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Receipt settings layout improved")
        print("✅ Customer receipts fixed (logo, total alignment)")
        print("✅ History receipts redesigned (unique items)")
        print("✅ Print formatting optimized for 15pt font")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ Receipt fixes may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🧾 Receipt fixes successfully implemented!")
        print("📐 Settings area bigger, preview area smaller")
        print("🖼️ Logo prints at very top of receipts")
        print("💰 Total text left-aligned to prevent cutoff")
        print("📋 History shows unique items with quantities")
        print("🖨️ Optimized for 15pt font printing")
    else:
        print("\n❌ Receipt fixes need attention")
    
    exit(0 if success else 1)
