#!/usr/bin/env python3
"""
Central Remote Management Server
Handles 5000 pre-configured accounts for professional POS deployment
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import sqlite3
import hashlib
import jwt
import datetime
from functools import wraps
import os
import json

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'central-pos-remote-management-2024'
API_VERSION = 'v1'

class CentralAccountManager:
    """Manage central account system"""
    
    def __init__(self):
        self.db_path = 'remote_management_accounts.db'
    
    def get_connection(self):
        """Get database connection"""
        if not os.path.exists(self.db_path):
            raise Exception("Account database not found. Run create_5000_accounts.py first.")
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def authenticate_account(self, username, password):
        """Authenticate using pre-configured account"""
        conn = self.get_connection()
        try:
            c = conn.cursor()
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            c.execute("""
                SELECT username, account_status, client_name, license_code
                FROM remote_accounts 
                WHERE username = ? AND password_hash = ?
            """, (username, password_hash))
            
            account = c.fetchone()
            if account:
                # Update last login
                c.execute("""
                    UPDATE remote_accounts 
                    SET last_login = ?, account_status = 'active'
                    WHERE username = ?
                """, (datetime.datetime.now().isoformat(), username))
                conn.commit()
                
                return {
                    'username': account['username'],
                    'account_status': account['account_status'],
                    'client_name': account['client_name'],
                    'license_code': account['license_code'],
                    'is_authenticated': True
                }
            return None
        finally:
            conn.close()
    
    def register_client_connection(self, username, client_ip, client_fingerprint):
        """Register client connection"""
        conn = self.get_connection()
        try:
            c = conn.cursor()
            
            # Deactivate old connections for this username
            c.execute("""
                UPDATE client_connections 
                SET is_active = 0 
                WHERE username = ?
            """, (username,))
            
            # Add new connection
            c.execute("""
                INSERT INTO client_connections 
                (username, client_ip, client_fingerprint, connection_date, last_activity)
                VALUES (?, ?, ?, ?, ?)
            """, (username, client_ip, client_fingerprint, 
                  datetime.datetime.now().isoformat(),
                  datetime.datetime.now().isoformat()))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"Error registering connection: {e}")
            return False
        finally:
            conn.close()
    
    def log_activity(self, username, action, details=None, ip_address=None):
        """Log user activity"""
        conn = self.get_connection()
        try:
            c = conn.cursor()
            c.execute("""
                INSERT INTO activity_log 
                (username, action, details, timestamp, ip_address)
                VALUES (?, ?, ?, ?, ?)
            """, (username, action, details, 
                  datetime.datetime.now().isoformat(), ip_address))
            conn.commit()
        except Exception as e:
            print(f"Error logging activity: {e}")
        finally:
            conn.close()
    
    def get_account_stats(self):
        """Get account usage statistics"""
        conn = self.get_connection()
        try:
            c = conn.cursor()
            
            # Account status counts
            c.execute("""
                SELECT account_status, COUNT(*) as count 
                FROM remote_accounts 
                GROUP BY account_status
            """)
            status_counts = {row['account_status']: row['count'] for row in c.fetchall()}
            
            # Total accounts
            c.execute("SELECT COUNT(*) as total FROM remote_accounts")
            total = c.fetchone()['total']
            
            # Active connections
            c.execute("""
                SELECT COUNT(*) as active 
                FROM client_connections 
                WHERE is_active = 1
            """)
            active_connections = c.fetchone()['active']
            
            # Recent activity
            c.execute("""
                SELECT COUNT(*) as recent_activity 
                FROM activity_log 
                WHERE timestamp > datetime('now', '-24 hours')
            """)
            recent_activity = c.fetchone()['recent_activity']
            
            return {
                'total_accounts': total,
                'available': status_counts.get('available', 0),
                'assigned': status_counts.get('assigned', 0),
                'active': status_counts.get('active', 0),
                'active_connections': active_connections,
                'recent_activity': recent_activity
            }
        finally:
            conn.close()

# Initialize account manager
account_manager = CentralAccountManager()

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
            
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except:
            return jsonify({'error': 'Token is invalid'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated

@app.route('/')
def dashboard():
    """Central management dashboard"""
    dashboard_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LeComptoir Remote Management</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: white; }
            .card { background: rgba(45, 45, 45, 0.9); border: 1px solid #ff8c00; }
            .btn-primary { background: #ff8c00; border-color: #ff8c00; }
            .btn-primary:hover { background: #e67e00; border-color: #e67e00; }
            .navbar { background: rgba(26, 26, 26, 0.95) !important; }
            .stat-card { transition: transform 0.3s; }
            .stat-card:hover { transform: translateY(-5px); }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-dark">
            <div class="container">
                <span class="navbar-brand">
                    <i class="fas fa-store"></i>
                    <strong>LeComptoir</strong> Remote Management
                </span>
                <div id="connectionStatus" class="badge bg-success">
                    <i class="fas fa-wifi"></i> Online
                </div>
            </div>
        </nav>

        <!-- LeComptoir Header -->
        <div class="container mt-3">
            <div class="text-center mb-4">
                <h1 class="display-6" style="color: #ff8c00; font-weight: bold;">
                    <i class="fas fa-store-alt"></i> LeComptoir
                </h1>
                <p class="text-muted">Professional Point of Sale Remote Management System</p>
            </div>
        </div>

        <div class="container mt-4">
            <div id="loginSection">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-store"></i> LeComptoir Remote Access</h4>
                                <p class="mb-0">Enter your assigned remote management credentials</p>
                            </div>
                            <div class="card-body">
                                <form id="loginForm">
                                    <div class="mb-3">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" 
                                               placeholder="pos_XXXX" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-sign-in-alt"></i> Connect to LeComptoir
                                    </button>
                                </form>
                                <div class="mt-3 text-center">
                                    <small class="text-muted">
                                        Enter the LeComptoir credentials provided during POS setup
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="dashboardSection" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <h2><i class="fas fa-tachometer-alt"></i> LeComptoir Dashboard</h2>
                        <p class="text-muted">Account: <span id="accountInfo"></span></p>
                        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-outline-danger btn-sm float-end" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>

                <div class="row" id="statsCards">
                    <!-- Stats will be populated here -->
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> LeComptoir Account Information</h5>
                            </div>
                            <div class="card-body" id="accountDetails">
                                Loading account details...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            let authToken = localStorage.getItem('central_pos_token');
            let currentUser = null;

            // Login form handler
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                try {
                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, password })
                    });

                    const data = await response.json();
                    if (data.token) {
                        authToken = data.token;
                        currentUser = data.user;
                        localStorage.setItem('central_pos_token', authToken);
                        showDashboard();
                    } else {
                        alert('Login failed: ' + (data.error || 'Invalid credentials'));
                    }
                } catch (error) {
                    alert('Connection error: ' + error.message);
                }
            });

            function showDashboard() {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                document.getElementById('accountInfo').textContent = currentUser.username;
                refreshData();
            }

            function logout() {
                localStorage.removeItem('central_pos_token');
                authToken = null;
                currentUser = null;
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('dashboardSection').style.display = 'none';
            }

            async function refreshData() {
                if (!authToken) return;

                try {
                    // Get account stats
                    const response = await fetch('/api/v1/account/info', {
                        headers: { 'Authorization': 'Bearer ' + authToken }
                    });
                    const data = await response.json();
                    updateAccountInfo(data);
                } catch (error) {
                    console.error('Error refreshing data:', error);
                }
            }

            function updateAccountInfo(data) {
                const accountHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Username:</strong> ${data.username}<br>
                            <strong>Status:</strong> <span class="badge bg-success">${data.account_status}</span><br>
                            <strong>Client Name:</strong> ${data.client_name || 'Not set'}<br>
                        </div>
                        <div class="col-md-6">
                            <strong>License Code:</strong> ${data.license_code || 'Not assigned'}<br>
                            <strong>Last Login:</strong> ${data.last_login || 'Never'}<br>
                            <strong>Connection Status:</strong> <span class="badge bg-success">Connected</span>
                        </div>
                    </div>
                `;
                document.getElementById('accountDetails').innerHTML = accountHtml;
            }

            // Auto-refresh every 30 seconds
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """
    return render_template_string(dashboard_html)

# API Routes
@app.route(f'/api/{API_VERSION}/auth/login', methods=['POST'])
def login():
    """Authenticate using pre-configured account"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password required'}), 400
    
    # Get client IP
    client_ip = request.remote_addr
    
    # Authenticate account
    user = account_manager.authenticate_account(username, password)
    if not user:
        return jsonify({'error': 'Invalid credentials'}), 401
    
    # Register connection
    client_fingerprint = f"{client_ip}_{username}"
    account_manager.register_client_connection(username, client_ip, client_fingerprint)
    
    # Log activity
    account_manager.log_activity(username, 'login', f'Successful login from {client_ip}', client_ip)
    
    # Generate JWT token
    token = jwt.encode({
        'user': user,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }, app.config['SECRET_KEY'], algorithm='HS256')
    
    return jsonify({
        'token': token,
        'user': user,
        'expires_in': 86400
    })

@app.route(f'/api/{API_VERSION}/account/info')
@token_required
def get_account_info(current_user):
    """Get current account information"""
    return jsonify(current_user)

@app.route(f'/api/{API_VERSION}/admin/stats')
def get_admin_stats():
    """Get system statistics (admin only)"""
    try:
        stats = account_manager.get_account_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🏪 LECOMPTOIR REMOTE MANAGEMENT SERVER")
    print("=" * 60)
    print("🌐 Professional SaaS-style deployment")
    print("👥 Supports 5,000 pre-configured accounts")
    print("🔐 Secure authentication and client management")
    print("=" * 60)
    print("📱 Dashboard: http://localhost:5000")
    print("🌍 For worldwide access: Use ngrok http 5000")
    print("=" * 60)
    
    try:
        # Test database connection
        account_manager.get_connection().close()
        print("✅ Account database connected successfully")
        
        # Get stats
        stats = account_manager.get_account_stats()
        print(f"📊 Total accounts: {stats['total_accounts']}")
        print(f"📊 Available: {stats['available']}")
        print(f"📊 Active: {stats['active']}")
        print(f"📊 Active connections: {stats['active_connections']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Run 'python create_5000_accounts.py' first to create accounts")
        exit(1)
    
    print("\n⚡ Starting LeComptoir server...")
    print("🌐 Dashboard will be available at: http://localhost:5000")
    print("🔐 Test login: pos_1000 / V2%7y@Yv")
    print("=" * 60)
    app.run(host='0.0.0.0', port=5000, debug=True)
