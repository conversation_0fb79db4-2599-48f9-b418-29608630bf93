#!/usr/bin/env python3
"""
Test the obfuscated version
"""

import sys
import os
from pathlib import Path

def test_obfuscated_version():
    """Test if the obfuscated version works"""
    
    print("Testing Obfuscated POS System")
    print("=" * 40)
    
    # Add obfuscated directory to path
    obf_dir = Path("YES_OBFUSCATED")
    if not obf_dir.exists():
        print("❌ YES_OBFUSCATED directory not found!")
        return False
    
    sys.path.insert(0, str(obf_dir.absolute()))
    
    try:
        print("📦 Testing obfuscated imports...")
        
        # Test database import
        import database
        print("✅ Database module imported (obfuscated)")
        
        # Test translations import
        import translations
        print("✅ Translations module imported (obfuscated)")
        
        # Test license_client import
        import license_client
        print("✅ License client module imported (obfuscated)")
        
        # Test database initialization
        print("\n🔧 Testing database functions...")
        database.init_database()
        print("✅ Database initialization successful")
        
        print("\n🎯 Testing main application import...")
        import pos_app
        print("✅ POS app module imported (obfuscated)")
        
        print("\n" + "=" * 40)
        print("🎉 OBFUSCATED VERSION TEST PASSED!")
        print("✅ All obfuscated modules work correctly")
        print("✅ Code is protected but functional")
        
        return True
        
    except Exception as e:
        print(f"\n❌ OBFUSCATED VERSION TEST FAILED!")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_obfuscated_version()
    if success:
        print("\n🔒 The obfuscated version is working correctly!")
        print("Security level: 6-7/10")
    else:
        print("\n❌ The obfuscated version has issues.")
    sys.exit(0 if success else 1)
