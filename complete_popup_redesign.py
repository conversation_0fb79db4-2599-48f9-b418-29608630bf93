#!/usr/bin/env python3
"""
Complete redesign of ALL popup windows with modern orange/black aesthetic
"""

import os
import re

def find_all_popup_windows():
    """Find all popup windows in the codebase"""
    
    print("🔍 FINDING ALL POPUP WINDOWS")
    print("=" * 30)
    
    files_to_check = [
        'user_management.py',
        'product_management.py', 
        'sales_history.py',
        'receipt_settings.py',
        'storage_management.py',
        'pos_screen.py',
        'number_keyboard.py'
    ]
    
    all_popups = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all Toplevel windows
            toplevel_matches = re.findall(r'(\w+)\s*=\s*tk\.Toplevel\(.*?\)', content)
            geometry_matches = re.findall(r'geometry\("(\d+x\d+)"\)', content)
            
            if toplevel_matches or geometry_matches:
                all_popups[file_path] = {
                    'toplevel_vars': toplevel_matches,
                    'sizes': geometry_matches
                }
                print(f"📋 {file_path}: {len(toplevel_matches)} popups, sizes: {geometry_matches}")
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
    
    return all_popups

def redesign_user_management():
    """Redesign user management popups"""
    
    print("\n👤 REDESIGNING USER MANAGEMENT")
    print("=" * 32)
    
    # Fix default button color to orange
    try:
        with open('user_management.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Change default button color from white to orange
        content = content.replace(
            "color_var = tk.StringVar(value=user_data['button_color'] if user_data else '#f8f9fa')",
            "color_var = tk.StringVar(value=user_data['button_color'] if user_data else '#ff8c00')"
        )
        
        with open('user_management.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Fixed default button color to orange")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing user management: {e}")
        return False

def redesign_product_management():
    """Redesign ALL product management popups"""
    
    print("\n📦 REDESIGNING PRODUCT MANAGEMENT")
    print("=" * 35)
    
    try:
        with open('product_management.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. Redesign add_product choice dialog
        old_choice_dialog = '''    def add_product(self):
        """Add new product with option for bulk entry"""
        # Create a choice dialog - BIGGER SIZE
        choice_dialog = tk.Toplevel(self.root)
        choice_dialog.title("Add Products")
        choice_dialog.geometry("500x300")  # Increased from 400x200
        choice_dialog.configure(bg='#1a1a1a')
        choice_dialog.transient(self.root)
        choice_dialog.grab_set()

        # Center the dialog
        choice_dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # Main frame
        main_frame = tk.Frame(choice_dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # Title
        title_label = tk.Label(main_frame, text="Choose how to add products:",
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(pady=(0, 30))

        # Single product button - BIGGER
        single_btn = tk.Button(main_frame, text="📝 Add Single Product",
                              font=('Segoe UI', 14), bg='#007bff', fg='white', relief='flat', bd=0,
                              padx=30, pady=15, width=25,
                              command=lambda: [choice_dialog.destroy(), self.show_product_dialog()])
        single_btn.pack(pady=(0, 15))

        # Bulk products button - BIGGER
        bulk_btn = tk.Button(main_frame, text="📋 Add Multiple Products",
                            font=('Segoe UI', 14), bg='#28a745', fg='white', relief='flat', bd=0,
                            padx=30, pady=15, width=25,
                            command=lambda: [choice_dialog.destroy(), self.show_bulk_product_dialog()])
        bulk_btn.pack(pady=(0, 15))

        # Cancel button - BIGGER
        cancel_btn = tk.Button(main_frame, text="❌ Cancel",
                              font=('Segoe UI', 12), bg='#6c757d', fg='white', relief='flat', bd=0,
                              padx=30, pady=10, width=15,
                              command=choice_dialog.destroy)
        cancel_btn.pack()'''

        new_choice_dialog = '''    def add_product(self):
        """Add new product with option for bulk entry"""
        # Create a choice dialog with modern orange/black design
        choice_dialog = tk.Toplevel(self.root)
        choice_dialog.title("Add Products")
        choice_dialog.geometry("600x450")  # Much larger
        choice_dialog.configure(bg='#1a1a1a')
        choice_dialog.transient(self.root)
        choice_dialog.grab_set()

        # Center the dialog
        choice_dialog.update_idletasks()
        x = (choice_dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (choice_dialog.winfo_screenheight() // 2) - (450 // 2)
        choice_dialog.geometry(f"600x450+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(choice_dialog, bg='#ff8c00', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame, text="📦 Add Products", 
                               font=('Segoe UI', 18, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Main frame
        main_frame = tk.Frame(choice_dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=50, pady=40)

        # Subtitle
        subtitle_label = tk.Label(main_frame, text="Choose how to add products:",
                                 font=('Segoe UI', 14), bg='#1a1a1a', fg='white')
        subtitle_label.pack(pady=(0, 40))

        # Single product button - BIGGER with modern design
        single_btn = tk.Button(main_frame, text="📝 Add Single Product",
                              font=('Segoe UI', 16, 'bold'), bg='#007bff', fg='white', 
                              relief='flat', bd=0, padx=40, pady=20, width=20,
                              activebackground='#0056b3', activeforeground='white',
                              command=lambda: [choice_dialog.destroy(), self.show_product_dialog()])
        single_btn.pack(pady=(0, 20))

        # Bulk products button - BIGGER with modern design
        bulk_btn = tk.Button(main_frame, text="📋 Add Multiple Products",
                            font=('Segoe UI', 16, 'bold'), bg='#28a745', fg='white', 
                            relief='flat', bd=0, padx=40, pady=20, width=20,
                            activebackground='#218838', activeforeground='white',
                            command=lambda: [choice_dialog.destroy(), self.show_bulk_product_dialog()])
        bulk_btn.pack(pady=(0, 20))

        # Cancel button - BIGGER with modern design
        cancel_btn = tk.Button(main_frame, text="❌ Cancel",
                              font=('Segoe UI', 14, 'bold'), bg='#6c757d', fg='white', 
                              relief='flat', bd=0, padx=30, pady=15, width=15,
                              activebackground='#5a6268', activeforeground='white',
                              command=choice_dialog.destroy)
        cancel_btn.pack()'''

        content = content.replace(old_choice_dialog, new_choice_dialog)
        
        print("✅ Redesigned add product choice dialog")
        
        # 2. Fix bulk product dialog text visibility
        # Fix instructions text color
        content = content.replace(
            'font=(\\'Segoe UI\\', 10), bg=\\'#404040\\', fg=\\'#495057\\'',
            'font=(\\'Segoe UI\\', 11), bg=\\'#404040\\', fg=\\'white\\'',
        )
        
        # Fix category frame labels
        content = content.replace(
            'font=(\\'Segoe UI\\', 12, \\'bold\\'), bg=\\'#2d2d2d\\'',
            'font=(\\'Segoe UI\\', 12, \\'bold\\'), bg=\\'#2d2d2d\\', fg=\\'white\\'',
        )
        
        # Fix text area labels
        content = content.replace(
            'font=(\\'Segoe UI\\', 12, \\'bold\\'), bg=\\'#2d2d2d\\'',
            'font=(\\'Segoe UI\\', 12, \\'bold\\'), bg=\\'#2d2d2d\\', fg=\\'white\\'',
        )
        
        print("✅ Fixed bulk product dialog text visibility")
        
        with open('product_management.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ Error redesigning product management: {e}")
        return False

def main():
    """Complete popup redesign"""
    
    print("🎨 COMPLETE POPUP REDESIGN")
    print("=" * 27)
    
    # Find all popups first
    all_popups = find_all_popup_windows()
    
    # Redesign each module
    redesigns = [
        ("User Management", redesign_user_management),
        ("Product Management", redesign_product_management),
    ]
    
    success_count = 0
    
    for name, redesign_func in redesigns:
        print(f"\n🔧 {name}")
        print("-" * 30)
        
        try:
            if redesign_func():
                print(f"✅ {name} redesigned successfully")
                success_count += 1
            else:
                print(f"❌ {name} redesign failed")
        except Exception as e:
            print(f"❌ {name} error: {e}")
    
    print("\n" + "=" * 40)
    print("📊 COMPLETE REDESIGN RESULTS")
    print("=" * 40)
    
    if success_count >= 2:
        print("🎉 MAJOR POPUP REDESIGNS COMPLETE!")
        print("✅ Default button color fixed to orange")
        print("✅ Add product choice dialog redesigned")
        print("✅ Bulk dialog text visibility fixed")
        print("✅ Modern orange/black aesthetic applied")
    else:
        print("⚠️ Some redesigns failed")
    
    return success_count >= 2

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎨 Major popup redesigns completed!")
        print("🧡 Orange/black aesthetic throughout")
        print("📱 Modern flat design applied")
        print("🔤 Better text visibility")
    else:
        print("\n❌ Popup redesign needs attention")
    
    exit(0 if success else 1)
