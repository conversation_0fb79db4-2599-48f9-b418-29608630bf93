# POS System - Client Version

This is a protected client version of the POS System.

## Installation

1. Ensure Python 3.8+ is installed on your system
2. Install required dependencies using one of these methods:

   **Option A: Automatic installation (Recommended)**
   ```
   python install.py
   ```

   **Option B: Manual installation**
   ```
   pip install -r requirements.txt
   ```

## Running the Application

### Option 1: Use the launcher script
```
python start_pos.py
```

### Option 2: Create desktop shortcut
```
python create_desktop_shortcut.py
```

## Features

- Complete POS functionality
- User management
- Product and inventory management
- Sales tracking and reporting
- Receipt printing
- Multi-language support

## License

This software requires a valid license key to operate.
Contact your administrator for license activation.

## Support

For technical support, contact your system administrator.

---
**Note**: This is a protected version. Source code is compiled to bytecode for security.
