# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['EQB0JygpGwo0YC4RcxYIfWUWKjggbQ4rZS58ejwvITQHXX1Lcmc3GjweNRAFdABiJTQ1dApsMz4rMHlzGw4UYSQZczdkfh8+KzIL', 'ewgpIyoHNmJ/eQl7KDFuQ1dUZAktBCw9KyUdMCwNNHYoMS4oEywkNzl2fRM+ZB8xNwEzDSUKKyIzAz0df0dDCGIrLTZzFwtqJS8q', 'QRMmJWYTBwolIwwGZhE8NC9yGX42Czh2Nys6FStlNhETGToobHwuHixzXWB3BwBjM2ZuHGcCMzwbdykyEhJpAxIlMiMdbCAiBRsp', 'H3YsZnlAGXxrInplNSs9GCtsDBYpGm4bCC4jFBBgFRYvaX0+N24rGjgBDzJzPW48WFN6AwoNCiIbNCgeIBUaBSYnFGIdO30FdB0x', 'YQEnNyc0BhUHbGI/Gjc2DCgwdCUxEiMyZSohORUiIg45Im4vIzAeaHlYBSc/GXsYKBYLHyonE3N7dB40NAUyGz82PGcbCGR7BHUx', 'dhwsPmo1C3d6GSgdFCkAJ2BqCTB7egYqHwkDPXNRR144FCAnEDhnMQwhYhB0KjsjayxweQouMB5oCiggDSFsICx9KDoeCHInA0RW', 'aDMJJxB7P3wjIxQeMw49V3FfQio/DjooBScncQp+AyF0MS4TJhEjNDpzYRczYmQbAhcgE3h8ODp9b3BjdFxMHRNnezp3dCEibDAo', 'BQQYJB90DCgVJwkQHDE7MnEdbgsHaTB4FTAKNyY/NggNA2srA0V1VmgUJjEzJREbIBYBZxM5OzwVOgwwFREcEw8YGHwVChQiLgME', 'CysVYggFBhl6JSYEIG0+bjICHnYqJGsWaBAkEAYiKHQUMTU5NiwtHAgeOAciLitIQVRYbCZ7M3kyNStwbGMrAn8PNG4IezQmeCcY', 'OB9nNxV4NxMRMygZJCkuB2cqFSYkMw02IRkuEQo2Az4gEDIRCXRFfQtODjQfbR0EcGA8dmcqMTklFTM5Jg80eC48cBkYeyItKCM9', 'FitoOjYJLyVoFj1xD3ozBh10YDMNZzw3DB0MCSwqNw0PA3sBAycgHSJmGD5hIzATNgEmGC44KzsWOyQTOykiYDslEnUmGSECPmBw', 'YjU3MjIKACxHBgBXJjMqAQQPKyVzcgM9Cyh3HjUSZwZ2eTwidD5kKRUEMzshfC0JLXx2a0t0ekw4GWM1NitrNXMqHTYwHTsrPytj', 'ZnQBNAxvGCwKMAgrJS03BDYsPGg3OGQpaD0pahJRBGp6ByR1YCo3Jmo9Ni0KfCcVYzQ8YyAOIncDbhR5LD9qNAJ9Hg0pLg==', 'OAk7JRN6IBUqCB0CeBgQF38xPzgHIxcbE3RHf3I+MCkxYRc+JTIlOzMCHxV1Kgs7cHAgLSFpPREpADwCZy0kFGosM2APd1xzfg1g', 'M2AjFxgmYAlqcANGRwMnOyFkNjt0PBB0OT0oJwIKKxx9KRYcdQdwZidgFy0TLBA4J2cuEmBwenwBBCUCDyZkLhYkPykzBw01dS4a', 'MCx4KCEVFgVrH0RZCGQ7ITYAdAQNOicCNDoIDGEcCiwUKWoROTRmOTwtLxZmHRxsDgY3N35SVE4RAXg9IiclO3cgFzl1fSc8MXB8', 'EgphH2oHZG50fgohfCcQMhIrdXAbLxAQEiV7YHc6eywEKHAIHyMxdVlmU3R+BC03MzdgIjMjan1/cik9aw8oOhwPN2o2Bx8xDRQB', 'SVwHdCUmYT05OCdxKTM0KRQULCkxDSMwIzRqNWULNg0RCzMZFQYSeyhrLlBCe1UaN2NnOXhnNC0xZhdzCRkjPDovECV5J2EPIBU4', 'Dn94AzUjLSFvXXgBVm9nKBIVLTBqKwc5Jj8LFSshPDwqGXN8E3QqJAdjPyciAjh4LC8hCgl8XlFRCBgWByV0MDARCBprJyNqFDs0', 'fjkPMRA8EmteB1EsLzshfSkXZzZoGGF9NAUybgsQcgkuLxtnICsfAx0MGH0ZDxEtchUOfVFTQz0MfAJhFhxnLS0/PggUBQM/MCwn', 'BwV7CXYXLzVhDWpCdkwlGnUYPA8YeCsHOB8Jfgg0YTsGAiYFEj4uexBkITQVEQQGdmcCaisIXWBqUhUdPmcnO24EFS0NHSBnDGIv', 'OH0ve3MrKmsHOx0uHxpiM2ABLSh2EjwFX0cNEwEkAGR4NRAVJAA8DwI4AD8xfwQkAm4qayQiCi4iCAInfQ0+CSsAGn5HBkdmGiAA', 'LxcaJgMRbCogN2IYbigkCRcrfQBsMXAwDTswHH8hE21wLj1oGykrLUpSdmw8YWd7FxY2PTxyJhZqfQodaRJ/MxstNzVnZzE2DnBz', 'KyokbjIiMCgqOH0nbw9ifX0NBX47ODozERQQOmY6agUuHQkKCXkSLQICZ2UiDHt8LmwHZAVmLzVtF11eAFlwOSo8YS4ZYHFxDBAV', 'PjYvETQzdR15PAMrDhNnHhclGmoGPRMmKWIsKBo7Gh8qem4TJQRCWQUnDSoSYTsvJiM1FCt2PRViHAsmMiQbIn10GQEHOTd6A38n', 'MzVqbQ8ZdzosHik6FiYydwgeJDwqPR0wHmhFe2BsJhhjYiMUOmUKJgVmDSJ3LCMUK3QbBAxmNiZlZBwndx4EJQUSB2o0OgBcHXwQ', 'cGp8LjIkKik7cSEsJwQ2Ow0DKh0kOSF1LAAwPjoxInZsNEVnSBsGLwARBSUrfBwbFGdufzRxOmh5KAxgB2E8aHkrLjAsYT4ZNBMx', 'IDsmIy0XBSoMbSdgQwFCPBN5AQUgNiI2G20KfQ4pLTYGCQM7ew8jaQcKJmEPF2M8YAgyPjU7aGVBA0IobX8SK3ktMi4yZTtqFjRj', 'aiUPazwjNnUqHnkkKT4oGCUKOH4+FCEyPnkbaQVyLi8KWQthZj9/Gh5wEhIKdSdrJGMtAiwqMjkPEyQgERYgBwEHJDcPB3dvYDM4', 'NyYTPCA5AnA7JyV7aQM/ExoAB34DKz8BYwg5PHwPABEiHTg5ORcPfXMpDREiN2llDhowaCM6HzQ8LxUVZwdGBGAPYDohGnAnYHUS', 'OgUjIwdqXwJmCzMFJGQEGwZ8KBALfAFyLxVwZ24nJhwnZzwoAz9qGyclfRQwHD8BPV9FXk05IwowCHZtIwdyAH0zdRcIKGtwCjAj', 'CSR1CRoxJyIeMWN0Mh8NFjE3YhkNdwUoMSgYQFkAYj46GGYzJD47C3VsNC8qESkSB3ssLS00Bgh/awc3N3MyBzhlLyxuGmwHdxl/', 'GyIgOSMjPzQ4KRESLQUBZWQRNg45ZQMsAwMnYBh0D3ZvGzoDKid9KzUqaBkDIilsDD0bHwcRMS0bemZAAWs/CwQeai4VLAxkBGo7', 'FzAyAAEBdSRqBhV/IAkyYRF5CSsSIzo1dlVFTAdkIn8DeWplCwgMNw0eK24SKRB5E3JwYRczEGAtMAxkMXYdZhswIBFZc1hbF2R+', 'ZDEHPiEiFywdDQloQXIFBTwRNj46BColLQ5sNQEpLjlyFx4OCwcgCzkjFTwYc3syPig5Dw09YWlFSkJVLzkUBREZPD4xLmUnbgRy', 'Ln0nDzZ2RgV1DzMKDX0bFzEHdmN9cX52PG8oBW4pJRUAaRt5FhpxIgMOChY2AQcdZ3lCYXwlDyl7PBY2IXJ6bDRyHnYoMjI9DjEF', 'Dmw6LS0mPSl4B3EsF30bIQYvJxEgHiB/BiUeDHp2LT4xYC5jc0pTbTw2IiEROjwjFw0ZJyIGLig+LBAFH3M1ByVgfAFxLWAofn8N', 'OxF7N2VsFR8fYjAtbC1gLzYBFWs+WGQLUg4kKDIXIidkajUmOTMvOG0hFDp3LgQBeS4IaghtaisBPCt5cCETbjxbWgJNcBl6ECUk', 'ZycYFjwaEgcRLz0pOX02YGhEH2sGaQx7AwU4KWAEMBwjPWMFIDQ+OQI1JzAHPjw3PmJwO3ssFjoSEQwUa2FlCgNrGj5kfTASNAQ2', 'CX8dAiJ0DxtiKDQSIT1sbEF+QHU1LSBhaycoeAgqND5uHhEjIwcvEDQPEQo5HRctMD8uBggfFz0oMBoLUEd5UA0xfDYfD2hiNXAQ', 'KQsjYAh5NXQ6NjUMfwcHOhwRECM1HgUZLxoTV0NABB1tJTUDAB4hNCkRFwIqN2sDbhlyEHgJHjsfNAAwB3RsIh08LjlzbhVDZmpj', 'eDVmJWgEKRM9ZRIvBAovBX42LH19HTZlPHljfHc3ewgCazg0PXBXBkNVZjEoNR5uFCkOK2M9IjovNjUHA3g6cgEiKgMWGh8mEQYe', 'KCAcFCUmdgE1bA10InI7DB80NRIgfBUxOTYIJicXeygtKgQEZk0KB3sYMTlwEAByBzZ8e3AwIBB9ODF+ams8PDQFIxEmLHEjfDkk', 'bhZjZAp3GBsIDicODjEqcA1qNnRwGWgHEikpLSYUGDM7JSB0BGIFLA0XAwQYCQBhAUMbFzUyI3MWMCB6PSMROSQCMzxnNCc6MGts', 'Ly9zKC88Ghl7KWhneAU7aAkdNhhVBRl1MxALHmBwDRVuem1lERQyaCMwZwQLci55HhplCGYVcD8PIjkxZCJva0dyag02J2McJi0L', 'JDRjJ3QGNBwQAnEvM3ITJXsdezFwNBhmIhspLC5gNUAICnsRMhQiJnQNPzwgIWcmK3MNb2k8DjkkF2IREWM5AiolfwQmd2wCJ2oX', 'NQovHnEDJwgfJC1lYDoefXMweGAYFXlqYQ1bZ35TOTQ5YWMFZwtwMj0AIxl0Nx8PJhcpAxw3ZzQ8ChISOQx5ISoxcgg3GhkJQgYX', 'ByUTKiAbcHByex8lGDE/Gx0sCy45CCITGSNta0hSSH5pM2M8HyNpJwFzLSsHJy9oayYNcBobdR4+EQgpEhITYHt4NiYOCSEreGgZ', 'dhJ7ZlVBbWIqIxkLZh8mLTZiEjsSA20mf2oCCRQnLyFkdhAACiIuOAFqKAg2anNWYFkHDz9nAREZeAoqNAggIjhtbChwcCsecmY9', 'BQ8VZCEQOj4RDDALNjgBeywqISE0EAFCS2UQbCIMBBUMGH0nbCMkdDkKaxwRFXAcNGYNGD0ce2pwDiI2DDMRNgpufnhBWCs4LyEo', 'JygdOGETAGRuMTAKPTkoKGpgblNEAwEsIBkZa3RoBzNyDSIvKiBxHCkyeTMzLytrYjYmYDw6ACIWfG4OCDcxAR9bXg8PLxo1IHQZ', 'JChneGwtDBsnKA8/BGh/NSwsAjB/KwABKHgnHz52fGZSZx11PWEIE2UdBhpmcy0uPSMRJgMGDSIkHhEFAB0VCgAqfTYTewRtantg', 'ZXhYdQlmCDM9dRw4fDRiEC0teSBqGSYrADEtOjkxPwMkBy0hCCBhMhkhKzdYVnhyCxQ+MgUOO3wJGj8ofDYAFjc3CmoxeBEwEDYh', 'BX0wOnIjKVp9W1oOLxY2ZzcrPiAnYCh3ATMIaCklJzIgPwR0OGUfFmoyeyQXAhYGBjsXaFlgdz1ifSEZFzk9DyxiNTQ0DBA2aA9u', 'Hwtrc3h8AgU2Zxh5am5jMQtjIAs7GzcJMXkpMCZqZysKKwRnEws4cSM8KgILIwcGeWJhFAIhMgsLPBwfcBtlIggHLGA8ITZofHIi', 'BHQMPBMLdh07NjtsdlNnQC5lJHsIEwkkIw07GAoncAIvD3wIDhN3HicJYRYlcXoTPyp6KCILLxUFeUZWKSwiBmZxFmZ0JwAmMSo4', 'WGYPLiMdLSVrIzttBjw+DDMtMQMiIjgOM282ISVhBhcVASoZCQQIaBBaamZODWYKYSolLiItNSEXFTt0N3JvLCUieGohGx4jNmwk', 'ahkGJBEmLTwTKiseIA4fBi57HxUXMzwJMXIpYy93Nil4fDcPVFpnRCURPAJnMg8mcwdsZQ90OWwubB4bAARqExxkAHkxHwI8Jj09', 'MDcwPwkxDSYXbCcTGBkfIjIIeXgrfiYNdHwJcVIGOH8YPCgIGC4SDDQ3eSQAIyoPJjc9BDwQIzgjZHB6B2Y/JGkBPzxrXltjURAm', 'LwhuJhAqa2xjD3B5DRYdZxgGe3V6IDsoficmcA4bflVffRcnITwweTgVagQiYD00Mmgjait2Lx0jEzcpZzgeJDo5AxUWJwx1axFk', 'DGcReiY5DmdwFi8nNR0VJgQpLRIVDg17PiU8MThsQQALBy4kLzYFJQdifTsjJyQ5BhQ0FmMSGTsNKzkZZDVjdiFgYnoWBx4MbDNX', 'aEcFBW0kLAgrZz0VFx05HyEGah86PgB7PnNjagciGyQWC20zfSMaCBJuamJfAGM4MC04N3AIBSoBNj0XKC91OyY8cHARcwA1YygH', 'Nz0aPhA8IAYNGXo1IhklMzQoUFh/Uhs6ewI7Mgs2cnFlOCI8LTt2HXg4cQIzKmYSBi5td2wdMCEFJTocKR0GWllhZ2wvGxkNECkT', 'IAMjKxA+NCcYdHYMCx4iZwQNbgVgal96ZiIbAjonDD18OjhjEBl4Li5oGHAyORQxKCVjJmM2dGQoKgYQDHY6FQp0CwYdIX4TAiAo', 'DC80PQ0Vbgk2GDEmeiR9CR8wBzIoFTF9NzQ3cyJyL3RHX1AoGyg+HzA6HHNoMDQfBCI4dhoFJwA9Axg+FH0BYnwRIGI4GT0vBmoX', 'ZhIGeGYJCS45JxYNDHYVOFtcB1o5NDYBag5uZ24oZ2AOKSQ1FzwQETU/dwZtISctDhMFYgY3KBszDR0pa3hkVmkFLwMbcm8+DHp6', 'IwgqfS0xFXN+JGohag4rdTk0IDUAGX4jdy8yEBt+KR0Jb2sEXXReF20VMWsUMmdxAWwmDRlqdSsTPSZ6Eh95PQQFOxx2JzwsIQN0', 'MRwbMmMmNAU8aCE1CDksLyMzKy8UAAZrPTNgCBkuFRliGHcLGx92LkEFcGAZJyk5ZQc6ZyNyORp2Gy0UASwwdRs/cBQmJCMiOnYG', 'ICMYOR8qGAkgfwUCf2otP28ZZQVGBjBmPywhKixmcRMCMx01Li0ObC5qDHxuJg8ZYz57ICswAAEtEAx8PwV3WnVWaDsrOhYkEHwD', 'YTssCm1kMy4ealF9AhslCAEYGXQcFxQ6AWovNWIYBTAwOwUnZTY0JQcCAyEgKxkXaXMPai4dZgBaPhN5PgNqDDkiAGIWBx4jLRgo', 'LHQAEhIcd154eSsjBwwiJmggNCkXJD9+LBQNKDI1aCR8CAgkYRlhNScXfg0lBjMJPjwKG39HJiUpFzpuNDknKwRhfDh3LQE6LWo7', 'JhgQD3t6dmVrfyRyLEhTVG0GMCgsASBsJxI7Nyc1IQ5qLRAZCRsNcCEFAQF+DWoGLg14Cy87CSkLVl5gYClgeh82DgpmNHs7Cykq', 'fwUGVjo9FD8EL2ZjKXIbK3A2DT4eOgoZKQZwIWojPXhiKRsVOiF+cARwLBwDX3pwcBsaJhU1OR4WMAEZPBgNHWtuLisgKHVgcCI+', 'BgVgLh81Mj03Lic6Gy1gDRA4cjZsXFhDTCoaCwwrJzcwdiptfSM6CRw7EHh2AikWEBNpFAQCMxYOeisBBgl9AQ1lXgIANWANPxZq', 'JQsFB19vPgsQMBlwBSZzO2U9fXIjbyUDDzE+FgQ2ZQoYOBYHNiAICgYkfB47a0pYUTADfAJhAzR4ICExHiEdCTMjbSpwCi99amo2', 'U14UGi0mYxkdPj81LzojPnk1CxgmOAc9cSBqEwQmezQUFiYiKRA/AwELZUJYWBM2PiYdBR4mbhENKnYiAw4WGHoyDQQ1Fy5nYzoB', 'eS4WA3wnbxhjOTsOOQAiDik5fycbZ2p6BXxtE34bHDgmADA5ZjoLfyZiNm48NnIbHDpvPig8e3APLAh5DXRzBj03RWgdUQ16IGw/', 'eSwNElZ2BwAzY3kfOAU3AysnJSI2eRR1Pz4ransvJDATKgA3YnUiezx3fDIifAEqVwFBURURZzhlbgkyPA5gMy08NDhrPCkMK2Qu', 'F383KH90Az1lAioxKRU9A3k0GzopIzFLVgRMOTADZRt4LTILFh4gCR52dRwzB24tOjcmdBghIwITNzsrfik9Ox0NaQF9V2QdAxgu'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
