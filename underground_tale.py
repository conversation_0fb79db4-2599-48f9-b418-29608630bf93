import pygame
import random
import math
import json
import os
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
GOLD = (255, 215, 0)
SILVER = (192, 192, 192)
DARK_RED = (139, 0, 0)
DARK_GREEN = (0, 100, 0)

# Animation and UI classes
class Animation:
    def __init__(self, duration=60, loop=False):
        self.duration = duration
        self.current_frame = 0
        self.loop = loop
        self.active = True

    def update(self):
        if self.active:
            self.current_frame += 1
            if self.current_frame >= self.duration:
                if self.loop:
                    self.current_frame = 0
                else:
                    self.active = False

    def get_progress(self):
        return min(1.0, self.current_frame / self.duration)

    def reset(self):
        self.current_frame = 0
        self.active = True

class FloatingText:
    def __init__(self, x, y, text, color=WHITE, duration=120):
        self.x = x
        self.y = y
        self.start_y = y
        self.text = text
        self.color = color
        self.duration = duration
        self.timer = 0
        self.active = True

    def update(self):
        if self.active:
            self.timer += 1
            self.y = self.start_y - (self.timer * 0.5)  # Float upward
            if self.timer >= self.duration:
                self.active = False

    def draw(self, screen, font):
        if self.active:
            alpha = max(0, 255 - (self.timer * 255 // self.duration))
            text_surface = font.render(self.text, True, self.color)
            text_surface.set_alpha(alpha)
            screen.blit(text_surface, (self.x, self.y))

# Enhanced asset drawing functions with animations
def draw_dice(screen, x, y, size, sides, value=None, color=WHITE, animation_frame=0, rolling=False):
    """Draw an animated dice with specified number of sides"""
    if value is None:
        value = random.randint(1, sides)

    # Rolling animation
    if rolling:
        wobble = math.sin(animation_frame * 0.3) * 3
        x += wobble
        y += wobble * 0.5
        # Show random values while rolling
        display_value = random.randint(1, sides)
    else:
        display_value = value

    # Draw dice shadow
    shadow_offset = 3
    pygame.draw.rect(screen, (50, 50, 50),
                    (x + shadow_offset, y + shadow_offset, size, size))

    # Draw dice body with 3D effect
    pygame.draw.rect(screen, color, (x, y, size, size))
    pygame.draw.rect(screen, BLACK, (x, y, size, size), 2)

    # Add 3D highlight
    pygame.draw.line(screen, WHITE, (x, y), (x + size, y), 2)
    pygame.draw.line(screen, WHITE, (x, y), (x, y + size), 2)
    pygame.draw.line(screen, GRAY, (x + size, y), (x + size, y + size), 2)
    pygame.draw.line(screen, GRAY, (x, y + size), (x + size, y + size), 2)

    center_x = x + size // 2
    center_y = y + size // 2

    if sides == 2:  # Coin with flip animation
        if rolling:
            # Spinning coin effect
            width = max(5, int(size * abs(math.cos(animation_frame * 0.2))))
            pygame.draw.ellipse(screen, GOLD, (center_x - width//2, y, width, size))
        else:
            if display_value == 1:
                pygame.draw.circle(screen, GOLD, (center_x, center_y), size // 3)
                font = pygame.font.Font(None, size // 3)
                text = font.render("H", True, BLACK)
                screen.blit(text, (center_x - text.get_width()//2, center_y - text.get_height()//2))
            else:
                pygame.draw.circle(screen, SILVER, (center_x, center_y), size // 3)
                font = pygame.font.Font(None, size // 3)
                text = font.render("T", True, BLACK)
                screen.blit(text, (center_x - text.get_width()//2, center_y - text.get_height()//2))
    else:
        # Standard dice with dots or numbers
        if sides <= 6:
            draw_dice_dots(screen, center_x, center_y, size, display_value)
        else:
            font = pygame.font.Font(None, size // 2)
            text = font.render(str(display_value), True, BLACK)
            screen.blit(text, (center_x - text.get_width()//2, center_y - text.get_height()//2))

    return value

def draw_dice_dots(screen, center_x, center_y, size, value):
    """Draw traditional dice dots"""
    dot_size = size // 12
    offset = size // 6

    # Dot positions for each value
    dot_patterns = {
        1: [(0, 0)],
        2: [(-offset, -offset), (offset, offset)],
        3: [(-offset, -offset), (0, 0), (offset, offset)],
        4: [(-offset, -offset), (offset, -offset), (-offset, offset), (offset, offset)],
        5: [(-offset, -offset), (offset, -offset), (0, 0), (-offset, offset), (offset, offset)],
        6: [(-offset, -offset), (offset, -offset), (-offset, 0), (offset, 0), (-offset, offset), (offset, offset)]
    }

    if value in dot_patterns:
        for dx, dy in dot_patterns[value]:
            pygame.draw.circle(screen, BLACK, (center_x + dx, center_y + dy), dot_size)

def draw_roulette_wheel(screen, x, y, size, spinning=False, animation_frame=0):
    """Draw an animated roulette wheel"""
    center_x = x + size // 2
    center_y = y + size // 2

    # Draw shadow
    pygame.draw.circle(screen, (30, 30, 30), (center_x + 3, center_y + 3), size // 2)

    # Draw outer wheel with gradient effect
    for i in range(5):
        radius = size // 2 - i
        color_intensity = 100 + i * 30
        wheel_color = (0, color_intensity, 0)
        pygame.draw.circle(screen, wheel_color, (center_x, center_y), radius)

    pygame.draw.circle(screen, GOLD, (center_x, center_y), size // 2, 4)

    # Draw segments with rotation
    segments = 18  # More realistic roulette
    rotation = animation_frame * 0.05 if spinning else 0

    for i in range(segments):
        angle = (i * (2 * math.pi / segments)) + rotation
        next_angle = ((i + 1) * (2 * math.pi / segments)) + rotation

        # Alternate red and black
        if i == 0:  # Green 0
            color = GREEN
        elif i == 9:  # Green 00
            color = GREEN
        else:
            color = RED if i % 2 == 0 else BLACK

        # Draw segment as a triangle
        inner_radius = size // 6
        outer_radius = size // 2 - 5

        points = [
            (center_x, center_y),
            (center_x + outer_radius * math.cos(angle),
             center_y + outer_radius * math.sin(angle)),
            (center_x + outer_radius * math.cos(next_angle),
             center_y + outer_radius * math.sin(next_angle))
        ]

        pygame.draw.polygon(screen, color, points)
        pygame.draw.polygon(screen, GOLD, points, 1)

        # Draw numbers
        if i < 10:
            num_angle = angle + (next_angle - angle) / 2
            num_x = center_x + (outer_radius - 15) * math.cos(num_angle)
            num_y = center_y + (outer_radius - 15) * math.sin(num_angle)

            font = pygame.font.Font(None, 16)
            number = str(i) if i != 9 else "00"
            text = font.render(number, True, WHITE)
            screen.blit(text, (num_x - text.get_width()//2, num_y - text.get_height()//2))

    # Draw center hub
    pygame.draw.circle(screen, GOLD, (center_x, center_y), size // 8)
    pygame.draw.circle(screen, YELLOW, (center_x, center_y), size // 10)

    # Draw ball with realistic physics
    if spinning:
        ball_speed = 0.15 - (animation_frame * 0.0001)  # Slow down over time
        ball_angle = animation_frame * ball_speed
        ball_radius = size // 2 - 20 + math.sin(animation_frame * 0.1) * 5  # Wobble
        ball_x = center_x + ball_radius * math.cos(ball_angle)
        ball_y = center_y + ball_radius * math.sin(ball_angle)

        # Ball shadow
        pygame.draw.circle(screen, (100, 100, 100), (int(ball_x + 2), int(ball_y + 2)), 5)
        # Ball
        pygame.draw.circle(screen, WHITE, (int(ball_x), int(ball_y)), 5)
        pygame.draw.circle(screen, GRAY, (int(ball_x), int(ball_y)), 5, 1)

def draw_card(screen, x, y, width, height, suit, value, face_down=False, animation_frame=0, flipping=False):
    """Draw an animated playing card"""
    # Card shadow
    pygame.draw.rect(screen, (50, 50, 50), (x + 3, y + 3, width, height))

    if flipping:
        # Flipping animation - compress width
        flip_progress = abs(math.sin(animation_frame * 0.2))
        current_width = int(width * flip_progress)
        if current_width < 5:
            current_width = 5
        x_offset = (width - current_width) // 2

        # Draw compressed card
        pygame.draw.rect(screen, BLUE if flip_progress < 0.5 else WHITE,
                        (x + x_offset, y, current_width, height))
        pygame.draw.rect(screen, BLACK, (x + x_offset, y, current_width, height), 2)
        return

    if face_down:
        # Card back with elegant pattern
        pygame.draw.rect(screen, BLUE, (x, y, width, height))
        pygame.draw.rect(screen, GOLD, (x, y, width, height), 3)

        # Decorative pattern
        for i in range(2):
            for j in range(3):
                pattern_x = x + width//4 + i * width//2
                pattern_y = y + height//6 + j * height//3
                pygame.draw.circle(screen, GOLD, (pattern_x, pattern_y), 4)
                pygame.draw.circle(screen, WHITE, (pattern_x, pattern_y), 2)
    else:
        # Card front with rounded corners effect
        pygame.draw.rect(screen, WHITE, (x, y, width, height))
        pygame.draw.rect(screen, BLACK, (x, y, width, height), 2)

        # Corner rounding effect
        corner_size = 5
        pygame.draw.circle(screen, WHITE, (x + corner_size, y + corner_size), corner_size)
        pygame.draw.circle(screen, WHITE, (x + width - corner_size, y + corner_size), corner_size)
        pygame.draw.circle(screen, WHITE, (x + corner_size, y + height - corner_size), corner_size)
        pygame.draw.circle(screen, WHITE, (x + width - corner_size, y + height - corner_size), corner_size)

        # Determine color
        color = RED if suit in ['♥', '♦'] else BLACK

        # Draw value in corners
        font = pygame.font.Font(None, height // 4)
        value_text = font.render(str(value), True, color)
        screen.blit(value_text, (x + 5, y + 5))
        screen.blit(value_text, (x + width - value_text.get_width() - 5,
                                y + height - value_text.get_height() - 5))

        # Draw large suit in center
        big_font = pygame.font.Font(None, height // 2)
        suit_text = big_font.render(suit, True, color)
        suit_x = x + width//2 - suit_text.get_width()//2
        suit_y = y + height//2 - suit_text.get_height()//2
        screen.blit(suit_text, (suit_x, suit_y))

        # Draw small suits in corners
        small_font = pygame.font.Font(None, height // 6)
        small_suit = small_font.render(suit, True, color)
        screen.blit(small_suit, (x + 5, y + height//4))
        screen.blit(small_suit, (x + width - small_suit.get_width() - 5,
                                y + height - height//4 - small_suit.get_height()))

def draw_slot_machine(screen, x, y, width, height, reels, animation_frame=0, spinning=False):
    """Draw an animated slot machine"""
    # Machine shadow
    pygame.draw.rect(screen, (30, 30, 30), (x + 5, y + 5, width, height))

    # Draw machine body with gradient
    for i in range(5):
        color_val = 150 - i * 20
        pygame.draw.rect(screen, (color_val, color_val, color_val),
                        (x + i, y + i, width - 2*i, height - 2*i))

    pygame.draw.rect(screen, GOLD, (x, y, width, height), 4)

    # Draw title
    title_font = pygame.font.Font(None, 24)
    title_text = title_font.render("LUCKY SLOTS", True, GOLD)
    title_x = x + width//2 - title_text.get_width()//2
    screen.blit(title_text, (title_x, y + 10))

    # Draw reels with spinning animation
    reel_width = width // 5
    reel_height = height // 2
    reel_y = y + height // 3

    symbols = ['🍒', '🍋', '⭐', '💎', '7']
    symbol_colors = [RED, YELLOW, GOLD, CYAN, GREEN]

    for i in range(3):
        reel_x = x + width // 6 + i * (width // 4)

        # Reel background
        pygame.draw.rect(screen, BLACK, (reel_x, reel_y, reel_width, reel_height))
        pygame.draw.rect(screen, WHITE, (reel_x + 2, reel_y + 2,
                                       reel_width - 4, reel_height - 4))
        pygame.draw.rect(screen, BLACK, (reel_x, reel_y, reel_width, reel_height), 2)

        if spinning:
            # Show multiple symbols scrolling
            scroll_offset = (animation_frame * (i + 1) * 2) % (reel_height + 20)

            for j in range(4):  # Show 4 symbols for scrolling effect
                symbol_y = reel_y - 20 + j * 30 - scroll_offset
                if reel_y <= symbol_y <= reel_y + reel_height:
                    symbol_idx = (reels[i] + j) % len(symbols)
                    symbol = symbols[symbol_idx]
                    color = symbol_colors[symbol_idx]

                    font = pygame.font.Font(None, 32)
                    text = font.render(symbol, True, color)
                    text_x = reel_x + reel_width//2 - text.get_width()//2
                    screen.blit(text, (text_x, symbol_y))
        else:
            # Show final symbol
            if i < len(reels):
                symbol_idx = reels[i] % len(symbols)
                symbol = symbols[symbol_idx]
                color = symbol_colors[symbol_idx]

                font = pygame.font.Font(None, 40)
                text = font.render(symbol, True, color)
                text_x = reel_x + reel_width//2 - text.get_width()//2
                text_y = reel_y + reel_height//2 - text.get_height()//2
                screen.blit(text, (text_x, text_y))

        # Reel glass effect
        pygame.draw.rect(screen, (255, 255, 255, 50), (reel_x, reel_y, reel_width//3, reel_height//3))

    # Draw lever
    lever_x = x + width - 20
    lever_y = y + height // 2
    pygame.draw.rect(screen, BROWN, (lever_x, lever_y, 15, 40))
    pygame.draw.circle(screen, RED, (lever_x + 7, lever_y), 8)

    # Draw coin slot
    slot_x = x + 10
    slot_y = y + height - 30
    pygame.draw.rect(screen, BLACK, (slot_x, slot_y, 30, 5))

    # Draw payout tray
    pygame.draw.rect(screen, SILVER, (x + 10, y + height - 15, width - 20, 10))

def draw_poker_chips(screen, x, y, size, animation_frame=0, wobbling=False):
    """Draw a stack of poker chips"""
    chip_colors = [RED, BLUE, GREEN, GOLD, WHITE]
    chip_height = 4

    for i in range(5):
        chip_y = y + size - (i * chip_height) - chip_height
        if wobbling:
            wobble = math.sin(animation_frame * 0.2 + i) * 2
            chip_x = x + wobble
        else:
            chip_x = x

        color = chip_colors[i % len(chip_colors)]
        draw_chip(screen, chip_x + size//2, chip_y + chip_height//2, size//4,
                 [5, 10, 25, 100, 500][i], color)

def draw_blackjack_table(screen, x, y, width, height, cards, animation_frame=0):
    """Draw a blackjack table with cards"""
    # Table
    pygame.draw.ellipse(screen, DARK_GREEN, (x, y, width, height))
    pygame.draw.ellipse(screen, GOLD, (x, y, width, height), 3)

    # Dealer cards
    for i, (suit, value) in enumerate(cards[:2]):
        card_x = x + width//4 + i * 40
        card_y = y + 20
        face_down = i == 1  # Second card face down
        draw_card(screen, card_x, card_y, 30, 45, suit, value, face_down)

    # Player area
    pygame.draw.arc(screen, WHITE, (x + 20, y + height//2, width - 40, height//3), 0, math.pi, 2)

def draw_lottery_machine(screen, x, y, size, balls, animation_frame=0, active=False):
    """Draw a lottery ball machine"""
    # Machine body
    pygame.draw.circle(screen, (200, 200, 255), (x + size//2, y + size//2), size//2)
    pygame.draw.circle(screen, BLACK, (x + size//2, y + size//2), size//2, 3)

    # Balls
    ball_colors = [RED, BLUE, GREEN, YELLOW, PURPLE, ORANGE]
    for i, ball_num in enumerate(balls):
        if active:
            # Bouncing animation
            ball_x = x + size//4 + random.randint(-10, 10)
            ball_y = y + size//4 + random.randint(-10, 10)
        else:
            ball_x = x + size//4 + (i % 3) * size//6
            ball_y = y + size//4 + (i // 3) * size//6

        color = ball_colors[i % len(ball_colors)]
        pygame.draw.circle(screen, color, (ball_x, ball_y), 8)

        # Number on ball
        font = pygame.font.Font(None, 16)
        text = font.render(str(ball_num), True, BLACK)
        screen.blit(text, (ball_x - text.get_width()//2, ball_y - text.get_height()//2))

def draw_bingo_card(screen, x, y, width, height, called_numbers):
    """Draw a bingo card"""
    # Card background
    pygame.draw.rect(screen, WHITE, (x, y, width, height))
    pygame.draw.rect(screen, BLACK, (x, y, width, height), 2)

    # BINGO header
    letters = ['B', 'I', 'N', 'G', 'O']
    col_width = width // 5

    for i, letter in enumerate(letters):
        col_x = x + i * col_width
        pygame.draw.rect(screen, RED, (col_x, y, col_width, 20))
        font = pygame.font.Font(None, 18)
        text = font.render(letter, True, WHITE)
        screen.blit(text, (col_x + col_width//2 - text.get_width()//2, y + 2))

    # Numbers grid
    for row in range(5):
        for col in range(5):
            cell_x = x + col * col_width
            cell_y = y + 20 + row * 15

            if row == 2 and col == 2:  # Free space
                pygame.draw.rect(screen, GOLD, (cell_x, cell_y, col_width, 15))
                font = pygame.font.Font(None, 12)
                text = font.render("FREE", True, BLACK)
                screen.blit(text, (cell_x + 2, cell_y + 2))
            else:
                number = col * 15 + row + 1
                if number in called_numbers:
                    pygame.draw.rect(screen, GREEN, (cell_x, cell_y, col_width, 15))

                font = pygame.font.Font(None, 12)
                text = font.render(str(number), True, BLACK)
                screen.blit(text, (cell_x + 2, cell_y + 2))

def draw_craps_table(screen, x, y, width, height, dice_values, animation_frame=0):
    """Draw a craps table"""
    # Table
    pygame.draw.rect(screen, DARK_GREEN, (x, y, width, height))
    pygame.draw.rect(screen, WHITE, (x, y, width, height), 3)

    # Pass line
    pygame.draw.rect(screen, WHITE, (x + 10, y + height - 30, width - 20, 20), 2)
    font = pygame.font.Font(None, 16)
    text = font.render("PASS LINE", True, WHITE)
    screen.blit(text, (x + 20, y + height - 25))

    # Dice
    for i, value in enumerate(dice_values):
        dice_x = x + width//3 + i * 40
        dice_y = y + height//3
        draw_dice(screen, dice_x, dice_y, 25, 6, value, WHITE, animation_frame, False)

def draw_wheel_of_fortune(screen, x, y, size, animation_frame=0, spinning=False):
    """Draw a wheel of fortune"""
    center_x = x + size // 2
    center_y = y + size // 2

    # Wheel segments
    segments = 24
    rotation = animation_frame * 0.1 if spinning else 0

    for i in range(segments):
        angle = (i * 2 * math.pi / segments) + rotation
        next_angle = ((i + 1) * 2 * math.pi / segments) + rotation

        # Alternate colors
        color = GOLD if i % 2 == 0 else RED

        points = [
            (center_x, center_y),
            (center_x + (size//2 - 5) * math.cos(angle),
             center_y + (size//2 - 5) * math.sin(angle)),
            (center_x + (size//2 - 5) * math.cos(next_angle),
             center_y + (size//2 - 5) * math.sin(next_angle))
        ]

        pygame.draw.polygon(screen, color, points)
        pygame.draw.polygon(screen, BLACK, points, 1)

    # Center hub
    pygame.draw.circle(screen, BLACK, (center_x, center_y), 20)
    pygame.draw.circle(screen, GOLD, (center_x, center_y), 15)

    # Pointer
    pointer_angle = math.pi / 2  # Top
    pointer_x = center_x + (size//2 + 10) * math.cos(pointer_angle)
    pointer_y = center_y + (size//2 + 10) * math.sin(pointer_angle)
    pygame.draw.polygon(screen, RED, [
        (pointer_x, pointer_y),
        (pointer_x - 10, pointer_y + 15),
        (pointer_x + 10, pointer_y + 15)
    ])

def draw_chip(screen, x, y, size, value, color):
    """Draw a casino chip"""
    # Draw chip body
    pygame.draw.circle(screen, color, (x, y), size)
    pygame.draw.circle(screen, WHITE, (x, y), size, 3)
    pygame.draw.circle(screen, color, (x, y), size - 5, 2)

    # Draw value
    font = pygame.font.Font(None, size)
    text = font.render(str(value), True, WHITE)
    screen.blit(text, (x - text.get_width()//2, y - text.get_height()//2))

def draw_player_soul(screen, x, y, size, color=RED):
    """Draw the player's soul (heart)"""
    # Draw heart shape
    heart_points = []
    for angle in range(0, 360, 10):
        rad = math.radians(angle)
        # Heart equation
        heart_x = 16 * (math.sin(rad) ** 3)
        heart_y = 13 * math.cos(rad) - 5 * math.cos(2*rad) - 2 * math.cos(3*rad) - math.cos(4*rad)

        # Scale and position
        scaled_x = x + heart_x * size / 32
        scaled_y = y - heart_y * size / 32  # Negative because pygame y is flipped
        heart_points.append((scaled_x, scaled_y))

    if len(heart_points) > 2:
        pygame.draw.polygon(screen, color, heart_points)
        pygame.draw.polygon(screen, WHITE, heart_points, 2)

def draw_casino_background(screen):
    """Draw casino-themed background"""
    # Dark background with pattern
    screen.fill(DARK_GREEN)

    # Draw diamond pattern
    for x in range(0, SCREEN_WIDTH, 100):
        for y in range(0, SCREEN_HEIGHT, 100):
            diamond_points = [
                (x + 50, y + 25),
                (x + 75, y + 50),
                (x + 50, y + 75),
                (x + 25, y + 50)
            ]
            pygame.draw.polygon(screen, (0, 80, 0), diamond_points)
            pygame.draw.polygon(screen, GOLD, diamond_points, 1)

    # Draw decorative border
    pygame.draw.rect(screen, GOLD, (0, 0, SCREEN_WIDTH, SCREEN_HEIGHT), 5)

class HowToPlayMenu:
    def __init__(self):
        self.pages = [
            {
                "title": "🎰 CASINO UNDERGROUND - HOW TO PLAY 🎰",
                "content": [
                    "",
                    "Welcome to the Casino Underground!",
                    "A mystical realm where chance rules all.",
                    "",
                    "🎲 BASIC CONTROLS:",
                    "• Arrow Keys/WASD: Move and navigate menus",
                    "• ENTER: Confirm selections and advance dialogue",
                    "• ESC: Go back or quit",
                    "• I: Open this help menu anytime",
                    "",
                    "🎯 OBJECTIVE:",
                    "Navigate through casino-themed encounters,",
                    "using luck and strategy to overcome",
                    "gambling monsters and reach the end!",
                    "",
                    "Press → for Combat Guide"
                ]
            },
            {
                "title": "⚔️ COMBAT SYSTEM",
                "content": [
                    "",
                    "🎲 TURN-BASED COMBAT:",
                    "Choose from four options each turn:",
                    "",
                    "• FIGHT (Red): Attack with dice rolls",
                    "  - Roll D20 for attack damage",
                    "  - Natural 20 = Critical Hit (2x damage)",
                    "  - Natural 1 = Critical Miss (no damage)",
                    "",
                    "• ACT (Blue): Interact with monsters",
                    "  - Each monster has unique actions",
                    "  - Some actions help you spare them",
                    "",
                    "• ITEM (Green): Use healing items",
                    "• MERCY (Yellow): Attempt to spare the monster",
                    "",
                    "Press → for Monster Types"
                ]
            },
            {
                "title": "👾 CASINO MONSTERS",
                "content": [
                    "",
                    "🪙 LUCKY COIN (D2): Flip for heads/tails",
                    "🎲 DICE FAMILY: D4, D6, D12, D20 (Boss level!)",
                    "🎯 ROULETTE WHEEL: Spin for red/black/green",
                    "🃏 CARD SHARK: Poker-themed with bluffing",
                    "🎰 SLOT MACHINE: Three reels, jackpot potential",
                    "",
                    "🎪 MORE CASINO GAMES:",
                    "• Blackjack Dealer: Hit, stand, or double down",
                    "• Poker Chip Stack: Don't let it topple!",
                    "• Lottery Ball Machine: Pick your numbers",
                    "• Bingo Caller: Call bingo to win",
                    "• Craps Table: Pass line or don't pass",
                    "• Wheel of Fortune: Spin to win big",
                    "• Scratch Cards: Instant win or lose",
                    "• THE HOUSE: Final boss of all casinos!",
                    "",
                    "Press → for Overworld Guide"
                ]
            },
            {
                "title": "🗺️ CASINO FLOOR MAP",
                "content": [
                    "",
                    "🏰 EXPLORE THE CASINO UNDERGROUND:",
                    "Navigate through different themed areas:",
                    "",
                    "🎲 DICE ROOM: Coin, D4, D6 enemies",
                    "🃏 CARD ROOM: Card Shark, Blackjack Dealer",
                    "🎰 SLOT HALL: Slot Machines, Bingo, Scratch Cards",
                    "🎯 MAIN FLOOR: Roulette, Poker Chips, Lottery",
                    "👑 BOSS ROOM: Face The House itself!",
                    "",
                    "🗺️ MINIMAP FEATURES:",
                    "• Red dot: Your location",
                    "• Purple dots: Regular enemies",
                    "• Gold dot: Boss enemy",
                    "• Real-time enemy tracking",
                    "",
                    "💡 TIP: Defeated enemies disappear from map!",
                    "",
                    "Press → for Bullet Hell Guide"
                ]
            },
            {
                "title": "💥 BULLET HELL MECHANICS",
                "content": [
                    "",
                    "After your turn, dodge the monster's attack!",
                    "",
                    "🎯 YOUR SOUL (❤️):",
                    "• Red heart represents your soul",
                    "• Move with Arrow Keys/WASD",
                    "• Avoid all bullets to take no damage",
                    "",
                    "🎲 BULLET PATTERNS:",
                    "• Dice: Bullets equal to their roll",
                    "• Roulette: Spinning wheel patterns",
                    "• Cards: Suit-shaped formations",
                    "• Slots: Three columns like reels",
                    "",
                    "💡 TIP: Watch the monster's animation",
                    "to predict their attack pattern!",
                    "",
                    "Press → for Chance Mechanics"
                ]
            },
            {
                "title": "🎲 CHANCE & LUCK MECHANICS",
                "content": [
                    "",
                    "Everything involves chance in the Casino!",
                    "",
                    "🎯 ATTACK ROLLS:",
                    "• You roll D20 for every attack",
                    "• Monsters roll their dice for defense",
                    "• Higher rolls = better results",
                    "",
                    "🛡️ MONSTER DEFENSES:",
                    "• Dice: Max roll = no damage taken",
                    "• Roulette: Green (0/00) = immunity",
                    "• Cards: Face cards reduce damage",
                    "• Slots: Jackpot = massive counter-attack",
                    "",
                    "💰 STRATEGY:",
                    "• Sometimes luck isn't on your side",
                    "• Use ACT options to change the odds",
                    "• Sparing might be safer than fighting!",
                    "",
                    "Press ← to go back, ESC to close"
                ]
            }
        ]
        self.current_page = 0
        self.animation = Animation(30, False)

    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE or event.key == pygame.K_i:
                return "close"
            elif event.key == pygame.K_RIGHT:
                if self.current_page < len(self.pages) - 1:
                    self.current_page += 1
                    self.animation.reset()
            elif event.key == pygame.K_LEFT:
                if self.current_page > 0:
                    self.current_page -= 1
                    self.animation.reset()
        return None

    def update(self):
        self.animation.update()

    def draw(self, screen, font, big_font):
        # Draw semi-transparent background
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(200)
        overlay.fill(BLACK)
        screen.blit(overlay, (0, 0))

        # Draw help window with animation
        window_width = 700
        window_height = 500
        window_x = (SCREEN_WIDTH - window_width) // 2
        window_y = (SCREEN_HEIGHT - window_height) // 2

        # Slide in animation
        progress = self.animation.get_progress()
        animated_y = window_y + int((1 - progress) * 100)

        # Window background
        pygame.draw.rect(screen, DARK_GREEN, (window_x, animated_y, window_width, window_height))
        pygame.draw.rect(screen, GOLD, (window_x, animated_y, window_width, window_height), 4)

        # Title
        page = self.pages[self.current_page]
        title_text = big_font.render(page["title"], True, GOLD)
        title_x = window_x + window_width//2 - title_text.get_width()//2
        screen.blit(title_text, (title_x, animated_y + 20))

        # Content
        y_offset = animated_y + 70
        for line in page["content"]:
            if line.startswith("•"):
                color = WHITE
                x_offset = window_x + 40
            elif line.startswith("🎲") or line.startswith("🎯") or line.startswith("💡"):
                color = YELLOW
                x_offset = window_x + 20
            elif line == "":
                y_offset += 10
                continue
            else:
                color = WHITE
                x_offset = window_x + 20

            text = font.render(line, True, color)
            screen.blit(text, (x_offset, y_offset))
            y_offset += 25

        # Page indicator
        page_text = font.render(f"Page {self.current_page + 1} of {len(self.pages)}", True, GRAY)
        screen.blit(page_text, (window_x + 20, animated_y + window_height - 30))

        # Navigation hints
        if self.current_page > 0:
            left_text = font.render("← Previous", True, YELLOW)
            screen.blit(left_text, (window_x + 150, animated_y + window_height - 30))

        if self.current_page < len(self.pages) - 1:
            right_text = font.render("Next →", True, YELLOW)
            screen.blit(right_text, (window_x + window_width - 100, animated_y + window_height - 30))

class GameState(Enum):
    OVERWORLD = 0
    BATTLE = 1
    DIALOGUE = 2
    MENU = 3
    STORY = 4
    HOW_TO_PLAY = 5

class TileType(Enum):
    FLOOR = 0
    WALL = 1
    DOOR = 2
    ENEMY = 3
    TREASURE = 4
    STAIRS = 5
    SHOP = 6

class ItemType(Enum):
    WEAPON = 0
    ARMOR = 1
    POTION = 2
    SCROLL = 3
    TREASURE = 4

class Item:
    def __init__(self, name, item_type, value, description, rarity="common"):
        self.name = name
        self.item_type = item_type
        self.value = value
        self.description = description
        self.rarity = rarity

    def get_color(self):
        """Get color based on rarity"""
        rarity_colors = {
            "common": WHITE,
            "uncommon": GREEN,
            "rare": BLUE,
            "epic": PURPLE,
            "legendary": GOLD
        }
        return rarity_colors.get(self.rarity, WHITE)

class BattleState(Enum):
    MENU = 0
    FIGHT = 1
    ACT = 2
    ITEM = 3
    MERCY = 4
    BULLET_HELL = 5

@dataclass
class PlayerStats:
    name: str = "Frisk"
    level: int = 1
    hp: int = 20
    max_hp: int = 20
    attack: int = 10
    defense: int = 10
    exp: int = 0
    exp_to_next: int = 10
    gold: int = 0

    def gain_exp(self, amount):
        """Gain experience and check for level up"""
        self.exp += amount
        if self.exp >= self.exp_to_next:
            return self.level_up()
        return None

    def level_up(self):
        """Level up the player"""
        self.level += 1
        self.exp -= self.exp_to_next
        self.exp_to_next = int(self.exp_to_next * 1.5)

        # Stat increases
        hp_gain = random.randint(3, 8)
        att_gain = random.randint(1, 3)
        def_gain = random.randint(1, 2)

        self.max_hp += hp_gain
        self.hp = self.max_hp  # Full heal on level up
        self.attack += att_gain
        self.defense += def_gain

        return hp_gain, att_gain, def_gain
    
class Bullet:
    def __init__(self, x, y, dx, dy, color=WHITE, size=4):
        self.x = x
        self.y = y
        self.dx = dx
        self.dy = dy
        self.color = color
        self.size = size
        self.active = True
    
    def update(self):
        self.x += self.dx
        self.y += self.dy
        
        # Remove if off screen
        if (self.x < -50 or self.x > SCREEN_WIDTH + 50 or 
            self.y < -50 or self.y > SCREEN_HEIGHT + 50):
            self.active = False
    
    def draw(self, screen):
        if self.active:
            pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)

class Monster:
    def __init__(self, name, hp, attack, defense, dialogue, acts, spare_condition=None, monster_type="dice"):
        self.name = name
        self.max_hp = hp
        self.hp = hp
        self.attack = attack
        self.defense = defense
        self.dialogue = dialogue
        self.acts = acts
        self.spare_condition = spare_condition
        self.can_spare = False
        self.acted_upon = []
        self.turn_count = 0
        self.monster_type = monster_type
        self.dice_sides = 6  # Default
        self.last_roll = 1
        self.spinning = False
        self.card_suit = "♠"
        self.card_value = "A"
        self.slot_reels = [0, 0, 0]

        # Animation states
        self.animation_frame = 0
        self.rolling = False
        self.spinning = False
        self.flipping = False
        self.roll_animation = Animation(60, False)
        self.floating_texts = []

    def get_dialogue(self):
        if self.turn_count < len(self.dialogue):
            return self.dialogue[self.turn_count]
        return random.choice(self.dialogue[-3:])  # Random from last 3

    def take_damage(self, damage):
        # Chance-based damage reduction
        if self.monster_type == "dice":
            roll = random.randint(1, self.dice_sides)
            self.last_roll = roll
            if roll == self.dice_sides:  # Max roll = no damage
                return 0
            actual_damage = max(1, damage - self.defense - roll//2)
        elif self.monster_type == "roulette":
            spin = random.randint(0, 37)  # 0-36 + 00
            if spin == 0 or spin == 37:  # Green = no damage
                return 0
            actual_damage = max(1, damage - self.defense)
        elif self.monster_type == "card":
            card_roll = random.randint(1, 13)
            if card_roll >= 11:  # Face card = reduced damage
                actual_damage = max(1, (damage - self.defense) // 2)
            else:
                actual_damage = max(1, damage - self.defense)
        else:
            actual_damage = max(1, damage - self.defense)

        self.hp = max(0, self.hp - actual_damage)
        return actual_damage

    def is_alive(self):
        return self.hp > 0

    def check_spare_condition(self):
        if self.spare_condition:
            return self.spare_condition(self)
        return self.can_spare

    def get_chance_attack(self):
        """Calculate attack damage based on chance mechanics"""
        base_attack = self.attack

        if self.monster_type == "dice":
            # Start rolling animation
            self.rolling = True
            self.roll_animation.reset()

            roll = random.randint(1, self.dice_sides)
            self.last_roll = roll

            # Add floating text for roll
            self.floating_texts.append(FloatingText(400, 200, f"Rolled {roll}!", YELLOW))

            return base_attack + roll
        elif self.monster_type == "roulette":
            self.spinning = True
            spin = random.randint(0, 37)

            if spin == 0 or spin == 37:  # Green
                self.floating_texts.append(FloatingText(400, 200, "GREEN! Double damage!", GREEN))
                return base_attack * 2
            elif spin % 2 == 0:  # Even
                self.floating_texts.append(FloatingText(400, 200, f"Red {spin}!", RED))
                return base_attack + 2
            else:  # Odd
                self.floating_texts.append(FloatingText(400, 200, f"Black {spin}!", WHITE))
                return base_attack + 1
        elif self.monster_type == "card":
            self.flipping = True
            card = random.randint(1, 13)
            self.card_value = str(card) if card <= 10 else ["J", "Q", "K"][card-11]

            if card == 1:  # Ace
                self.floating_texts.append(FloatingText(400, 200, "ACE! Bonus damage!", GOLD))
                return base_attack + 5
            elif card >= 11:  # Face card
                self.floating_texts.append(FloatingText(400, 200, f"{self.card_value}! Face card!", PURPLE))
                return base_attack + 3
            else:
                return base_attack + card // 2
        elif self.monster_type == "slot":
            self.slot_reels = [random.randint(0, 4) for _ in range(3)]
            if self.slot_reels[0] == self.slot_reels[1] == self.slot_reels[2]:  # Jackpot
                self.floating_texts.append(FloatingText(400, 200, "JACKPOT! Triple damage!", GOLD))
                return base_attack * 3
            elif self.slot_reels[0] == self.slot_reels[1] or self.slot_reels[1] == self.slot_reels[2]:
                self.floating_texts.append(FloatingText(400, 200, "Two of a kind!", YELLOW))
                return base_attack + 4
            else:
                return base_attack
        else:
            return base_attack

    def update_animations(self):
        """Update monster animations"""
        self.animation_frame += 1

        # Update roll animation
        self.roll_animation.update()
        if not self.roll_animation.active:
            self.rolling = False

        # Stop spinning after a while
        if self.spinning and self.animation_frame % 180 == 0:
            self.spinning = False

        # Stop flipping after a while
        if self.flipping and self.animation_frame % 120 == 0:
            self.flipping = False

        # Update floating texts
        for text in self.floating_texts[:]:
            text.update()
            if not text.active:
                self.floating_texts.remove(text)

class BattleBox:
    def __init__(self, x, y, width, height):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.border_width = 4
    
    def draw(self, screen):
        # Outer border (white)
        pygame.draw.rect(screen, WHITE, 
                        (self.x - self.border_width, self.y - self.border_width,
                         self.width + 2 * self.border_width, self.height + 2 * self.border_width))
        # Inner area (black)
        pygame.draw.rect(screen, BLACK, (self.x, self.y, self.width, self.height))
    
    def contains_point(self, x, y):
        return (self.x <= x <= self.x + self.width and 
                self.y <= y <= self.y + self.height)

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = 8
        self.color = RED
        self.speed = 3
        self.stats = PlayerStats()
        self.inventory = ["Bandage", "Monster Candy"]
        self.equipment = {"weapon": None, "armor": None}
        
    def update(self, battle_box):
        keys = pygame.key.get_pressed()
        
        old_x, old_y = self.x, self.y
        
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.x -= self.speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.x += self.speed
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.y -= self.speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.y += self.speed
        
        # Keep player inside battle box
        self.x = max(battle_box.x + self.size, 
                    min(battle_box.x + battle_box.width - self.size, self.x))
        self.y = max(battle_box.y + self.size, 
                    min(battle_box.y + battle_box.height - self.size, self.y))
    
    def draw(self, screen):
        draw_player_soul(screen, self.x, self.y, self.size, self.color)
    
    def check_collision(self, bullets):
        for bullet in bullets:
            if not bullet.active:
                continue
            distance = math.sqrt((self.x - bullet.x)**2 + (self.y - bullet.y)**2)
            if distance < self.size//2 + bullet.size:
                return True
        return False

    def is_alive(self):
        """Check if player is alive"""
        return self.stats.hp > 0

    def heal(self, amount):
        """Heal the player"""
        self.stats.hp = min(self.stats.max_hp, self.stats.hp + amount)

    def get_defense(self):
        """Get total defense including equipment"""
        total_defense = self.stats.defense
        if "armor" in self.equipment and self.equipment["armor"]:
            total_defense += self.equipment["armor"].value
        return total_defense

    def get_attack(self):
        """Get total attack including equipment"""
        total_attack = self.stats.attack
        if "weapon" in self.equipment and self.equipment["weapon"]:
            total_attack += self.equipment["weapon"].value
        return total_attack

def create_monsters():
    """Create the casino-themed monster database"""

    def coin_spare_condition(monster):
        return "flip" in monster.acted_upon and monster.last_roll == 1  # Heads

    def dice_spare_condition(monster):
        return "roll" in monster.acted_upon and monster.last_roll == monster.dice_sides  # Max roll

    def roulette_spare_condition(monster):
        return "bet" in monster.acted_upon or monster.turn_count >= 4

    def card_spare_condition(monster):
        return "bluff" in monster.acted_upon or "fold" in monster.acted_upon

    def slot_spare_condition(monster):
        return "jackpot" in monster.acted_upon or all(r == monster.slot_reels[0] for r in monster.slot_reels)

    monsters = {
        "coin": Monster(
            name="Lucky Coin",
            hp=25,
            attack=4,
            defense=1,
            dialogue=[
                "A shimmering coin blocks your path!",
                "The coin spins in the air, heads or tails?",
                "Luck is on someone's side today...",
                "The coin gleams with possibility."
            ],
            acts=["Flip", "Call Heads", "Call Tails"],
            spare_condition=coin_spare_condition,
            monster_type="dice"
        ),

        "d4": Monster(
            name="D4 Dice",
            hp=40,
            attack=6,
            defense=2,
            dialogue=[
                "A four-sided die appears!",
                "The D4 rolls around menacingly.",
                "Sharp edges and random numbers!",
                "Will you risk it all on a roll?"
            ],
            acts=["Roll", "Predict", "Gamble"],
            spare_condition=dice_spare_condition,
            monster_type="dice"
        ),

        "d6": Monster(
            name="D6 Dice",
            hp=60,
            attack=8,
            defense=3,
            dialogue=[
                "A classic six-sided die blocks your way!",
                "The die rattles with anticipation.",
                "Six sides, infinite possibilities!",
                "Lady Luck is watching..."
            ],
            acts=["Roll", "Bet High", "Bet Low"],
            spare_condition=dice_spare_condition,
            monster_type="dice"
        ),

        "d12": Monster(
            name="D12 Dice",
            hp=120,
            attack=12,
            defense=4,
            dialogue=[
                "A twelve-sided die of power appears!",
                "The dodecahedron spins mysteriously.",
                "Twelve faces, twelve chances!",
                "The odds are getting interesting..."
            ],
            acts=["Roll", "Calculate", "Trust Luck"],
            spare_condition=dice_spare_condition,
            monster_type="dice"
        ),

        "d20": Monster(
            name="D20 Dice",
            hp=200,
            attack=20,
            defense=5,
            dialogue=[
                "The legendary D20 appears!",
                "Twenty sides of pure chance!",
                "Critical hit or critical miss?",
                "The ultimate gamble awaits!"
            ],
            acts=["Roll", "Pray", "Go All In"],
            spare_condition=dice_spare_condition,
            monster_type="dice"
        ),

        "roulette": Monster(
            name="Roulette Wheel",
            hp=150,
            attack=15,
            defense=3,
            dialogue=[
                "The roulette wheel spins before you!",
                "Red or black? Odd or even?",
                "The ball bounces around the wheel...",
                "Place your bets, the wheel is spinning!"
            ],
            acts=["Bet Red", "Bet Black", "Bet Green"],
            spare_condition=roulette_spare_condition,
            monster_type="roulette"
        ),

        "card_shark": Monster(
            name="Card Shark",
            hp=100,
            attack=10,
            defense=4,
            dialogue=[
                "A mysterious card dealer appears!",
                "The cards are shuffled and ready...",
                "Do you feel lucky, punk?",
                "The house always wins... or does it?"
            ],
            acts=["Bluff", "Fold", "All In"],
            spare_condition=card_spare_condition,
            monster_type="card"
        ),

        "slot_machine": Monster(
            name="Slot Machine",
            hp=180,
            attack=14,
            defense=6,
            dialogue=[
                "A one-armed bandit blocks your path!",
                "The reels are spinning wildly!",
                "Cherries, lemons, and sevens!",
                "Will you hit the jackpot?"
            ],
            acts=["Pull Lever", "Jackpot", "Cash Out"],
            spare_condition=slot_spare_condition,
            monster_type="slot"
        ),

        "poker_chip": Monster(
            name="Poker Chip Stack",
            hp=80,
            attack=9,
            defense=3,
            dialogue=[
                "A towering stack of poker chips!",
                "The chips are wobbling dangerously...",
                "Red, blue, green, and gold chips!",
                "Don't let the stack fall!"
            ],
            acts=["Stack", "Topple", "Count"],
            spare_condition=lambda m: "stack" in m.acted_upon,
            monster_type="chip"
        ),

        "blackjack_dealer": Monster(
            name="Blackjack Dealer",
            hp=120,
            attack=11,
            defense=4,
            dialogue=[
                "The dealer shuffles cards menacingly!",
                "Hit or stand? The choice is yours...",
                "The house edge is in their favor!",
                "Twenty-one or bust!"
            ],
            acts=["Hit", "Stand", "Double Down"],
            spare_condition=lambda m: "stand" in m.acted_upon,
            monster_type="blackjack"
        ),

        "lottery_ball": Monster(
            name="Lottery Ball Machine",
            hp=90,
            attack=8,
            defense=2,
            dialogue=[
                "Numbered balls bounce around wildly!",
                "Pick your lucky numbers!",
                "The jackpot is growing...",
                "Will your numbers come up?"
            ],
            acts=["Pick Numbers", "Quick Pick", "Jackpot"],
            spare_condition=lambda m: "jackpot" in m.acted_upon,
            monster_type="lottery"
        ),

        "bingo_caller": Monster(
            name="Bingo Caller",
            hp=70,
            attack=7,
            defense=3,
            dialogue=[
                "B-7! I-19! N-32! G-55! O-71!",
                "The caller shouts numbers rapidly!",
                "Do you have a winning card?",
                "BINGO! Or is it?"
            ],
            acts=["Call Bingo", "Check Card", "Daub"],
            spare_condition=lambda m: "call bingo" in m.acted_upon,
            monster_type="bingo"
        ),

        "craps_table": Monster(
            name="Craps Table",
            hp=160,
            attack=13,
            defense=5,
            dialogue=[
                "The craps table is alive with energy!",
                "Come on seven! Come on eleven!",
                "The dice are hot tonight!",
                "Place your bets!"
            ],
            acts=["Pass Line", "Don't Pass", "Hard Ways"],
            spare_condition=lambda m: "pass line" in m.acted_upon,
            monster_type="craps"
        ),

        "wheel_of_fortune": Monster(
            name="Wheel of Fortune",
            hp=200,
            attack=16,
            defense=7,
            dialogue=[
                "A massive wheel spins before you!",
                "Big money! Big prizes! Big wheel!",
                "Where will the wheel stop?",
                "Spin to win!"
            ],
            acts=["Spin", "Buy Vowel", "Solve"],
            spare_condition=lambda m: "solve" in m.acted_upon,
            monster_type="wheel"
        ),

        "keno_board": Monster(
            name="Keno Board",
            hp=110,
            attack=10,
            defense=4,
            dialogue=[
                "Numbers light up on the keno board!",
                "Pick your spots wisely...",
                "The more you match, the more you win!",
                "Keno! The game of chance!"
            ],
            acts=["Pick Spots", "Quick Pick", "Match"],
            spare_condition=lambda m: "match" in m.acted_upon,
            monster_type="keno"
        ),

        "scratch_card": Monster(
            name="Giant Scratch Card",
            hp=50,
            attack=6,
            defense=1,
            dialogue=[
                "A giant scratch-off card appears!",
                "Scratch to reveal your fate!",
                "Three cherries in a row?",
                "Instant winner or instant loser?"
            ],
            acts=["Scratch", "Reveal All", "Cash In"],
            spare_condition=lambda m: "cash in" in m.acted_upon,
            monster_type="scratch"
        ),

        "casino_boss": Monster(
            name="The House",
            hp=500,
            attack=25,
            defense=10,
            dialogue=[
                "The House itself manifests before you!",
                "The house always wins... or does it?",
                "You've beaten all the games...",
                "But can you beat the odds themselves?",
                "The final gamble begins!"
            ],
            acts=["All In", "Fold", "Call Bluff"],
            spare_condition=lambda m: "call bluff" in m.acted_upon,
            monster_type="boss"
        )
    }

    # Set dice sides for dice monsters
    monsters["coin"].dice_sides = 2
    monsters["d4"].dice_sides = 4
    monsters["d6"].dice_sides = 6
    monsters["d12"].dice_sides = 12
    monsters["d20"].dice_sides = 20

    return monsters

class BattleSystem:
    def __init__(self, monster, player):
        self.monster = monster
        self.player = player
        self.state = BattleState.MENU
        self.battle_box = BattleBox(200, 350, 400, 150)
        self.bullets = []
        self.menu_selection = 0
        self.act_selection = 0
        self.item_selection = 0
        self.bullet_hell_timer = 0
        self.bullet_hell_duration = 180  # 3 seconds at 60 FPS
        self.damage_flash = 0
        self.message = ""
        self.message_timer = 0
        
        # Reset player position for battle
        self.player.x = self.battle_box.x + self.battle_box.width // 2
        self.player.y = self.battle_box.y + self.battle_box.height // 2
    
    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if self.state == BattleState.MENU:
                if event.key == pygame.K_LEFT:
                    self.menu_selection = max(0, self.menu_selection - 1)
                elif event.key == pygame.K_RIGHT:
                    self.menu_selection = min(3, self.menu_selection + 1)
                elif event.key == pygame.K_RETURN:
                    if self.menu_selection == 0:  # FIGHT
                        self.state = BattleState.FIGHT
                        self.attack_monster()
                    elif self.menu_selection == 1:  # ACT
                        self.state = BattleState.ACT
                        self.act_selection = 0
                    elif self.menu_selection == 2:  # ITEM
                        self.state = BattleState.ITEM
                        self.item_selection = 0
                    elif self.menu_selection == 3:  # MERCY
                        self.state = BattleState.MERCY
            
            elif self.state == BattleState.ACT:
                if event.key == pygame.K_UP:
                    self.act_selection = max(0, self.act_selection - 1)
                elif event.key == pygame.K_DOWN:
                    self.act_selection = min(len(self.monster.acts) - 1, self.act_selection + 1)
                elif event.key == pygame.K_RETURN:
                    self.perform_act()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
            
            elif self.state == BattleState.ITEM:
                if event.key == pygame.K_UP:
                    self.item_selection = max(0, self.item_selection - 1)
                elif event.key == pygame.K_DOWN:
                    self.item_selection = min(len(self.player.inventory) - 1, self.item_selection + 1)
                elif event.key == pygame.K_RETURN:
                    self.use_item()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
            
            elif self.state == BattleState.MERCY:
                if event.key == pygame.K_RETURN:
                    return self.try_spare()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
        
        return None
    
    def attack_monster(self):
        # Player rolls for attack
        player_roll = random.randint(1, 20)  # D20 attack roll
        base_damage = self.player.get_attack()

        if player_roll == 20:  # Critical hit
            damage = base_damage * 2
            actual_damage = self.monster.take_damage(damage)
            self.message = f"Critical hit! You dealt {actual_damage} damage!"
        elif player_roll == 1:  # Critical miss
            damage = 0
            actual_damage = 0
            self.message = "Critical miss! You stumble and deal no damage!"
        else:
            damage = base_damage + player_roll // 4
            actual_damage = self.monster.take_damage(damage)
            if actual_damage == 0:
                self.message = f"Lucky! {self.monster.name} avoided all damage!"
            else:
                self.message = f"You dealt {actual_damage} damage!"

        self.message_timer = 120

        if not self.monster.is_alive():
            return "victory"

        self.start_bullet_hell()
    
    def perform_act(self):
        act = self.monster.acts[self.act_selection].lower()
        self.monster.acted_upon.append(act)
        
        if act == "talk":
            self.message = "You talk to " + self.monster.name + "."
        elif act == "spare":
            self.message = "You show mercy."
            self.monster.acted_upon.append("spare")
        elif act == "hug":
            self.message = "You hug " + self.monster.name + "."
        elif act == "flirt":
            self.message = "You flirt with " + self.monster.name + "."
        elif act == "date":
            self.message = "You ask " + self.monster.name + " on a date!"
            self.monster.acted_upon.append("date")
        elif act == "joke":
            self.message = "You tell a joke."
        else:
            self.message = f"You {act}."
        
        self.message_timer = 120
        self.start_bullet_hell()
    
    def use_item(self):
        if self.item_selection < len(self.player.inventory):
            item = self.player.inventory[self.item_selection]

            # Handle string items (simple items)
            if isinstance(item, str):
                if item == "Bandage":
                    heal = min(10, self.player.stats.max_hp - self.player.stats.hp)
                    self.player.stats.hp += heal
                    self.message = f"You used Bandage and recovered {heal} HP!"
                    self.player.inventory.remove(item)
                elif item == "Monster Candy":
                    heal = min(15, self.player.stats.max_hp - self.player.stats.hp)
                    self.player.stats.hp += heal
                    self.message = f"You ate Monster Candy and recovered {heal} HP!"
                    self.player.inventory.remove(item)
            # Handle Item objects
            elif hasattr(item, 'item_type'):
                if item.item_type == ItemType.POTION:
                    self.player.heal(item.value)
                    self.message = f"You drink {item.name} and restore {item.value} HP!"
                    self.player.inventory.remove(item)
                elif item.item_type == ItemType.WEAPON:
                    old_weapon = self.player.equipment["weapon"]
                    self.player.equipment["weapon"] = item
                    self.player.inventory.remove(item)
                    if old_weapon:
                        self.player.inventory.append(old_weapon)
                    self.message = f"You equip {item.name}!"
                elif item.item_type == ItemType.ARMOR:
                    old_armor = self.player.equipment["armor"]
                    self.player.equipment["armor"] = item
                    self.player.inventory.remove(item)
                    if old_armor:
                        self.player.inventory.append(old_armor)
                    self.message = f"You equip {item.name}!"
                elif item.item_type == ItemType.TREASURE:
                    self.player.stats.gold += item.value
                    self.player.inventory.remove(item)
                    self.message = f"You sell {item.name} for {item.value} gold!"

            self.message_timer = 120
            self.start_bullet_hell()
    
    def try_spare(self):
        if self.monster.check_spare_condition():
            return "spared"
        else:
            self.message = f"{self.monster.name} doesn't want to be spared."
            self.message_timer = 120
            self.start_bullet_hell()
        return None
    
    def start_bullet_hell(self):
        self.state = BattleState.BULLET_HELL
        self.bullet_hell_timer = 0
        self.bullets = []
        self.monster.turn_count += 1

    def update_bullet_hell(self):
        self.bullet_hell_timer += 1

        # Generate bullets based on monster type
        if self.monster.monster_type == "dice":
            self.dice_pattern()
        elif self.monster.monster_type == "roulette":
            self.roulette_pattern()
        elif self.monster.monster_type == "card":
            self.card_pattern()
        elif self.monster.monster_type == "slot":
            self.slot_pattern()
        else:
            self.default_pattern()

        # Update bullets
        for bullet in self.bullets[:]:
            bullet.update()
            if not bullet.active:
                self.bullets.remove(bullet)

        # Check collision
        if self.player.check_collision(self.bullets):
            damage = self.monster.get_chance_attack()
            actual_damage = max(1, damage - self.player.get_defense())
            self.player.stats.hp = max(0, self.player.stats.hp - actual_damage)
            self.damage_flash = 30

            # Add screen shake and floating damage text
            # Note: We'll need to pass game reference to add these effects

            if not self.player.is_alive():
                return "game_over"

        # End bullet hell after duration
        if self.bullet_hell_timer >= self.bullet_hell_duration:
            self.state = BattleState.MENU
            self.menu_selection = 0

        return None

    def dice_pattern(self):
        # Dice roll pattern - bullets based on dice sides
        if self.bullet_hell_timer % 25 == 0:
            roll = random.randint(1, self.monster.dice_sides)
            self.monster.last_roll = roll

            # Create bullets equal to the roll
            for i in range(roll):
                angle = (i * 2 * math.pi) / roll
                x = self.battle_box.x + self.battle_box.width // 2
                y = self.battle_box.y + self.battle_box.height // 2
                dx = math.cos(angle) * 2
                dy = math.sin(angle) * 2
                self.bullets.append(Bullet(x, y, dx, dy, WHITE, 5))

    def roulette_pattern(self):
        # Roulette wheel pattern - spinning bullets
        if self.bullet_hell_timer % 20 == 0:
            center_x = self.battle_box.x + self.battle_box.width // 2
            center_y = self.battle_box.y + self.battle_box.height // 2

            # Create spinning pattern
            for i in range(8):
                angle = (i * 2 * math.pi / 8) + (self.bullet_hell_timer * 0.1)
                x = center_x + 50 * math.cos(angle)
                y = center_y + 50 * math.sin(angle)
                dx = math.cos(angle + math.pi/2) * 1.5
                dy = math.sin(angle + math.pi/2) * 1.5
                color = RED if i % 2 == 0 else BLACK
                self.bullets.append(Bullet(x, y, dx, dy, color, 4))

    def card_pattern(self):
        # Card suit pattern
        if self.bullet_hell_timer % 30 == 0:
            # Create bullets in card suit formation
            patterns = [
                # Spade pattern
                [(0, -20), (-10, -10), (10, -10), (-15, 0), (15, 0)],
                # Heart pattern
                [(-10, -10), (10, -10), (-15, 0), (0, 0), (15, 0), (0, 10)],
                # Diamond pattern
                [(0, -15), (-10, 0), (10, 0), (0, 15)],
                # Club pattern
                [(-10, -10), (10, -10), (0, 0), (-10, 10), (10, 10)]
            ]

            pattern = random.choice(patterns)
            center_x = self.battle_box.x + self.battle_box.width // 2
            center_y = self.battle_box.y + self.battle_box.height // 2

            for dx, dy in pattern:
                self.bullets.append(Bullet(
                    center_x + dx, center_y + dy,
                    dx * 0.05, dy * 0.05,
                    random.choice([RED, BLACK]), 4
                ))

    def slot_pattern(self):
        # Slot machine pattern - three columns
        if self.bullet_hell_timer % 35 == 0:
            # Three columns like slot reels
            for col in range(3):
                x = self.battle_box.x + (col + 1) * self.battle_box.width // 4

                # Random number of bullets per column (1-5 like slot symbols)
                num_bullets = random.randint(1, 5)
                for i in range(num_bullets):
                    y = self.battle_box.y - 10 - (i * 20)
                    color = [YELLOW, GREEN, BLUE, PURPLE, GOLD][i % 5]
                    self.bullets.append(Bullet(x, y, 0, 2, color, 6))

    def default_pattern(self):
        # Simple pattern for unknown types
        if self.bullet_hell_timer % 30 == 0:
            self.bullets.append(Bullet(
                random.randint(self.battle_box.x, self.battle_box.x + self.battle_box.width),
                self.battle_box.y - 10,
                0, 2, WHITE
            ))

    def update(self):
        if self.damage_flash > 0:
            self.damage_flash -= 1

        if self.message_timer > 0:
            self.message_timer -= 1

        if self.state == BattleState.BULLET_HELL:
            self.player.update(self.battle_box)
            return self.update_bullet_hell()

        return None

    def draw(self, screen, font, big_font):
        # Draw casino background
        draw_casino_background(screen)

        # Draw monster
        self.draw_monster(screen, font, big_font)

        # HP bar
        hp_ratio = self.monster.hp / self.monster.max_hp
        bar_width = 200
        bar_height = 20
        pygame.draw.rect(screen, RED, (50, 90, bar_width, bar_height))
        pygame.draw.rect(screen, GREEN, (50, 90, int(bar_width * hp_ratio), bar_height))

        hp_text = font.render(f"{self.monster.hp}/{self.monster.max_hp}", True, WHITE)
        screen.blit(hp_text, (260, 90))

        # Player HP with chips
        player_hp_text = font.render(f"HP: {self.player.stats.hp}/{self.player.stats.max_hp}", True, WHITE)
        screen.blit(player_hp_text, (50, 520))

        # Draw chips representing HP
        chip_x = 50
        for i in range(min(5, self.player.stats.hp // 4)):
            draw_chip(screen, chip_x + i * 25, 545, 10, 5, RED)

        # Battle box
        self.battle_box.draw(screen)

        # Draw based on state
        if self.state == BattleState.MENU:
            self.draw_battle_menu(screen, font)
        elif self.state == BattleState.ACT:
            self.draw_act_menu(screen, font)
        elif self.state == BattleState.ITEM:
            self.draw_item_menu(screen, font)
        elif self.state == BattleState.MERCY:
            mercy_text = font.render("* SPARE", True, YELLOW if self.monster.check_spare_condition() else WHITE)
            screen.blit(mercy_text, (50, 300))
        elif self.state == BattleState.BULLET_HELL:
            # Draw bullets
            for bullet in self.bullets:
                bullet.draw(screen)

            # Draw player soul with damage flash
            if self.damage_flash > 0 and self.damage_flash % 6 < 3:
                pass  # Don't draw player (flashing effect)
            else:
                draw_player_soul(screen, self.player.x, self.player.y, 16)

            # Monster dialogue with chance info
            dialogue = self.monster.get_dialogue()
            dialogue_text = font.render(dialogue, True, WHITE)
            screen.blit(dialogue_text, (50, 150))

            # Show last roll/result
            if hasattr(self.monster, 'last_roll'):
                roll_text = font.render(f"Last roll: {self.monster.last_roll}", True, YELLOW)
                screen.blit(roll_text, (50, 175))

        # Draw message
        if self.message_timer > 0:
            message_text = font.render(self.message, True, WHITE)
            screen.blit(message_text, (50, 200))

    def draw_monster(self, screen, font, big_font):
        """Draw the monster based on its type with animations"""
        monster_text = big_font.render(self.monster.name, True, WHITE)
        screen.blit(monster_text, (50, 50))

        # Update monster animations
        self.monster.update_animations()

        # Draw monster visual based on type with animations
        monster_x = 500
        monster_y = 150

        if self.monster.monster_type == "dice":
            if "coin" in self.monster.name.lower():
                draw_dice(screen, monster_x, monster_y, 80, 2, self.monster.last_roll, GOLD,
                         self.monster.animation_frame, self.monster.rolling)
            elif "d4" in self.monster.name.lower():
                draw_dice(screen, monster_x, monster_y, 60, 4, self.monster.last_roll, BLUE,
                         self.monster.animation_frame, self.monster.rolling)
            elif "d6" in self.monster.name.lower():
                draw_dice(screen, monster_x, monster_y, 70, 6, self.monster.last_roll, WHITE,
                         self.monster.animation_frame, self.monster.rolling)
            elif "d12" in self.monster.name.lower():
                draw_dice(screen, monster_x, monster_y, 90, 12, self.monster.last_roll, PURPLE,
                         self.monster.animation_frame, self.monster.rolling)
            elif "d20" in self.monster.name.lower():
                draw_dice(screen, monster_x, monster_y, 100, 20, self.monster.last_roll, RED,
                         self.monster.animation_frame, self.monster.rolling)
        elif self.monster.monster_type == "roulette":
            draw_roulette_wheel(screen, monster_x, monster_y, 120, self.monster.spinning,
                              self.monster.animation_frame)
        elif self.monster.monster_type == "card":
            draw_card(screen, monster_x, monster_y, 60, 90, self.monster.card_suit,
                     self.monster.card_value, False, self.monster.animation_frame, self.monster.flipping)
        elif self.monster.monster_type == "slot":
            draw_slot_machine(screen, monster_x, monster_y, 150, 100, self.monster.slot_reels,
                            self.monster.animation_frame, self.monster.spinning)
        elif self.monster.monster_type == "chip":
            draw_poker_chips(screen, monster_x, monster_y, 80, self.monster.animation_frame, self.monster.rolling)
        elif self.monster.monster_type == "blackjack":
            cards = [("♠", "K"), ("♥", "A")]  # Example cards
            draw_blackjack_table(screen, monster_x, monster_y, 120, 80, cards, self.monster.animation_frame)
        elif self.monster.monster_type == "lottery":
            balls = [7, 14, 21, 35, 42, 49]  # Example lottery numbers
            draw_lottery_machine(screen, monster_x, monster_y, 100, balls, self.monster.animation_frame, self.monster.spinning)
        elif self.monster.monster_type == "bingo":
            called_numbers = [5, 17, 32, 48, 63]  # Example called numbers
            draw_bingo_card(screen, monster_x, monster_y, 100, 80, called_numbers)
        elif self.monster.monster_type == "craps":
            dice_values = [3, 4]  # Example dice values
            draw_craps_table(screen, monster_x, monster_y, 120, 80, dice_values, self.monster.animation_frame)
        elif self.monster.monster_type == "wheel":
            draw_wheel_of_fortune(screen, monster_x, monster_y, 120, self.monster.animation_frame, self.monster.spinning)
        elif self.monster.monster_type == "keno":
            # Draw a simple keno board
            pygame.draw.rect(screen, BLACK, (monster_x, monster_y, 120, 80))
            pygame.draw.rect(screen, WHITE, (monster_x, monster_y, 120, 80), 2)
            font_small = pygame.font.Font(None, 16)
            text = font_small.render("KENO", True, WHITE)
            screen.blit(text, (monster_x + 40, monster_y + 30))
        elif self.monster.monster_type == "scratch":
            # Draw a scratch card
            pygame.draw.rect(screen, SILVER, (monster_x, monster_y, 80, 60))
            pygame.draw.rect(screen, BLACK, (monster_x, monster_y, 80, 60), 2)
            font_small = pygame.font.Font(None, 16)
            text = font_small.render("SCRATCH", True, BLACK)
            screen.blit(text, (monster_x + 15, monster_y + 25))
        elif self.monster.monster_type == "boss":
            # Draw the house - a large imposing structure
            pygame.draw.rect(screen, BLACK, (monster_x, monster_y, 150, 120))
            pygame.draw.rect(screen, GOLD, (monster_x, monster_y, 150, 120), 4)
            pygame.draw.rect(screen, RED, (monster_x + 10, monster_y + 10, 130, 100))
            font_big = pygame.font.Font(None, 24)
            text = font_big.render("THE HOUSE", True, GOLD)
            screen.blit(text, (monster_x + 30, monster_y + 50))

        # Draw floating texts from monster
        for text in self.monster.floating_texts:
            text.draw(screen, font)

    def draw_battle_menu(self, screen, font):
        menu_items = ["FIGHT", "ACT", "ITEM", "MERCY"]
        menu_colors = [RED, BLUE, GREEN, YELLOW]

        for i, (item, color) in enumerate(zip(menu_items, menu_colors)):
            text_color = color if i == self.menu_selection else WHITE
            text = font.render(f"* {item}", True, text_color)
            screen.blit(text, (50 + i * 150, 300))

    def draw_act_menu(self, screen, font):
        for i, act in enumerate(self.monster.acts):
            text_color = YELLOW if i == self.act_selection else WHITE
            text = font.render(f"* {act}", True, text_color)
            screen.blit(text, (50, 300 + i * 30))

    def draw_item_menu(self, screen, font):
        if not self.player.inventory:
            text = font.render("* Empty", True, WHITE)
            screen.blit(text, (50, 300))
        else:
            for i, item in enumerate(self.player.inventory):
                text_color = YELLOW if i == self.item_selection else WHITE

                # Handle both string items and Item objects
                if isinstance(item, str):
                    item_name = item
                else:
                    item_name = item.name if hasattr(item, 'name') else str(item)

                text = font.render(f"* {item_name}", True, text_color)
                screen.blit(text, (50, 300 + i * 30))

class StoryScene:
    def __init__(self, text, choices=None, next_scene=None):
        self.text = text
        self.choices = choices or []
        self.next_scene = next_scene
        self.current_line = 0
        self.text_speed = 2
        self.text_timer = 0
        self.choice_selection = 0

    def update(self):
        self.text_timer += 1
        if self.text_timer >= self.text_speed:
            if self.current_line < len(self.text):
                self.current_line += 1
            self.text_timer = 0

    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                if self.current_line < len(self.text):
                    self.current_line = len(self.text)  # Skip to end
                elif self.choices:
                    return self.choices[self.choice_selection][1]  # Return next scene
                else:
                    return self.next_scene
            elif event.key == pygame.K_UP and self.choices:
                self.choice_selection = max(0, self.choice_selection - 1)
            elif event.key == pygame.K_DOWN and self.choices:
                self.choice_selection = min(len(self.choices) - 1, self.choice_selection + 1)
        return None

    def draw(self, screen, font, big_font):
        screen.fill(BLACK)

        # Draw text
        y_offset = 100
        displayed_text = self.text[:self.current_line]

        for line in displayed_text:
            text_surface = font.render(line, True, WHITE)
            screen.blit(text_surface, (50, y_offset))
            y_offset += 30

        # Draw choices if text is complete
        if self.current_line >= len(self.text) and self.choices:
            y_offset += 50
            for i, (choice_text, _) in enumerate(self.choices):
                color = YELLOW if i == self.choice_selection else WHITE
                choice_surface = font.render(f"* {choice_text}", True, color)
                screen.blit(choice_surface, (70, y_offset))
                y_offset += 40

def create_story_scenes():
    """Create the casino-themed story scenes"""
    scenes = {
        "intro": StoryScene([
            "Welcome to the GRAND CASINO UNDERGROUND!",
            "",
            "A mystical realm where chance rules all,",
            "and every encounter is a gamble.",
            "",
            "You are a young gambler who has stumbled",
            "into this magical casino dimension.",
            "",
            "Here, monsters made of dice, cards, and chance",
            "challenge visitors to games of luck and skill.",
            "",
            "Will you play it safe, or risk it all?",
            "",
            "The house always wins... or does it?"
        ], next_scene="entrance"),

        "entrance": StoryScene([
            "You find yourself in a grand casino lobby.",
            "",
            "Neon lights flash around you, and the sound",
            "of slot machines fills the air.",
            "",
            "The casino floor stretches before you,",
            "filled with gambling monsters and games of chance.",
            "",
            "Your adventure in the Casino Underground begins!"
        ], next_scene="enter_casino"),

        "post_coin": StoryScene([
            "DEALER: Excellent! You've learned the basics!",
            "",
            "DEALER: In this casino, every monster has their own",
            "game of chance.",
            "",
            "DEALER: You can choose to fight them with luck,",
            "or find other ways to win their games.",
            "",
            "DEALER: Remember, sometimes the best bet",
            "is knowing when NOT to bet.",
            "",
            "DEALER: Good luck, high roller!"
        ], next_scene="dice_hall"),

        "toriel_fight_intro": StoryScene([
            "TORIEL: My child...",
            "",
            "TORIEL: I cannot let you leave the Ruins.",
            "",
            "TORIEL: It is dangerous outside.",
            "The other monsters will try to hurt you.",
            "",
            "TORIEL: I am only trying to protect you.",
            "",
            "TORIEL: Please, go to your room."
        ], choices=[
            ("I want to leave", "toriel_battle"),
            ("I'll stay here", "stay_ending")
        ]),

        "stay_ending": StoryScene([
            "You decide to stay with Toriel.",
            "",
            "She takes care of you like her own child.",
            "",
            "You live peacefully in the Ruins,",
            "reading books and eating pie.",
            "",
            "But sometimes you wonder...",
            "what lies beyond the door?",
            "",
            "THE END",
            "",
            "(This is one of many possible endings)"
        ], next_scene="restart"),

        "toriel_spared": StoryScene([
            "TORIEL: ...",
            "",
            "TORIEL: You really are different from the others.",
            "",
            "TORIEL: I cannot stop you from leaving...",
            "",
            "TORIEL: But please, be careful out there.",
            "",
            "TORIEL: Goodbye, my child."
        ], next_scene="snowdin_intro"),

        "snowdin_intro": StoryScene([
            "You walk through a long corridor.",
            "",
            "Eventually, you reach a door.",
            "",
            "As you step through, you feel a chill.",
            "",
            "You have entered Snowdin Forest.",
            "",
            "Snow crunches under your feet.",
            "",
            "In the distance, you see a figure",
            "standing by a lamp post..."
        ], next_scene="sans_intro"),

        "sans_intro": StoryScene([
            "???: human.",
            "",
            "???: don't you know how to greet a new pal?",
            "",
            "???: turn around and shake my hand.",
            "",
            "*You turn around and extend your hand.*",
            "",
            "*BZZZZT!*",
            "",
            "SANS: heh, the old whoopee cushion in the hand trick.",
            "it's ALWAYS funny.",
            "",
            "SANS: anyway, i'm sans. sans the skeleton."
        ], next_scene="papyrus_intro")
    }

    return scenes

class OverworldMap:
    def __init__(self):
        self.width = 20
        self.height = 15
        self.tile_size = 32
        self.player_x = 10
        self.player_y = 12
        self.camera_x = 0
        self.camera_y = 0

        # Create casino floor layout
        self.tiles = self.generate_casino_floor()
        self.enemies = self.place_enemies()
        self.treasures = []

        # Animation
        self.animation_frame = 0

    def generate_casino_floor(self):
        """Generate the casino floor layout"""
        tiles = [[TileType.WALL for _ in range(self.width)] for _ in range(self.height)]

        # Create main floor area
        for y in range(2, self.height - 2):
            for x in range(2, self.width - 2):
                tiles[y][x] = TileType.FLOOR

        # Create rooms/areas
        # Dice Room (top-left)
        for y in range(1, 5):
            for x in range(1, 6):
                tiles[y][x] = TileType.FLOOR

        # Card Room (top-right)
        for y in range(1, 5):
            for x in range(self.width - 6, self.width - 1):
                tiles[y][x] = TileType.FLOOR

        # Slot Machine Hall (bottom)
        for y in range(self.height - 5, self.height - 1):
            for x in range(3, self.width - 3):
                tiles[y][x] = TileType.FLOOR

        # Boss Room (center-top)
        for y in range(1, 4):
            for x in range(8, 12):
                tiles[y][x] = TileType.FLOOR

        # Add doors
        tiles[4][3] = TileType.DOOR  # Dice room door
        tiles[4][self.width - 4] = TileType.DOOR  # Card room door
        tiles[self.height - 5][10] = TileType.DOOR  # Slot hall door
        tiles[3][10] = TileType.DOOR  # Boss room door

        # Add stairs (entrance)
        tiles[self.height - 2][10] = TileType.STAIRS

        return tiles

    def place_enemies(self):
        """Place enemies on the map"""
        enemies = {}

        # Dice area enemies
        enemies[(3, 2)] = "coin"
        enemies[(4, 3)] = "d4"
        enemies[(2, 3)] = "d6"

        # Card area enemies
        enemies[(3, self.width - 3)] = "card_shark"
        enemies[(2, self.width - 4)] = "blackjack_dealer"

        # Main floor enemies
        enemies[(6, 8)] = "roulette"
        enemies[(8, 6)] = "poker_chip"
        enemies[(8, 14)] = "lottery_ball"
        enemies[(10, 10)] = "craps_table"

        # Slot hall enemies
        enemies[(self.height - 3, 5)] = "slot_machine"
        enemies[(self.height - 3, 10)] = "bingo_caller"
        enemies[(self.height - 3, 15)] = "scratch_card"

        # Special enemies
        enemies[(7, 12)] = "d12"
        enemies[(9, 8)] = "d20"
        enemies[(6, 16)] = "keno_board"
        enemies[(11, 6)] = "wheel_of_fortune"

        # Boss
        enemies[(2, 10)] = "casino_boss"

        return enemies

    def get_tile_at(self, x, y):
        """Get tile type at position"""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.tiles[y][x]
        return TileType.WALL

    def get_enemy_at(self, x, y):
        """Get enemy at position"""
        return self.enemies.get((y, x))

    def remove_enemy(self, x, y):
        """Remove enemy from map"""
        if (y, x) in self.enemies:
            del self.enemies[(y, x)]

    def can_move_to(self, x, y):
        """Check if player can move to position"""
        tile = self.get_tile_at(x, y)
        return tile in [TileType.FLOOR, TileType.DOOR, TileType.STAIRS, TileType.ENEMY]

    def move_player(self, dx, dy):
        """Move player and return any events"""
        new_x = self.player_x + dx
        new_y = self.player_y + dy

        if self.can_move_to(new_x, new_y):
            self.player_x = new_x
            self.player_y = new_y

            # Update camera to follow player
            self.update_camera()

            # Check for enemy encounter
            enemy = self.get_enemy_at(new_x, new_y)
            if enemy:
                return ("battle", enemy)

            # Check for stairs
            if self.get_tile_at(new_x, new_y) == TileType.STAIRS:
                return ("stairs", None)

        return (None, None)

    def update_camera(self):
        """Update camera to follow player"""
        screen_tiles_x = 25  # How many tiles fit on screen
        screen_tiles_y = 18

        # Center camera on player
        self.camera_x = self.player_x - screen_tiles_x // 2
        self.camera_y = self.player_y - screen_tiles_y // 2

        # Keep camera in bounds
        self.camera_x = max(0, min(self.width - screen_tiles_x, self.camera_x))
        self.camera_y = max(0, min(self.height - screen_tiles_y, self.camera_y))

    def update(self):
        """Update animations"""
        self.animation_frame += 1

    def draw(self, screen, font):
        """Draw the overworld map"""
        # Draw casino background
        draw_casino_background(screen)

        # Draw tiles
        for y in range(self.height):
            for x in range(self.width):
                screen_x = (x - self.camera_x) * self.tile_size
                screen_y = (y - self.camera_y) * self.tile_size

                # Only draw tiles that are on screen
                if -self.tile_size <= screen_x <= SCREEN_WIDTH and -self.tile_size <= screen_y <= SCREEN_HEIGHT:
                    self.draw_tile(screen, screen_x, screen_y, self.tiles[y][x])

        # Draw enemies
        for (ey, ex), enemy_type in self.enemies.items():
            screen_x = (ex - self.camera_x) * self.tile_size
            screen_y = (ey - self.camera_y) * self.tile_size

            if -self.tile_size <= screen_x <= SCREEN_WIDTH and -self.tile_size <= screen_y <= SCREEN_HEIGHT:
                self.draw_enemy_icon(screen, screen_x, screen_y, enemy_type)

        # Draw player
        player_screen_x = (self.player_x - self.camera_x) * self.tile_size
        player_screen_y = (self.player_y - self.camera_y) * self.tile_size
        draw_player_soul(screen, player_screen_x + self.tile_size//2,
                        player_screen_y + self.tile_size//2, 16)

        # Draw UI
        self.draw_ui(screen, font)

    def draw_tile(self, screen, x, y, tile_type):
        """Draw a single tile"""
        if tile_type == TileType.WALL:
            pygame.draw.rect(screen, DARK_GRAY, (x, y, self.tile_size, self.tile_size))
            pygame.draw.rect(screen, BLACK, (x, y, self.tile_size, self.tile_size), 1)
        elif tile_type == TileType.FLOOR:
            pygame.draw.rect(screen, (0, 80, 0), (x, y, self.tile_size, self.tile_size))
            # Add carpet pattern
            if (x // self.tile_size + y // self.tile_size) % 2 == 0:
                pygame.draw.rect(screen, (0, 90, 0), (x + 2, y + 2, self.tile_size - 4, self.tile_size - 4))
        elif tile_type == TileType.DOOR:
            pygame.draw.rect(screen, BROWN, (x, y, self.tile_size, self.tile_size))
            pygame.draw.rect(screen, GOLD, (x, y, self.tile_size, self.tile_size), 2)
        elif tile_type == TileType.STAIRS:
            pygame.draw.rect(screen, GRAY, (x, y, self.tile_size, self.tile_size))
            # Draw stairs pattern
            for i in range(4):
                pygame.draw.line(screen, WHITE,
                               (x + i * 8, y + self.tile_size),
                               (x + i * 8 + 8, y), 2)

    def draw_enemy_icon(self, screen, x, y, enemy_type):
        """Draw enemy icon on map"""
        center_x = x + self.tile_size // 2
        center_y = y + self.tile_size // 2

        # Pulsing effect
        pulse = math.sin(self.animation_frame * 0.1) * 2

        if "dice" in enemy_type or "coin" in enemy_type or "d" in enemy_type:
            color = WHITE if "coin" not in enemy_type else GOLD
            size = 12 + pulse
            pygame.draw.rect(screen, color, (center_x - size//2, center_y - size//2, size, size))
            pygame.draw.rect(screen, BLACK, (center_x - size//2, center_y - size//2, size, size), 1)
        elif "card" in enemy_type or "blackjack" in enemy_type:
            pygame.draw.rect(screen, WHITE, (center_x - 8, center_y - 10, 16, 20))
            pygame.draw.rect(screen, BLACK, (center_x - 8, center_y - 10, 16, 20), 1)
        elif "roulette" in enemy_type:
            pygame.draw.circle(screen, RED, (center_x, center_y), int(10 + pulse))
            pygame.draw.circle(screen, BLACK, (center_x, center_y), int(10 + pulse), 1)
        elif "slot" in enemy_type:
            pygame.draw.rect(screen, SILVER, (center_x - 10, center_y - 8, 20, 16))
            pygame.draw.rect(screen, BLACK, (center_x - 10, center_y - 8, 20, 16), 1)
        elif "boss" in enemy_type or "house" in enemy_type:
            pygame.draw.circle(screen, GOLD, (center_x, center_y), int(15 + pulse))
            pygame.draw.circle(screen, RED, (center_x, center_y), int(12 + pulse))
            pygame.draw.circle(screen, BLACK, (center_x, center_y), int(15 + pulse), 2)
        else:
            # Generic enemy icon
            pygame.draw.circle(screen, PURPLE, (center_x, center_y), int(8 + pulse))
            pygame.draw.circle(screen, BLACK, (center_x, center_y), int(8 + pulse), 1)

    def draw_ui(self, screen, font):
        """Draw overworld UI"""
        # Mini-map
        minimap_size = 150
        minimap_x = SCREEN_WIDTH - minimap_size - 10
        minimap_y = 10

        pygame.draw.rect(screen, BLACK, (minimap_x, minimap_y, minimap_size, minimap_size))
        pygame.draw.rect(screen, WHITE, (minimap_x, minimap_y, minimap_size, minimap_size), 2)

        # Draw minimap tiles
        scale = minimap_size / max(self.width, self.height)
        for y in range(self.height):
            for x in range(self.width):
                tile_x = minimap_x + int(x * scale)
                tile_y = minimap_y + int(y * scale)
                tile_size = max(1, int(scale))

                tile_type = self.tiles[y][x]
                if tile_type == TileType.WALL:
                    color = DARK_GRAY
                elif tile_type == TileType.FLOOR:
                    color = GREEN
                elif tile_type == TileType.DOOR:
                    color = BROWN
                elif tile_type == TileType.STAIRS:
                    color = YELLOW
                else:
                    color = GRAY

                pygame.draw.rect(screen, color, (tile_x, tile_y, tile_size, tile_size))

        # Draw player on minimap
        player_minimap_x = minimap_x + int(self.player_x * scale)
        player_minimap_y = minimap_y + int(self.player_y * scale)
        pygame.draw.circle(screen, RED, (player_minimap_x, player_minimap_y), 3)

        # Draw enemies on minimap
        for (ey, ex), enemy_type in self.enemies.items():
            enemy_minimap_x = minimap_x + int(ex * scale)
            enemy_minimap_y = minimap_y + int(ey * scale)
            if "boss" in enemy_type:
                pygame.draw.circle(screen, GOLD, (enemy_minimap_x, enemy_minimap_y), 2)
            else:
                pygame.draw.circle(screen, PURPLE, (enemy_minimap_x, enemy_minimap_y), 1)

        # Instructions
        instructions = [
            "WASD/Arrows: Move",
            "Walk into enemies to battle",
            "Red dot: You",
            "Purple dots: Enemies",
            "Gold dot: Boss"
        ]

        for i, instruction in enumerate(instructions):
            text = font.render(instruction, True, WHITE)
            screen.blit(text, (10, SCREEN_HEIGHT - 120 + i * 20))

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Casino Underground - A Chance-Based RPG")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.big_font = pygame.font.Font(None, 36)

        # Game state
        self.state = GameState.STORY
        self.previous_state = GameState.STORY
        self.player = Player(400, 450)
        self.monsters = create_monsters()
        self.story_scenes = create_story_scenes()
        self.current_scene = "intro"
        self.battle_system = None
        self.overworld_map = OverworldMap()

        # Casino progression tracking
        self.flags = {
            "met_coin": False,
            "fought_dice": False,
            "played_roulette": False,
            "beat_slots": False,
            "high_roller": False
        }

        # UI and animations
        self.how_to_play_menu = HowToPlayMenu()
        self.floating_texts = []
        self.screen_shake = 0
        self.transition_alpha = 0

    def handle_input(self, event):
        # Global input - How to Play menu
        if event.type == pygame.KEYDOWN and event.key == pygame.K_i:
            if self.state == GameState.HOW_TO_PLAY:
                self.state = self.previous_state
            else:
                self.previous_state = self.state
                self.state = GameState.HOW_TO_PLAY
            return

        if self.state == GameState.HOW_TO_PLAY:
            result = self.how_to_play_menu.handle_input(event)
            if result == "close":
                self.state = self.previous_state

        elif self.state == GameState.OVERWORLD:
            if event.type == pygame.KEYDOWN:
                dx, dy = 0, 0
                if event.key == pygame.K_LEFT or event.key == pygame.K_a:
                    dx = -1
                elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                    dx = 1
                elif event.key == pygame.K_UP or event.key == pygame.K_w:
                    dy = -1
                elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                    dy = 1

                if dx != 0 or dy != 0:
                    event_type, data = self.overworld_map.move_player(dx, dy)
                    if event_type == "battle":
                        self.start_battle(data)
                    elif event_type == "stairs":
                        # Could transition to different floors
                        pass

        elif self.state == GameState.STORY:
            next_scene = self.story_scenes[self.current_scene].handle_input(event)
            if next_scene:
                if next_scene == "tutorial_battle":
                    self.start_battle("coin")
                elif next_scene == "enter_casino":
                    self.state = GameState.OVERWORLD
                elif next_scene == "dice_battle":
                    self.start_battle("d6")
                elif next_scene == "roulette_battle":
                    self.start_battle("roulette")
                elif next_scene == "restart":
                    self.__init__()  # Restart game
                else:
                    self.current_scene = next_scene

        elif self.state == GameState.BATTLE:
            result = self.battle_system.handle_input(event)
            if result == "victory":
                self.end_battle_victory()
            elif result == "spared":
                self.end_battle_spared()
            elif result == "game_over":
                self.game_over()

    def start_battle(self, monster_name):
        self.previous_state = self.state
        self.state = GameState.BATTLE
        monster = self.monsters[monster_name]
        # Reset monster for battle
        monster.hp = monster.max_hp
        monster.turn_count = 0
        monster.acted_upon = []
        self.battle_system = BattleSystem(monster, self.player)

    def end_battle_victory(self):
        # Remove enemy from overworld map
        self.overworld_map.remove_enemy(self.overworld_map.player_x, self.overworld_map.player_y)

        # Return to overworld or story based on context
        if self.previous_state == GameState.OVERWORLD:
            self.state = GameState.OVERWORLD
        else:
            self.state = GameState.STORY
            monster_name = self.battle_system.monster.name

            if monster_name == "Lucky Coin":
                self.current_scene = "post_coin"
                self.flags["met_coin"] = True
            elif "Dice" in monster_name:
                self.current_scene = "dice_hall"
                self.flags["fought_dice"] = True

    def end_battle_spared(self):
        # Remove enemy from overworld map
        self.overworld_map.remove_enemy(self.overworld_map.player_x, self.overworld_map.player_y)

        # Return to overworld or story based on context
        if self.previous_state == GameState.OVERWORLD:
            self.state = GameState.OVERWORLD
        else:
            self.state = GameState.STORY
            monster_name = self.battle_system.monster.name

            if monster_name == "Lucky Coin":
                self.current_scene = "post_coin"
                self.flags["met_coin"] = True
            elif "Roulette" in monster_name:
                self.current_scene = "roulette_hall"
                self.flags["played_roulette"] = True
            elif "Slot" in monster_name:
                self.current_scene = "slots_hall"
                self.flags["beat_slots"] = True

    def game_over(self):
        # Simple game over - restart
        self.__init__()

    def update(self):
        # Update screen shake
        if self.screen_shake > 0:
            self.screen_shake -= 1

        # Update floating texts
        for text in self.floating_texts[:]:
            text.update()
            if not text.active:
                self.floating_texts.remove(text)

        if self.state == GameState.HOW_TO_PLAY:
            self.how_to_play_menu.update()
        elif self.state == GameState.OVERWORLD:
            self.overworld_map.update()
        elif self.state == GameState.STORY:
            self.story_scenes[self.current_scene].update()
        elif self.state == GameState.BATTLE:
            result = self.battle_system.update()
            if result == "game_over":
                self.game_over()

    def draw(self):
        # Apply screen shake
        shake_x = random.randint(-self.screen_shake, self.screen_shake) if self.screen_shake > 0 else 0
        shake_y = random.randint(-self.screen_shake, self.screen_shake) if self.screen_shake > 0 else 0

        if self.state == GameState.STORY:
            self.story_scenes[self.current_scene].draw(self.screen, self.font, self.big_font)
        elif self.state == GameState.OVERWORLD:
            self.overworld_map.draw(self.screen, self.font)
        elif self.state == GameState.BATTLE:
            self.screen.fill(BLACK)
            self.battle_system.draw(self.screen, self.font, self.big_font)

        # Draw floating texts
        for text in self.floating_texts:
            text.draw(self.screen, self.font)

        # Draw How to Play menu on top
        if self.state == GameState.HOW_TO_PLAY:
            self.how_to_play_menu.draw(self.screen, self.font, self.big_font)

        # Draw help hint
        if self.state in [GameState.STORY, GameState.BATTLE, GameState.OVERWORLD]:
            help_text = self.font.render("Press I for Help", True, GRAY)
            self.screen.blit(help_text, (SCREEN_WIDTH - help_text.get_width() - 10, 10))

        pygame.display.flip()

    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    else:
                        self.handle_input(event)

            self.update()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()

if __name__ == "__main__":
    game = Game()
    game.run()
