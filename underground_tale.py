import pygame
import random
import math
import json
import os
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)

class GameState(Enum):
    OVERWORLD = 0
    BATTLE = 1
    DIALOGUE = 2
    MENU = 3
    STORY = 4

class BattleState(Enum):
    MENU = 0
    FIGHT = 1
    ACT = 2
    ITEM = 3
    MERCY = 4
    BULLET_HELL = 5

@dataclass
class PlayerStats:
    name: str = "Frisk"
    level: int = 1
    hp: int = 20
    max_hp: int = 20
    attack: int = 10
    defense: int = 10
    exp: int = 0
    gold: int = 0
    
class Bullet:
    def __init__(self, x, y, dx, dy, color=WHITE, size=4):
        self.x = x
        self.y = y
        self.dx = dx
        self.dy = dy
        self.color = color
        self.size = size
        self.active = True
    
    def update(self):
        self.x += self.dx
        self.y += self.dy
        
        # Remove if off screen
        if (self.x < -50 or self.x > SCREEN_WIDTH + 50 or 
            self.y < -50 or self.y > SCREEN_HEIGHT + 50):
            self.active = False
    
    def draw(self, screen):
        if self.active:
            pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)

class Monster:
    def __init__(self, name, hp, attack, defense, dialogue, acts, spare_condition=None):
        self.name = name
        self.max_hp = hp
        self.hp = hp
        self.attack = attack
        self.defense = defense
        self.dialogue = dialogue
        self.acts = acts
        self.spare_condition = spare_condition
        self.can_spare = False
        self.acted_upon = []
        self.turn_count = 0
        
    def get_dialogue(self):
        if self.turn_count < len(self.dialogue):
            return self.dialogue[self.turn_count]
        return random.choice(self.dialogue[-3:])  # Random from last 3
    
    def take_damage(self, damage):
        actual_damage = max(1, damage - self.defense)
        self.hp = max(0, self.hp - actual_damage)
        return actual_damage
    
    def is_alive(self):
        return self.hp > 0
    
    def check_spare_condition(self):
        if self.spare_condition:
            return self.spare_condition(self)
        return self.can_spare

class BattleBox:
    def __init__(self, x, y, width, height):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.border_width = 4
    
    def draw(self, screen):
        # Outer border (white)
        pygame.draw.rect(screen, WHITE, 
                        (self.x - self.border_width, self.y - self.border_width,
                         self.width + 2 * self.border_width, self.height + 2 * self.border_width))
        # Inner area (black)
        pygame.draw.rect(screen, BLACK, (self.x, self.y, self.width, self.height))
    
    def contains_point(self, x, y):
        return (self.x <= x <= self.x + self.width and 
                self.y <= y <= self.y + self.height)

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = 8
        self.color = RED
        self.speed = 3
        self.stats = PlayerStats()
        self.inventory = ["Bandage", "Monster Candy"]
        
    def update(self, battle_box):
        keys = pygame.key.get_pressed()
        
        old_x, old_y = self.x, self.y
        
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.x -= self.speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.x += self.speed
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.y -= self.speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.y += self.speed
        
        # Keep player inside battle box
        self.x = max(battle_box.x + self.size, 
                    min(battle_box.x + battle_box.width - self.size, self.x))
        self.y = max(battle_box.y + self.size, 
                    min(battle_box.y + battle_box.height - self.size, self.y))
    
    def draw(self, screen):
        pygame.draw.rect(screen, self.color, 
                        (self.x - self.size//2, self.y - self.size//2, self.size, self.size))
    
    def check_collision(self, bullets):
        for bullet in bullets:
            if not bullet.active:
                continue
            distance = math.sqrt((self.x - bullet.x)**2 + (self.y - bullet.y)**2)
            if distance < self.size//2 + bullet.size:
                return True
        return False

def create_monsters():
    """Create the monster database"""
    
    def dummy_spare_condition(monster):
        return "talk" in monster.acted_upon
    
    def toriel_spare_condition(monster):
        return monster.turn_count >= 5 or "spare" in monster.acted_upon
    
    def papyrus_spare_condition(monster):
        return "date" in monster.acted_upon or monster.turn_count >= 3
    
    monsters = {
        "dummy": Monster(
            name="Training Dummy",
            hp=30,
            attack=5,
            defense=2,
            dialogue=[
                "The dummy seems unresponsive.",
                "The dummy continues to stare at you.",
                "The dummy is getting tired of this.",
                "The dummy wants this to end."
            ],
            acts=["Talk", "Hug", "Ignore"],
            spare_condition=dummy_spare_condition
        ),
        
        "toriel": Monster(
            name="Toriel",
            hp=440,
            attack=8,
            defense=1,
            dialogue=[
                "Toriel blocks your way!",
                "Toriel looks at you with a sad expression.",
                "Toriel is trying to protect you.",
                "Toriel doesn't want to fight.",
                "Toriel's attacks are getting weaker...",
                "Toriel can barely keep fighting..."
            ],
            acts=["Talk", "Spare", "Hug"],
            spare_condition=toriel_spare_condition
        ),
        
        "papyrus": Monster(
            name="Papyrus",
            hp=680,
            attack=12,
            defense=3,
            dialogue=[
                "PAPYRUS blocks your way!",
                "NYEH HEH HEH! PREPARE FOR MY SPECIAL ATTACK!",
                "PAPYRUS is making spaghetti-related puns.",
                "PAPYRUS believes in you!",
                "PAPYRUS is getting excited!"
            ],
            acts=["Talk", "Flirt", "Date"],
            spare_condition=papyrus_spare_condition
        ),
        
        "sans": Monster(
            name="Sans",
            hp=1,
            attack=1,
            defense=1,
            dialogue=[
                "sans.",
                "you're gonna have a bad time.",
                "heh.",
                "...",
                "why don't you get a job?"
            ],
            acts=["Talk", "Joke", "Spare"],
            spare_condition=lambda m: True  # Sans can always be spared
        )
    }
    
    return monsters

class BattleSystem:
    def __init__(self, monster, player):
        self.monster = monster
        self.player = player
        self.state = BattleState.MENU
        self.battle_box = BattleBox(200, 350, 400, 150)
        self.bullets = []
        self.menu_selection = 0
        self.act_selection = 0
        self.item_selection = 0
        self.bullet_hell_timer = 0
        self.bullet_hell_duration = 180  # 3 seconds at 60 FPS
        self.damage_flash = 0
        self.message = ""
        self.message_timer = 0
        
        # Reset player position for battle
        self.player.x = self.battle_box.x + self.battle_box.width // 2
        self.player.y = self.battle_box.y + self.battle_box.height // 2
    
    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if self.state == BattleState.MENU:
                if event.key == pygame.K_LEFT:
                    self.menu_selection = max(0, self.menu_selection - 1)
                elif event.key == pygame.K_RIGHT:
                    self.menu_selection = min(3, self.menu_selection + 1)
                elif event.key == pygame.K_RETURN:
                    if self.menu_selection == 0:  # FIGHT
                        self.state = BattleState.FIGHT
                        self.attack_monster()
                    elif self.menu_selection == 1:  # ACT
                        self.state = BattleState.ACT
                        self.act_selection = 0
                    elif self.menu_selection == 2:  # ITEM
                        self.state = BattleState.ITEM
                        self.item_selection = 0
                    elif self.menu_selection == 3:  # MERCY
                        self.state = BattleState.MERCY
            
            elif self.state == BattleState.ACT:
                if event.key == pygame.K_UP:
                    self.act_selection = max(0, self.act_selection - 1)
                elif event.key == pygame.K_DOWN:
                    self.act_selection = min(len(self.monster.acts) - 1, self.act_selection + 1)
                elif event.key == pygame.K_RETURN:
                    self.perform_act()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
            
            elif self.state == BattleState.ITEM:
                if event.key == pygame.K_UP:
                    self.item_selection = max(0, self.item_selection - 1)
                elif event.key == pygame.K_DOWN:
                    self.item_selection = min(len(self.player.inventory) - 1, self.item_selection + 1)
                elif event.key == pygame.K_RETURN:
                    self.use_item()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
            
            elif self.state == BattleState.MERCY:
                if event.key == pygame.K_RETURN:
                    return self.try_spare()
                elif event.key == pygame.K_ESCAPE:
                    self.state = BattleState.MENU
        
        return None
    
    def attack_monster(self):
        damage = self.player.stats.attack + random.randint(-2, 2)
        actual_damage = self.monster.take_damage(damage)
        self.message = f"You dealt {actual_damage} damage!"
        self.message_timer = 120
        
        if not self.monster.is_alive():
            return "victory"
        
        self.start_bullet_hell()
    
    def perform_act(self):
        act = self.monster.acts[self.act_selection].lower()
        self.monster.acted_upon.append(act)
        
        if act == "talk":
            self.message = "You talk to " + self.monster.name + "."
        elif act == "spare":
            self.message = "You show mercy."
            self.monster.acted_upon.append("spare")
        elif act == "hug":
            self.message = "You hug " + self.monster.name + "."
        elif act == "flirt":
            self.message = "You flirt with " + self.monster.name + "."
        elif act == "date":
            self.message = "You ask " + self.monster.name + " on a date!"
            self.monster.acted_upon.append("date")
        elif act == "joke":
            self.message = "You tell a joke."
        else:
            self.message = f"You {act}."
        
        self.message_timer = 120
        self.start_bullet_hell()
    
    def use_item(self):
        if self.item_selection < len(self.player.inventory):
            item = self.player.inventory[self.item_selection]
            
            if item == "Bandage":
                heal = min(10, self.player.stats.max_hp - self.player.stats.hp)
                self.player.stats.hp += heal
                self.message = f"You used Bandage and recovered {heal} HP!"
                self.player.inventory.remove(item)
            elif item == "Monster Candy":
                heal = min(15, self.player.stats.max_hp - self.player.stats.hp)
                self.player.stats.hp += heal
                self.message = f"You ate Monster Candy and recovered {heal} HP!"
                self.player.inventory.remove(item)
            
            self.message_timer = 120
            self.start_bullet_hell()
    
    def try_spare(self):
        if self.monster.check_spare_condition():
            return "spared"
        else:
            self.message = f"{self.monster.name} doesn't want to be spared."
            self.message_timer = 120
            self.start_bullet_hell()
        return None
    
    def start_bullet_hell(self):
        self.state = BattleState.BULLET_HELL
        self.bullet_hell_timer = 0
        self.bullets = []
        self.monster.turn_count += 1

    def update_bullet_hell(self):
        self.bullet_hell_timer += 1

        # Generate bullets based on monster
        if self.monster.name == "Training Dummy":
            self.dummy_pattern()
        elif self.monster.name == "Toriel":
            self.toriel_pattern()
        elif self.monster.name == "Papyrus":
            self.papyrus_pattern()
        elif self.monster.name == "Sans":
            self.sans_pattern()

        # Update bullets
        for bullet in self.bullets[:]:
            bullet.update()
            if not bullet.active:
                self.bullets.remove(bullet)

        # Check collision
        if self.player.check_collision(self.bullets):
            damage = max(1, self.monster.attack - self.player.stats.defense)
            self.player.stats.hp = max(0, self.player.stats.hp - damage)
            self.damage_flash = 30

            if self.player.stats.hp <= 0:
                return "game_over"

        # End bullet hell after duration
        if self.bullet_hell_timer >= self.bullet_hell_duration:
            self.state = BattleState.MENU
            self.menu_selection = 0

        return None

    def dummy_pattern(self):
        # Simple pattern - bullets from sides
        if self.bullet_hell_timer % 30 == 0:
            # Left side
            self.bullets.append(Bullet(
                self.battle_box.x - 10,
                random.randint(self.battle_box.y, self.battle_box.y + self.battle_box.height),
                2, 0, WHITE
            ))
            # Right side
            self.bullets.append(Bullet(
                self.battle_box.x + self.battle_box.width + 10,
                random.randint(self.battle_box.y, self.battle_box.y + self.battle_box.height),
                -2, 0, WHITE
            ))

    def toriel_pattern(self):
        # Gentle pattern - slow moving bullets
        if self.bullet_hell_timer % 40 == 0:
            # Fire from top
            for i in range(3):
                self.bullets.append(Bullet(
                    self.battle_box.x + (i + 1) * self.battle_box.width // 4,
                    self.battle_box.y - 10,
                    0, 1, ORANGE
                ))

    def papyrus_pattern(self):
        # Bone pattern - more complex
        if self.bullet_hell_timer % 20 == 0:
            # Vertical bones
            self.bullets.append(Bullet(
                random.randint(self.battle_box.x, self.battle_box.x + self.battle_box.width),
                self.battle_box.y - 10,
                0, 3, WHITE, 6
            ))

        if self.bullet_hell_timer % 35 == 0:
            # Horizontal bones
            self.bullets.append(Bullet(
                self.battle_box.x - 10,
                random.randint(self.battle_box.y, self.battle_box.y + self.battle_box.height),
                4, 0, WHITE, 6
            ))

    def sans_pattern(self):
        # Chaotic pattern - very difficult
        if self.bullet_hell_timer % 10 == 0:
            # Random bullets from all directions
            side = random.randint(0, 3)
            if side == 0:  # Top
                x = random.randint(self.battle_box.x, self.battle_box.x + self.battle_box.width)
                y = self.battle_box.y - 10
                dx = random.uniform(-1, 1)
                dy = random.uniform(2, 4)
            elif side == 1:  # Right
                x = self.battle_box.x + self.battle_box.width + 10
                y = random.randint(self.battle_box.y, self.battle_box.y + self.battle_box.height)
                dx = random.uniform(-4, -2)
                dy = random.uniform(-1, 1)
            elif side == 2:  # Bottom
                x = random.randint(self.battle_box.x, self.battle_box.x + self.battle_box.width)
                y = self.battle_box.y + self.battle_box.height + 10
                dx = random.uniform(-1, 1)
                dy = random.uniform(-4, -2)
            else:  # Left
                x = self.battle_box.x - 10
                y = random.randint(self.battle_box.y, self.battle_box.y + self.battle_box.height)
                dx = random.uniform(2, 4)
                dy = random.uniform(-1, 1)

            self.bullets.append(Bullet(x, y, dx, dy, CYAN, 3))

    def update(self):
        if self.damage_flash > 0:
            self.damage_flash -= 1

        if self.message_timer > 0:
            self.message_timer -= 1

        if self.state == BattleState.BULLET_HELL:
            self.player.update(self.battle_box)
            return self.update_bullet_hell()

        return None

    def draw(self, screen, font, big_font):
        # Draw monster info
        monster_text = big_font.render(self.monster.name, True, WHITE)
        screen.blit(monster_text, (50, 50))

        # HP bar
        hp_ratio = self.monster.hp / self.monster.max_hp
        bar_width = 200
        bar_height = 20
        pygame.draw.rect(screen, RED, (50, 90, bar_width, bar_height))
        pygame.draw.rect(screen, GREEN, (50, 90, int(bar_width * hp_ratio), bar_height))

        hp_text = font.render(f"{self.monster.hp}/{self.monster.max_hp}", True, WHITE)
        screen.blit(hp_text, (260, 90))

        # Player HP
        player_hp_text = font.render(f"HP: {self.player.stats.hp}/{self.player.stats.max_hp}", True, WHITE)
        screen.blit(player_hp_text, (50, 520))

        # Battle box
        self.battle_box.draw(screen)

        # Draw based on state
        if self.state == BattleState.MENU:
            self.draw_battle_menu(screen, font)
        elif self.state == BattleState.ACT:
            self.draw_act_menu(screen, font)
        elif self.state == BattleState.ITEM:
            self.draw_item_menu(screen, font)
        elif self.state == BattleState.MERCY:
            mercy_text = font.render("* SPARE", True, YELLOW if self.monster.check_spare_condition() else WHITE)
            screen.blit(mercy_text, (50, 300))
        elif self.state == BattleState.BULLET_HELL:
            # Draw bullets
            for bullet in self.bullets:
                bullet.draw(screen)

            # Draw player with damage flash
            if self.damage_flash > 0 and self.damage_flash % 6 < 3:
                pass  # Don't draw player (flashing effect)
            else:
                self.player.draw(screen)

            # Monster dialogue
            dialogue = self.monster.get_dialogue()
            dialogue_text = font.render(dialogue, True, WHITE)
            screen.blit(dialogue_text, (50, 150))

        # Draw message
        if self.message_timer > 0:
            message_text = font.render(self.message, True, WHITE)
            screen.blit(message_text, (50, 200))

    def draw_battle_menu(self, screen, font):
        menu_items = ["FIGHT", "ACT", "ITEM", "MERCY"]
        menu_colors = [RED, BLUE, GREEN, YELLOW]

        for i, (item, color) in enumerate(zip(menu_items, menu_colors)):
            text_color = color if i == self.menu_selection else WHITE
            text = font.render(f"* {item}", True, text_color)
            screen.blit(text, (50 + i * 150, 300))

    def draw_act_menu(self, screen, font):
        for i, act in enumerate(self.monster.acts):
            text_color = YELLOW if i == self.act_selection else WHITE
            text = font.render(f"* {act}", True, text_color)
            screen.blit(text, (50, 300 + i * 30))

    def draw_item_menu(self, screen, font):
        if not self.player.inventory:
            text = font.render("* Empty", True, WHITE)
            screen.blit(text, (50, 300))
        else:
            for i, item in enumerate(self.player.inventory):
                text_color = YELLOW if i == self.item_selection else WHITE
                text = font.render(f"* {item}", True, text_color)
                screen.blit(text, (50, 300 + i * 30))

class StoryScene:
    def __init__(self, text, choices=None, next_scene=None):
        self.text = text
        self.choices = choices or []
        self.next_scene = next_scene
        self.current_line = 0
        self.text_speed = 2
        self.text_timer = 0
        self.choice_selection = 0

    def update(self):
        self.text_timer += 1
        if self.text_timer >= self.text_speed:
            if self.current_line < len(self.text):
                self.current_line += 1
            self.text_timer = 0

    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                if self.current_line < len(self.text):
                    self.current_line = len(self.text)  # Skip to end
                elif self.choices:
                    return self.choices[self.choice_selection][1]  # Return next scene
                else:
                    return self.next_scene
            elif event.key == pygame.K_UP and self.choices:
                self.choice_selection = max(0, self.choice_selection - 1)
            elif event.key == pygame.K_DOWN and self.choices:
                self.choice_selection = min(len(self.choices) - 1, self.choice_selection + 1)
        return None

    def draw(self, screen, font, big_font):
        screen.fill(BLACK)

        # Draw text
        y_offset = 100
        displayed_text = self.text[:self.current_line]

        for line in displayed_text:
            text_surface = font.render(line, True, WHITE)
            screen.blit(text_surface, (50, y_offset))
            y_offset += 30

        # Draw choices if text is complete
        if self.current_line >= len(self.text) and self.choices:
            y_offset += 50
            for i, (choice_text, _) in enumerate(self.choices):
                color = YELLOW if i == self.choice_selection else WHITE
                choice_surface = font.render(f"* {choice_text}", True, color)
                screen.blit(choice_surface, (70, y_offset))
                y_offset += 40

def create_story_scenes():
    """Create the story scenes"""
    scenes = {
        "intro": StoryScene([
            "Long ago, two races ruled over Earth:",
            "HUMANS and MONSTERS.",
            "",
            "One day, war broke out between the two races.",
            "After a long battle, the humans were victorious.",
            "",
            "They sealed the monsters underground with a magic spell.",
            "",
            "Many years later...",
            "",
            "MT. EBOTT",
            "201X",
            "",
            "Legends say that those who climb the mountain",
            "never return."
        ], next_scene="fall"),

        "fall": StoryScene([
            "You are a human child who has fallen into",
            "the Underground, a large cavern below Mt. Ebott.",
            "",
            "You land on a bed of golden flowers.",
            "",
            "As you get up, you notice a small flower",
            "with a face looking at you..."
        ], next_scene="flowey"),

        "flowey": StoryScene([
            "FLOWEY: Howdy! I'm FLOWEY. FLOWEY the FLOWER!",
            "",
            "FLOWEY: You're new to the UNDERGROUND, aren'tcha?",
            "",
            "FLOWEY: Golly, you must be so confused.",
            "",
            "FLOWEY: Someone ought to teach you how things",
            "work around here!",
            "",
            "FLOWEY: I guess little old me will have to do.",
            "",
            "FLOWEY: Ready? Here we go!"
        ], next_scene="tutorial_battle"),

        "post_dummy": StoryScene([
            "TORIEL: Oh my! You encountered a dummy!",
            "",
            "TORIEL: You handled that very well, my child.",
            "",
            "TORIEL: There are many monsters in the Underground.",
            "Some may try to hurt you.",
            "",
            "TORIEL: But remember, you can always show MERCY.",
            "",
            "TORIEL: Come, let me show you to your room."
        ], next_scene="toriel_fight_intro"),

        "toriel_fight_intro": StoryScene([
            "TORIEL: My child...",
            "",
            "TORIEL: I cannot let you leave the Ruins.",
            "",
            "TORIEL: It is dangerous outside.",
            "The other monsters will try to hurt you.",
            "",
            "TORIEL: I am only trying to protect you.",
            "",
            "TORIEL: Please, go to your room."
        ], choices=[
            ("I want to leave", "toriel_battle"),
            ("I'll stay here", "stay_ending")
        ]),

        "stay_ending": StoryScene([
            "You decide to stay with Toriel.",
            "",
            "She takes care of you like her own child.",
            "",
            "You live peacefully in the Ruins,",
            "reading books and eating pie.",
            "",
            "But sometimes you wonder...",
            "what lies beyond the door?",
            "",
            "THE END",
            "",
            "(This is one of many possible endings)"
        ], next_scene="restart"),

        "toriel_spared": StoryScene([
            "TORIEL: ...",
            "",
            "TORIEL: You really are different from the others.",
            "",
            "TORIEL: I cannot stop you from leaving...",
            "",
            "TORIEL: But please, be careful out there.",
            "",
            "TORIEL: Goodbye, my child."
        ], next_scene="snowdin_intro"),

        "snowdin_intro": StoryScene([
            "You walk through a long corridor.",
            "",
            "Eventually, you reach a door.",
            "",
            "As you step through, you feel a chill.",
            "",
            "You have entered Snowdin Forest.",
            "",
            "Snow crunches under your feet.",
            "",
            "In the distance, you see a figure",
            "standing by a lamp post..."
        ], next_scene="sans_intro"),

        "sans_intro": StoryScene([
            "???: human.",
            "",
            "???: don't you know how to greet a new pal?",
            "",
            "???: turn around and shake my hand.",
            "",
            "*You turn around and extend your hand.*",
            "",
            "*BZZZZT!*",
            "",
            "SANS: heh, the old whoopee cushion in the hand trick.",
            "it's ALWAYS funny.",
            "",
            "SANS: anyway, i'm sans. sans the skeleton."
        ], next_scene="papyrus_intro")
    }

    return scenes

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Underground Tale - An Undertale-Inspired RPG")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.big_font = pygame.font.Font(None, 36)

        # Game state
        self.state = GameState.STORY
        self.player = Player(400, 450)
        self.monsters = create_monsters()
        self.story_scenes = create_story_scenes()
        self.current_scene = "intro"
        self.battle_system = None

        # Progression tracking
        self.flags = {
            "met_flowey": False,
            "fought_dummy": False,
            "met_toriel": False,
            "left_ruins": False
        }

    def handle_input(self, event):
        if self.state == GameState.STORY:
            next_scene = self.story_scenes[self.current_scene].handle_input(event)
            if next_scene:
                if next_scene == "tutorial_battle":
                    self.start_battle("dummy")
                elif next_scene == "toriel_battle":
                    self.start_battle("toriel")
                elif next_scene == "restart":
                    self.__init__()  # Restart game
                else:
                    self.current_scene = next_scene

        elif self.state == GameState.BATTLE:
            result = self.battle_system.handle_input(event)
            if result == "victory":
                self.end_battle_victory()
            elif result == "spared":
                self.end_battle_spared()
            elif result == "game_over":
                self.game_over()

    def start_battle(self, monster_name):
        self.state = GameState.BATTLE
        monster = self.monsters[monster_name]
        # Reset monster for battle
        monster.hp = monster.max_hp
        monster.turn_count = 0
        monster.acted_upon = []
        self.battle_system = BattleSystem(monster, self.player)

    def end_battle_victory(self):
        self.state = GameState.STORY
        monster_name = self.battle_system.monster.name

        if monster_name == "Training Dummy":
            self.current_scene = "post_dummy"
            self.flags["fought_dummy"] = True
        # Add more victory conditions here

    def end_battle_spared(self):
        self.state = GameState.STORY
        monster_name = self.battle_system.monster.name

        if monster_name == "Training Dummy":
            self.current_scene = "post_dummy"
            self.flags["fought_dummy"] = True
        elif monster_name == "Toriel":
            self.current_scene = "toriel_spared"
            self.flags["left_ruins"] = True

    def game_over(self):
        # Simple game over - restart
        self.__init__()

    def update(self):
        if self.state == GameState.STORY:
            self.story_scenes[self.current_scene].update()
        elif self.state == GameState.BATTLE:
            result = self.battle_system.update()
            if result == "game_over":
                self.game_over()

    def draw(self):
        if self.state == GameState.STORY:
            self.story_scenes[self.current_scene].draw(self.screen, self.font, self.big_font)
        elif self.state == GameState.BATTLE:
            self.screen.fill(BLACK)
            self.battle_system.draw(self.screen, self.font, self.big_font)

        pygame.display.flip()

    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    else:
                        self.handle_input(event)

            self.update()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()

if __name__ == "__main__":
    game = Game()
    game.run()
