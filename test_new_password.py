#!/usr/bin/env python3
"""
Test the new admin password
"""

import sys
import hashlib
from pathlib import Path

def test_password_change():
    """Test that the admin password has been changed correctly"""
    
    print("Testing New Admin Password")
    print("=" * 40)
    
    # Test in main directory
    print("📋 Testing main directory...")
    sys.path.insert(0, str(Path(".").absolute()))
    
    try:
        import database
        
        # Initialize database to create admin user
        database.init_database()
        print("✅ Database initialized")
        
        # Test authentication with new password
        new_password = "@H@W@LeComptoir@"
        user = database.authenticate_user("admin", new_password)
        
        if user:
            print(f"✅ Admin login successful with new password: {new_password}")
            print(f"   User details: {user}")
        else:
            print(f"❌ Admin login failed with new password: {new_password}")
            return False
        
        # Test that old password no longer works
        old_password = "0000"
        user_old = database.authenticate_user("admin", old_password)
        
        if user_old:
            print(f"⚠️  WARNING: Old password still works: {old_password}")
            return False
        else:
            print(f"✅ Old password correctly rejected: {old_password}")
        
    except Exception as e:
        print(f"❌ Error testing main directory: {e}")
        return False
    
    # Test in YES directory
    print("\n📋 Testing YES directory...")
    sys.path.insert(0, str(Path("YES").absolute()))
    
    try:
        # Clear module cache to reload
        if 'database' in sys.modules:
            del sys.modules['database']
        
        import database as database_yes
        
        # Initialize database
        database_yes.init_database()
        print("✅ YES database initialized")
        
        # Test authentication with new password
        user_yes = database_yes.authenticate_user("admin", new_password)
        
        if user_yes:
            print(f"✅ YES admin login successful with new password: {new_password}")
        else:
            print(f"❌ YES admin login failed with new password: {new_password}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing YES directory: {e}")
        return False
    
    # Test in YES_OBFUSCATED directory
    print("\n📋 Testing YES_OBFUSCATED directory...")
    sys.path.insert(0, str(Path("YES_OBFUSCATED").absolute()))
    
    try:
        # Clear module cache to reload
        if 'database' in sys.modules:
            del sys.modules['database']
        
        import database as database_obf
        
        # Initialize database
        database_obf.init_database()
        print("✅ YES_OBFUSCATED database initialized")
        
        # Test authentication with new password
        user_obf = database_obf.authenticate_user("admin", new_password)
        
        if user_obf:
            print(f"✅ YES_OBFUSCATED admin login successful with new password: {new_password}")
        else:
            print(f"❌ YES_OBFUSCATED admin login failed with new password: {new_password}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing YES_OBFUSCATED directory: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 PASSWORD CHANGE TEST PASSED!")
    print("=" * 40)
    print(f"✅ New admin password: {new_password}")
    print("✅ Password works in all versions:")
    print("   - Main directory")
    print("   - YES (protected)")
    print("   - YES_OBFUSCATED (secure)")
    print("✅ Old password (0000) correctly rejected")
    
    return True

if __name__ == "__main__":
    success = test_password_change()
    if success:
        print("\n🔑 Admin password successfully changed!")
        print("🔐 Use 'admin' / '@H@W@LeComptoir@' to login")
    else:
        print("\n❌ Password change test failed!")
    sys.exit(0 if success else 1)
