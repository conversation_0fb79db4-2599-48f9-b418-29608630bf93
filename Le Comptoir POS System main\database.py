# Protected POS System Module
# This file contains encrypted business logic
# Unauthorized modification may result in system instability
# Contact system administrator for support



import base64
import zlib
import sys
import time
import random

# System configuration data
CONFIG_DATA_1 = "{'system': 'pos', 'version': '2.1.3', 'encryption': 'enabled'}"
CONFIG_DATA_2 = "SELECT * FROM system_config WHERE active = 1"
CONFIG_DATA_3 = "def get_system_info(): return 'POS System v2.1.3'"

# Encrypted application data
_CHUNK_MAP = {random.randint(1000, 9999): chunk for chunk in ['OSQwDHAecDAeGnd1Gx9yFi4KAA4MYBY/Yn8dCiR7Hh4qZzEhPhd+QkhNBWYVERwZKgYyJDZrHAUoA2EJYzchOHMrJmQEHntuNwUk', 'GT99MSQ5FgAYPyJ2CAs9KGUVdzModxl6ZAsufA4tOWZoGUItMy9ieWotPz00GBQ/OyNiOi07CQ8BNyAzEzMNOhY7Eg01NnQaERMz', 'ASouACg8OAYYDyYqFSRkCWFpFxl7FQlxEWY3DQgiFBQoBgBrWDNmGxoaJhMDFgEgJRI2Mh0VcDEAegccGQo6Yh8VAiw8GQgEOXkd', 'UGcKBSUidXskLzsgMzAEGnAJBh4aKioHC3kVHTUfHD0DfTBhcDx6BR4cPGxgRAtaOxMdJ30wGDVwJR0AMToUPAkLHHlzESYQGTke', 'ZwguBCwaJz0OCzo1UXFeQDEUeGE5EAsxJHIlayEpeTgWGj8ZFR8NABUKZigWai4REAknKxIWCyhodGJzCjwZABspGTULJhJlKgoo', 'PhEjeXZqIAkSEQICESYGGWMuOQo3ZR0kBgcCFC5pZmV9fjUbZxwiDgtjcXBsHigIdh40NQUxaDooMSllOC17MXY+BQ52dC52I2YK', 'ZgMlKw1zOBcWJCoNFg8SenwcOQwIFz4BeApwB2YbPiIIHjUpKGEQcn4ICTAeBAQkPhwGNjEXPGExBiMsIyloMityGF9KQ0EcJwth', 'ci8GdCg2Gz4XPXUrNiAEAjoNFw8MCnQHRmhiBDMwNzoCfHISfSQbCGs/JSxwJRwOaxh/YSNiAnEMLCMFMTooNQdiU3R4bzcfMz8A', 'Cgp+ADU2ewM+dxcGCQAuSARzTidmJwJ9Oy8hcAYFNXEvCBQaDToKcDx0JQdpJDogEDtgMHcNFg9yNmYAQ2tQZ2YfEgsuZx5wBS89', 'AgQAfDEhJhZtXgdfbjItC2UhLz4iBHciJAQ8LW1uOQ0MIRlqZw4qIj0BcBMWByIfNQAUPi96QFlDHjh1OAYKDxxyChllPQNuGx0S', 'HTIdW1ENLzl/BAsJKhgWEw19AX8zFWoWEDVsDDR5Dh4qHi5wDQE6JX9oHhUrG31xZl81bQ49ICoJeBILGyonPXIxFwUGcBp+czBt', 'eBEdLXcyZSIbJSIdGQAICTg9ARkqEHcRDAJsAyE9GSUJLj1RZ2ZNGwUiJ2t1Lws1bBooCQ8kFQNtOQciH3ElbTcxNzIrDhJxCA8l', 'JyQ0aWE8AT03ARxiFT9qKHwTa3hoCx89OglsBXEGHy0NAhk9GHR1GDMYeBsMaiUSGiR6YQEvInkYKRstADB0ZGd6H2kUAiIEcmo3', 'L2IdMCggGGEHKxIOeDM3DTk8fWdwKXstCwYJEXIbKUhxHXc5ZxV7IyNuYHA3AAMSOAM8DQcueDBgERowYQU+NwZoLiAIGSwnETNo', 'K3MTZiF4IBJvOTIQcRwteW0HIQgTDw9/Lh16G34TFxpQBnhcJyMCLjkPJTcmEhQ0JmMZPXYGDDd0Az8jLmM+PGk=', 'PQg9MxgkJn8ibypEW0NhOzcnGyoOJjVxFRxjLysJMhdsIAl0AHQkaCU+PToPJmw5PBhwAjQ6cHMFAn8IbT4RHXIPMCILIjFqOQIg', 'IAhjZBQpZwp3EiMJAXIrGA8teCQPbjUXFAR+NSIPIS47d2tgDD09HXl6bjQSBCFrMyk1cyUyHRIoNQ0UHBoELgUJfS8hOwBlbmhm', 'aBMlOWM/MR4xPAVwMTJyKXlgXFcGGzomCCoTHXELDWEKJSlxF2wccig8FxRmAGUmNxwSACsheWY/EWwwXXN9bSkmBBIjJmw4MzAU', 'awEDcgofG2NuOxI/FTcjHAg9cnpiJjwWOmB2Lm0AWHpjaSAcE3kQEGJ8ezwedmMyFDoqOnkTPXN9BzwzPzcnFxAvOS8NLQN2LB0E', 'EXcjJwl4by88djcFH3RwFRcHGWo1bjUkejkBLz83Fg1oeHAhGHIXdCY3JWdwJjlieH0nHgstOwd6WFw5YnwuCiAXHS8EYCcrAhtr', 'ZQ8ZYgAjewQxbTZ/EyQsADY8NAQVLAEBBDwNKiUaIRUMSklkTSg4CDo5OxkJNzUPfQsGCzgjJQUDIXhuFzxiYBUaDiZ/Ow4bdCkT', 'S2wGGmM+IwIyJnQMDTRxeDsDYR0mdgJgBysbaWd3BRVxPXFkPTAoFBNpSF97QTUBYzwVKw9jC3oxJQI+ODwOCgQUCiAgZTdmAQgM', 'BBo6PCMgbTZ4WR1/aTEhYWIldCsxdDEkKWcJazYsPycCOS0AdCMYFS4HB2Qtfg8XDhxrOgNzGXYlOTknKytnPSo2LTwpBxseHQwr', 'ASV7OCwPYC0mAQgqJnkxaiVjKwk8fStrNGRkOxxxNz4feTR5czB0XQIZQglmPw4AcnBjHTkvZy8mEA5sbBksdw0gEwspGSE+DQ8k', 'NzIHf3RWXHAHPC1kcDRgciYhfXI9cSMvcCR2MjgKATY+ChcZEjVkKHgYajkmLip5aFZmFyY1EjU0OWUSOSxhIy8JMwg5ey03GXRg', 'YxQBc3I3DQkwOmc/cXt6BAkbPQwOOVoDWHAIDwsSIxAKZwYtFyI3BAAKKjl9AwcACzAsNjwbGBV3NgEnPGd6bjYocWlERi9nIyc3', 'NT07LB0DJgVUQx8+LygZPHMNYnN1AgY/Aw0oDwd+CS0sHz04EjwnOx1sFQN2ehEELAgJBFt9Rg8CPhAXABdjagYMPyt/cDcqEzgp', 'cDM0cWUEGyU4HQQldQkALCcGYR0cKi8INBp3Bz93YRMIJQEOBCQjLX8aLwg8CzFaG0FNJSUePDskNmUrMg0zJzU2KzwpASl7JiF9', 'LwMmHw0lcz0raRQmGDYMFTN/GBgzPDEwOnZjd2VsPQ8xBhkKASgSPwARHAxpOGZ+DXsjAwcoPRNgFR8uHg9/JwkpEjELXHdwfiU2', 'KiNtDTcsCAY0OBMXIWcdBSRmfDk7IgccEUh2QHZuOX8gAhEPFQF6LWsqFHcrKxVxJWwfdyEQID8jYwglDnEVLQ0zCC4FUUpgRSc8', 'NwodIDA6NhwvJhthBSQ5CxEgZScJNiUQKHE3JwkLKzp3F2xLBWoBKic8JWQlBQoSDxgEcw0zNBU7OiAMAj9jDT5qfX92Dxh5GzQy', 'KxRrGTwbKA5xC2phPwYxFwQwOQogZwAIMRZaSAING2EOPigjOzgreiEbACk7Em4IPzYyEQQRawE2dhoOem0rIH0vPAofLQVAXGR0', 'L29XZ2ZAay8rJSUvCxAgBjYafB0WKRczBiwAfQceazkeDB0/dBAZHQ8FERUQEQp6ekEMOAkAAS4yEDQLJ2I1egkrKA0qDiY4PH0r', 'ARY7LhYsVVZYehM4HS0AMHArKGwaZHV0Ey88ZyQEIWByHydoZSkfcRQ3Ync9OSECcgldeFxDBxQCNhMlPBEzJwMgKzl4AxE3MCcI', 'Hwcjd3AZEX0XJj04LhABBCo/dhA/DhM5RlpGByoXeWMWdikkC3YjAyM+IB0jLjAiIjFqG2kgagICCg8MGyo4PR4kb3QDagNmbn4e', 'ASd0KihhJCEhMSYHcTcgD2R2KRtAe1oNa3o6Ixs0MSUDLgcAKx0Abm0IeREOBgsAJmAcCDwDbGwkfCQRKDdyLX9hAwMbExoxYzcc', 'EjcnZyQMOB4ECDM5HA4XdElqTB0DARcaJSg9dQkXESwceQpqPgoPOQMkZS07YAoQDWwecB8MFzk3CQd5Xnt5azQFNwMEagMddj1n', 'ASthbHgDHWwXHyZjNXYZNnYxDBgVNgIPKyYcLGgmIwosBAQ3FjcrDgENCRIoJhw7dFp/ADt6HQRiNgkJMHMsPCM+LyMRMSYHCn18', 'PRUpEjVxbWcxGA4QOhBxLRclLiRrZDp9EDctMBs1PA4+cxERRwhQARkAHzIbdSY5Ci4SE3wGJgNhEHsqOhtqFgcIfT0hFxVtBBci', 'BCh5Jy0sNxQ1Xht7ezk+PjIrJyswIDs/NBUiAjdtBToVIWAcZygUFi4YDgE8AGR4CCEPYC1CG0BRMxkKZgV5MT0rciRlPw54KBRt', 'OjscdiN8OjowCn00NmFXQAQxMyYkAiczMH11NCQ1fQYIDnQkLS0hIyYIfzgrZS00LX0NL20DcR1pUwlrUj1jfwVgABklAi42FhQc', 'JBs4HHIWOCVACHxfE2EaEzw0GGs0LmwZDQN2bi8FHRspchIcFxgqKydxMGIsLS9teRcODwpAcUIYIX8ABC0aJTU2eityLjg8FDUu', 'HmQ2FnRcQXBsGwE+ECQSJmUJCCJ9dCsTFW8rInUwMT0/PDI4JDZ3MQ0fexgtYHR2KWNRBVoQA34ZKC0LYC0EZisydXg1IAUjK3s8', 'WhFhNiZhGXQ+NRUNHRcCIi03Z3Evei8IZhgXZQsCIRUZBmQoLAcxHmYEXlZzGCEtJmEGKwkBaB8qLi94Cj1uPgogPmoCED59An8q', 'MwwrKgIjeSxjZmRsJht/Kw19OQEAEQ9+YlgGEmAiNmYzLBULFzsZJz9qDm1veQc1HhwacCciPyYMFWAKATsMMSJhLnxkYFIpACcs', 'RQwpYig4J2ouHTx6EigTGAgIb20sG3p/MWNuBiNkZQI2NngAPj4BdRhoXFZQYToYLSwrKzEkEhMjOhIvExgpKX4qenNuC2llEToF', 'fikLeD0QNhZkeRxwdGV+JyIdci92KUV4egcxZ3UOHxUPCwAUIiEnZxFpDzRxGRszN2UIYDd7JHcoNQIABy88CSMvend3YyosfhZ5', 'ABU/CDA+Nxp8EC8xPhE4DBonKTMLRWRTZBQQFCYldQwKKHAsJD8oEBcNFikjADh1ajgmYjszKzoTPBh7OQMwHwVmYwRjZmI9PQgR', 'BmM0EDwWKQkiES1hfAACNRU6egYnCCEIPQp5HyIgdQx8f30aGxEqEFFfQlgGYx0YBRkKFiYbGxp9ZyAodgp+EBQudBUlBWQbFis3', 'bh4zBTALdj8jHgs2NB09fTosLAthMQEuFC4FQFpCGWMGDBRyKjUGLG0fKXt3bj0OLzkWBR8cDXsjexINAQI+AzY6KiRqOUFiUEES', 'OXwvBA5gOXpRAHo5DTZhNCYlHBIMICEKdQspAzJ7NTIuMCUnBB8kY30Gfz4FfW8SPB8KYht7AWoABThjdT1lKQItKysoLm9uBSMK', 'AVF1BxgPeygjOiMraDQrAiNyax8LMAB2AR9lMWZ9OhZ0O2EvPHgFPD9gJ3VZZl8MEXsmOnJqCR96emN2eS49bmoLcnMEFiUPMzUY', 'dDUMKx4GLTw4DAEzcXByeyd9HR8FFiEraDo/fRd0AW4gHQoFZQw5YA1keXU3FyowGwcdBBceO2wcGHYGCh49Nyd4HjZxIXsWDzZ+', 'IAI8CXMGZgcjGy0fIRoLdx1aORQeBAUraiVwcj8HEygzbBYRJzcJKi4DBgE0Aic2CzIICxRmBzY2Cmt4e3UHMgN/OwU8BHI7ZgMD', 'YXxoLSdgPCd+JyFqfBtXcxc7ejEIbjlgBnMXZywYIGpobnENLAwyYR4aYDUnLmwyJHp8cGAEFTVTV3xuagIGLDR2FTsTIgULM39y', 'BAJ8MRk9Kh8Hfg4JG350alhsAHkfIhYFazYVOjUrHxseLjQGDjUmCxgHZjgiHwsmEgc4DAobfBoQaFhVdWk7CScVBD4pbhlmIg0q', 'UUJFYWgiCyJlNDkYAhsUEHYFBHE6KXwKEzM1EWwIAT83NBMaPAEkMgwvMRZGQUdHHQB6JiAkbBwHDQITNHkLDC4bKzQlJy8YcCUa', 'ABk6Ans/di0MDhctOgp3GCZKUX1RETgqGhwZKGR0ACZ9dictGx4tCQ16MTYGGmErHjkgKTUTHAwpZAkXalZqBXYqM3wXPwsqORA6', 'KQsgPQcOLXQkNj0IfwckDAgTDjkHKQoNMg4eW2FIc2cWHwY9AA8FbjsyFGpjJWhuGAkSASk1AgtmBng6FTYHMwp6GDp3Kg9TUUUf', 'KHoLID0yFAkyB2gjJCZ8OgU2azlxXXkDF20oOgELM2o1Jmd5CygiCTYNfHQJOQArDBooJS4TADACFQsyOiQ1LmFpYEIcOnxjOwIP', 'AQ9yKhEycS0QZDVlDBcLK20jJg58GTsHZDIdL1tZZ2wqETpiay4lKSY6Ejc3OxhtGCs+GXt8ajBqMycYMm4mI3A1Pit8MRRpWUIA', 'eBpyLmATX0ZEUm04BTgUcm8mdAwjNCYvNnU1bip1NxsgE2gVEBd/EzUOAy5/NGQ9Fj4dSgRCJTs0ez9xHAYNOwU/CyJ4LDZoI3Qw', 'bhckcBwaEg8NCxB0LTQqIDUdbxJndjEgJxt9JAdmYDA8CAQGYlcOBjQZNHluPh87GGYEL3ULMXQBcjopcB5sY2UfBhEEOjoKdmh5', 'OAIcdWooOj0sLCYCLX0xeyUWBSc5PDs/fjguPR8eW2dgdS0kBm0odBARPXZkYQQeGy5uah90AQciZWcxeQIOFQQXCCNhOCluG2xc', 'LAYcEhIDRAABLGN7DWAUOjIkOw05AzsGDDg0MA83JGpldAFmYBk1JTAKA2UwMz8WEwpWYWBpNzsSPhQQBQ4IFjQMICIWGit5EQJ6', 'AWMTCh0DbgAxMQZjBBZ2GBtuCi0EKjwHAj83dhI+GTkfOi1wMzFVARleNQMoZj13FB49JBQUMx91byAmLxgMGgcoHRc5fwd0BCAA', 'bBc5fGUbMRAhMB0HBWUReh0+PHIHfAQNO2QpPi4cIzQAJzQSdyBoNgouOT05LgABPjIqc2gofUMZQmoCPg4TdSchMnM9PAw/LjE1', 'awV9RWAFPDo4Z3EHNnEIExkqegQJDzY8eTMRdj1tJz8EMHEGNR4iCRE6ByBnXlp/Qg1nOBJgEykKKzstOD02eTg2bgcEDxx8ODFh', 'Cz4McnRyFzY4N2MpLB97PS8hKDQBbhY2RQNZQGgCKyB9JA5kEXQWFCAvamx2bSUkd3JzJTsfIGACcXYAfBkDJid9Aw9FAWpaLiYb', 'az0ICz49Dwx7O3YRfQ0sKAEJWFgmbSg3OhRsGjMRAyspFQAvNT05LCcTEidvZRwEIzcVDQc9ITgRLw8udXtCUzgYexAADDVkBjUg', 'CBUdEw5hJ2gYABt4CXgnbBdEe2dTFRA2JiMIC3wJEiw/FSIbYjQ1BQ8nMT89C2RgYGEdKA0uDBgHDXJsPABCQAUTEiYfCCktJxQp', 'NA8JLAsbKzg4Bh50JTU4Fig/ERsGGwgEKQYoAzVlYQUBKSE/F2EjNmY9ciYzLyp1Oy0mYyQpM3ZgDyYWGw4Edg0KATspKSNvGEEA', 'DwcpPmBgbCkxYAUsfRszMwgQfQJBXhI+fDkZDGseFCBkeTMpEHFsbi8kOQ0gOi0lEyEkMwsEIxccMR8HCGhRBnENERgVYWQCBxlx', 'AT8PbWFxLSB9KGdqMBIaKjQ7AXABcGF5OyAdOyUBLXw7MilsHX58Ynl0Nj5kYSw8anwpGmUHByAjDzkydQQkdn08KCZ7ZjU3EGZ5', 'byYgNgUYLiozEjk+JDl5bmEKHxVofgE5Ln8YezdqGmQeKh0yLQ4DE0VSdWMcfikYHwY8aigZOgs2BBUgbDEJEhk4FR4zBXkbHgA2', 'ezV+PjliOBAYLjUwNioJIgAcB38YIiggIScaA3cFdno8GDs8BTxybz0DH3NaGBQ/PxEZEzASLyQgBzx3Ky88fDYLPSQiKAo6H2AE', 'HmpiEWEkPTNqAD8TYAIufilvaRl/fQUVYhhsCG50ZRwlYDx9Kg5tNRc6LXJ6LT50PCM7ZG5wewt/exp8Cw0ZREd8ZjIDfH8DESUH', 'dDUYahc7EgceDCg+JjMANm4xLTAZPC0hCy02YnJUXBk2PmNgGRI/ARsbCigNBG8LPSx5dyp2Fxo6IDUEc3QeGngAKQFxAxdlA3pg', 'YQA3HTYIP2USJHA0KzcQFnEeFWszFBMkHzByLBBkKQs4chcyZ0lwTDRlFhc2MjJlNgJmJwE4ID8VEDt4ew8oAQwlMSYyChozAwpl', 'ezh4PgR2FSYfAjgKbhsHAQlxJzwraxR9NgFzCCQ5GCwJJQYeEXFmf0MbGRUyFHZrNA96PxV2fisrdjM/eBQoNARmGzc1OBEUPxk8', 'IjRmOTMgcxY+KC0kBBgTBTUSBng/JzY+Oh56aQEQahRbfUpmGCI0Nh4RNngxaGELJiJyMRcXJgcpLn0+PB07Kz8vBzw/PDo2DywJ', 'JwoFHDV4A3A0Mid/BAYELhUsRXwCVTs3Y2RqBywQJAwtNDwBNAs9LyYmCzsyAT00BiUtAAZjHxkcbjkkLSVFVH4MORM+Jj15EgMW', 'cy8QCCobHAMmLCscPSBhZB9yNSIeIyVsGi9rGHRkBV88B3UhMHl0ZDILHSIIDQkvEQZ9cCwddj80AwsEDS83MXkhNmwTHwARAENg', 'ez0dOnVDUEcQMTkdM3A3MgMNYGonBCltMQsLCQ87c2ZmCn02FgovAA41CAomdz0bc1RgYg08fwZlNBcfESBiZGoHGTRqbnANCH4g', 'JhhwKAsXfCAoaAtnf00eBTY2PzQaa3EJbRgEKgVjCBArKXt6MAg+Pj8KLREHOA1+YRYPMiglc14ETTAlJX8DcXQ0ajo+AHN8NC5s', 'Vx0FOXoFEysvDGA0cmEoIAoINjooAnU2fiEgGX8kAGU2cRwwBiEPKBQBBkh9Vl0wJX5lBzQnNR0EEyE9CSIwHDY7KQYOLmFrHhE4'][1:-1].split(', ')}

def _initialize_security():
    """Initialize security subsystem"""
    for _ in range(random.randint(5, 15)):
        time.sleep(0.1)
        print("Initializing security...")

def _decrypt_application():
    """Decrypt application data"""
    _initialize_security()
    
    try:
        # Reassemble chunks in correct order
        sorted_chunks = [_CHUNK_MAP[key] for key in sorted(_CHUNK_MAP.keys())]
        layer3 = ''.join(chunk.strip("'") for chunk in sorted_chunks)
        
        # Decrypt with system key
        key = "POS_SYSTEM_ENCRYPTION_KEY_2024_ENTERPRISE"
        xor_data = base64.b64decode(layer3.encode('ascii'))
        layer2_bytes = bytearray()
        
        for i, byte in enumerate(xor_data):
            layer2_bytes.append(byte ^ ord(key[i % len(key)]))
        
        layer2 = bytes(layer2_bytes).decode('utf-8')
        
        # Decompress
        layer1 = zlib.decompress(base64.b64decode(layer2.encode('utf-8'))).decode('utf-8')
        
        # Final decode
        original = base64.b64decode(layer1.encode('ascii')).decode('utf-8')
        
        return original

    except Exception as e:
        print("Decryption failed: " + str(e))
        print("File may be corrupted or system key invalid")
        sys.exit(1)

# Execute the application code
try:
    _decoded_source = _decrypt_application()
    exec(_decoded_source, globals())
except Exception as e:
    print("Application startup failed: " + str(e))
    print("Contact system administrator for support")
    sys.exit(1)
