#!/usr/bin/env python3
"""
Casino Underground - A Chance-Based RPG
=======================================

A thrilling casino-themed RPG where luck meets strategy, featuring:
- Chance-based combat with dice, cards, and casino games
- Unique bullet-hell patterns themed around gambling
- Beautiful hand-drawn casino assets and animations
- Risk vs reward gameplay mechanics
- The ultimate test of luck and skill

STORY:
You are a young gambler who has stumbled into the mystical
Casino Underground, where monsters made of dice, cards, and
chance challenge visitors to games of luck and skill.

CORE FEATURES:
🎲 Dice-based combat with D4, D6, D12, and D20 enemies
🎰 Slot machine monsters with jackpot mechanics
🃏 Card-themed enemies with suit-based attacks
🎯 Roulette wheel bosses with spinning bullet patterns
💰 Casino chip health system and gambling mechanics
🎨 Beautiful hand-drawn casino assets and animations

COMBAT SYSTEM:
- Turn-based battles with chance-based damage
- Roll dice to attack - higher rolls deal more damage
- Each monster type has unique gambling mechanics
- Dodge themed bullet patterns (dice dots, card suits, etc.)
- Risk vs reward - high stakes, high rewards!

CASINO MONSTERS YOU'LL FACE:
- Lucky Coin: Flip for heads or tails
- D4/D6/D12/D20 Dice: Roll for damage and defense
- Roulette Wheel: Spin for massive damage or protection
- Card Shark: Bluff, fold, or go all in
- Slot Machine: Hit the jackpot or bust
- And more gambling-themed creatures!

CONTROLS:
- Arrow Keys/WASD: Navigate menus and dodge bullets
- ENTER: Confirm selections and advance dialogue
- ESC: Quit game
- During bullet hell: Move your heart to avoid casino-themed bullets!

CHANCE MECHANICS:
Every action involves an element of chance. Attack rolls, defense rolls,
and special abilities all use dice mechanics. Sometimes luck is on your
side, sometimes it isn't - that's the thrill of the casino!

REQUIREMENTS:
- Python 3.6+
- Pygame library

The game will automatically check for and install Pygame if needed.
"""

import sys
import subprocess
import os

def check_pygame():
    """Check if pygame is installed, offer to install if not."""
    try:
        import pygame
        return True
    except ImportError:
        print("💔 Pygame is not installed!")
        print("To install pygame, run: pip install pygame")
        print()
        
        response = input("Would you like me to try installing it now? (y/n): ").lower()
        if response == 'y':
            try:
                print("Installing pygame...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
                print("💖 Pygame installed successfully!")
                return True
            except subprocess.CalledProcessError:
                print("💔 Failed to install pygame. Please install it manually.")
                return False
        return False

def main():
    print("=" * 60)
    print("    🎰 CASINO UNDERGROUND - CHANCE-BASED RPG 🎰")
    print("=" * 60)
    print()
    print("🎲 Welcome to the Casino Underground! 🎲")
    print()
    print("In this world, luck rules all.")
    print("Will you risk it all, or play it safe?")
    print("The house always wins... or does it?")
    print()

    if not check_pygame():
        print("Cannot start the adventure without pygame. Exiting...")
        input("Press Enter to exit...")
        return

    print("🎰 Starting Casino Underground...")
    print("🎲 Remember: Fortune favors the bold!")
    print("🃏 May the odds be ever in your favor...")
    print()
    
    # Import and run the game
    try:
        from underground_tale import Game
        game = Game()
        game.run()
    except Exception as e:
        print(f"💔 Error starting game: {e}")
        print("Make sure underground_tale.py is in the same directory.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
