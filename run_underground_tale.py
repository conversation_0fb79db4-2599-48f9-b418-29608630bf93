#!/usr/bin/env python3
"""
Underground Tale - An Undertale-Inspired RPG
============================================

A heartfelt RPG inspired by the beloved game Undertale, featuring:
- Story-driven gameplay with meaningful choices
- Unique bullet-hell combat system
- Multiple endings based on your decisions
- Memorable characters and dialogue
- The power to show mercy or fight

STORY:
You are a human child who has fallen into the Underground, a world
beneath the surface where monsters have been banished. Navigate this
strange world, meet its inhabitants, and discover that your choices
have real consequences.

CORE FEATURES:
🎭 Rich storytelling with branching narratives
⚔️ Innovative bullet-hell combat system
💝 Choice-based gameplay - FIGHT or show MERCY
🎵 Multiple memorable characters with unique personalities
🌟 Multiple endings based on your moral choices
💖 Themes of friendship, determination, and compassion

COMBAT SYSTEM:
- Turn-based battles with real-time bullet dodging
- Choose to FIGHT, ACT, use ITEMs, or show MERCY
- Each monster has unique bullet patterns
- Your choices in battle affect the story
- Sparing enemies often leads to friendship

CHARACTERS YOU'LL MEET:
- Training Dummy: Your first encounter
- <PERSON><PERSON>: A motherly goat monster who protects you
- <PERSON><PERSON>: A laid-back skeleton with a mysterious past
- <PERSON><PERSON><PERSON>: <PERSON><PERSON>' energetic brother who loves puzzles
- And many more unique personalities!

CONTROLS:
- Arrow Keys/WASD: Navigate menus and dodge bullets
- ENTER: Confirm selections and advance dialogue
- ESC: Quit game
- During bullet hell: Move to avoid the white bullets!

MORAL CHOICES:
This game explores themes of mercy, friendship, and the consequences
of violence. Every monster you encounter can be spared through
understanding and compassion. Your choices shape not just the story,
but the very world around you.

REQUIREMENTS:
- Python 3.6+
- Pygame library

The game will automatically check for and install Pygame if needed.
"""

import sys
import subprocess
import os

def check_pygame():
    """Check if pygame is installed, offer to install if not."""
    try:
        import pygame
        return True
    except ImportError:
        print("💔 Pygame is not installed!")
        print("To install pygame, run: pip install pygame")
        print()
        
        response = input("Would you like me to try installing it now? (y/n): ").lower()
        if response == 'y':
            try:
                print("Installing pygame...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
                print("💖 Pygame installed successfully!")
                return True
            except subprocess.CalledProcessError:
                print("💔 Failed to install pygame. Please install it manually.")
                return False
        return False

def main():
    print("=" * 60)
    print("    💖 UNDERGROUND TALE - AN UNDERTALE-INSPIRED RPG 💖")
    print("=" * 60)
    print()
    print("🌟 Welcome to the Underground! 🌟")
    print()
    print("In this world, your choices matter.")
    print("You can choose to fight... or you can choose to show mercy.")
    print("What kind of person will you be?")
    print()
    
    if not check_pygame():
        print("Cannot start the adventure without pygame. Exiting...")
        input("Press Enter to exit...")
        return
    
    print("💖 Starting Underground Tale...")
    print("💡 Remember: You can always show MERCY!")
    print("🎯 Your choices will determine your fate...")
    print()
    
    # Import and run the game
    try:
        from underground_tale import Game
        game = Game()
        game.run()
    except Exception as e:
        print(f"💔 Error starting game: {e}")
        print("Make sure underground_tale.py is in the same directory.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
