#!/usr/bin/env python3
"""
Test the demo email functionality
"""

def test_demo_email():
    print("=== TESTING DEMO EMAIL FUNCTIONALITY ===")
    
    try:
        from email_manager import EmailManager
        from pdf_generator import PDFGenerator
        
        # Create instances
        em = EmailManager()
        pdf_gen = PDFGenerator()
        
        print("✅ Email manager and PDF generator created")
        
        # Test SMTP connection (should work in demo mode)
        success, message = em.test_smtp_connection()
        print(f"SMTP Test: {'✅ SUCCESS' if success else '❌ FAILED'}")
        print(f"Message: {message}")
        
        # Enable email and add test address
        em.save_smtp_config(True)
        em.add_email_address("<EMAIL>")
        
        print("\n=== TESTING EMAIL SENDING (DEMO MODE) ===")
        
        # Generate test PDF
        test_content = """SALES HISTORY REPORT
==================================================
Period: Today
Cashier: admin
Generated: 13/06/2025 16:30:25

Date/Time           User        Total
--------------------------------------------------
13/06/2025 16:25:30 admin       25.50 €
13/06/2025 15:15:20 cashier     18.75 €
--------------------------------------------------
TOTAL: 44.25 €"""
        
        business_info = {
            'business_name': 'Le Comptoir POS',
            'business_address': '123 Business Street',
            'business_phone': '+1234567890'
        }
        
        print("Generating test PDF...")
        pdf_path = pdf_gen.create_history_pdf(test_content, business_info)
        
        if pdf_path:
            print(f"✅ PDF generated: {pdf_path}")
            
            # Test email sending (should work in demo mode)
            subject = "Sales History Report - Demo Test"
            body = """Dear Recipient,

Please find attached the sales history report.

This is a test email from the POS system demo mode.

Best regards,
Le Comptoir POS System"""
            
            print("Sending test email...")
            success, message = em.send_history_email(pdf_path, subject, body, "Le Comptoir POS")
            
            print(f"Email Send: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"Message: {message}")
            
            # Clean up
            pdf_gen.cleanup_temp_file(pdf_path)
            em.remove_email_address("<EMAIL>")
            
            if success:
                print("\n🎉 DEMO EMAIL SYSTEM WORKING!")
                print("Check the 'demo_emails' folder for saved email files.")
                return True
            else:
                print("\n❌ Demo email system failed")
                return False
        else:
            print("❌ Failed to generate PDF")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_demo_email()
