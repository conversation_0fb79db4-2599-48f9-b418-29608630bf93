#!/usr/bin/env python3
"""
Test script to verify the protected version works
"""

import os
import sys
from pathlib import Path

def test_protected_version():
    """Test if the protected version can be imported and run"""
    
    print("Testing Protected POS System Version")
    print("=" * 40)
    
    # Change to YES directory
    yes_dir = Path("YES")
    if not yes_dir.exists():
        print("❌ YES directory not found!")
        return False
    
    # Add YES directory to Python path
    sys.path.insert(0, str(yes_dir.absolute()))
    
    try:
        print("📁 Testing directory structure...")
        required_files = [
            "main.py", "pos_app.py", "database.py", "login_screen.py",
            "pos_screen.py", "number_keyboard.py", "user_management.py",
            "product_management.py", "sales_history.py", "receipt_settings.py",
            "receipt_generator.py", "storage_management.py", "translations.py",
            "license_client.py", "pos_system.db", "requirements.txt"
        ]
        
        missing_files = []
        for file in required_files:
            if not (yes_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Missing files: {missing_files}")
            return False
        else:
            print("✅ All required files present")
        
        print("\n📦 Testing imports...")
        
        # Test basic imports
        try:
            import database
            print("✅ database module imported")
        except Exception as e:
            print(f"❌ Failed to import database: {e}")
            return False
        
        try:
            import translations
            print("✅ translations module imported")
        except Exception as e:
            print(f"❌ Failed to import translations: {e}")
            return False
        
        try:
            import license_client
            print("✅ license_client module imported")
        except Exception as e:
            print(f"❌ Failed to import license_client: {e}")
            return False
        
        print("\n🔧 Testing database initialization...")
        try:
            # Test database functions
            database.init_database()
            print("✅ Database initialization successful")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            return False
        
        print("\n🎯 Testing main application import...")
        try:
            import pos_app
            print("✅ pos_app module imported")
            
            # Test if we can create the application object (without running GUI)
            # This tests most of the import chain
            app = pos_app.POSApplication()
            print("✅ POSApplication object created successfully")
            
        except Exception as e:
            print(f"❌ Failed to create POSApplication: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 40)
        print("🎉 PROTECTED VERSION TEST PASSED!")
        print("✅ The protected version is working correctly")
        print("✅ All modules can be imported")
        print("✅ Database functions work")
        print("✅ Application can be initialized")
        print("\n📋 To run the application:")
        print("   cd YES")
        print("   python main.py")
        print("   OR")
        print("   python start_pos.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ PROTECTED VERSION TEST FAILED!")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_protected_version()
    sys.exit(0 if success else 1)
