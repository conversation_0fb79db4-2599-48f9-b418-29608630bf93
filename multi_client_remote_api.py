#!/usr/bin/env python3
"""
Multi-Client POS Remote Management API Server
Manage multiple POS systems from a single dashboard
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import sqlite3
import hashlib
import jwt
import datetime
from functools import wraps
import os
import threading
import time
from pathlib import Path

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'multi-client-pos-remote-management-2024'
API_VERSION = 'v1'

class MultiClientManager:
    """Manage multiple POS client connections"""
    
    def __init__(self):
        self.clients = {}
        self.init_master_database()
    
    def init_master_database(self):
        """Initialize master database for client management"""
        conn = sqlite3.connect('master_remote.db')
        conn.row_factory = sqlite3.Row
        try:
            c = conn.cursor()
            
            # Clients table
            c.execute("""
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY,
                    client_id TEXT UNIQUE NOT NULL,
                    client_name TEXT NOT NULL,
                    database_path TEXT NOT NULL,
                    created_date TEXT NOT NULL,
                    last_active TEXT,
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            # Remote authentication table
            c.execute("""
                CREATE TABLE IF NOT EXISTS remote_auth (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    client_access TEXT NOT NULL,  -- JSON array of client_ids
                    created_date TEXT NOT NULL,
                    last_login TEXT,
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            # Create default admin if not exists
            c.execute("SELECT COUNT(*) FROM remote_auth WHERE username = 'admin'")
            if c.fetchone()[0] == 0:
                password_hash = hashlib.sha256("0000".encode()).hexdigest()
                c.execute("""
                    INSERT INTO remote_auth (username, password_hash, client_access, created_date)
                    VALUES (?, ?, ?, ?)
                """, ("admin", password_hash, "[]", datetime.datetime.now().isoformat()))
            
            conn.commit()
        finally:
            conn.close()
    
    def register_client(self, client_id, client_name, database_path):
        """Register a new POS client"""
        conn = sqlite3.connect('master_remote.db')
        conn.row_factory = sqlite3.Row
        try:
            c = conn.cursor()
            c.execute("""
                INSERT OR REPLACE INTO clients 
                (client_id, client_name, database_path, created_date, last_active)
                VALUES (?, ?, ?, ?, ?)
            """, (client_id, client_name, database_path, 
                  datetime.datetime.now().isoformat(),
                  datetime.datetime.now().isoformat()))
            conn.commit()
            return True
        except Exception as e:
            print(f"Error registering client: {e}")
            return False
        finally:
            conn.close()
    
    def get_client_database(self, client_id):
        """Get database connection for specific client"""
        conn = sqlite3.connect('master_remote.db')
        conn.row_factory = sqlite3.Row
        try:
            c = conn.cursor()
            c.execute("SELECT database_path FROM clients WHERE client_id = ? AND is_active = 1", (client_id,))
            result = c.fetchone()
            if result:
                db_path = result['database_path']
                if os.path.exists(db_path):
                    client_conn = sqlite3.connect(db_path)
                    client_conn.row_factory = sqlite3.Row
                    return client_conn
            return None
        finally:
            conn.close()
    
    def get_user_clients(self, username):
        """Get list of clients user has access to"""
        conn = sqlite3.connect('master_remote.db')
        conn.row_factory = sqlite3.Row
        try:
            c = conn.cursor()
            c.execute("SELECT client_access FROM remote_auth WHERE username = ? AND is_active = 1", (username,))
            result = c.fetchone()
            if result:
                import json
                client_access = json.loads(result['client_access'])
                
                # If empty array, give access to all clients (admin)
                if not client_access:
                    c.execute("SELECT client_id, client_name FROM clients WHERE is_active = 1")
                    return [{'client_id': row['client_id'], 'client_name': row['client_name']} 
                           for row in c.fetchall()]
                else:
                    # Get specific clients
                    placeholders = ','.join(['?' for _ in client_access])
                    c.execute(f"""
                        SELECT client_id, client_name FROM clients 
                        WHERE client_id IN ({placeholders}) AND is_active = 1
                    """, client_access)
                    return [{'client_id': row['client_id'], 'client_name': row['client_name']} 
                           for row in c.fetchall()]
            return []
        finally:
            conn.close()

# Initialize multi-client manager
client_manager = MultiClientManager()

# Auto-register current POS system as default client
if os.path.exists('pos_system.db'):
    client_manager.register_client('default', 'Main Store', 'pos_system.db')

def authenticate_remote_user(username, password):
    """Authenticate remote user with multi-client access"""
    conn = sqlite3.connect('master_remote.db')
    conn.row_factory = sqlite3.Row
    try:
        c = conn.cursor()
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        c.execute("""
            SELECT username, client_access FROM remote_auth 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, password_hash))
        
        user = c.fetchone()
        if user:
            # Update last login
            c.execute("""
                UPDATE remote_auth SET last_login = ? WHERE username = ?
            """, (datetime.datetime.now().isoformat(), username))
            conn.commit()
            
            # Get user's accessible clients
            clients = client_manager.get_user_clients(username)
            
            return {
                'username': user['username'],
                'is_admin': True,
                'clients': clients,
                'last_login': datetime.datetime.now().isoformat()
            }
        return None
    finally:
        conn.close()

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
            
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except:
            return jsonify({'error': 'Token is invalid'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated

def get_client_stats(client_id):
    """Get statistics for specific client"""
    conn = client_manager.get_client_database(client_id)
    if not conn:
        return None
    
    try:
        c = conn.cursor()
        
        # Get basic stats
        c.execute("SELECT COUNT(*) as total_sales FROM sales")
        total_sales = c.fetchone()['total_sales']
        
        c.execute("SELECT COUNT(*) as total_products FROM products")
        total_products = c.fetchone()['total_products']
        
        # Today's sales
        today = datetime.date.today().isoformat()
        c.execute("SELECT COUNT(*) as today_sales FROM sales WHERE DATE(sale_date) = ?", (today,))
        today_sales = c.fetchone()['today_sales']
        
        c.execute("SELECT SUM(total_amount) as today_total FROM sales WHERE DATE(sale_date) = ?", (today,))
        today_total = c.fetchone()['today_total'] or 0
        
        return {
            'client_id': client_id,
            'total_sales': total_sales,
            'total_products': total_products,
            'today_sales_count': today_sales,
            'today_sales_total': float(today_total)
        }
    finally:
        conn.close()

@app.route('/')
def dashboard():
    """Multi-client dashboard"""
    # Dashboard HTML with client selection
    dashboard_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Multi-Client POS Remote Management</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: white; }
            .card { background: rgba(45, 45, 45, 0.9); border: 1px solid #ff8c00; }
            .btn-primary { background: #ff8c00; border-color: #ff8c00; }
            .btn-primary:hover { background: #e67e00; border-color: #e67e00; }
            .navbar { background: rgba(26, 26, 26, 0.95) !important; }
            .client-selector { background: rgba(255, 140, 0, 0.1); border: 1px solid #ff8c00; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-dark">
            <div class="container">
                <span class="navbar-brand">🏪 Multi-Client POS Management</span>
                <div id="connectionStatus" class="badge bg-success">Connected</div>
            </div>
        </nav>

        <div class="container mt-4">
            <div id="loginSection">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-lock"></i> Multi-Client Access Login</h4>
                            </div>
                            <div class="card-body">
                                <form id="loginForm">
                                    <div class="mb-3">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-sign-in-alt"></i> Login
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="dashboardSection" style="display: none;">
                <!-- Client Selector -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card client-selector">
                            <div class="card-body">
                                <h5><i class="fas fa-store"></i> Select Store/Client</h5>
                                <select class="form-select" id="clientSelector" onchange="switchClient()">
                                    <option value="">Select a store...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Content -->
                <div id="clientDashboard" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2><i class="fas fa-tachometer-alt"></i> <span id="currentClientName">Store</span> Overview</h2>
                            <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                            <button class="btn btn-outline-danger btn-sm float-end" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </div>

                    <div class="row" id="statsCards">
                        <!-- Stats cards will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            let authToken = localStorage.getItem('pos_auth_token');
            let currentClient = null;
            let userClients = [];

            // Login form handler
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                try {
                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, password })
                    });

                    const data = await response.json();
                    if (data.token) {
                        authToken = data.token;
                        userClients = data.user.clients;
                        localStorage.setItem('pos_auth_token', authToken);
                        showDashboard();
                    } else {
                        alert('Login failed: ' + (data.error || 'Invalid credentials'));
                    }
                } catch (error) {
                    alert('Connection error: ' + error.message);
                }
            });

            function showDashboard() {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                
                // Populate client selector
                const selector = document.getElementById('clientSelector');
                selector.innerHTML = '<option value="">Select a store...</option>';
                userClients.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.client_id;
                    option.textContent = client.client_name;
                    selector.appendChild(option);
                });
            }

            function switchClient() {
                const clientId = document.getElementById('clientSelector').value;
                if (clientId) {
                    currentClient = clientId;
                    const clientName = userClients.find(c => c.client_id === clientId)?.client_name || 'Store';
                    document.getElementById('currentClientName').textContent = clientName;
                    document.getElementById('clientDashboard').style.display = 'block';
                    refreshData();
                } else {
                    document.getElementById('clientDashboard').style.display = 'none';
                }
            }

            async function refreshData() {
                if (!authToken || !currentClient) return;

                try {
                    const response = await fetch(`/api/v1/clients/${currentClient}/stats`, {
                        headers: { 'Authorization': 'Bearer ' + authToken }
                    });
                    const stats = await response.json();
                    updateStatsCards(stats);
                } catch (error) {
                    console.error('Error refreshing data:', error);
                }
            }

            function updateStatsCards(stats) {
                const cardsHtml = `
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                                <h4>${stats.total_sales}</h4>
                                <p class="text-muted">Total Sales</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-box fa-2x text-success mb-2"></i>
                                <h4>${stats.total_products}</h4>
                                <p class="text-muted">Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-calendar-day fa-2x text-warning mb-2"></i>
                                <h4>${stats.today_sales_count}</h4>
                                <p class="text-muted">Today's Sales</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-dollar-sign fa-2x text-info mb-2"></i>
                                <h4>${stats.today_sales_total?.toFixed(2) || '0.00'} MAD</h4>
                                <p class="text-muted">Today's Revenue</p>
                            </div>
                        </div>
                    </div>
                `;
                document.getElementById('statsCards').innerHTML = cardsHtml;
            }

            function logout() {
                localStorage.removeItem('pos_auth_token');
                authToken = null;
                currentClient = null;
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('dashboardSection').style.display = 'none';
            }
        </script>
    </body>
    </html>
    """
    return render_template_string(dashboard_html)

# API Routes
@app.route(f'/api/{API_VERSION}/auth/login', methods=['POST'])
def login():
    """Authenticate user and return JWT token with client access"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password required'}), 400
    
    user = authenticate_remote_user(username, password)
    if not user:
        return jsonify({'error': 'Invalid credentials'}), 401
    
    token = jwt.encode({
        'user': user,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }, app.config['SECRET_KEY'], algorithm='HS256')
    
    return jsonify({
        'token': token,
        'user': user,
        'expires_in': 86400
    })

@app.route(f'/api/{API_VERSION}/clients/<client_id>/stats')
@token_required
def get_client_stats_api(current_user, client_id):
    """Get statistics for specific client"""
    # Check if user has access to this client
    user_client_ids = [c['client_id'] for c in current_user.get('clients', [])]
    if client_id not in user_client_ids:
        return jsonify({'error': 'Access denied to this client'}), 403
    
    stats = get_client_stats(client_id)
    if stats:
        return jsonify(stats)
    else:
        return jsonify({'error': 'Client not found or database unavailable'}), 404

if __name__ == '__main__':
    print("🚀 Starting Multi-Client POS Remote Management...")
    print("=" * 60)
    print("📱 Dashboard: http://localhost:5000")
    print("🔐 Default Login: admin / 0000")
    print("🏪 Supports multiple POS clients")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5000, debug=False)
