#!/usr/bin/env python3
"""
Create Simple Desktop Shortcut
"""

import os
import sys

def create_batch_shortcut():
    """Create a simple batch file shortcut"""
    try:
        # Get desktop path
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Get current directory and Python executable
        current_dir = os.path.abspath("YES_SECURE")
        python_exe = sys.executable
        
        # Create batch file content
        batch_content = f'''@echo off
title POS System - Le Comptoir
cd /d "{current_dir}"
echo ========================================
echo    POS SYSTEM - LE COMPTOIR
echo ========================================
echo Starting POS System...
echo.
"{python_exe}" main.py
if errorlevel 1 (
    echo.
    echo Error occurred. Press any key to close.
    pause >nul
)
'''
        
        # Write batch file
        batch_path = os.path.join(desktop, "POS System - Le Comptoir.bat")
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ Desktop shortcut created: {batch_path}")
        
        # Create usage instructions
        instructions = f"""# POS SYSTEM DAILY USE

## Quick Start:
1. Double-click "POS System - Le Comptoir.bat" on your desktop
2. Enter license code: LC-78C8C6898C3A4
3. Login: admin / @H@W@LeComptoir@

## System Location:
{current_dir}

## Manual Start:
If shortcut doesn't work:
1. Open folder: {current_dir}
2. Double-click main.py
3. Or run: python main.py

## Security Level: 5/10
- Basic file protection
- License validation
- Clean deployment structure

For support, contact system administrator.
"""
        
        instructions_path = os.path.join(desktop, "POS System Instructions.txt")
        with open(instructions_path, 'w') as f:
            f.write(instructions)
        
        print(f"✅ Instructions created: {instructions_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create shortcut: {e}")
        return False

def main():
    print("🔗 Creating Simple Desktop Shortcut")
    print("=" * 35)
    
    if not os.path.exists("YES_SECURE/main.py"):
        print("❌ YES_SECURE/main.py not found!")
        return False
    
    success = create_batch_shortcut()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("✅ Desktop shortcut created")
        print("✅ Instructions file created")
        print("\n📋 For daily use:")
        print("Double-click 'POS System - Le Comptoir.bat' on desktop")
    else:
        print("\n❌ Failed to create shortcut")
    
    return success

if __name__ == "__main__":
    main()
