"""
License Client - Client-side license validation
This version only validates licenses, cannot create or manage them
"""

import sqlite3
import hashlib
import uuid
import json
from datetime import datetime, timedelta
from pathlib import Path
import platform
import getpass

class LicenseClient:
    """Client-side license validation only"""

    def __init__(self, server_db_path=None):
        """Initialize license client"""
        # Client doesn't create database, only validates
        self.server_db_path = server_db_path or "license_system.db"

    def get_computer_id(self):
        """Generate unique computer identifier"""
        computer_info = f"{platform.node()}-{platform.system()}-{getpass.getuser()}"
        computer_id = hashlib.md5(computer_info.encode()).hexdigest()[:16].upper()
        return computer_id

    def get_system_info(self):
        """Get system information for license tracking"""
        return {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "computer_name": platform.node()
        }

    def validate_license_offline(self, license_key):
        """Validate license without database access (basic format check)"""
        try:
            # Basic format validation
            if not license_key or len(license_key) < 10:
                return {"valid": False, "error": "Invalid license key format"}

            if not license_key.startswith("LC-"):
                return {"valid": False, "error": "Invalid license key format"}

            # Check for hardcoded 24-hour trial license
            if license_key == "LC-78C8C6898C3A4":
                return self.validate_trial_license(license_key)

            # Check if license file exists locally
            license_file = "license.key"
            if Path(license_file).exists():
                with open(license_file, 'r') as f:
                    stored_key = f.read().strip()
                if stored_key == license_key:
                    return {"valid": True, "message": "License validated (offline mode)"}

            # For offline mode, if format is correct, allow activation
            # This enables initial setup when database is not accessible
            print(f"Offline validation: License format valid, allowing activation")
            return {"valid": True, "message": "License validated (offline mode - format check passed)"}

        except Exception as e:
            return {"valid": False, "error": f"License validation error: {e}"}

    def validate_trial_license(self, license_key):
        """Validate the hardcoded 24-hour trial license LC-78C8C6898C3A4"""
        try:
            trial_file = "trial_license.dat"
            current_time = datetime.now()

            # Check if trial has been used before
            if Path(trial_file).exists():
                with open(trial_file, 'r') as f:
                    trial_data = json.loads(f.read())

                # Check if this is the same license
                if trial_data.get('license_key') != license_key:
                    return {"valid": False, "error": "Trial license mismatch"}

                # Check if trial has expired
                start_time = datetime.fromisoformat(trial_data['start_time'])
                elapsed_hours = (current_time - start_time).total_seconds() / 3600

                if elapsed_hours >= 24:
                    return {"valid": False, "error": "24-hour trial period has expired"}

                remaining_hours = 24 - elapsed_hours
                return {
                    "valid": True,
                    "message": f"Trial license active. {remaining_hours:.1f} hours remaining"
                }
            else:
                # First time using trial license
                trial_data = {
                    'license_key': license_key,
                    'start_time': current_time.isoformat(),
                    'computer_id': self.get_computer_id()
                }

                with open(trial_file, 'w') as f:
                    f.write(json.dumps(trial_data))

                return {
                    "valid": True,
                    "message": "24-hour trial license activated. Trial period started."
                }

        except Exception as e:
            return {"valid": False, "error": f"Trial license validation error: {e}"}

    def validate_license_with_server(self, license_key, computer_id, computer_name=None, system_info=None):
        """Validate license with server database (if available)"""
        try:
            # Check for hardcoded 24-hour trial license first
            if license_key == "LC-78C8C6898C3A4":
                return self.validate_trial_license(license_key)

            # Try to find license database in multiple locations
            possible_db_paths = [
                self.server_db_path,
                "../license_system.db",  # Parent directory
                "../../license_system.db",  # Two levels up
                Path.home() / "license_system.db",  # User home
                "C:/POS_System/license_system.db"  # Absolute path
            ]

            db_path = None
            for path in possible_db_paths:
                if Path(path).exists():
                    db_path = path
                    break

            if not db_path:
                # No database found, use basic validation
                print("License database not found, using offline validation")
                return self.validate_license_offline(license_key)

            print(f"Using license database: {db_path}")
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            # Check if license exists and is active
            c.execute('''
                SELECT * FROM license_keys
                WHERE license_key = ? AND is_active = 1
            ''', (license_key,))

            license_data = c.fetchone()
            if not license_data:
                conn.close()
                return {"valid": False, "error": "Invalid or inactive license key"}

            # Check expiration
            if license_data['expires_date']:
                expires = datetime.fromisoformat(license_data['expires_date'])
                if datetime.now() > expires:
                    conn.close()
                    return {"valid": False, "error": "License key has expired"}

            # Check if this computer is already activated
            c.execute('''
                SELECT * FROM installations
                WHERE license_key = ? AND computer_id = ? AND is_active = 1
            ''', (license_key, computer_id))

            existing_installation = c.fetchone()
            if existing_installation:
                # Update last seen
                c.execute('''
                    UPDATE installations
                    SET last_seen = ?, computer_name = ?
                    WHERE license_key = ? AND computer_id = ?
                ''', (datetime.now().isoformat(), computer_name, license_key, computer_id))
                conn.commit()
                conn.close()
                return {"valid": True, "message": "License validated successfully"}

            # Check activation limit
            c.execute('''
                SELECT COUNT(*) as active_count FROM installations
                WHERE license_key = ? AND is_active = 1
            ''', (license_key,))

            active_count = c.fetchone()['active_count']
            if active_count >= license_data['max_activations']:
                conn.close()
                return {"valid": False, "error": f"License key has reached maximum activations ({license_data['max_activations']})"}

            # Activate license for this computer
            c.execute('''
                INSERT INTO installations
                (license_key, computer_id, computer_name, installation_date, last_seen, system_info)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (license_key, computer_id, computer_name, datetime.now().isoformat(),
                  datetime.now().isoformat(), system_info))

            # Update activation count
            c.execute('''
                UPDATE license_keys
                SET current_activations = current_activations + 1
                WHERE license_key = ?
            ''', (license_key,))

            conn.commit()
            conn.close()
            return {"valid": True, "message": "License activated successfully"}

        except Exception as e:
            return {"valid": False, "error": f"Database error: {e}"}





    def save_license_locally(self, license_key):
        """Save validated license key locally"""
        try:
            with open("license.key", 'w') as f:
                f.write(license_key)
            return True
        except Exception as e:
            print(f"Error saving license: {e}")
            return False

    def get_local_license(self):
        """Get license key from local file"""
        try:
            license_file = "license.key"
            if Path(license_file).exists():
                with open(license_file, 'r') as f:
                    return f.read().strip()
            return None
        except Exception as e:
            print(f"Error reading license: {e}")
            return None

    def check_license(self):
        """Main license check function for client"""
        try:
            # Get computer info
            computer_id = self.get_computer_id()
            computer_name = platform.node()
            system_info = str(self.get_system_info())

            # Check for local license first
            local_license = self.get_local_license()
            if local_license:
                # Validate with local database
                result = self.validate_license_with_server(
                    local_license, computer_id, computer_name, system_info
                )
                if result["valid"]:
                    return True, result["message"]
                else:
                    # License invalid, remove local file
                    try:
                        Path("license.key").unlink()
                    except:
                        pass
                    return False, result["error"]

            # No local license, request from user
            return False, "No license found"

        except Exception as e:
            return False, f"License check error: {e}"

# Test the client
if __name__ == "__main__":
    client = LicenseClient()

    # Show computer info
    computer_id = client.get_computer_id()
    computer_name = platform.node()

    print("License Client Test")
    print("=" * 30)
    print(f"Computer Name: {computer_name}")
    print(f"Computer ID: {computer_id}")

    # Test license check
    valid, message = client.check_license()
    print(f"License Status: {'Valid' if valid else 'Invalid'}")
    print(f"Message: {message}")
