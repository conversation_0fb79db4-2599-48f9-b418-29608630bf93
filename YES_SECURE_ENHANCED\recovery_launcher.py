#!/usr/bin/env python3
"""
Recovery Launcher for Secure POS System
Bypasses security for emergency access and setup
"""

import os
import sys
import shutil

def print_header():
    """Print recovery header"""
    print("=" * 50)
    print("🔧 SECURE POS RECOVERY LAUNCHER")
    print("=" * 50)
    print("⚠️ This tool bypasses security for setup/recovery")
    print()

def restore_from_backup():
    """Restore files from backup"""
    print("🔄 RESTORING FROM BACKUP")
    print("-" * 25)
    
    backup_files = [f for f in os.listdir(".") if f.endswith('.backup')]
    
    if not backup_files:
        print("❌ No backup files found!")
        return False
    
    print(f"Found {len(backup_files)} backup files:")
    for backup in backup_files:
        original = backup.replace('.backup', '')
        print(f"  📄 {backup} -> {original}")
    
    choice = input("\nRestore all backup files? (y/n): ").lower()
    
    if choice == 'y':
        restored = 0
        for backup in backup_files:
            try:
                original = backup.replace('.backup', '')
                shutil.copy2(backup, original)
                print(f"✅ Restored: {original}")
                restored += 1
            except Exception as e:
                print(f"❌ Failed to restore {original}: {e}")
        
        print(f"\n📊 Restored {restored}/{len(backup_files)} files")
        return restored > 0
    
    return False

def authorize_machine_directly():
    """Directly authorize machine without GUI"""
    print("🔑 DIRECT MACHINE AUTHORIZATION")
    print("-" * 30)
    
    try:
        from security_manager import security_manager
        
        print("✅ Security manager loaded")
        
        # Show machine info
        machine_info = security_manager.get_machine_info()
        print(f"🖥️ Machine ID: {machine_info['machine_id']}")
        print(f"🖥️ System: {machine_info['system']}")
        print(f"🖥️ Node: {machine_info['node']}")
        
        auth_code = input("\nEnter authorization code: ").strip()
        
        if security_manager.authorize_machine(auth_code):
            print("✅ Machine authorized successfully!")
            return True
        else:
            print("❌ Invalid authorization code!")
            return False
            
    except Exception as e:
        print(f"❌ Authorization error: {e}")
        return False

def launch_direct():
    """Launch POS directly without security"""
    print("🚀 DIRECT LAUNCH (NO SECURITY)")
    print("-" * 32)
    
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    print("⚠️ WARNING: Launching without security checks!")
    choice = input("Continue? (y/n): ").lower()
    
    if choice == 'y':
        try:
            import subprocess
            print("🚀 Launching POS system...")
            result = subprocess.run([sys.executable, "main.py"], check=False)
            print(f"✅ POS system exited with code: {result.returncode}")
            return True
        except Exception as e:
            print(f"❌ Launch error: {e}")
            return False
    
    return False

def test_system():
    """Test system components"""
    print("🧪 SYSTEM COMPONENT TEST")
    print("-" * 24)
    
    tests = [
        ("main.py", "Main application file"),
        ("pos_app.py", "POS application core"),
        ("database.py", "Database module"),
        ("security_manager.py", "Security manager"),
        ("assets/logo.png", "Logo asset"),
        ("pos_system.db", "Database file")
    ]
    
    passed = 0
    for file_path, description in tests:
        if os.path.exists(file_path):
            print(f"✅ {description}")
            passed += 1
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\n📊 Test Results: {passed}/{len(tests)} components found")
    return passed == len(tests)

def main():
    """Main recovery function"""
    print_header()
    
    while True:
        print("📋 RECOVERY MENU:")
        print("1. 🔑 Authorize Machine")
        print("2. 🔄 Restore from Backup")
        print("3. 🚀 Direct Launch (No Security)")
        print("4. 🧪 Test System Components")
        print("5. ❌ Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == "1":
            print()
            if authorize_machine_directly():
                print("\n✅ Authorization successful!")
                print("You can now use the normal launcher.")
            else:
                print("\n❌ Authorization failed!")
                
        elif choice == "2":
            print()
            if restore_from_backup():
                print("\n✅ Backup restoration completed!")
                print("System should now work normally.")
            else:
                print("\n❌ Backup restoration failed!")
                
        elif choice == "3":
            print()
            launch_direct()
            
        elif choice == "4":
            print()
            if test_system():
                print("\n✅ All components found!")
            else:
                print("\n⚠️ Some components missing!")
                
        elif choice == "5":
            print("\n👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice!")
        
        print("\n" + "-" * 50)
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Recovery error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
