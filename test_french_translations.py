#!/usr/bin/env python3
"""
Test the French translations in all receipt types
"""

import sys
import os
from pathlib import Path

def test_customer_receipt_french():
    """Test that customer receipt uses French text"""
    
    print("Testing Customer Receipt French")
    print("=" * 32)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_french = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for French transaction details
            if "Heure:" in content and "Caissier:" in content and "Reçu #:" in content:
                print(f"   ✅ Transaction details in French (Heure, Caissier, Reçu)")
            else:
                print(f"   ❌ Transaction details not in French")
                all_french = False
            
            # Check for French transaction details header
            if "DÉTAILS DE LA TRANSACTION" in content:
                print(f"   ✅ Transaction details header in French")
            else:
                print(f"   ❌ Transaction details header not in French")
                all_french = False
            
            # Check for French footer
            if "Merci pour votre visite!" in content:
                print(f"   ✅ Default footer in French")
            else:
                print(f"   ❌ Default footer not in French")
                all_french = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_french = False
    
    return all_french

def test_summary_receipt_french():
    """Test that summary receipt uses French text"""
    
    print("\nTesting Summary Receipt French")
    print("=" * 31)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_french = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for French summary header
            if "RÉSUMÉ DE TRANSACTION" in content:
                print(f"   ✅ Summary header in French")
            else:
                print(f"   ❌ Summary header not in French")
                all_french = False
            
            # Check for French internal copy
            if "Copie Interne" in content:
                print(f"   ✅ Internal copy in French")
            else:
                print(f"   ❌ Internal copy not in French")
                all_french = False
            
            # Check for French time and cashier in summary
            if "generate_summary_receipt" in content and "Heure:" in content and "Caissier:" in content:
                print(f"   ✅ Summary details in French")
            else:
                print(f"   ❌ Summary details not in French")
                all_french = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_french = False
    
    return all_french

def test_history_receipt_french():
    """Test that history receipt uses French text"""
    
    print("\nTesting History Receipt French")
    print("=" * 31)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_french = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for French history header
            if "RAPPORT D'HISTORIQUE DES VENTES" in content:
                print(f"   ✅ History report header in French")
            else:
                print(f"   ❌ History report header not in French")
                all_french = False
            
            # Check for French table headers
            if "Produit:" in content and "Prix" in content and "Qté" in content:
                print(f"   ✅ Table headers in French (Produit, Prix, Qté)")
            else:
                print(f"   ❌ Table headers not in French")
                all_french = False
            
            # Check for French period and cashier
            if "Période:" in content and "Aujourd'hui" in content:
                print(f"   ✅ Period info in French (Période, Aujourd'hui)")
            else:
                print(f"   ❌ Period info not in French")
                all_french = False
            
            # Check for French "All Users"
            if "Tous les Utilisateurs" in content:
                print(f"   ✅ All Users in French")
            else:
                print(f"   ❌ All Users not in French")
                all_french = False
            
            # Check for French "No products found"
            if "Aucun produit trouvé" in content:
                print(f"   ✅ No products found in French")
            else:
                print(f"   ❌ No products found not in French")
                all_french = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_french = False
    
    return all_french

def test_default_settings_french():
    """Test that default settings use French text"""
    
    print("\nTesting Default Settings French")
    print("=" * 32)
    
    files_to_check = [
        "receipt_generator.py",
        "YES/receipt_generator.py"
    ]
    
    all_french = True
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Checking: {file_path}")
            
            # Check for French default footer in get_default_settings
            if "get_default_settings" in content and "Merci pour votre visite!" in content:
                print(f"   ✅ Default settings footer in French")
            else:
                print(f"   ❌ Default settings footer not in French")
                all_french = False
            
            # Check for French default footer in load_receipt_settings
            if "load_receipt_settings" in content and "French default" in content:
                print(f"   ✅ Load settings footer in French")
            else:
                print(f"   ❌ Load settings footer not in French")
                all_french = False
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            all_french = False
    
    return all_french

def test_obfuscated_version():
    """Test that obfuscated version was updated"""
    
    print("\nTesting Obfuscated Version")
    print("=" * 27)
    
    obf_file = "YES_OBFUSCATED/receipt_generator.py"
    
    if Path(obf_file).exists():
        print(f"✅ Found: {obf_file}")
        
        # Check if it's actually obfuscated
        try:
            with open(obf_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Obfuscated files should have scrambled names
            if len(content) > 100 and ('import' in content):
                print(f"   ✅ File appears to be obfuscated")
                return True
            else:
                print(f"   ⚠️ File may not be properly obfuscated")
                return False
        except:
            print(f"   ✅ File is obfuscated (cannot read normally)")
            return True
    else:
        print(f"❌ Missing: {obf_file}")
        return False

def main():
    """Run all French translation tests"""
    
    print("🇫🇷 FRENCH TRANSLATIONS TEST SUITE")
    print("=" * 36)
    
    tests = [
        ("Customer Receipt French", test_customer_receipt_french),
        ("Summary Receipt French", test_summary_receipt_french),
        ("History Receipt French", test_history_receipt_french),
        ("Default Settings French", test_default_settings_french),
        ("Obfuscated Version", test_obfuscated_version)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 36)
    print("📊 RESULTS")
    print("=" * 36)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Customer receipts use French text")
        print("✅ Summary receipts use French text")
        print("✅ History reports use French text")
        print("✅ Default settings use French text")
        print("✅ All versions updated correctly")
    else:
        print("⚠️ Some tests failed")
        print("❌ French translations may not be complete")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🇫🇷 French translations successfully implemented!")
        print("📄 Customer receipts: Heure, Caissier, Reçu, Détails de la Transaction")
        print("📄 Summary receipts: Résumé de Transaction, Copie Interne")
        print("📄 History reports: Rapport d'Historique, Produit, Prix, Qté, Période")
        print("📄 Default footer: Merci pour votre visite!")
        print("🔄 All versions (main, protected, obfuscated) updated")
    else:
        print("\n❌ French translations need attention")
    
    exit(0 if success else 1)
