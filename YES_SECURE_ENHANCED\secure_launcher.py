#!/usr/bin/env python3
"""
Secure Launcher for POS System
Provides multiple security checks before allowing system access
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, simpledialog
import subprocess
from security_manager import security_manager

class SecureLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔒 Secure POS System Launcher")
        self.root.geometry("500x400")
        self.root.configure(bg='#1a1a1a')
        self.root.resizable(False, False)
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"500x400+{x}+{y}")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the launcher UI"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#ff8c00', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🔒 SECURE POS SYSTEM", 
                              font=('Segoe UI', 18, 'bold'), bg='#ff8c00', fg='white')
        title_label.pack(expand=True)
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # Security status
        self.status_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        self.status_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(self.status_frame, text="🛡️ Security Status", 
                font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(pady=(10, 5))
        
        self.status_label = tk.Label(self.status_frame, text="Checking security...", 
                                    font=('Segoe UI', 11), bg='#2d2d2d', fg='white')
        self.status_label.pack(pady=(0, 10))
        
        # Machine info
        info_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(info_frame, text="💻 Machine Information", 
                font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(pady=(10, 5))
        
        machine_info = security_manager.get_machine_info()
        info_text = f"Machine ID: {machine_info['machine_id'][:16]}...\n"
        info_text += f"System: {machine_info['system']} {machine_info['machine']}\n"
        info_text += f"Node: {machine_info['node']}"
        
        tk.Label(info_frame, text=info_text, 
                font=('Segoe UI', 9), bg='#2d2d2d', fg='white').pack(pady=(0, 10))
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.launch_btn = tk.Button(button_frame, text="🚀 Launch POS System",
                                   font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                                   padx=30, pady=10, state='disabled',
                                   command=self.launch_pos_system)
        self.launch_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        auth_btn = tk.Button(button_frame, text="🔑 Authorize Machine",
                            font=('Segoe UI', 12, 'bold'), bg='#007bff', fg='white',
                            padx=30, pady=10, command=self.authorize_machine)
        auth_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        exit_btn = tk.Button(button_frame, text="❌ Exit",
                            font=('Segoe UI', 12, 'bold'), bg='#dc3545', fg='white',
                            padx=30, pady=10, command=self.root.quit)
        exit_btn.pack(side=tk.RIGHT)
        
        # Start security check
        self.root.after(1000, self.perform_security_check)
    
    def perform_security_check(self):
        """Perform comprehensive security check"""
        try:
            self.status_label.config(text="🔍 Performing security checks...", fg='yellow')
            self.root.update()
            
            # Perform security check
            security_ok, message = security_manager.security_check()
            
            if security_ok:
                self.status_label.config(text="✅ Security checks passed - System ready", fg='#28a745')
                self.launch_btn.config(state='normal')
            else:
                if "UNAUTHORIZED_MACHINE" in message:
                    self.status_label.config(text="🚫 Machine not authorized", fg='#dc3545')
                    self.show_authorization_prompt()
                elif "FILE_INTEGRITY_VIOLATION" in message:
                    self.status_label.config(text="⚠️ File integrity violation detected", fg='#dc3545')
                    self.show_integrity_warning(message)
                else:
                    self.status_label.config(text=f"❌ Security check failed: {message}", fg='#dc3545')
                    
        except Exception as e:
            self.status_label.config(text=f"❌ Security error: {e}", fg='#dc3545')
    
    def show_authorization_prompt(self):
        """Show authorization prompt for new machines"""
        result = messagebox.askyesno(
            "Machine Authorization Required",
            "This machine is not authorized to run the POS system.\n\n"
            "Do you have an authorization code to register this machine?"
        )
        
        if result:
            self.authorize_machine()
    
    def authorize_machine(self):
        """Authorize the current machine"""
        auth_code = simpledialog.askstring(
            "Machine Authorization",
            "Enter authorization code:",
            show='*'
        )
        
        if auth_code:
            if security_manager.authorize_machine(auth_code):
                messagebox.showinfo("Success", "Machine authorized successfully!")
                self.perform_security_check()
            else:
                messagebox.showerror("Error", "Invalid authorization code!")
    
    def show_integrity_warning(self, message):
        """Show file integrity warning"""
        result = messagebox.askyesno(
            "Security Warning",
            f"File integrity violation detected!\n\n{message}\n\n"
            "The system files may have been tampered with.\n"
            "Do you want to continue anyway? (NOT RECOMMENDED)"
        )
        
        if result:
            self.launch_btn.config(state='normal')
            self.status_label.config(text="⚠️ Launching with integrity warnings", fg='orange')
    
    def launch_pos_system(self):
        """Launch the main POS system"""
        try:
            self.status_label.config(text="🚀 Launching POS system...", fg='#28a745')
            self.root.update()

            # Log launch event
            try:
                security_manager.log_security_event("SYSTEM_LAUNCH", "POS system launched")
            except:
                pass  # Continue even if logging fails

            # Hide launcher
            self.root.withdraw()

            # Launch main system
            if os.path.exists("main.py"):
                try:
                    # Use os.system for better compatibility
                    result = os.system(f'"{sys.executable}" main.py')
                    if result != 0:
                        print(f"POS system exited with code: {result}")
                except Exception as e:
                    print(f"Launch error: {e}")
                    # Try alternative launch method
                    try:
                        subprocess.run([sys.executable, "main.py"], check=False)
                    except Exception as e2:
                        messagebox.showerror("Launch Error", f"Failed to launch POS system: {e2}")
            else:
                messagebox.showerror("Error", "main.py not found!")

            # Show launcher again when POS closes
            self.root.deiconify()
            self.status_label.config(text="✅ POS system closed", fg='white')

        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch POS system: {e}")
            self.root.deiconify()
    
    def run(self):
        """Run the secure launcher"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            pass

def main():
    """Main entry point"""
    try:
        # Check if running from correct directory
        if not os.path.exists("main.py"):
            print("❌ Error: main.py not found!")
            print("Please run this launcher from the POS system directory.")
            input("Press Enter to exit...")
            return
        
        # Create and run launcher
        launcher = SecureLauncher()
        launcher.run()
        
    except Exception as e:
        print(f"❌ Launcher error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
