"""
Translation system for POS System
Provides multi-language support (English/French)
"""

TRANSLATIONS = {
    'english': {
        # Login and basic interface
        'login_title': 'POS System Login',
        'username': 'Username:',
        'password': 'Password:',
        'login': 'Login',
        'logout': 'Logout',
        'language': 'Language:',
        'logged_in_as': 'Logged in as:',
        'exit': 'Exit',

        # Main interface
        'categories': 'Categories',
        'products': 'Products',
        'shopping_cart': 'Shopping Cart & Actions',
        'quick_actions': 'Quick Actions',
        'checkout': 'CHECKOUT',
        'add_extra': 'Add Extra',
        'remove': 'Remove',
        'clear_cart': 'Clear Cart',
        'total': 'Total:',

        # Navigation
        'users': 'Users',
        'history': 'History',
        'receipts': 'Receipts',
        'user_management': 'User Management',
        'product_management': 'Product Management',
        'sales_history': 'Sales History',
        'receipt_settings': 'Receipt Settings',

        # Dialog and message boxes
        'success': 'Success',
        'error': 'Error',
        'warning': 'Warning',
        'confirm': 'Confirm',
        'cancel': 'Cancel',
        'ok': 'OK',
        'yes': 'Yes',
        'no': 'No',
        'close': 'Close',
        'save': 'Save',
        'delete': 'Delete',
        'edit': 'Edit',
        'add': 'Add',
        'clear': 'Clear',
        'enter': 'Enter',
        'back': 'Back',
        'print': 'Print',
        'reset': 'Reset',
        'form': 'Form',

        # User management
        'add_user': 'Add User',
        'edit_user': 'Edit User',
        'delete_user': 'Delete User',
        'username_field': 'Username',
        'password_field': 'Password',
        'role': 'Role',
        'admin': 'Admin',
        'user': 'User',
        'confirm_delete_user': 'Are you sure you want to delete user',
        'user_deleted_success': 'User deleted successfully',
        'user_added_success': 'User added successfully',
        'user_updated_success': 'User updated successfully',
        'username_required': 'Username is required',
        'password_required': 'Password is required',
        'username_password_required': 'Please enter username and password',
        'username_exists': 'Username already exists',
        'failed_add_user': 'Failed to add user',
        'failed_update_user': 'Failed to update user',
        'failed_delete_user': 'Failed to delete user',

        # Product management
        'add_product': 'Add Product',
        'edit_product': 'Edit Product',
        'delete_product': 'Delete Product',
        'product_name': 'Product Name',
        'category': 'Category',
        'price': 'Price',
        'image': 'Image',
        'select_image': 'Select Image',
        'no_image_selected': 'No image selected',
        'confirm_delete_product': 'Are you sure you want to delete product',
        'product_deleted_success': 'Product deleted successfully',
        'product_added_success': 'Product added successfully',
        'product_updated_success': 'Product updated successfully',
        'name_category_price_required': 'Name, category and price are required',
        'invalid_price': 'Please enter a valid price (0 or greater)',
        'price_cannot_negative': 'Price cannot be negative',
        'select_valid_category': 'Please select a valid category',
        'failed_add_product': 'Failed to add product',
        'failed_update_product': 'Failed to update product',
        'failed_delete_product': 'Failed to delete product',

        # Category management
        'add_category': 'Add Category',
        'edit_category': 'Edit Category',
        'delete_category': 'Delete Category',
        'category_name': 'Category Name',
        'confirm_delete_category': 'Are you sure you want to delete category',
        'category_deleted_success': 'Category deleted successfully',
        'category_added_success': 'Category added successfully',
        'category_updated_success': 'Category updated successfully',
        'category_name_required': 'Category name is required',
        'failed_add_category': 'Failed to add category',
        'failed_update_category': 'Failed to update category',
        'failed_delete_category': 'Failed to delete category',

        # Sales and checkout
        'sale_completed': 'Sale completed! Total:',
        'failed_complete_sale': 'Failed to complete sale',
        'cart_empty': 'Cart is empty',
        'invalid_login': 'Invalid username or password',
        'access_denied': 'Access Denied',
        'admin_access_required': 'Admin access required',

        # Extra charges
        'add_extra_expense': 'Add Extra Expense',
        'description_optional': 'Description (optional):',
        'amount': 'Amount:',
        'extra': 'extra',
        'amount_required': 'Please enter an amount',
        'amount_greater_zero': 'Amount must be greater than 0',
        'enter_valid_amount': 'Please enter a valid amount',
        'enter_amount': 'Enter Amount',
        'enter_password': 'Enter Password',

        # Receipt settings
        'business_name': 'Business Name',
        'business_address': 'Business Address',
        'business_phone': 'Business Phone',
        'header_text': 'Header Text',
        'footer_text': 'Footer Text',
        'logo': 'Logo',
        'select_logo': 'Select Logo',
        'no_logo_selected': 'No logo selected',
        'paper_size': 'Paper Size',
        'font_size': 'Font Size',
        'preview': 'Preview',
        'reset_settings': 'Reset Settings',
        'confirm_reset': 'Are you sure you want to reset all receipt settings to defaults?',
        'settings_saved': 'Settings saved successfully',
        'failed_save_settings': 'Failed to save settings',

        # Paper sizes and printing
        'custom_sizes': 'Custom Sizes',
        'manage_custom_sizes': 'Manage Custom Sizes',
        'add_custom_size': 'Add Custom Size',
        'size_name': 'Size Name',
        'width': 'Width',
        'height': 'Height',
        'pixels': 'pixels',
        'custom_size_added': 'Custom size added successfully!',
        'custom_size_deleted': 'Custom size deleted successfully!',
        'select_custom_size_delete': 'Please select a custom size to delete',
        'confirm_delete_custom_size': 'Are you sure you want to delete the custom size',
        'failed_add_custom_size': 'Failed to add custom size',
        'failed_delete_custom_size': 'Failed to delete custom size',
        'size_name_required': 'Size name is required',
        'valid_dimensions_required': 'Please enter valid width and height',
        'dimensions_positive': 'Width and height must be positive numbers',

        # Sales history
        'filter_24h': 'Filter 24h',
        'filter_today': 'Filter Today',
        'apply_filters': 'Apply Filters',
        'show_all': 'Show All',
        'print_history': 'Print History',
        'delete_all_history': 'Delete All History',
        'confirm_delete_all_history': 'Are you sure you want to delete all sales history? This action cannot be undone.',
        'all_history_deleted': 'All sales history has been deleted successfully',
        'failed_delete_history': 'Failed to delete sales history',
        'sales_history_saved': 'Sales history saved to sales_history.txt',
        'could_not_print_history': 'Could not print sales history',
        'failed_print_history': 'Failed to print sales history',

        # Management area headers and labels
        'date': 'Date',
        'time': 'Time',
        'user_column': 'User',
        'total_column': 'Total',
        'items': 'Items',
        'actions': 'Actions',
        'select_user_to_edit': 'Please select a user to edit',
        'select_user_to_delete': 'Please select a user to delete',
        'select_product_to_edit': 'Please select a product to edit',
        'select_product_to_delete': 'Please select a product to delete',
        'select_category_to_edit': 'Please select a category to edit',
        'select_category_to_delete': 'Please select a category to delete',
        'name': 'Name',

        # General messages
        'loading_error': 'Error loading',
        'saving_error': 'Error saving',
        'database_error': 'Database error',
        'file_error': 'File error',
        'network_error': 'Network error',
        'unknown_error': 'Unknown error',

        # Property text
        'property_text': 'Intellectual property of Hossam and Walid, call 0697088427 to get yours',

        # Receipt default text
        'default_business_name': 'Your Business Name',
        'default_business_address': 'Your Business Address',
        'default_business_phone': 'Your Phone Number',
        'default_header_text': 'POS SYSTEM RECEIPT',
        'default_footer_text': 'Thank you for your business!',
        'transaction_summary': 'TRANSACTION SUMMARY',
        'date_label': 'Date:',
        'cashier_label': 'Cashier:',
        'total_amount': 'TOTAL AMOUNT:',
        'sales_history_report': 'SALES HISTORY REPORT',
        'period_label': 'Period:',
        'user_label': 'User:',
        'generated_label': 'Generated:',
        'total_sales': 'TOTAL SALES',
        'transactions': 'TRANSACTIONS',

        # Main receipt content
        'transaction_details': 'TRANSACTION DETAILS',
        'sale_id': 'Sale ID:',
        'items_purchased': 'ITEMS PURCHASED',

        # User color selection
        'button_color': 'Button Color',
        'choose_color': 'Choose Color',

        # Transaction inspection dialog
        'transaction_details_title': 'Transaction Details',
        'transaction_number': 'Transaction #',
        'date_colon': 'Date:',
        'time_colon': 'Time:',
        'cashier_colon': 'Cashier:',
        'total_colon': 'Total:',
        'items_sold': 'Items Sold',
        'no_items_found': 'No items found for this transaction',
        'each': 'each',
        'close': 'Close',
        'error': 'Error',
        'could_not_load_details': 'Could not load transaction details',
        'double_click_instruction': 'Double-click any transaction to view detailed information',

        # Storage management
        'storage_management': 'Storage Management',
        'storage': 'Storage',
        'items_in_storage': 'Items in Storage',
        'current_stock': 'Current Stock',
        'min_level': 'Min Level',
        'max_level': 'Max Level',
        'unit': 'Unit',
        'status': 'Status',
        'update_stock': 'Update Stock',
        'set_min_max_levels': 'Set Min/Max Levels',
        'delete_selected': 'Delete Selected',
        'refresh': 'Refresh',
        'add_stock': 'Add Stock',
        'remove_stock': 'Remove Stock',
        'quantity': 'Quantity',
        'update_type': 'Update Type',
        'minimum_stock_level': 'Minimum Stock Level',
        'maximum_stock_level': 'Maximum Stock Level',
        'unit_of_measurement': 'Unit of Measurement',
        'save_changes': 'Save Changes',
        'stock_updated_success': 'Stock updated successfully!',
        'new_stock': 'New stock:',
        'levels_updated_success': 'Levels updated successfully!',
        'select_item_update_stock': 'Please select an item to update stock.',
        'select_item_set_levels': 'Please select an item to set levels.',
        'select_item_delete': 'Please select an item to delete from storage tracking.',
        'confirm_delete_storage': 'Are you sure you want to remove',
        'from_storage_tracking': 'from storage tracking?',
        'delete_storage_warning': 'This will:\n• Remove the item from storage management\n• Delete all stock movement history\n• Stop automatic stock tracking for this product\n\nThe product itself will remain in the system.',
        'removed_from_storage': 'has been removed from storage tracking.',
        'quantity_positive': 'Quantity must be a positive number.',
        'enter_valid_quantity': 'Please enter a valid quantity.',
        'levels_positive': 'Levels must be positive numbers.',
        'min_less_than_max': 'Minimum level must be less than maximum level.',
        'enter_valid_levels': 'Please enter valid numbers for levels.',
        'product_not_found': 'Product not found.',
        'failed_update_stock': 'Failed to update stock:',
        'failed_update_levels': 'Failed to update levels:',
        'failed_delete_storage': 'Failed to delete from storage:',
        'low_stock': 'LOW STOCK',
        'overstocked': 'OVERSTOCKED',
        'ok_stock': 'OK',
        'track_in_storage': 'Track this item in storage',
        'unit_for_storage': 'Unit of Measurement (for storage):',

        # Email settings
        'email_settings': 'Email Settings',
        'smtp_configuration': 'SMTP Configuration',
        'enable_email': 'Enable Email Feature',
        'smtp_server': 'SMTP Server',
        'smtp_port': 'SMTP Port',
        'sender_email': 'Sender Email',
        'app_password': 'App Password',
        'test_connection': 'Test Connection',
        'email_recipients': 'Email Recipients',
        'add_email': 'Add Email',
        'remove_selected': 'Remove Selected',
        'save_settings': 'Save Settings',
        'email_added': 'Email address added successfully',
        'email_removed': 'Email address removed successfully',
        'invalid_email': 'Invalid email address format',
        'email_exists': 'Email address already exists',
        'smtp_test_success': 'SMTP connection successful',
        'smtp_test_failed': 'SMTP connection failed',
        'email_sent': 'Email sent successfully',
        'email_failed': 'Failed to send email',
        'generating_pdf': 'Generating PDF...',
        'sending_email': 'Sending email...',

        # Display settings
        'number_of_columns': 'Number of product columns:',
        'button_size_pixels': 'Product button size (pixels):',
    },
    'french': {
        # Login and basic interface
        'login_title': 'Connexion PDV',
        'username': 'Nom d\'utilisateur:',
        'password': 'Mot de passe:',
        'login': 'Connexion',
        'logout': 'Déconnexion',
        'language': 'Langue:',
        'logged_in_as': 'Connecté en tant que:',
        'exit': 'Sortir',

        # Main interface
        'categories': 'Catégories',
        'products': 'Produits',
        'shopping_cart': 'Panier & Actions',
        'quick_actions': 'Actions Rapides',
        'checkout': 'PAYER',
        'add_extra': 'Ajouter Extra',
        'remove': 'Supprimer',
        'clear_cart': 'Vider Panier',
        'total': 'Total:',

        # Navigation
        'users': 'Utilisateurs',
        'history': 'Historique',
        'receipts': 'Reçus',
        'user_management': 'Gestion Utilisateurs',
        'product_management': 'Gestion Produits',
        'sales_history': 'Historique Ventes',
        'receipt_settings': 'Paramètres Reçus',

        # Dialog and message boxes
        'success': 'Succès',
        'error': 'Erreur',
        'warning': 'Avertissement',
        'confirm': 'Confirmer',
        'cancel': 'Annuler',
        'ok': 'OK',
        'yes': 'Oui',
        'no': 'Non',
        'close': 'Fermer',
        'save': 'Enregistrer',
        'delete': 'Supprimer',
        'edit': 'Modifier',
        'add': 'Ajouter',
        'clear': 'Effacer',
        'enter': 'Entrer',
        'back': 'Retour',
        'print': 'Imprimer',
        'reset': 'Réinitialiser',
        'form': 'Formulaire',

        # User management
        'add_user': 'Ajouter Utilisateur',
        'edit_user': 'Modifier Utilisateur',
        'delete_user': 'Supprimer Utilisateur',
        'username_field': 'Nom d\'utilisateur',
        'password_field': 'Mot de passe',
        'role': 'Rôle',
        'admin': 'Administrateur',
        'user': 'Utilisateur',
        'confirm_delete_user': 'Êtes-vous sûr de vouloir supprimer l\'utilisateur',
        'user_deleted_success': 'Utilisateur supprimé avec succès',
        'user_added_success': 'Utilisateur ajouté avec succès',
        'user_updated_success': 'Utilisateur mis à jour avec succès',
        'username_required': 'Le nom d\'utilisateur est requis',
        'password_required': 'Le mot de passe est requis',
        'username_password_required': 'Veuillez entrer le nom d\'utilisateur et le mot de passe',
        'username_exists': 'Le nom d\'utilisateur existe déjà',
        'failed_add_user': 'Échec de l\'ajout de l\'utilisateur',
        'failed_update_user': 'Échec de la mise à jour de l\'utilisateur',
        'failed_delete_user': 'Échec de la suppression de l\'utilisateur',

        # Product management
        'add_product': 'Ajouter Produit',
        'edit_product': 'Modifier Produit',
        'delete_product': 'Supprimer Produit',
        'product_name': 'Nom du Produit',
        'category': 'Catégorie',
        'price': 'Prix',
        'image': 'Image',
        'select_image': 'Sélectionner Image',
        'no_image_selected': 'Aucune image sélectionnée',
        'confirm_delete_product': 'Êtes-vous sûr de vouloir supprimer le produit',
        'product_deleted_success': 'Produit supprimé avec succès',
        'product_added_success': 'Produit ajouté avec succès',
        'product_updated_success': 'Produit mis à jour avec succès',
        'name_category_price_required': 'Le nom, la catégorie et le prix sont requis',
        'invalid_price': 'Veuillez entrer un prix valide (0 ou plus)',
        'price_cannot_negative': 'Le prix ne peut pas être négatif',
        'select_valid_category': 'Veuillez sélectionner une catégorie valide',
        'failed_add_product': 'Échec de l\'ajout du produit',
        'failed_update_product': 'Échec de la mise à jour du produit',
        'failed_delete_product': 'Échec de la suppression du produit',

        # Category management
        'add_category': 'Ajouter Catégorie',
        'edit_category': 'Modifier Catégorie',
        'delete_category': 'Supprimer Catégorie',
        'category_name': 'Nom de Catégorie',
        'confirm_delete_category': 'Êtes-vous sûr de vouloir supprimer la catégorie',
        'category_deleted_success': 'Catégorie supprimée avec succès',
        'category_added_success': 'Catégorie ajoutée avec succès',
        'category_updated_success': 'Catégorie mise à jour avec succès',
        'category_name_required': 'Le nom de catégorie est requis',
        'failed_add_category': 'Échec de l\'ajout de la catégorie',
        'failed_update_category': 'Échec de la mise à jour de la catégorie',
        'failed_delete_category': 'Échec de la suppression de la catégorie',

        # Sales and checkout
        'sale_completed': 'Vente terminée! Total:',
        'failed_complete_sale': 'Échec de la finalisation de la vente',
        'cart_empty': 'Le panier est vide',
        'invalid_login': 'Nom d\'utilisateur ou mot de passe invalide',
        'access_denied': 'Accès Refusé',
        'admin_access_required': 'Accès administrateur requis',

        # Extra charges
        'add_extra_expense': 'Ajouter Frais Supplémentaire',
        'description_optional': 'Description (optionnelle):',
        'amount': 'Montant:',
        'extra': 'extra',
        'amount_required': 'Veuillez entrer un montant',
        'amount_greater_zero': 'Le montant doit être supérieur à 0',
        'enter_valid_amount': 'Veuillez entrer un montant valide',
        'enter_amount': 'Entrer Montant',
        'enter_password': 'Entrer Mot de Passe',

        # Receipt settings
        'business_name': 'Nom de l\'Entreprise',
        'business_address': 'Adresse de l\'Entreprise',
        'business_phone': 'Téléphone de l\'Entreprise',
        'header_text': 'Texte d\'En-tête',
        'footer_text': 'Texte de Pied de Page',
        'logo': 'Logo',
        'select_logo': 'Sélectionner Logo',
        'no_logo_selected': 'Aucun logo sélectionné',
        'paper_size': 'Taille du Papier',
        'font_size': 'Taille de Police',
        'preview': 'Aperçu',
        'reset_settings': 'Réinitialiser Paramètres',
        'confirm_reset': 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres de reçu aux valeurs par défaut?',
        'settings_saved': 'Paramètres enregistrés avec succès',
        'failed_save_settings': 'Échec de l\'enregistrement des paramètres',

        # Paper sizes and printing
        'custom_sizes': 'Tailles Personnalisées',
        'manage_custom_sizes': 'Gérer Tailles Personnalisées',
        'add_custom_size': 'Ajouter Taille Personnalisée',
        'size_name': 'Nom de Taille',
        'width': 'Largeur',
        'height': 'Hauteur',
        'pixels': 'pixels',
        'custom_size_added': 'Taille personnalisée ajoutée avec succès!',
        'custom_size_deleted': 'Taille personnalisée supprimée avec succès!',
        'select_custom_size_delete': 'Veuillez sélectionner une taille personnalisée à supprimer',
        'confirm_delete_custom_size': 'Êtes-vous sûr de vouloir supprimer la taille personnalisée',
        'failed_add_custom_size': 'Échec de l\'ajout de la taille personnalisée',
        'failed_delete_custom_size': 'Échec de la suppression de la taille personnalisée',
        'size_name_required': 'Le nom de taille est requis',
        'valid_dimensions_required': 'Veuillez entrer une largeur et hauteur valides',
        'dimensions_positive': 'La largeur et la hauteur doivent être des nombres positifs',

        # Sales history
        'filter_24h': 'Filtrer 24h',
        'filter_today': 'Filtrer Aujourd\'hui',
        'apply_filters': 'Appliquer Filtres',
        'show_all': 'Afficher Tout',
        'print_history': 'Imprimer Historique',
        'delete_all_history': 'Supprimer Tout l\'Historique',
        'confirm_delete_all_history': 'Êtes-vous sûr de vouloir supprimer tout l\'historique des ventes? Cette action ne peut pas être annulée.',
        'all_history_deleted': 'Tout l\'historique des ventes a été supprimé avec succès',
        'failed_delete_history': 'Échec de la suppression de l\'historique des ventes',
        'sales_history_saved': 'Historique des ventes sauvegardé dans sales_history.txt',
        'could_not_print_history': 'Impossible d\'imprimer l\'historique des ventes',
        'failed_print_history': 'Échec de l\'impression de l\'historique des ventes',

        # Management area headers and labels
        'date': 'Date',
        'time': 'Heure',
        'user_column': 'Utilisateur',
        'total_column': 'Total',
        'items': 'Articles',
        'actions': 'Actions',
        'select_user_to_edit': 'Veuillez sélectionner un utilisateur à modifier',
        'select_user_to_delete': 'Veuillez sélectionner un utilisateur à supprimer',
        'select_product_to_edit': 'Veuillez sélectionner un produit à modifier',
        'select_product_to_delete': 'Veuillez sélectionner un produit à supprimer',
        'select_category_to_edit': 'Veuillez sélectionner une catégorie à modifier',
        'select_category_to_delete': 'Veuillez sélectionner une catégorie à supprimer',
        'name': 'Nom',

        # General messages
        'loading_error': 'Erreur de chargement',
        'saving_error': 'Erreur de sauvegarde',
        'database_error': 'Erreur de base de données',
        'file_error': 'Erreur de fichier',
        'network_error': 'Erreur réseau',
        'unknown_error': 'Erreur inconnue',

        # Property text
        'property_text': 'Propriété intellectuelle de Hossam et Walid, appelez 0697088427 pour obtenir le vôtre',

        # Receipt default text
        'default_business_name': 'Nom de Votre Entreprise',
        'default_business_address': 'Adresse de Votre Entreprise',
        'default_business_phone': 'Votre Numéro de Téléphone',
        'default_header_text': 'REÇU SYSTÈME POS',
        'default_footer_text': 'Merci pour votre visite!',
        'transaction_summary': 'RÉSUMÉ DE TRANSACTION',
        'date_label': 'Date:',
        'cashier_label': 'Caissier:',
        'total_amount': 'MONTANT TOTAL:',
        'sales_history_report': 'RAPPORT HISTORIQUE DES VENTES',
        'period_label': 'Période:',
        'user_label': 'Utilisateur:',
        'generated_label': 'Généré:',
        'total_sales': 'TOTAL DES VENTES',
        'transactions': 'TRANSACTIONS',

        # Main receipt content
        'transaction_details': 'DÉTAILS DE TRANSACTION',
        'sale_id': 'ID Vente:',
        'items_purchased': 'ARTICLES ACHETÉS',

        # User color selection
        'button_color': 'Couleur Bouton',
        'choose_color': 'Choisir Couleur',

        # Storage management
        'storage_management': 'Gestion des Stocks',
        'storage': 'Stock',
        'items_in_storage': 'Articles en Stock',
        'current_stock': 'Stock Actuel',
        'min_level': 'Niveau Min',
        'max_level': 'Niveau Max',
        'unit': 'Unité',
        'status': 'Statut',
        'update_stock': 'Mettre à Jour Stock',
        'set_min_max_levels': 'Définir Niveaux Min/Max',
        'delete_selected': 'Supprimer Sélectionné',
        'refresh': 'Actualiser',
        'add_stock': 'Ajouter Stock',
        'remove_stock': 'Retirer Stock',
        'quantity': 'Quantité',
        'update_type': 'Type de Mise à Jour',
        'minimum_stock_level': 'Niveau de Stock Minimum',
        'maximum_stock_level': 'Niveau de Stock Maximum',
        'unit_of_measurement': 'Unité de Mesure',
        'save_changes': 'Enregistrer Modifications',
        'stock_updated_success': 'Stock mis à jour avec succès!',
        'new_stock': 'Nouveau stock:',
        'levels_updated_success': 'Niveaux mis à jour avec succès!',
        'select_item_update_stock': 'Veuillez sélectionner un article pour mettre à jour le stock.',
        'select_item_set_levels': 'Veuillez sélectionner un article pour définir les niveaux.',
        'select_item_delete': 'Veuillez sélectionner un article à supprimer du suivi des stocks.',
        'confirm_delete_storage': 'Êtes-vous sûr de vouloir retirer',
        'from_storage_tracking': 'du suivi des stocks?',
        'delete_storage_warning': 'Ceci va:\n• Retirer l\'article de la gestion des stocks\n• Supprimer tout l\'historique des mouvements de stock\n• Arrêter le suivi automatique des stocks pour ce produit\n\nLe produit lui-même restera dans le système.',
        'removed_from_storage': 'a été retiré du suivi des stocks.',
        'quantity_positive': 'La quantité doit être un nombre positif.',
        'enter_valid_quantity': 'Veuillez entrer une quantité valide.',
        'levels_positive': 'Les niveaux doivent être des nombres positifs.',
        'min_less_than_max': 'Le niveau minimum doit être inférieur au niveau maximum.',
        'enter_valid_levels': 'Veuillez entrer des nombres valides pour les niveaux.',
        'product_not_found': 'Produit non trouvé.',
        'failed_update_stock': 'Échec de la mise à jour du stock:',
        'failed_update_levels': 'Échec de la mise à jour des niveaux:',
        'failed_delete_storage': 'Échec de la suppression du stock:',
        'low_stock': 'STOCK FAIBLE',
        'overstocked': 'SURSTOCKÉ',
        'ok_stock': 'OK',
        'track_in_storage': 'Suivre cet article en stock',
        'unit_for_storage': 'Unité de Mesure (pour le stock):',

        # Transaction inspection dialog
        'transaction_details_title': 'Détails de Transaction',
        'transaction_number': 'Transaction #',
        'date_colon': 'Date:',
        'time_colon': 'Heure:',
        'cashier_colon': 'Caissier:',
        'total_colon': 'Total:',
        'items_sold': 'Articles Vendus',
        'no_items_found': 'Aucun article trouvé pour cette transaction',
        'each': 'chacun',
        'close': 'Fermer',
        'error': 'Erreur',
        'could_not_load_details': 'Impossible de charger les détails de la transaction',
        'double_click_instruction': 'Double-cliquez sur une transaction pour voir les informations détaillées',

        # Email settings
        'email_settings': 'Paramètres Email',
        'smtp_configuration': 'Configuration SMTP',
        'enable_email': 'Activer la Fonction Email',
        'smtp_server': 'Serveur SMTP',
        'smtp_port': 'Port SMTP',
        'sender_email': 'Email Expéditeur',
        'app_password': 'Mot de Passe App',
        'test_connection': 'Tester Connexion',
        'email_recipients': 'Destinataires Email',
        'add_email': 'Ajouter Email',
        'remove_selected': 'Supprimer Sélectionné',
        'save_settings': 'Enregistrer Paramètres',
        'email_added': 'Adresse email ajoutée avec succès',
        'email_removed': 'Adresse email supprimée avec succès',
        'invalid_email': 'Format d\'adresse email invalide',
        'email_exists': 'L\'adresse email existe déjà',
        'smtp_test_success': 'Connexion SMTP réussie',
        'smtp_test_failed': 'Échec de la connexion SMTP',
        'email_sent': 'Email envoyé avec succès',
        'email_failed': 'Échec de l\'envoi de l\'email',
        'generating_pdf': 'Génération du PDF...',
        'sending_email': 'Envoi de l\'email...',

        # Display settings
        'number_of_columns': 'Nombre de colonnes de produits:',
        'button_size_pixels': 'Taille des boutons produits (pixels):',
    }
}

def get_text(language, key):
    """Get translated text for given language and key"""
    return TRANSLATIONS.get(language, {}).get(key, key)
