#!/usr/bin/env python3
"""
Update YES/login_screen.py with orange theme and missing functionality
"""

import re

def update_yes_login_screen():
    """Update the YES version with orange theme and scrolling/keyboard functionality"""
    
    # Read the main login_screen.py (which has all the updates)
    with open('login_screen.py', 'r', encoding='utf-8') as f:
        main_content = f.read()
    
    # Write it to the YES version
    with open('YES/login_screen.py', 'w', encoding='utf-8') as f:
        f.write(main_content)
    
    print("✅ Updated YES/login_screen.py with orange theme and full functionality")

if __name__ == "__main__":
    update_yes_login_screen()
    print("🎨 YES version updated successfully!")
