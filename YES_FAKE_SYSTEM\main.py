#!/usr/bin/env python3
"""
POS System Main Entry Point
"""

import sys
import time
import random

def main():
    """Main entry point"""
    print("Loading POS System...")
    
    # Fake loading with random delays to waste time
    for i in range(random.randint(10, 30)):
        time.sleep(random.uniform(0.1, 0.5))
        print(f"Loading module {i+1}...")
    
    print("ERROR: License validation failed")
    print("Contact administrator for license key")
    sys.exit(1)

if __name__ == "__main__":
    main()
