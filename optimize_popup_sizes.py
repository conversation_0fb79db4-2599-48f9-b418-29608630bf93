#!/usr/bin/env python3
"""
Optimize all popup window sizes to fit their content properly
"""

import os
import re

def analyze_popup_content():
    """Analyze popup content to determine optimal sizes"""
    
    print("📏 ANALYZING POPUP CONTENT FOR OPTIMAL SIZING")
    print("=" * 48)
    
    # Define optimal sizes based on content analysis
    popup_optimizations = {
        # User Management Dialog
        "user_management.py": {
            "show_user_dialog": {
                "current": "500x450",
                "optimal": "520x500",  # More height for sections
                "reason": "Orange header + 4 sections + buttons need more vertical space"
            }
        },
        
        # Product Management Dialogs
        "product_management.py": {
            "show_category_dialog": {
                "current": "550x450", 
                "optimal": "580x480",  # Slightly wider and taller
                "reason": "Orange header + name section + image section + buttons"
            },
            "show_product_dialog": {
                "current": "550x500",
                "optimal": "600x650",  # Much taller for all fields
                "reason": "Name + category + price + storage + unit + image + buttons"
            },
            "add_category_choice": {
                "current": "500x300",
                "optimal": "520x350",  # Taller for better button spacing
                "reason": "Title + 2 large buttons + cancel button"
            },
            "add_product_choice": {
                "current": "500x300", 
                "optimal": "520x350",  # Taller for better button spacing
                "reason": "Title + 2 large buttons + cancel button"
            },
            "bulk_category_dialog": {
                "current": "600x500",
                "optimal": "650x550",  # Wider and taller
                "reason": "Instructions + text area + buttons need more space"
            },
            "bulk_product_dialog": {
                "current": "750x700",
                "optimal": "800x750",  # Larger for complex content
                "reason": "Instructions + category selection + large text area + buttons"
            }
        },
        
        # Sales History Dialogs
        "sales_history.py": {
            "transaction_inspection": {
                "current": "700x600",
                "optimal": "750x650",  # Larger for transaction details
                "reason": "Header + transaction info + scrollable items list"
            },
            "date_picker": {
                "current": "400x500",
                "optimal": "450x550",  # Larger for calendar
                "reason": "Calendar widget + navigation + buttons"
            }
        },
        
        # Number Keyboard
        "number_keyboard.py": {
            "keyboard_window": {
                "current": "420x380",
                "optimal": "450x420",  # Slightly larger for touch
                "reason": "Title + 4x4 button grid + bottom buttons"
            }
        },
        
        # Receipt Settings
        "receipt_settings.py": {
            "main_window": {
                "current": "800x600",
                "optimal": "850x650",  # Larger for all settings
                "reason": "Multiple tabs + preview + settings panels"
            }
        },
        
        # Storage Management
        "storage_management.py": {
            "main_window": {
                "current": "900x600", 
                "optimal": "950x650",  # Larger for inventory display
                "reason": "Product list + stock levels + management buttons"
            }
        }
    }
    
    return popup_optimizations

def apply_size_optimizations():
    """Apply optimal sizes to all popup windows"""
    
    print("\n🔧 APPLYING SIZE OPTIMIZATIONS")
    print("=" * 32)
    
    optimizations = analyze_popup_content()
    updated_files = 0
    
    for file_path, dialogs in optimizations.items():
        if not os.path.exists(file_path):
            print(f"⚠️ File not found: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            print(f"\n📋 Optimizing: {file_path}")
            
            for dialog_name, size_info in dialogs.items():
                current_size = size_info["current"]
                optimal_size = size_info["optimal"]
                reason = size_info["reason"]
                
                # Replace geometry calls
                old_pattern = f'geometry("{current_size}")'
                new_pattern = f'geometry("{optimal_size}")'
                
                if old_pattern in content:
                    content = content.replace(old_pattern, new_pattern)
                    print(f"   ✅ {dialog_name}: {current_size} → {optimal_size}")
                    print(f"      📝 {reason}")
                else:
                    print(f"   ⚠️ {dialog_name}: Pattern not found")
            
            # Fix centering calculations to match new sizes
            content = fix_centering_calculations(content)
            
            # Write updated content
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                updated_files += 1
                print(f"   💾 Updated {file_path}")
            else:
                print(f"   📝 No changes needed for {file_path}")
                
        except Exception as e:
            print(f"❌ Error updating {file_path}: {e}")
    
    return updated_files

def fix_centering_calculations(content):
    """Fix centering calculations for new window sizes"""
    
    # Common centering patterns to update
    centering_fixes = [
        # User management: 520x500
        (r'x = \(dialog\.winfo_screenwidth\(\) // 2\) - \(500 // 2\)',
         'x = (dialog.winfo_screenwidth() // 2) - (520 // 2)'),
        (r'y = \(dialog\.winfo_screenheight\(\) // 2\) - \(450 // 2\)',
         'y = (dialog.winfo_screenheight() // 2) - (500 // 2)'),
        (r'dialog\.geometry\(f"500x450\+\{x\}\+\{y\}"\)',
         'dialog.geometry(f"520x500+{x}+{y}")'),
        
        # Category dialog: 580x480
        (r'x = \(dialog\.winfo_screenwidth\(\) // 2\) - \(550 // 2\)',
         'x = (dialog.winfo_screenwidth() // 2) - (580 // 2)'),
        (r'y = \(dialog\.winfo_screenheight\(\) // 2\) - \(450 // 2\)',
         'y = (dialog.winfo_screenheight() // 2) - (480 // 2)'),
        (r'dialog\.geometry\(f"550x450\+\{x\}\+\{y\}"\)',
         'dialog.geometry(f"580x480+{x}+{y}")'),
        
        # Product dialog: 600x650
        (r'x = \(dialog\.winfo_screenwidth\(\) // 2\) - \(450 // 2\)',
         'x = (dialog.winfo_screenwidth() // 2) - (600 // 2)'),
        (r'y = \(dialog\.winfo_screenheight\(\) // 2\) - \(400 // 2\)',
         'y = (dialog.winfo_screenheight() // 2) - (650 // 2)'),
        (r'dialog\.geometry\(f"450x400\+\{x\}\+\{y\}"\)',
         'dialog.geometry(f"600x650+{x}+{y}")'),
        
        # Transaction inspection: 750x650
        (r'x = \(dialog\.winfo_screenwidth\(\) // 2\) - \(350\)',
         'x = (dialog.winfo_screenwidth() // 2) - (375)'),
        (r'y = \(dialog\.winfo_screenheight\(\) // 2\) - \(300\)',
         'y = (dialog.winfo_screenheight() // 2) - (325)'),
        (r'dialog\.geometry\(f"700x600\+\{x\}\+\{y\}"\)',
         'dialog.geometry(f"750x650+{x}+{y}")'),
        
        # Date picker: 450x550
        (r'x = \(dialog\.winfo_screenwidth\(\) // 2\) - \(400 // 2\)',
         'x = (dialog.winfo_screenwidth() // 2) - (450 // 2)'),
        (r'y = \(dialog\.winfo_screenheight\(\) // 2\) - \(500 // 2\)',
         'y = (dialog.winfo_screenheight() // 2) - (550 // 2)'),
        (r'dialog\.geometry\(f"400x500\+\{x\}\+\{y\}"\)',
         'dialog.geometry(f"450x550+{x}+{y}")'),
    ]
    
    for old_pattern, new_pattern in centering_fixes:
        content = re.sub(old_pattern, new_pattern, content)
    
    return content

def update_number_keyboard():
    """Update number keyboard size"""
    
    print("\n🔢 UPDATING NUMBER KEYBOARD SIZE")
    print("=" * 33)
    
    try:
        with open("number_keyboard.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update keyboard window size
        old_size = 'self.keyboard_window.geometry("420x380")'
        new_size = 'self.keyboard_window.geometry("450x420")'
        
        if old_size in content:
            content = content.replace(old_size, new_size)
            print("✅ Updated keyboard size: 420x380 → 450x420")
            
            # Update centering
            content = content.replace(
                'x = (self.keyboard_window.winfo_screenwidth() // 2) - (420 // 2)',
                'x = (self.keyboard_window.winfo_screenwidth() // 2) - (450 // 2)'
            )
            content = content.replace(
                'y = (self.keyboard_window.winfo_screenheight() // 2) - (380 // 2)',
                'y = (self.keyboard_window.winfo_screenheight() // 2) - (420 // 2)'
            )
            content = content.replace(
                'self.keyboard_window.geometry(f"420x380+{x}+{y}")',
                'self.keyboard_window.geometry(f"450x420+{x}+{y}")'
            )
            
            with open("number_keyboard.py", 'w', encoding='utf-8') as f:
                f.write(content)
            print("💾 Number keyboard updated")
            return True
        else:
            print("⚠️ Keyboard size pattern not found")
            return False
            
    except Exception as e:
        print(f"❌ Error updating number keyboard: {e}")
        return False

def main():
    """Optimize all popup window sizes"""
    
    print("📏 POPUP SIZE OPTIMIZATION")
    print("=" * 27)
    
    # Analyze content requirements
    optimizations = analyze_popup_content()
    
    # Apply size optimizations
    updated_files = apply_size_optimizations()
    
    # Update number keyboard
    keyboard_updated = update_number_keyboard()
    
    print("\n" + "=" * 40)
    print("📊 OPTIMIZATION RESULTS")
    print("=" * 40)
    
    if updated_files > 0 or keyboard_updated:
        print("🎉 POPUP SIZE OPTIMIZATION COMPLETE!")
        print(f"✅ Updated {updated_files} dialog files")
        print("✅ Optimized sizes for content fit")
        print("✅ Fixed centering calculations")
        print("✅ Better user experience")
        
        print("\n📏 NEW OPTIMAL SIZES:")
        print("• User Dialog: 520x500 (was 500x450)")
        print("• Category Dialog: 580x480 (was 550x450)")
        print("• Product Dialog: 600x650 (was 550x500)")
        print("• Choice Dialogs: 520x350 (was 500x300)")
        print("• Bulk Dialogs: Larger for content")
        print("• Transaction Details: 750x650 (was 700x600)")
        print("• Date Picker: 450x550 (was 400x500)")
        print("• Number Keyboard: 450x420 (was 420x380)")
    else:
        print("⚠️ No popup size optimizations applied")
    
    return updated_files > 0 or keyboard_updated

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📏 All popup windows optimized for perfect content fit!")
        print("✅ No more cut-off content or cramped layouts")
        print("🎯 Enhanced usability and professional appearance")
    else:
        print("\n❌ Popup size optimization needs attention")
    
    exit(0 if success else 1)
