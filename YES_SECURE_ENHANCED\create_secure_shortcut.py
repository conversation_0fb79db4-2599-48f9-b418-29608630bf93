#!/usr/bin/env python3
"""
Create Desktop Shortcut for Secure POS System
Creates shortcuts for easy daily use
"""

import os
import sys
import platform

def create_windows_shortcut():
    """Create Windows desktop shortcut"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Get desktop path
        desktop = winshell.desktop()
        
        # Get current directory and Python executable
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        # Create shortcut
        shortcut_path = os.path.join(desktop, "Secure POS System.lnk")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = python_exe
        shortcut.Arguments = "simple_secure_launcher.py"
        shortcut.WorkingDirectory = current_dir
        shortcut.IconLocation = os.path.join(current_dir, "assets", "logo.ico")
        shortcut.Description = "Secure POS System - Daily Use Launcher"
        shortcut.save()
        
        print(f"✅ Windows shortcut created: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️ Windows shortcut libraries not available")
        print("Installing required packages...")
        try:
            os.system("pip install pywin32 winshell")
            print("✅ Packages installed - please run this script again")
        except:
            print("❌ Failed to install packages")
        return False
    except Exception as e:
        print(f"❌ Failed to create Windows shortcut: {e}")
        return False

def create_windows_batch_shortcut():
    """Create Windows batch file shortcut (fallback)"""
    try:
        # Get desktop path
        import winreg
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                            r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders")
        desktop = winreg.QueryValueEx(key, "Desktop")[0]
        winreg.CloseKey(key)
        
        # Create batch file
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        batch_content = f'''@echo off
cd /d "{current_dir}"
"{python_exe}" simple_secure_launcher.py
pause
'''
        
        batch_path = os.path.join(desktop, "Secure POS System.bat")
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ Windows batch shortcut created: {batch_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create batch shortcut: {e}")
        return False

def create_linux_shortcut():
    """Create Linux desktop shortcut"""
    try:
        # Get desktop path
        desktop = os.path.expanduser("~/Desktop")
        if not os.path.exists(desktop):
            desktop = os.path.expanduser("~")
        
        # Get current directory and Python executable
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        # Create .desktop file
        desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=Secure POS System
Comment=Secure Point of Sale System
Exec={python_exe} {os.path.join(current_dir, "simple_secure_launcher.py")}
Icon={os.path.join(current_dir, "assets", "logo.png")}
Path={current_dir}
Terminal=false
StartupNotify=true
Categories=Office;Finance;
"""
        
        shortcut_path = os.path.join(desktop, "Secure POS System.desktop")
        with open(shortcut_path, 'w') as f:
            f.write(desktop_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Linux shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create Linux shortcut: {e}")
        return False

def create_macos_shortcut():
    """Create macOS shortcut"""
    try:
        # Get desktop path
        desktop = os.path.expanduser("~/Desktop")
        
        # Get current directory and Python executable
        current_dir = os.path.abspath(".")
        python_exe = sys.executable
        
        # Create shell script
        script_content = f"""#!/bin/bash
cd "{current_dir}"
"{python_exe}" simple_secure_launcher.py
"""
        
        shortcut_path = os.path.join(desktop, "Secure POS System.command")
        with open(shortcut_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ macOS shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create macOS shortcut: {e}")
        return False

def create_startup_guide():
    """Create a startup guide file"""
    try:
        guide_content = """# SECURE POS SYSTEM - DAILY USE GUIDE

## For Daily Use:
1. Double-click the "Secure POS System" shortcut on your desktop
2. Wait for security checks to complete
3. Click "🚀 Launch POS System" when ready
4. Use the POS system normally

## First Time Setup:
1. Run the desktop shortcut
2. Click "🔑 Authorize Machine"
3. Enter authorization code: SECURE_POS_2024_AUTH
4. Click "🚀 Launch POS System"

## Troubleshooting:
- If shortcut doesn't work, run "simple_secure_launcher.py" directly
- If authorization fails, contact system administrator
- If security warnings appear, contact support

## Security Features:
- Machine authorization prevents unauthorized use
- File encryption protects against code theft
- Integrity monitoring detects tampering
- Access logging tracks system usage

## Support:
For technical support or additional machine authorization,
contact your system administrator with your machine ID.

---
Security Level: 8/10 - Enhanced Protection
"""
        
        with open("DAILY_USE_GUIDE.txt", 'w') as f:
            f.write(guide_content)
        
        print("✅ Daily use guide created: DAILY_USE_GUIDE.txt")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create guide: {e}")
        return False

def main():
    """Main function"""
    print("🔗 CREATING SECURE POS DESKTOP SHORTCUT")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("simple_secure_launcher.py"):
        print("❌ Error: simple_secure_launcher.py not found!")
        print("Please run this script from the POS system directory.")
        input("Press Enter to exit...")
        return False
    
    system = platform.system()
    success = False
    
    print(f"🖥️ Detected system: {system}")
    
    if system == "Windows":
        print("\n🔗 Creating Windows shortcut...")
        # Try proper shortcut first, then fallback to batch
        success = create_windows_shortcut()
        if not success:
            print("🔄 Trying batch file fallback...")
            success = create_windows_batch_shortcut()
            
    elif system == "Linux":
        print("\n🔗 Creating Linux shortcut...")
        success = create_linux_shortcut()
        
    elif system == "Darwin":  # macOS
        print("\n🔗 Creating macOS shortcut...")
        success = create_macos_shortcut()
        
    else:
        print(f"❌ Unsupported system: {system}")
    
    # Create startup guide regardless
    guide_created = create_startup_guide()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DESKTOP SHORTCUT CREATED SUCCESSFULLY!")
        print("✅ Shortcut available on desktop")
        print("✅ Daily use guide created")
        print("\n📋 For daily use:")
        print("1. Double-click 'Secure POS System' on desktop")
        print("2. Click '🚀 Launch POS System' when ready")
        print("\n🔑 First time authorization code: SECURE_POS_2024_AUTH")
    else:
        print("⚠️ SHORTCUT CREATION FAILED")
        print("📋 Manual startup:")
        print("1. Navigate to POS system folder")
        print("2. Run: python simple_secure_launcher.py")
        
    if guide_created:
        print("📖 See DAILY_USE_GUIDE.txt for detailed instructions")
    
    input("\nPress Enter to continue...")
    return success

if __name__ == "__main__":
    main()
