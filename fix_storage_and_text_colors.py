#!/usr/bin/env python3
"""
Fix storage management theme and ensure all text is white on dark backgrounds
"""

import os
import re

def update_storage_and_text_colors(file_path):
    """Update storage management and fix text colors"""
    if not os.path.exists(file_path):
        print(f"⚠️ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Storage-specific updates
        storage_replacements = {
            # Backgrounds
            "bg='#f0f0f0'": "bg='#1a1a1a'",
            "bg='#2c3e50'": "bg='#2d2d2d'",
            "bg='white'": "bg='#2d2d2d'",
            
            # Text colors - ensure white text on dark backgrounds
            "fg='#2c3e50'": "fg='white'",
            "fg='black'": "fg='white'",
            
            # Fonts
            "font=('Helvetica'": "font=('Segoe UI'",
        }
        
        # Apply replacements
        for old_val, new_val in storage_replacements.items():
            content = content.replace(old_val, new_val)
        
        # Special regex patterns for complex cases
        regex_patterns = [
            # Frame with bg parameter
            (r"Frame\(([^,]+),\s*bg='white'", r"Frame(\1, bg='#2d2d2d'"),
            (r"Frame\(([^,]+),\s*bg='#f0f0f0'", r"Frame(\1, bg='#1a1a1a'"),
            
            # Label with bg parameter
            (r"Label\(([^,]+),([^,]*),\s*bg='white'", r"Label(\1,\2, bg='#2d2d2d'"),
            (r"Label\(([^,]+),([^,]*),\s*bg='#f0f0f0'", r"Label(\1,\2, bg='#1a1a1a'"),
            
            # Canvas with bg parameter
            (r"Canvas\(([^,]+),\s*bg='white'", r"Canvas(\1, bg='#2d2d2d'"),
            (r"Canvas\(([^,]+),\s*bg='#f0f0f0'", r"Canvas(\1, bg='#1a1a1a'"),
        ]
        
        for pattern, replacement in regex_patterns:
            content = re.sub(pattern, replacement, content)
        
        # Ensure text is white on dark backgrounds - more comprehensive
        text_color_fixes = [
            # Labels with dark backgrounds should have white text
            (r"(Label\([^)]+bg='#2d2d2d'[^)]+)fg='#[0-9a-fA-F]+'", r"\1fg='white'"),
            (r"(Label\([^)]+bg='#1a1a1a'[^)]+)fg='#[0-9a-fA-F]+'", r"\1fg='white'"),
            (r"(Label\([^)]+bg='#404040'[^)]+)fg='#[0-9a-fA-F]+'", r"\1fg='white'"),
            
            # Fix any remaining black text on dark backgrounds
            (r"(bg='#2d2d2d'[^)]+)fg='black'", r"\1fg='white'"),
            (r"(bg='#1a1a1a'[^)]+)fg='black'", r"\1fg='white'"),
            (r"(bg='#404040'[^)]+)fg='black'", r"\1fg='white'"),
        ]
        
        for pattern, replacement in text_color_fixes:
            content = re.sub(pattern, replacement, content)
        
        # Write back the updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Updated: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Fix storage management and text colors system-wide"""
    
    print("🔧 FIXING STORAGE MANAGEMENT AND TEXT COLORS")
    print("=" * 45)
    print("📝 Updating storage management theme")
    print("🔤 Ensuring white text on dark backgrounds")
    print()
    
    # List of files to update
    files_to_update = [
        # Main storage files
        'storage_management.py',
        'YES/storage_management.py',
        
        # All other files to ensure text colors are correct
        'pos_screen.py',
        'product_management.py',
        'user_management.py',
        'sales_history.py',
        'receipt_settings.py',
        'number_keyboard.py',
        
        # YES versions
        'YES/pos_screen.py',
        'YES/product_management.py',
        'YES/user_management.py',
        'YES/sales_history.py',
        'YES/receipt_settings.py',
        'YES/number_keyboard.py',
    ]
    
    updated_count = 0
    total_count = len(files_to_update)
    
    for file_path in files_to_update:
        if update_storage_and_text_colors(file_path):
            updated_count += 1
    
    print()
    print("=" * 45)
    print("📊 RESULTS")
    print("=" * 45)
    print(f"Files Updated: {updated_count}/{total_count}")
    
    if updated_count >= total_count - 2:  # Allow for a couple missing files
        print("🎉 STORAGE AND TEXT COLORS FIXED!")
        print("✅ Storage management themed")
        print("✅ White text on dark backgrounds")
        print("✅ Segoe UI font applied")
        print("✅ Button colors preserved")
    else:
        print("⚠️ Some files could not be updated")
        print("❌ Text color fixes may be incomplete")
    
    return updated_count >= total_count - 2

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 Storage management and text colors completely fixed!")
        print("📦 Storage interface now uses orange/black theme")
        print("⚪ All text is white on dark backgrounds")
        print("🔤 Segoe UI font applied throughout")
        print("🔘 Button colors preserved as requested")
    else:
        print("\n❌ Storage and text color fixes need attention")
    
    exit(0 if success else 1)
